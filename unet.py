import colorsys
import copy
import time
import math
import cv2



import numpy as np
from PIL import Image
from tensorflow.python.ops.gen_math_ops import sqrt

from nets.unet import Unet as unet
from skimage import morphology,draw
import matplotlib.pyplot as plt

#--------------------------------------------#
#   使用自己训练好的模型预测需要修改2个参数
#   model_path和num_classes都需要修改！
#   如果出现shape不匹配
#   一定要注意训练时的model_path和num_classes数的修改
#--------------------------------------------#
class Unet(object):
    _defaults = {
        #"model_path"        : 'logs/Epoch100-Total_Loss0.0642-Val_Loss0.0958.h5',
        "model_path"         : 'logs/Epoch85-Total_Loss0.1137-Val_Loss0.1175.h5',
        #"model_path"        : 'logs/Epoch95-Total_Loss0.0506-Val_Loss0.0337.h5',
        #"model_path"        : 'logs/Epoch98-Total_Loss0.0568-Val_Loss0.0532.h5',
      
        "model_image_size"  : (512, 512, 3),
        "num_classes"       : 4,
        
        #--------------------------------#
        #   blend参数用于控制是否
        #   让识别结果和原图混合
        #--------------------------------#
        "blend"             : True,
    }

    #---------------------------------------------------#
    #   初始化UNET
    #---------------------------------------------------#
    def __init__(self, **kwargs):
        self.__dict__.update(self._defaults)
        self.generate()

    #---------------------------------------------------#
    #   载入模型
    #---------------------------------------------------#
    def generate(self):
        #-------------------------------#
        #   载入模型与权值
        #-------------------------------#
        self.model = unet(self.model_image_size, self.num_classes)

        self.model.load_weights(self.model_path)
        print('{} model loaded.'.format(self.model_path))
        '''
        重点！！！self.colors为各类别显示出来的颜色
        '''
        if self.num_classes <= 21:
            self.colors = [(0, 0, 0), (0, 0, 255), (0, 255, 0), (255, 0, 0), (0, 0, 128), (128, 0, 128), (0, 128, 128), 
                    (128, 128, 128), (64, 0, 0), (192, 0, 0), (64, 128, 0), (192, 128, 0), (64, 0, 128), (192, 0, 128), 
                    (64, 128, 128), (192, 128, 128), (0, 64, 0), (128, 64, 0), (0, 192, 0), (128, 192, 0), (0, 64, 128), (128, 64, 12)]
                    
        else:
            # 画框设置不同的颜色
            hsv_tuples = [(x / len(self.class_names), 1., 1.)
                        for x in range(len(self.class_names))]
            self.colors = list(map(lambda x: colorsys.hsv_to_rgb(*x), hsv_tuples))
            self.colors = list(
                map(lambda x: (int(x[0] * 255), int(x[1] * 255), int(x[2] * 255)),
                    self.colors))

    def letterbox_image(self ,image, size):
        image = image.convert("RGB")
        iw, ih = image.size
        w, h = size
        scale = min(w/iw, h/ih)
        nw = int(iw*scale)
        nh = int(ih*scale)

        image = image.resize((nw,nh), Image.BICUBIC)
        new_image = Image.new('RGB', size, (128,128,128))
        new_image.paste(image, ((w-nw)//2, (h-nh)//2))
        return new_image,nw,nh

    #---------------------------------------------------#
    #   检测图片
    #---------------------------------------------------#
    def detect_image(self, image):
        #---------------------------------------------------------#
        #   在这里将图像转换成RGB图像，防止灰度图在预测时报错。
        #---------------------------------------------------------#
        image = image.convert('RGB')
        
        #---------------------------------------------------#
        #   对输入图像进行一个备份，后面用于绘图
        #---------------------------------------------------#
        old_img = copy.deepcopy(image)
        orininal_h = np.array(image).shape[0]
        orininal_w = np.array(image).shape[1]

        #---------------------------------------------------#
        #   进行不失真的resize，添加灰条，进行图像归一化
        #---------------------------------------------------#
        img, nw, nh = self.letterbox_image(image,(self.model_image_size[1],self.model_image_size[0]))
        img = np.asarray([np.array(img)/255])
        
        #---------------------------------------------------#
        #   图片传入网络进行预测
        #---------------------------------------------------#
        pr = self.model.predict(img)[0]
        #---------------------------------------------------#
        #   取出每一个像素点的种类
        #---------------------------------------------------#
        pr = pr.argmax(axis=-1).reshape([self.model_image_size[0],self.model_image_size[1]])
        #--------------------------------------#
        #   将灰条部分截取掉
        #--------------------------------------#
        pr = pr[int((self.model_image_size[0]-nh)//2):int((self.model_image_size[0]-nh)//2+nh), int((self.model_image_size[1]-nw)//2):int((self.model_image_size[1]-nw)//2+nw)]

        #------------------------------------------------#
        #   创建一副新图，并根据每个像素点的种类赋予颜色
        #------------------------------------------------#
        seg_img = np.zeros((np.shape(pr)[0],np.shape(pr)[1],3))
        seg_img1 = np.zeros((np.shape(pr)[0],np.shape(pr)[1],3))
        for c in range(self.num_classes):
        #c=1
            seg_img[:,:,0] += ((pr[:,: ] == c )*( self.colors[c][0] )).astype('uint8')
            seg_img[:,:,1] += ((pr[:,: ] == c )*( self.colors[c][1] )).astype('uint8')
            seg_img[:,:,2] += ((pr[:,: ] == c )*( self.colors[c][2] )).astype('uint8')

        for c in range(self.num_classes):
        #c=1
            if c!=2:
                seg_img1[:,:,0] += ((pr[:,: ] == c )*( self.colors[c][0] )).astype('uint8')
                seg_img1[:,:,1] += ((pr[:,: ] == c )*( self.colors[c][1] )).astype('uint8')
                seg_img1[:,:,2] += ((pr[:,: ] == c )*( self.colors[c][2] )).astype('uint8')
        i=0
        j=0
        count=0
        iz=0
        jz=0
        RX=0
        RY=0
        '''
        重点！！！计算某一类图像区域的中心点坐标
        '''
        
        for i in range(0,345,3):
            for j in range(0,510,3):
            
                if seg_img[i,j,2]==255:
                    jz=jz+j
                    iz=iz+i
                    count=count+1
                    
                
        if count!=0:        
            cx=round(iz/count)
            cy=round(jz/count)
            r=math.sqrt((cx-174)**2+(cy-256)**2)

            '''
            该程序为将图像区域的中心点在图像中显示出来
            for i in range(cx-3,cx+3,1):
                for j in range(cy-3,cy+3,1):
                    
                    seg_img[i,j,0] += 128
                    seg_img[i,j,1] += 128
                    seg_img[i,j,2] += 128
                    '''
            if r>50:

                RY=-(r-50)/20*(cx-174)/r
                RX=-(r-50)/20*(cy-256)/r 
                '''
        重点！！！计算机械臂需要旋转的角度
        '''
                #RY=-(150-100)/20*(cx-174)/r
                #RX=(150-100)/20*(cy-256)/r 
                # return RX,RY
        
        #------------------------------------------------#
        #   将新图片转换成Image的形式
        #------------------------------------------------#
        image1 = Image.fromarray(np.uint8(seg_img)).resize((orininal_w,orininal_h), Image.NEAREST)
        image2 = Image.fromarray(np.uint8(seg_img1)).resize((orininal_w,orininal_h), Image.NEAREST)
        
        #image_gujia = morphology.skeletonize(seg_img)

        #------------------------------------------------#
        #   将新图片和原图片混合
        #------------------------------------------------#
        if self.blend:
           
           image = Image.blend(old_img,image1,0.7)
           

        result = {"image":image, "RX":RX, "RY":RY,"count":count ,"image1":image1,"image2":image2}
        '''
        重点！！！程序的返回值，将在predict程序中被调用
        '''

        return result

    def get_FPS(self, image, test_interval):
        orininal_h = np.array(image).shape[0]
        orininal_w = np.array(image).shape[1]

        img, nw, nh = self.letterbox_image(image,(self.model_image_size[1],self.model_image_size[0]))
        img = np.asarray([np.array(img)/255])

        pr = self.model.predict(img)[0]
        pr = pr.argmax(axis=-1).reshape([self.model_image_size[0],self.model_image_size[1]])
        pr = pr[int((self.model_image_size[0]-nh)//2):int((self.model_image_size[0]-nh)//2+nh), int((self.model_image_size[1]-nw)//2):int((self.model_image_size[1]-nw)//2+nw)]
        
        image = Image.fromarray(np.uint8(pr)).resize((orininal_w,orininal_h), Image.NEAREST)

        t1 = time.time()
        for _ in range(test_interval):
            pr = self.model.predict(img)[0]
            pr = pr.argmax(axis=-1).reshape([self.model_image_size[0],self.model_image_size[1]])
            pr = pr[int((self.model_image_size[0]-nh)//2):int((self.model_image_size[0]-nh)//2+nh), int((self.model_image_size[1]-nw)//2):int((self.model_image_size[1]-nw)//2+nw)]
            image = Image.fromarray(np.uint8(pr)).resize((orininal_w,orininal_h), Image.NEAREST)
            
        t2 = time.time()
        tact_time = (t2 - t1) / test_interval
        return tact_time
        
