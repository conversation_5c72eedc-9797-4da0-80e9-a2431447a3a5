<?xml version="1.0" encoding="UTF-8"?>
<blueprint
    xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
    xmlns='http://www.osgi.org/xmlns/blueprint/v1.0.0'
    xsi:schemaLocation='http://www.osgi.org/xmlns/blueprint/v1.0.0 https://osgi.org/xmlns/blueprint/v1.0.0/blueprint.xsd'>

    <bean id="opencvnativeloader" class="org.opencv.osgi.OpenCVNativeLoader" scope="singleton" init-method="init" />

    <service id="opencvtestservice" ref="opencvnativeloader" interface="org.opencv.osgi.OpenCVInterface" />

</blueprint>
