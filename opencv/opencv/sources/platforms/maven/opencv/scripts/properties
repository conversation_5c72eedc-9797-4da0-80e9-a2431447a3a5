#!/bin/bash
#####################################################################
# This script extracts several properties and then writes them to a
# to a file, 'build.properties'. These properties include:
#
#	1. OpenCV version.
#	2. OpenVC version as a string for use by Maven.
#	3. The CPU binary word size.
#	4. Architecture string.
#	4. OSGi compatible CPU architecture string.
#
# There is no need to execute this script directly as it will be
# called during the Maven build process.
#
# Command-line parameters:
#   $1 - The build directory and where the output file will be written
#   $2 - The name of the output file to write to.
#
# Returns:
#   0 - Successfully written the properties file.
#   1 - Error occurred such as build directory does not exist
#	or OpenCV version could not be determined or an
#	unexpected error.
#
#   <PERSON> <contact (at) avionicengineers (d0t) com>
#   20 April 2016
#
#####################################################################

# Include some external functions and variables
. ./functions

#Test build directory exists
if [  ! -n "$1" ] || [ ! -d $1 ];then
    echo "Build directory not specified or does not exist!"
    exit 1
fi

if [ -n "${versionHeader}" ] && [ -e ${versionHeader} ];then

    extract_version

    bits=$(getconf LONG_BIT)
    architecture=$(arch)
    osgiProcessor="Not Set"

    case ${architecture} in
        x86*)
            echo "This is x86 (32 | 64) bit"
            case ${bits} in
                32)
                    osgiProcessor="x86"
                    ;;
                64)
                    osgiProcessor="x86_64"
                    ;;
                *)
                    osgiProcessor="Unknown"
            esac
            ;;
        arm*)
            echo "This is ARM"
            byteOrder=$(lscpu | grep -i "byte order")
            shopt -s nocasematch
            if [[ "${byteOrder}" == *little* ]]; then
                osgiProcessor="arm_le"
            elif [[ "${byteOrder}}" == *big* ]]; then
                osgiProcessor="arm_be"
            fi
            shopt -u nocasematch
            ;;
        *)
            echo "This is unknown architecture"
        esac

    echo "The version number will be ${majorVersion}.${minorVersion}.${revision}"
    echo "opencv.version=${majorVersion}.${minorVersion}.${revision}" > ${1}/${2}
    echo "lib.version.string=${majorVersion}${minorVersion}${revision}" >> ${1}/${2}
    echo "bits=${bits}" >> ${1}/${2}
    echo "architecture=$(arch)" >> ${1}/${2}
    echo "osgi.processor=${osgiProcessor}" >> ${1}/${2}
    exit 0
else
    echo "Could not locate file ${versionHeader} to determine versioning."
    exit 1
fi
