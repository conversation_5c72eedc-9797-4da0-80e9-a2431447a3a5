<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.opencv</groupId>
        <artifactId>opencv-parent</artifactId>
        <version>4.2.0</version>
    </parent>
    <groupId>org.opencv</groupId>
    <artifactId>opencv</artifactId>
    <packaging>bundle</packaging>
    <name>OpenCV</name>

    <properties>
        <source.path>../../..</source.path>
        <build.directory>${source.path}/build</build.directory>
        <nativelibrary.name>libopencv_java${lib.version.string}.so</nativelibrary.name>
        <resources.directory>${build.directory}/src</resources.directory>
    </properties>
    <build>
        <directory>../../../build/maven/opencv/target</directory>
        <outputDirectory>../../../build/src</outputDirectory>
        <sourceDirectory>../../../build/src</sourceDirectory>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-clean-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <filesets>
                        <fileset>
                            <directory>${build.directory}</directory>
                        </fileset>
                    </filesets>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>1.4.0</version>
                <executions>
                    <execution>
                        <id>get-opencv-version</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>bash</executable>
                            <workingDirectory>${project.basedir}/scripts</workingDirectory>
                            <arguments>
                                <argument>properties</argument>
                                <argument>${build.directory}</argument>
                                <argument>build.properties</argument>
                            </arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>setup-environment</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>bash</executable>
                            <workingDirectory>${project.basedir}/scripts</workingDirectory>
                            <arguments>
                              <!-- Optional packages should be placed BEFORE required ones
                              in the following argument list. -->
                                <argument>deb_package_check</argument>
                                <argument>-olibpng-dev|libpng12-dev</argument>
                                <argument>-olibopenjp2-7-dev|libjasper-dev</argument>
                                <argument>-opython-dev</argument>
                                <argument>-opython-numpy</argument>
                                <argument>build-essential</argument>
                                <argument>cmake</argument>
                                <argument>git</argument>
                                <argument>libgtk2.0-dev</argument>
                                <argument>pkg-config</argument>
                                <argument>libavcodec-dev</argument>
                                <argument>libavformat-dev</argument>
                                <argument>libswscale-dev</argument>
                                <argument>libtbb2</argument>
                                <argument>libtbb-dev</argument>
                                <argument>libjpeg-dev</argument>
                                <argument>libtiff5-dev</argument>
                                <argument>libdc1394-22-dev</argument>
                                <argument>execstack</argument>
                                <argument>ant</argument>
                            </arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>check-execstack</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <workingDirectory>${project.basedir}/scripts</workingDirectory>
                            <executable>bash</executable>
                            <arguments>
                                <argument>execstack_check</argument>
                                <argument>${build.directory}/lib/libopencv_java${lib.version.string}.so</argument>
                            </arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${resources.directory}</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>resources</directory>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>properties-maven-plugin</artifactId>
                <version>1.0.0</version>
                <executions>
                    <execution>
                        <id>set-arch-properties</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>read-project-properties</goal>
                        </goals>
                        <configuration>
                            <files>
                                <file>${build.directory}/build.properties</file>
                            </files>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>1.4.1</version>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <id>enforce-os</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireOS>
                                    <family>unix</family>
                                    <message>This POM is written to function on UNIX family of OS.
                                        More specifically it should be a Debian flavour of Linux.</message>
                                </requireOS>
                            </rules>
                        </configuration>
                    </execution>
                    <execution>
                        <id>enforce-environment</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireEnvironmentVariable>
                                    <level>ERROR</level>
                                    <variableName>ANT_HOME</variableName>
                                    <message>$ANT_HOME is not set. Build may fail.</message>
                                </requireEnvironmentVariable>
                                <requireEnvironmentVariable>
                                    <level>ERROR</level>
                                    <variableName>JAVA_HOME</variableName>
                                    <message>$JAVA_HOME is not set. Build will fail.</message>
                                </requireEnvironmentVariable>
                                <requireEnvironmentVariable>
                                    <level>WARN</level>
                                    <variableName>MAKEFLAGS</variableName>
                                    <message>No MAKEFLAGS environment variable. Build may be slow.
To speed up the build you can try exporting MAKEFLAGS=-jX where X equals the number of parallel builds.</message>
                                </requireEnvironmentVariable>
                            </rules>
                        </configuration>
                    </execution>
                    <execution>
                        <phase>process-resources</phase>
                        <id>check-versions-match</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireProperty>
                                    <property>project.version</property>
                                    <regex>${opencv.version}</regex>
                                    <regexMessage>The Maven POM version ${project.version} does not match the extracted OpenCV version ${opencv.version}.
To correct this please execute the following Maven command from the Maven root directory:
    mvn versions:set -DnewVersion=$(. ./opencv/scripts/functions &amp;&amp; cd ./opencv/scripts &amp;&amp; extract_version &amp;&amp; echo $REPLY)</regexMessage>
                                </requireProperty>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <version>2.3.7</version>
                <extensions>true</extensions>
                <configuration>
                    <instructions>
                        <Export-Package>*</Export-Package>
                        <Bundle-NativeCode>${nativelibrary.name};osname=linux;processor=${osgi.processor}</Bundle-NativeCode>
                        <Include-Resource>${build.directory}/lib/${nativelibrary.name}</Include-Resource>
                    </instructions>
                    <manifestLocation>${build.directory}/manifest</manifestLocation>
                    <niceManifest>true</niceManifest>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.googlecode.cmake-maven-project</groupId>
                <artifactId>cmake-maven-plugin</artifactId>
                <version>3.4.1-b2</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <id>cmake-generate</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <sourcePath>../../..</sourcePath>
                            <targetPath>../../../build</targetPath>
                            <generator>Unix Makefiles</generator>
                            <options>
                                <option>-DBUILD_SHARED_LIBS:BOOL=OFF</option>
                            </options>
                        </configuration>
                    </execution>
                    <execution>
                        <phase>generate-sources</phase>
                        <id>cmake-compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <target>opencv_java</target>
                            <projectDirectory>../../../build</projectDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.15</version>
            </plugin>
        </plugins>
    </build>
</project>
