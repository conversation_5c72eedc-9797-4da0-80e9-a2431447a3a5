<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="org.opencv.engine"
    android:versionCode="345@ANDROID_PLATFORM_ID@"
    android:versionName="3.45">

    <uses-sdk android:minSdkVersion="@ANDROID_NATIVE_API_LEVEL@" android:targetSdkVersion="22"/>
    <uses-feature android:name="android.hardware.touchscreen" android:required="false"/>

    <application
        android:icon="@drawable/icon"
        android:label="@string/app_name" android:allowBackup="true">

    <service android:exported="true" android:name="OpenCVEngineService" android:process=":OpenCVEngineProcess">
        <intent-filter>
            <action android:name="org.opencv.engine.BIND"></action>
        </intent-filter>
    </service>

    <activity
        android:name="org.opencv.engine.manager.ManagerActivity"
        android:label="@string/app_name"
        android:screenOrientation="portrait">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
    </activity>
    </application>
</manifest>
