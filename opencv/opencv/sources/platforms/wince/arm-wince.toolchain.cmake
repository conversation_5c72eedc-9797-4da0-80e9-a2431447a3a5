set(CMAKE_SYSTEM_NAME WindowsCE)

if(NOT CMAKE_SYSTEM_VERSION)
  set(CMAKE_SYSTEM_VERSION 8.0)
endif()

if(NOT CMAKE_SYSTEM_PROCESSOR)
  set(CMAKE_SYSTEM_PROCESSOR armv7-a)
endif()

if(NOT CMAKE_GENERATOR_TOOLSET)
  set(CMAKE_GENERATOR_TOOLSET CE800)
endif()

# Needed to make try_compile to succeed
if(BUILD_HEADLESS)
  set(CMAKE_USER_MAKE_RULES_OVERRIDE
      ${CMAKE_CURRENT_LIST_DIR}/arm-wince-headless-overrides.cmake)
endif()

if(NOT CMAKE_FIND_ROOT_PATH_MODE_PROGRAM)
  set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
endif()

if(NOT CMAKE_FIND_ROOT_PATH_MODE_LIBRARY)
  set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
endif()

if(NOT CMAKE_FIND_ROOT_PATH_MODE_INCLUDE)
  set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
endif()

if(NOT CMAKE_FIND_ROOT_PATH_MODE_PACKAGE)
  set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)
endif()
