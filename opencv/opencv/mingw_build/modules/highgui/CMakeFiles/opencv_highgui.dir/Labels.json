{"sources": [{"file": "D:/unet/opencv/opencv/sources/modules/highgui/include/opencv2/highgui.hpp", "labels": ["Main", "opencv_highgui", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/highgui/include/opencv2/highgui/highgui.hpp", "labels": ["Main", "opencv_highgui", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/highgui/include/opencv2/highgui/highgui_c.h", "labels": ["Main", "opencv_highgui", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/highgui/src/window.cpp", "labels": ["Main", "opencv_highgui", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/highgui/src/roiSelector.cpp", "labels": ["Main", "opencv_highgui", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/highgui/src/window_w32.cpp", "labels": ["Main", "opencv_highgui", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/highgui/src/precomp.hpp", "labels": ["Main", "opencv_highgui", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/cvconfig.h"}, {"file": "D:/unet/opencv/opencv/mingw_build/opencv2/opencv_modules.hpp"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/highgui/vs_version.rc"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/highgui/opencv_highgui_main.cpp"}], "target": {"labels": ["Main", "opencv_highgui", "<PERSON><PERSON><PERSON>"], "name": "opencv_highgui"}}