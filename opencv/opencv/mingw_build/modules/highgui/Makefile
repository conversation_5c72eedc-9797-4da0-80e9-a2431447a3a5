# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\modules\highgui\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/highgui/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/highgui/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/highgui/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/highgui/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
modules/highgui/CMakeFiles/opencv_highgui.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/highgui/CMakeFiles/opencv_highgui.dir/rule
.PHONY : modules/highgui/CMakeFiles/opencv_highgui.dir/rule

# Convenience name for target.
opencv_highgui: modules/highgui/CMakeFiles/opencv_highgui.dir/rule

.PHONY : opencv_highgui

# fast build rule for target.
opencv_highgui/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/build
.PHONY : opencv_highgui/fast

opencv_highgui_main.obj: opencv_highgui_main.cpp.obj

.PHONY : opencv_highgui_main.obj

# target to build an object file
opencv_highgui_main.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/opencv_highgui_main.cpp.obj
.PHONY : opencv_highgui_main.cpp.obj

opencv_highgui_main.i: opencv_highgui_main.cpp.i

.PHONY : opencv_highgui_main.i

# target to preprocess a source file
opencv_highgui_main.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/opencv_highgui_main.cpp.i
.PHONY : opencv_highgui_main.cpp.i

opencv_highgui_main.s: opencv_highgui_main.cpp.s

.PHONY : opencv_highgui_main.s

# target to generate assembly for a file
opencv_highgui_main.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/opencv_highgui_main.cpp.s
.PHONY : opencv_highgui_main.cpp.s

src/roiSelector.obj: src/roiSelector.cpp.obj

.PHONY : src/roiSelector.obj

# target to build an object file
src/roiSelector.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/src/roiSelector.cpp.obj
.PHONY : src/roiSelector.cpp.obj

src/roiSelector.i: src/roiSelector.cpp.i

.PHONY : src/roiSelector.i

# target to preprocess a source file
src/roiSelector.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/src/roiSelector.cpp.i
.PHONY : src/roiSelector.cpp.i

src/roiSelector.s: src/roiSelector.cpp.s

.PHONY : src/roiSelector.s

# target to generate assembly for a file
src/roiSelector.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/src/roiSelector.cpp.s
.PHONY : src/roiSelector.cpp.s

src/window.obj: src/window.cpp.obj

.PHONY : src/window.obj

# target to build an object file
src/window.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/src/window.cpp.obj
.PHONY : src/window.cpp.obj

src/window.i: src/window.cpp.i

.PHONY : src/window.i

# target to preprocess a source file
src/window.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/src/window.cpp.i
.PHONY : src/window.cpp.i

src/window.s: src/window.cpp.s

.PHONY : src/window.s

# target to generate assembly for a file
src/window.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/src/window.cpp.s
.PHONY : src/window.cpp.s

src/window_w32.obj: src/window_w32.cpp.obj

.PHONY : src/window_w32.obj

# target to build an object file
src/window_w32.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/src/window_w32.cpp.obj
.PHONY : src/window_w32.cpp.obj

src/window_w32.i: src/window_w32.cpp.i

.PHONY : src/window_w32.i

# target to preprocess a source file
src/window_w32.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/src/window_w32.cpp.i
.PHONY : src/window_w32.cpp.i

src/window_w32.s: src/window_w32.cpp.s

.PHONY : src/window_w32.s

# target to generate assembly for a file
src/window_w32.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/src/window_w32.cpp.s
.PHONY : src/window_w32.cpp.s

vs_version.obj: vs_version.rc.obj

.PHONY : vs_version.obj

# target to build an object file
vs_version.rc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/vs_version.rc.obj
.PHONY : vs_version.rc.obj

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... opencv_highgui
	@echo ... package
	@echo ... package_source
	@echo ... rebuild_cache
	@echo ... test
	@echo ... list_install_components
	@echo ... install/strip
	@echo ... opencv_highgui_main.obj
	@echo ... opencv_highgui_main.i
	@echo ... opencv_highgui_main.s
	@echo ... src/roiSelector.obj
	@echo ... src/roiSelector.i
	@echo ... src/roiSelector.s
	@echo ... src/window.obj
	@echo ... src/window.i
	@echo ... src/window.s
	@echo ... src/window_w32.obj
	@echo ... src/window_w32.i
	@echo ... src/window_w32.s
	@echo ... vs_version.obj
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

