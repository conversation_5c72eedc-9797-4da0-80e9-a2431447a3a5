# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\modules\objdetect\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/objdetect/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/objdetect/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/objdetect/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/objdetect/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
modules/objdetect/CMakeFiles/opencv_objdetect.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/objdetect/CMakeFiles/opencv_objdetect.dir/rule
.PHONY : modules/objdetect/CMakeFiles/opencv_objdetect.dir/rule

# Convenience name for target.
opencv_objdetect: modules/objdetect/CMakeFiles/opencv_objdetect.dir/rule

.PHONY : opencv_objdetect

# fast build rule for target.
opencv_objdetect/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/build
.PHONY : opencv_objdetect/fast

opencl_kernels_objdetect.obj: opencl_kernels_objdetect.cpp.obj

.PHONY : opencl_kernels_objdetect.obj

# target to build an object file
opencl_kernels_objdetect.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/opencl_kernels_objdetect.cpp.obj
.PHONY : opencl_kernels_objdetect.cpp.obj

opencl_kernels_objdetect.i: opencl_kernels_objdetect.cpp.i

.PHONY : opencl_kernels_objdetect.i

# target to preprocess a source file
opencl_kernels_objdetect.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/opencl_kernels_objdetect.cpp.i
.PHONY : opencl_kernels_objdetect.cpp.i

opencl_kernels_objdetect.s: opencl_kernels_objdetect.cpp.s

.PHONY : opencl_kernels_objdetect.s

# target to generate assembly for a file
opencl_kernels_objdetect.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/opencl_kernels_objdetect.cpp.s
.PHONY : opencl_kernels_objdetect.cpp.s

opencv_objdetect_main.obj: opencv_objdetect_main.cpp.obj

.PHONY : opencv_objdetect_main.obj

# target to build an object file
opencv_objdetect_main.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/opencv_objdetect_main.cpp.obj
.PHONY : opencv_objdetect_main.cpp.obj

opencv_objdetect_main.i: opencv_objdetect_main.cpp.i

.PHONY : opencv_objdetect_main.i

# target to preprocess a source file
opencv_objdetect_main.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/opencv_objdetect_main.cpp.i
.PHONY : opencv_objdetect_main.cpp.i

opencv_objdetect_main.s: opencv_objdetect_main.cpp.s

.PHONY : opencv_objdetect_main.s

# target to generate assembly for a file
opencv_objdetect_main.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/opencv_objdetect_main.cpp.s
.PHONY : opencv_objdetect_main.cpp.s

src/cascadedetect.obj: src/cascadedetect.cpp.obj

.PHONY : src/cascadedetect.obj

# target to build an object file
src/cascadedetect.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/cascadedetect.cpp.obj
.PHONY : src/cascadedetect.cpp.obj

src/cascadedetect.i: src/cascadedetect.cpp.i

.PHONY : src/cascadedetect.i

# target to preprocess a source file
src/cascadedetect.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/cascadedetect.cpp.i
.PHONY : src/cascadedetect.cpp.i

src/cascadedetect.s: src/cascadedetect.cpp.s

.PHONY : src/cascadedetect.s

# target to generate assembly for a file
src/cascadedetect.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/cascadedetect.cpp.s
.PHONY : src/cascadedetect.cpp.s

src/cascadedetect_convert.obj: src/cascadedetect_convert.cpp.obj

.PHONY : src/cascadedetect_convert.obj

# target to build an object file
src/cascadedetect_convert.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/cascadedetect_convert.cpp.obj
.PHONY : src/cascadedetect_convert.cpp.obj

src/cascadedetect_convert.i: src/cascadedetect_convert.cpp.i

.PHONY : src/cascadedetect_convert.i

# target to preprocess a source file
src/cascadedetect_convert.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/cascadedetect_convert.cpp.i
.PHONY : src/cascadedetect_convert.cpp.i

src/cascadedetect_convert.s: src/cascadedetect_convert.cpp.s

.PHONY : src/cascadedetect_convert.s

# target to generate assembly for a file
src/cascadedetect_convert.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/cascadedetect_convert.cpp.s
.PHONY : src/cascadedetect_convert.cpp.s

src/detection_based_tracker.obj: src/detection_based_tracker.cpp.obj

.PHONY : src/detection_based_tracker.obj

# target to build an object file
src/detection_based_tracker.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/detection_based_tracker.cpp.obj
.PHONY : src/detection_based_tracker.cpp.obj

src/detection_based_tracker.i: src/detection_based_tracker.cpp.i

.PHONY : src/detection_based_tracker.i

# target to preprocess a source file
src/detection_based_tracker.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/detection_based_tracker.cpp.i
.PHONY : src/detection_based_tracker.cpp.i

src/detection_based_tracker.s: src/detection_based_tracker.cpp.s

.PHONY : src/detection_based_tracker.s

# target to generate assembly for a file
src/detection_based_tracker.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/detection_based_tracker.cpp.s
.PHONY : src/detection_based_tracker.cpp.s

src/hog.obj: src/hog.cpp.obj

.PHONY : src/hog.obj

# target to build an object file
src/hog.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/hog.cpp.obj
.PHONY : src/hog.cpp.obj

src/hog.i: src/hog.cpp.i

.PHONY : src/hog.i

# target to preprocess a source file
src/hog.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/hog.cpp.i
.PHONY : src/hog.cpp.i

src/hog.s: src/hog.cpp.s

.PHONY : src/hog.s

# target to generate assembly for a file
src/hog.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/hog.cpp.s
.PHONY : src/hog.cpp.s

src/main.obj: src/main.cpp.obj

.PHONY : src/main.obj

# target to build an object file
src/main.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/main.cpp.obj
.PHONY : src/main.cpp.obj

src/main.i: src/main.cpp.i

.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s

.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/qrcode.obj: src/qrcode.cpp.obj

.PHONY : src/qrcode.obj

# target to build an object file
src/qrcode.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/qrcode.cpp.obj
.PHONY : src/qrcode.cpp.obj

src/qrcode.i: src/qrcode.cpp.i

.PHONY : src/qrcode.i

# target to preprocess a source file
src/qrcode.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/qrcode.cpp.i
.PHONY : src/qrcode.cpp.i

src/qrcode.s: src/qrcode.cpp.s

.PHONY : src/qrcode.s

# target to generate assembly for a file
src/qrcode.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/qrcode.cpp.s
.PHONY : src/qrcode.cpp.s

vs_version.obj: vs_version.rc.obj

.PHONY : vs_version.obj

# target to build an object file
vs_version.rc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/vs_version.rc.obj
.PHONY : vs_version.rc.obj

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... rebuild_cache
	@echo ... test
	@echo ... opencv_objdetect
	@echo ... package
	@echo ... package_source
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... list_install_components
	@echo ... install/strip
	@echo ... opencl_kernels_objdetect.obj
	@echo ... opencl_kernels_objdetect.i
	@echo ... opencl_kernels_objdetect.s
	@echo ... opencv_objdetect_main.obj
	@echo ... opencv_objdetect_main.i
	@echo ... opencv_objdetect_main.s
	@echo ... src/cascadedetect.obj
	@echo ... src/cascadedetect.i
	@echo ... src/cascadedetect.s
	@echo ... src/cascadedetect_convert.obj
	@echo ... src/cascadedetect_convert.i
	@echo ... src/cascadedetect_convert.s
	@echo ... src/detection_based_tracker.obj
	@echo ... src/detection_based_tracker.i
	@echo ... src/detection_based_tracker.s
	@echo ... src/hog.obj
	@echo ... src/hog.i
	@echo ... src/hog.s
	@echo ... src/main.obj
	@echo ... src/main.i
	@echo ... src/main.s
	@echo ... src/qrcode.obj
	@echo ... src/qrcode.i
	@echo ... src/qrcode.s
	@echo ... vs_version.obj
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

