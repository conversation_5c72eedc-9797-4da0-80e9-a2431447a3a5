{"sources": [{"file": "D:/unet/opencv/opencv/sources/modules/objdetect/include/opencv2/objdetect.hpp", "labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/objdetect/include/opencv2/objdetect/detection_based_tracker.hpp", "labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/objdetect/include/opencv2/objdetect/objdetect.hpp", "labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/objdetect/src/cascadedetect.cpp", "labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/objdetect/src/cascadedetect_convert.cpp", "labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/objdetect/src/detection_based_tracker.cpp", "labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/objdetect/src/hog.cpp", "labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/objdetect/src/main.cpp", "labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/objdetect/src/qrcode.cpp", "labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/objdetect/src/opencl/cascadedetect.cl", "labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/objdetect/src/opencl/objdetect_hog.cl", "labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/objdetect/opencl_kernels_objdetect.cpp", "labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/objdetect/opencl_kernels_objdetect.hpp", "labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/objdetect/src/cascadedetect.hpp", "labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/objdetect/src/precomp.hpp", "labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/cvconfig.h"}, {"file": "D:/unet/opencv/opencv/mingw_build/opencv2/opencv_modules.hpp"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/objdetect/vs_version.rc"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/objdetect/opencv_objdetect_main.cpp"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/objdetect/opencl_kernels_objdetect.cpp.rule"}], "target": {"labels": ["Main", "opencv_objdetect", "<PERSON><PERSON><PERSON>"], "name": "opencv_objdetect"}}