# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  "RC"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "D:/unet/opencv/opencv/mingw_build/modules/objdetect/opencl_kernels_objdetect.cpp" "D:/unet/opencv/opencv/mingw_build/modules/objdetect/CMakeFiles/opencv_objdetect.dir/opencl_kernels_objdetect.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/modules/objdetect/opencv_objdetect_main.cpp" "D:/unet/opencv/opencv/mingw_build/modules/objdetect/CMakeFiles/opencv_objdetect.dir/opencv_objdetect_main.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/objdetect/src/cascadedetect.cpp" "D:/unet/opencv/opencv/mingw_build/modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/cascadedetect.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/objdetect/src/cascadedetect_convert.cpp" "D:/unet/opencv/opencv/mingw_build/modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/cascadedetect_convert.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/objdetect/src/detection_based_tracker.cpp" "D:/unet/opencv/opencv/mingw_build/modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/detection_based_tracker.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/objdetect/src/hog.cpp" "D:/unet/opencv/opencv/mingw_build/modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/hog.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/objdetect/src/main.cpp" "D:/unet/opencv/opencv/mingw_build/modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/main.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/objdetect/src/qrcode.cpp" "D:/unet/opencv/opencv/mingw_build/modules/objdetect/CMakeFiles/opencv_objdetect.dir/src/qrcode.cpp.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/3rdparty/quirc/include"
  "D:/unet/opencv/opencv/sources/modules/objdetect/include"
  "modules/objdetect"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/flann/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "D:/unet/opencv/opencv/sources/modules/features2d/include"
  "D:/unet/opencv/opencv/sources/modules/calib3d/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )
set(CMAKE_DEPENDS_CHECK_RC
  "D:/unet/opencv/opencv/mingw_build/modules/objdetect/vs_version.rc" "D:/unet/opencv/opencv/mingw_build/modules/objdetect/CMakeFiles/opencv_objdetect.dir/vs_version.rc.obj"
  )

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_RC
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_RC_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/3rdparty/quirc/include"
  "D:/unet/opencv/opencv/sources/modules/objdetect/include"
  "modules/objdetect"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/flann/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "D:/unet/opencv/opencv/sources/modules/features2d/include"
  "D:/unet/opencv/opencv/sources/modules/calib3d/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/quirc/CMakeFiles/quirc.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/flann/CMakeFiles/opencv_flann.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/imgproc/CMakeFiles/opencv_imgproc.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/core/CMakeFiles/opencv_core.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
