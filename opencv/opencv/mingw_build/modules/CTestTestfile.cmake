# CMake generated Testfile for 
# Source directory: D:/unet/opencv/opencv/sources/modules
# Build directory: D:/unet/opencv/opencv/mingw_build/modules
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
subdirs(".firstpass/calib3d")
subdirs(".firstpass/core")
subdirs(".firstpass/dnn")
subdirs(".firstpass/features2d")
subdirs(".firstpass/flann")
subdirs(".firstpass/gapi")
subdirs(".firstpass/highgui")
subdirs(".firstpass/imgcodecs")
subdirs(".firstpass/imgproc")
subdirs(".firstpass/java")
subdirs(".firstpass/js")
subdirs(".firstpass/ml")
subdirs(".firstpass/objdetect")
subdirs(".firstpass/photo")
subdirs(".firstpass/python")
subdirs(".firstpass/stitching")
subdirs(".firstpass/ts")
subdirs(".firstpass/video")
subdirs(".firstpass/videoio")
subdirs(".firstpass/world")
subdirs("core")
subdirs("flann")
subdirs("imgproc")
subdirs("java_bindings_generator")
subdirs("ml")
subdirs("photo")
subdirs("python_tests")
subdirs("dnn")
subdirs("features2d")
subdirs("gapi")
subdirs("imgcodecs")
subdirs("videoio")
subdirs("calib3d")
subdirs("highgui")
subdirs("objdetect")
subdirs("stitching")
subdirs("video")
subdirs("python_bindings_generator")
subdirs("python3")
