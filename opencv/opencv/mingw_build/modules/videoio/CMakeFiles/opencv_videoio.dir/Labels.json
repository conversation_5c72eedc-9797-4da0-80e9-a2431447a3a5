{"sources": [{"file": "D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio.hpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio/cap_ios.h", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio/container_avi.private.hpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio/legacy/constants_c.h", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio/registry.hpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio/videoio.hpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio/videoio_c.h", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/src/precomp.hpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/src/cap_dshow.hpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/src/videoio_registry.cpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/src/videoio_c.cpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/src/cap.cpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/src/cap_images.cpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/src/cap_mjpeg_encoder.cpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/src/cap_mjpeg_decoder.cpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/src/backend_plugin.cpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/src/backend_static.cpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/src/container_avi.cpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/videoio/src/cap_dshow.cpp", "labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/cvconfig.h"}, {"file": "D:/unet/opencv/opencv/mingw_build/opencv2/opencv_modules.hpp"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/videoio/vs_version.rc"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/videoio/opencv_videoio_main.cpp"}], "target": {"labels": ["Main", "opencv_videoio", "<PERSON><PERSON><PERSON>"], "name": "opencv_videoio"}}