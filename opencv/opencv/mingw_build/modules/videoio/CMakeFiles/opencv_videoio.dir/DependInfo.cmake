# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  "RC"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "D:/unet/opencv/opencv/mingw_build/modules/videoio/opencv_videoio_main.cpp" "D:/unet/opencv/opencv/mingw_build/modules/videoio/CMakeFiles/opencv_videoio.dir/opencv_videoio_main.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/videoio/src/backend_plugin.cpp" "D:/unet/opencv/opencv/mingw_build/modules/videoio/CMakeFiles/opencv_videoio.dir/src/backend_plugin.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/videoio/src/backend_static.cpp" "D:/unet/opencv/opencv/mingw_build/modules/videoio/CMakeFiles/opencv_videoio.dir/src/backend_static.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/videoio/src/cap.cpp" "D:/unet/opencv/opencv/mingw_build/modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/videoio/src/cap_dshow.cpp" "D:/unet/opencv/opencv/mingw_build/modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_dshow.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/videoio/src/cap_images.cpp" "D:/unet/opencv/opencv/mingw_build/modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_images.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/videoio/src/cap_mjpeg_decoder.cpp" "D:/unet/opencv/opencv/mingw_build/modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_mjpeg_decoder.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/videoio/src/cap_mjpeg_encoder.cpp" "D:/unet/opencv/opencv/mingw_build/modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_mjpeg_encoder.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/videoio/src/container_avi.cpp" "D:/unet/opencv/opencv/mingw_build/modules/videoio/CMakeFiles/opencv_videoio.dir/src/container_avi.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/videoio/src/videoio_c.cpp" "D:/unet/opencv/opencv/mingw_build/modules/videoio/CMakeFiles/opencv_videoio.dir/src/videoio_c.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/videoio/src/videoio_registry.cpp" "D:/unet/opencv/opencv/mingw_build/modules/videoio/CMakeFiles/opencv_videoio.dir/src/videoio_registry.cpp.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "ENABLE_PLUGINS"
  "HAVE_DSHOW"
  "STRICT_PLUGIN_CHECK"
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/videoio/include"
  "modules/videoio"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )
set(CMAKE_DEPENDS_CHECK_RC
  "D:/unet/opencv/opencv/mingw_build/modules/videoio/vs_version.rc" "D:/unet/opencv/opencv/mingw_build/modules/videoio/CMakeFiles/opencv_videoio.dir/vs_version.rc.obj"
  )

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_RC
  "ENABLE_PLUGINS"
  "HAVE_DSHOW"
  "STRICT_PLUGIN_CHECK"
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_RC_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/videoio/include"
  "modules/videoio"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/imgproc/CMakeFiles/opencv_imgproc.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/core/CMakeFiles/opencv_core.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
