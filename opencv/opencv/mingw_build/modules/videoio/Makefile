# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\modules\videoio\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/videoio/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/videoio/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/videoio/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/videoio/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
modules/videoio/CMakeFiles/opencv_videoio.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/videoio/CMakeFiles/opencv_videoio.dir/rule
.PHONY : modules/videoio/CMakeFiles/opencv_videoio.dir/rule

# Convenience name for target.
opencv_videoio: modules/videoio/CMakeFiles/opencv_videoio.dir/rule

.PHONY : opencv_videoio

# fast build rule for target.
opencv_videoio/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/build
.PHONY : opencv_videoio/fast

opencv_videoio_main.obj: opencv_videoio_main.cpp.obj

.PHONY : opencv_videoio_main.obj

# target to build an object file
opencv_videoio_main.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/opencv_videoio_main.cpp.obj
.PHONY : opencv_videoio_main.cpp.obj

opencv_videoio_main.i: opencv_videoio_main.cpp.i

.PHONY : opencv_videoio_main.i

# target to preprocess a source file
opencv_videoio_main.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/opencv_videoio_main.cpp.i
.PHONY : opencv_videoio_main.cpp.i

opencv_videoio_main.s: opencv_videoio_main.cpp.s

.PHONY : opencv_videoio_main.s

# target to generate assembly for a file
opencv_videoio_main.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/opencv_videoio_main.cpp.s
.PHONY : opencv_videoio_main.cpp.s

src/backend_plugin.obj: src/backend_plugin.cpp.obj

.PHONY : src/backend_plugin.obj

# target to build an object file
src/backend_plugin.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/backend_plugin.cpp.obj
.PHONY : src/backend_plugin.cpp.obj

src/backend_plugin.i: src/backend_plugin.cpp.i

.PHONY : src/backend_plugin.i

# target to preprocess a source file
src/backend_plugin.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/backend_plugin.cpp.i
.PHONY : src/backend_plugin.cpp.i

src/backend_plugin.s: src/backend_plugin.cpp.s

.PHONY : src/backend_plugin.s

# target to generate assembly for a file
src/backend_plugin.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/backend_plugin.cpp.s
.PHONY : src/backend_plugin.cpp.s

src/backend_static.obj: src/backend_static.cpp.obj

.PHONY : src/backend_static.obj

# target to build an object file
src/backend_static.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/backend_static.cpp.obj
.PHONY : src/backend_static.cpp.obj

src/backend_static.i: src/backend_static.cpp.i

.PHONY : src/backend_static.i

# target to preprocess a source file
src/backend_static.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/backend_static.cpp.i
.PHONY : src/backend_static.cpp.i

src/backend_static.s: src/backend_static.cpp.s

.PHONY : src/backend_static.s

# target to generate assembly for a file
src/backend_static.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/backend_static.cpp.s
.PHONY : src/backend_static.cpp.s

src/cap.obj: src/cap.cpp.obj

.PHONY : src/cap.obj

# target to build an object file
src/cap.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap.cpp.obj
.PHONY : src/cap.cpp.obj

src/cap.i: src/cap.cpp.i

.PHONY : src/cap.i

# target to preprocess a source file
src/cap.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap.cpp.i
.PHONY : src/cap.cpp.i

src/cap.s: src/cap.cpp.s

.PHONY : src/cap.s

# target to generate assembly for a file
src/cap.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap.cpp.s
.PHONY : src/cap.cpp.s

src/cap_dshow.obj: src/cap_dshow.cpp.obj

.PHONY : src/cap_dshow.obj

# target to build an object file
src/cap_dshow.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_dshow.cpp.obj
.PHONY : src/cap_dshow.cpp.obj

src/cap_dshow.i: src/cap_dshow.cpp.i

.PHONY : src/cap_dshow.i

# target to preprocess a source file
src/cap_dshow.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_dshow.cpp.i
.PHONY : src/cap_dshow.cpp.i

src/cap_dshow.s: src/cap_dshow.cpp.s

.PHONY : src/cap_dshow.s

# target to generate assembly for a file
src/cap_dshow.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_dshow.cpp.s
.PHONY : src/cap_dshow.cpp.s

src/cap_images.obj: src/cap_images.cpp.obj

.PHONY : src/cap_images.obj

# target to build an object file
src/cap_images.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_images.cpp.obj
.PHONY : src/cap_images.cpp.obj

src/cap_images.i: src/cap_images.cpp.i

.PHONY : src/cap_images.i

# target to preprocess a source file
src/cap_images.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_images.cpp.i
.PHONY : src/cap_images.cpp.i

src/cap_images.s: src/cap_images.cpp.s

.PHONY : src/cap_images.s

# target to generate assembly for a file
src/cap_images.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_images.cpp.s
.PHONY : src/cap_images.cpp.s

src/cap_mjpeg_decoder.obj: src/cap_mjpeg_decoder.cpp.obj

.PHONY : src/cap_mjpeg_decoder.obj

# target to build an object file
src/cap_mjpeg_decoder.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_mjpeg_decoder.cpp.obj
.PHONY : src/cap_mjpeg_decoder.cpp.obj

src/cap_mjpeg_decoder.i: src/cap_mjpeg_decoder.cpp.i

.PHONY : src/cap_mjpeg_decoder.i

# target to preprocess a source file
src/cap_mjpeg_decoder.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_mjpeg_decoder.cpp.i
.PHONY : src/cap_mjpeg_decoder.cpp.i

src/cap_mjpeg_decoder.s: src/cap_mjpeg_decoder.cpp.s

.PHONY : src/cap_mjpeg_decoder.s

# target to generate assembly for a file
src/cap_mjpeg_decoder.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_mjpeg_decoder.cpp.s
.PHONY : src/cap_mjpeg_decoder.cpp.s

src/cap_mjpeg_encoder.obj: src/cap_mjpeg_encoder.cpp.obj

.PHONY : src/cap_mjpeg_encoder.obj

# target to build an object file
src/cap_mjpeg_encoder.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_mjpeg_encoder.cpp.obj
.PHONY : src/cap_mjpeg_encoder.cpp.obj

src/cap_mjpeg_encoder.i: src/cap_mjpeg_encoder.cpp.i

.PHONY : src/cap_mjpeg_encoder.i

# target to preprocess a source file
src/cap_mjpeg_encoder.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_mjpeg_encoder.cpp.i
.PHONY : src/cap_mjpeg_encoder.cpp.i

src/cap_mjpeg_encoder.s: src/cap_mjpeg_encoder.cpp.s

.PHONY : src/cap_mjpeg_encoder.s

# target to generate assembly for a file
src/cap_mjpeg_encoder.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/cap_mjpeg_encoder.cpp.s
.PHONY : src/cap_mjpeg_encoder.cpp.s

src/container_avi.obj: src/container_avi.cpp.obj

.PHONY : src/container_avi.obj

# target to build an object file
src/container_avi.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/container_avi.cpp.obj
.PHONY : src/container_avi.cpp.obj

src/container_avi.i: src/container_avi.cpp.i

.PHONY : src/container_avi.i

# target to preprocess a source file
src/container_avi.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/container_avi.cpp.i
.PHONY : src/container_avi.cpp.i

src/container_avi.s: src/container_avi.cpp.s

.PHONY : src/container_avi.s

# target to generate assembly for a file
src/container_avi.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/container_avi.cpp.s
.PHONY : src/container_avi.cpp.s

src/videoio_c.obj: src/videoio_c.cpp.obj

.PHONY : src/videoio_c.obj

# target to build an object file
src/videoio_c.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/videoio_c.cpp.obj
.PHONY : src/videoio_c.cpp.obj

src/videoio_c.i: src/videoio_c.cpp.i

.PHONY : src/videoio_c.i

# target to preprocess a source file
src/videoio_c.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/videoio_c.cpp.i
.PHONY : src/videoio_c.cpp.i

src/videoio_c.s: src/videoio_c.cpp.s

.PHONY : src/videoio_c.s

# target to generate assembly for a file
src/videoio_c.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/videoio_c.cpp.s
.PHONY : src/videoio_c.cpp.s

src/videoio_registry.obj: src/videoio_registry.cpp.obj

.PHONY : src/videoio_registry.obj

# target to build an object file
src/videoio_registry.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/videoio_registry.cpp.obj
.PHONY : src/videoio_registry.cpp.obj

src/videoio_registry.i: src/videoio_registry.cpp.i

.PHONY : src/videoio_registry.i

# target to preprocess a source file
src/videoio_registry.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/videoio_registry.cpp.i
.PHONY : src/videoio_registry.cpp.i

src/videoio_registry.s: src/videoio_registry.cpp.s

.PHONY : src/videoio_registry.s

# target to generate assembly for a file
src/videoio_registry.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/src/videoio_registry.cpp.s
.PHONY : src/videoio_registry.cpp.s

vs_version.obj: vs_version.rc.obj

.PHONY : vs_version.obj

# target to build an object file
vs_version.rc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/vs_version.rc.obj
.PHONY : vs_version.rc.obj

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... opencv_videoio
	@echo ... package
	@echo ... package_source
	@echo ... rebuild_cache
	@echo ... test
	@echo ... list_install_components
	@echo ... install/strip
	@echo ... opencv_videoio_main.obj
	@echo ... opencv_videoio_main.i
	@echo ... opencv_videoio_main.s
	@echo ... src/backend_plugin.obj
	@echo ... src/backend_plugin.i
	@echo ... src/backend_plugin.s
	@echo ... src/backend_static.obj
	@echo ... src/backend_static.i
	@echo ... src/backend_static.s
	@echo ... src/cap.obj
	@echo ... src/cap.i
	@echo ... src/cap.s
	@echo ... src/cap_dshow.obj
	@echo ... src/cap_dshow.i
	@echo ... src/cap_dshow.s
	@echo ... src/cap_images.obj
	@echo ... src/cap_images.i
	@echo ... src/cap_images.s
	@echo ... src/cap_mjpeg_decoder.obj
	@echo ... src/cap_mjpeg_decoder.i
	@echo ... src/cap_mjpeg_decoder.s
	@echo ... src/cap_mjpeg_encoder.obj
	@echo ... src/cap_mjpeg_encoder.i
	@echo ... src/cap_mjpeg_encoder.s
	@echo ... src/container_avi.obj
	@echo ... src/container_avi.i
	@echo ... src/container_avi.s
	@echo ... src/videoio_c.obj
	@echo ... src/videoio_c.i
	@echo ... src/videoio_c.s
	@echo ... src/videoio_registry.obj
	@echo ... src/videoio_registry.i
	@echo ... src/videoio_registry.s
	@echo ... vs_version.obj
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

