# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\modules\stitching\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/stitching/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/stitching/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/stitching/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/stitching/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
modules/stitching/CMakeFiles/opencv_stitching.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/stitching/CMakeFiles/opencv_stitching.dir/rule
.PHONY : modules/stitching/CMakeFiles/opencv_stitching.dir/rule

# Convenience name for target.
opencv_stitching: modules/stitching/CMakeFiles/opencv_stitching.dir/rule

.PHONY : opencv_stitching

# fast build rule for target.
opencv_stitching/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/build
.PHONY : opencv_stitching/fast

opencl_kernels_stitching.obj: opencl_kernels_stitching.cpp.obj

.PHONY : opencl_kernels_stitching.obj

# target to build an object file
opencl_kernels_stitching.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/opencl_kernels_stitching.cpp.obj
.PHONY : opencl_kernels_stitching.cpp.obj

opencl_kernels_stitching.i: opencl_kernels_stitching.cpp.i

.PHONY : opencl_kernels_stitching.i

# target to preprocess a source file
opencl_kernels_stitching.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/opencl_kernels_stitching.cpp.i
.PHONY : opencl_kernels_stitching.cpp.i

opencl_kernels_stitching.s: opencl_kernels_stitching.cpp.s

.PHONY : opencl_kernels_stitching.s

# target to generate assembly for a file
opencl_kernels_stitching.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/opencl_kernels_stitching.cpp.s
.PHONY : opencl_kernels_stitching.cpp.s

opencv_stitching_main.obj: opencv_stitching_main.cpp.obj

.PHONY : opencv_stitching_main.obj

# target to build an object file
opencv_stitching_main.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/opencv_stitching_main.cpp.obj
.PHONY : opencv_stitching_main.cpp.obj

opencv_stitching_main.i: opencv_stitching_main.cpp.i

.PHONY : opencv_stitching_main.i

# target to preprocess a source file
opencv_stitching_main.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/opencv_stitching_main.cpp.i
.PHONY : opencv_stitching_main.cpp.i

opencv_stitching_main.s: opencv_stitching_main.cpp.s

.PHONY : opencv_stitching_main.s

# target to generate assembly for a file
opencv_stitching_main.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/opencv_stitching_main.cpp.s
.PHONY : opencv_stitching_main.cpp.s

src/autocalib.obj: src/autocalib.cpp.obj

.PHONY : src/autocalib.obj

# target to build an object file
src/autocalib.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/autocalib.cpp.obj
.PHONY : src/autocalib.cpp.obj

src/autocalib.i: src/autocalib.cpp.i

.PHONY : src/autocalib.i

# target to preprocess a source file
src/autocalib.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/autocalib.cpp.i
.PHONY : src/autocalib.cpp.i

src/autocalib.s: src/autocalib.cpp.s

.PHONY : src/autocalib.s

# target to generate assembly for a file
src/autocalib.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/autocalib.cpp.s
.PHONY : src/autocalib.cpp.s

src/blenders.obj: src/blenders.cpp.obj

.PHONY : src/blenders.obj

# target to build an object file
src/blenders.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/blenders.cpp.obj
.PHONY : src/blenders.cpp.obj

src/blenders.i: src/blenders.cpp.i

.PHONY : src/blenders.i

# target to preprocess a source file
src/blenders.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/blenders.cpp.i
.PHONY : src/blenders.cpp.i

src/blenders.s: src/blenders.cpp.s

.PHONY : src/blenders.s

# target to generate assembly for a file
src/blenders.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/blenders.cpp.s
.PHONY : src/blenders.cpp.s

src/camera.obj: src/camera.cpp.obj

.PHONY : src/camera.obj

# target to build an object file
src/camera.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/camera.cpp.obj
.PHONY : src/camera.cpp.obj

src/camera.i: src/camera.cpp.i

.PHONY : src/camera.i

# target to preprocess a source file
src/camera.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/camera.cpp.i
.PHONY : src/camera.cpp.i

src/camera.s: src/camera.cpp.s

.PHONY : src/camera.s

# target to generate assembly for a file
src/camera.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/camera.cpp.s
.PHONY : src/camera.cpp.s

src/exposure_compensate.obj: src/exposure_compensate.cpp.obj

.PHONY : src/exposure_compensate.obj

# target to build an object file
src/exposure_compensate.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/exposure_compensate.cpp.obj
.PHONY : src/exposure_compensate.cpp.obj

src/exposure_compensate.i: src/exposure_compensate.cpp.i

.PHONY : src/exposure_compensate.i

# target to preprocess a source file
src/exposure_compensate.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/exposure_compensate.cpp.i
.PHONY : src/exposure_compensate.cpp.i

src/exposure_compensate.s: src/exposure_compensate.cpp.s

.PHONY : src/exposure_compensate.s

# target to generate assembly for a file
src/exposure_compensate.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/exposure_compensate.cpp.s
.PHONY : src/exposure_compensate.cpp.s

src/matchers.obj: src/matchers.cpp.obj

.PHONY : src/matchers.obj

# target to build an object file
src/matchers.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/matchers.cpp.obj
.PHONY : src/matchers.cpp.obj

src/matchers.i: src/matchers.cpp.i

.PHONY : src/matchers.i

# target to preprocess a source file
src/matchers.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/matchers.cpp.i
.PHONY : src/matchers.cpp.i

src/matchers.s: src/matchers.cpp.s

.PHONY : src/matchers.s

# target to generate assembly for a file
src/matchers.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/matchers.cpp.s
.PHONY : src/matchers.cpp.s

src/motion_estimators.obj: src/motion_estimators.cpp.obj

.PHONY : src/motion_estimators.obj

# target to build an object file
src/motion_estimators.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/motion_estimators.cpp.obj
.PHONY : src/motion_estimators.cpp.obj

src/motion_estimators.i: src/motion_estimators.cpp.i

.PHONY : src/motion_estimators.i

# target to preprocess a source file
src/motion_estimators.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/motion_estimators.cpp.i
.PHONY : src/motion_estimators.cpp.i

src/motion_estimators.s: src/motion_estimators.cpp.s

.PHONY : src/motion_estimators.s

# target to generate assembly for a file
src/motion_estimators.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/motion_estimators.cpp.s
.PHONY : src/motion_estimators.cpp.s

src/seam_finders.obj: src/seam_finders.cpp.obj

.PHONY : src/seam_finders.obj

# target to build an object file
src/seam_finders.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/seam_finders.cpp.obj
.PHONY : src/seam_finders.cpp.obj

src/seam_finders.i: src/seam_finders.cpp.i

.PHONY : src/seam_finders.i

# target to preprocess a source file
src/seam_finders.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/seam_finders.cpp.i
.PHONY : src/seam_finders.cpp.i

src/seam_finders.s: src/seam_finders.cpp.s

.PHONY : src/seam_finders.s

# target to generate assembly for a file
src/seam_finders.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/seam_finders.cpp.s
.PHONY : src/seam_finders.cpp.s

src/stitcher.obj: src/stitcher.cpp.obj

.PHONY : src/stitcher.obj

# target to build an object file
src/stitcher.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/stitcher.cpp.obj
.PHONY : src/stitcher.cpp.obj

src/stitcher.i: src/stitcher.cpp.i

.PHONY : src/stitcher.i

# target to preprocess a source file
src/stitcher.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/stitcher.cpp.i
.PHONY : src/stitcher.cpp.i

src/stitcher.s: src/stitcher.cpp.s

.PHONY : src/stitcher.s

# target to generate assembly for a file
src/stitcher.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/stitcher.cpp.s
.PHONY : src/stitcher.cpp.s

src/timelapsers.obj: src/timelapsers.cpp.obj

.PHONY : src/timelapsers.obj

# target to build an object file
src/timelapsers.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/timelapsers.cpp.obj
.PHONY : src/timelapsers.cpp.obj

src/timelapsers.i: src/timelapsers.cpp.i

.PHONY : src/timelapsers.i

# target to preprocess a source file
src/timelapsers.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/timelapsers.cpp.i
.PHONY : src/timelapsers.cpp.i

src/timelapsers.s: src/timelapsers.cpp.s

.PHONY : src/timelapsers.s

# target to generate assembly for a file
src/timelapsers.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/timelapsers.cpp.s
.PHONY : src/timelapsers.cpp.s

src/util.obj: src/util.cpp.obj

.PHONY : src/util.obj

# target to build an object file
src/util.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/util.cpp.obj
.PHONY : src/util.cpp.obj

src/util.i: src/util.cpp.i

.PHONY : src/util.i

# target to preprocess a source file
src/util.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/util.cpp.i
.PHONY : src/util.cpp.i

src/util.s: src/util.cpp.s

.PHONY : src/util.s

# target to generate assembly for a file
src/util.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/util.cpp.s
.PHONY : src/util.cpp.s

src/warpers.obj: src/warpers.cpp.obj

.PHONY : src/warpers.obj

# target to build an object file
src/warpers.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/warpers.cpp.obj
.PHONY : src/warpers.cpp.obj

src/warpers.i: src/warpers.cpp.i

.PHONY : src/warpers.i

# target to preprocess a source file
src/warpers.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/warpers.cpp.i
.PHONY : src/warpers.cpp.i

src/warpers.s: src/warpers.cpp.s

.PHONY : src/warpers.s

# target to generate assembly for a file
src/warpers.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/warpers.cpp.s
.PHONY : src/warpers.cpp.s

src/warpers_cuda.obj: src/warpers_cuda.cpp.obj

.PHONY : src/warpers_cuda.obj

# target to build an object file
src/warpers_cuda.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/warpers_cuda.cpp.obj
.PHONY : src/warpers_cuda.cpp.obj

src/warpers_cuda.i: src/warpers_cuda.cpp.i

.PHONY : src/warpers_cuda.i

# target to preprocess a source file
src/warpers_cuda.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/warpers_cuda.cpp.i
.PHONY : src/warpers_cuda.cpp.i

src/warpers_cuda.s: src/warpers_cuda.cpp.s

.PHONY : src/warpers_cuda.s

# target to generate assembly for a file
src/warpers_cuda.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/src/warpers_cuda.cpp.s
.PHONY : src/warpers_cuda.cpp.s

vs_version.obj: vs_version.rc.obj

.PHONY : vs_version.obj

# target to build an object file
vs_version.rc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/vs_version.rc.obj
.PHONY : vs_version.rc.obj

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... opencv_stitching
	@echo ... package
	@echo ... package_source
	@echo ... rebuild_cache
	@echo ... test
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... list_install_components
	@echo ... install/strip
	@echo ... opencl_kernels_stitching.obj
	@echo ... opencl_kernels_stitching.i
	@echo ... opencl_kernels_stitching.s
	@echo ... opencv_stitching_main.obj
	@echo ... opencv_stitching_main.i
	@echo ... opencv_stitching_main.s
	@echo ... src/autocalib.obj
	@echo ... src/autocalib.i
	@echo ... src/autocalib.s
	@echo ... src/blenders.obj
	@echo ... src/blenders.i
	@echo ... src/blenders.s
	@echo ... src/camera.obj
	@echo ... src/camera.i
	@echo ... src/camera.s
	@echo ... src/exposure_compensate.obj
	@echo ... src/exposure_compensate.i
	@echo ... src/exposure_compensate.s
	@echo ... src/matchers.obj
	@echo ... src/matchers.i
	@echo ... src/matchers.s
	@echo ... src/motion_estimators.obj
	@echo ... src/motion_estimators.i
	@echo ... src/motion_estimators.s
	@echo ... src/seam_finders.obj
	@echo ... src/seam_finders.i
	@echo ... src/seam_finders.s
	@echo ... src/stitcher.obj
	@echo ... src/stitcher.i
	@echo ... src/stitcher.s
	@echo ... src/timelapsers.obj
	@echo ... src/timelapsers.i
	@echo ... src/timelapsers.s
	@echo ... src/util.obj
	@echo ... src/util.i
	@echo ... src/util.s
	@echo ... src/warpers.obj
	@echo ... src/warpers.i
	@echo ... src/warpers.s
	@echo ... src/warpers_cuda.obj
	@echo ... src/warpers_cuda.i
	@echo ... src/warpers_cuda.s
	@echo ... vs_version.obj
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

