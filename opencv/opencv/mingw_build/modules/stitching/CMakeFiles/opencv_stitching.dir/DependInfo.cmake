# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  "RC"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "D:/unet/opencv/opencv/mingw_build/modules/stitching/opencl_kernels_stitching.cpp" "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/opencl_kernels_stitching.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/modules/stitching/opencv_stitching_main.cpp" "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/opencv_stitching_main.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/stitching/src/autocalib.cpp" "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/src/autocalib.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/stitching/src/blenders.cpp" "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/src/blenders.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/stitching/src/camera.cpp" "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/src/camera.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/stitching/src/exposure_compensate.cpp" "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/src/exposure_compensate.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/stitching/src/matchers.cpp" "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/src/matchers.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/stitching/src/motion_estimators.cpp" "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/src/motion_estimators.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/stitching/src/seam_finders.cpp" "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/src/seam_finders.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/stitching/src/stitcher.cpp" "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/src/stitcher.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/stitching/src/timelapsers.cpp" "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/src/timelapsers.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/stitching/src/util.cpp" "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/src/util.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/stitching/src/warpers.cpp" "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/src/warpers.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/stitching/src/warpers_cuda.cpp" "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/src/warpers_cuda.cpp.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/stitching/include"
  "modules/stitching"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/flann/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "D:/unet/opencv/opencv/sources/modules/features2d/include"
  "D:/unet/opencv/opencv/sources/modules/calib3d/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )
set(CMAKE_DEPENDS_CHECK_RC
  "D:/unet/opencv/opencv/mingw_build/modules/stitching/vs_version.rc" "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/vs_version.rc.obj"
  )

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_RC
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_RC_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/stitching/include"
  "modules/stitching"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/flann/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "D:/unet/opencv/opencv/sources/modules/features2d/include"
  "D:/unet/opencv/opencv/sources/modules/calib3d/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/flann/CMakeFiles/opencv_flann.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/imgproc/CMakeFiles/opencv_imgproc.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/core/CMakeFiles/opencv_core.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
