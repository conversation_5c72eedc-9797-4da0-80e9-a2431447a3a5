# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp" "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "3rdparty/ade/ade-0.1.1f/sources/ade/include"
  "."
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
