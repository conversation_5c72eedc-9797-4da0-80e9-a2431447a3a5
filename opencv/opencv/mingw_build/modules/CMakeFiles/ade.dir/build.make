# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

# Include any dependencies generated for this target.
include modules/CMakeFiles/ade.dir/depend.make

# Include the progress variables for this target.
include modules/CMakeFiles/ade.dir/progress.make

# Include the compile flags for this target's objects.
include modules/CMakeFiles/ade.dir/flags.make

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\alloc.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\alloc.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\alloc.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\alloc.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\alloc.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\alloc.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\assert.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\assert.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\assert.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\assert.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\assert.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\assert.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\check_cycles.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\check_cycles.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\check_cycles.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\check_cycles.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\check_cycles.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\check_cycles.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\edge.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\edge.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\edge.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\edge.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\edge.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\edge.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\execution_engine.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\execution_engine.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\execution_engine.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\execution_engine.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\execution_engine.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\execution_engine.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\graph.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\graph.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\graph.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\graph.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\graph.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\graph.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_accessor.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_accessor.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_accessor.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_accessor.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_accessor.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_accessor.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor_ref.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor_ref.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor_ref.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor_ref.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor_ref.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor_ref.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor_view.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor_view.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor_view.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor_view.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor_view.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\memory_descriptor_view.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\metadata.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\metadata.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\metadata.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\metadata.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\metadata.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\metadata.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\metatypes.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\metatypes.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\metatypes.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\metatypes.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\metatypes.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\metatypes.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\node.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\node.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\node.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\node.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\node.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\node.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\passes\communications.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\passes\communications.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\passes\communications.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\passes\communications.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\passes\communications.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\passes\communications.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\search.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\search.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\search.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\search.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\search.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\search.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\subgraphs.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\subgraphs.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\subgraphs.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\subgraphs.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\subgraphs.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\subgraphs.cpp.s

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.obj: modules/CMakeFiles/ade.dir/flags.make
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.obj: modules/CMakeFiles/ade.dir/includes_CXX.rsp
modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.obj: 3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\topological_sort.cpp.obj -c D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\topological_sort.cpp

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\topological_sort.cpp > CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\topological_sort.cpp.i

modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\3rdparty\ade\ade-0.1.1f\sources\ade\source\topological_sort.cpp -o CMakeFiles\ade.dir\__\3rdparty\ade\ade-0.1.1f\sources\ade\source\topological_sort.cpp.s

# Object files for target ade
ade_OBJECTS = \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.obj" \
"CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.obj"

# External object files for target ade
ade_EXTERNAL_OBJECTS =

lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.obj
lib/libade.a: modules/CMakeFiles/ade.dir/build.make
lib/libade.a: modules/CMakeFiles/ade.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Linking CXX static library ..\lib\libade.a"
	cd /d D:\unet\opencv\opencv\mingw_build\modules && $(CMAKE_COMMAND) -P CMakeFiles\ade.dir\cmake_clean_target.cmake
	cd /d D:\unet\opencv\opencv\mingw_build\modules && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\ade.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
modules/CMakeFiles/ade.dir/build: lib/libade.a

.PHONY : modules/CMakeFiles/ade.dir/build

modules/CMakeFiles/ade.dir/clean:
	cd /d D:\unet\opencv\opencv\mingw_build\modules && $(CMAKE_COMMAND) -P CMakeFiles\ade.dir\cmake_clean.cmake
.PHONY : modules/CMakeFiles/ade.dir/clean

modules/CMakeFiles/ade.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\unet\opencv\opencv\sources D:\unet\opencv\opencv\sources\modules D:\unet\opencv\opencv\mingw_build D:\unet\opencv\opencv\mingw_build\modules D:\unet\opencv\opencv\mingw_build\modules\CMakeFiles\ade.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : modules/CMakeFiles/ade.dir/depend

