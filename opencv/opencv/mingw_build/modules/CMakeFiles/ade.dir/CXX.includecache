#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/communication/callback_connector.hpp
vector
-
functional
-
atomic
-
memory
-
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/communication/ade/util/assert.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/communication/comm_buffer.hpp
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/communication/ade/util/assert.hpp
ade/memory/memory_types.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/communication/ade/memory/memory_types.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/communication/comm_interface.hpp
memory
-
ade/memory/memory_types.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/communication/ade/memory/memory_types.hpp
ade/memory/memory_descriptor_ref.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/communication/ade/memory/memory_descriptor_ref.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/edge.hpp
memory
-
handle.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/handle.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/execution_engine/backend.hpp
memory
-

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/execution_engine/executable.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/execution_engine/execution_engine.hpp
memory
-
vector
-
utility
-
functional
-
initializer_list
-
unordered_set
-
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/execution_engine/ade/util/assert.hpp
ade/util/map_range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/execution_engine/ade/util/map_range.hpp
ade/util/algorithm.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/execution_engine/ade/util/algorithm.hpp
ade/util/type_traits.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/execution_engine/ade/util/type_traits.hpp
ade/graph_listener.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/execution_engine/ade/graph_listener.hpp
ade/passmanager.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/execution_engine/ade/passmanager.hpp
ade/passes/pass_base.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/execution_engine/ade/passes/pass_base.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/graph.hpp
memory
-
functional
-
vector
-
unordered_map
-
string
-
util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/assert.hpp
util/range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/range.hpp
util/map_range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/map_range.hpp
handle.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/handle.hpp
edge.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/edge.hpp
node.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/node.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/graph_listener.hpp
handle.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/handle.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/handle.hpp
memory
-
iosfwd
-
functional
-
util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/assert.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/helpers/search.hpp
unordered_set
-
unordered_map
-
ade/node.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/helpers/ade/node.hpp
ade/util/algorithm.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/helpers/ade/util/algorithm.hpp
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/helpers/ade/util/assert.hpp
ade/util/func_ref.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/helpers/ade/util/func_ref.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/helpers/subgraphs.hpp
vector
-
unordered_set
-
utility
-
ade/node.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/helpers/ade/node.hpp
ade/helpers/search.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/helpers/ade/helpers/search.hpp
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/helpers/ade/util/assert.hpp
ade/util/algorithm.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/helpers/ade/util/algorithm.hpp
ade/util/func_ref.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/helpers/ade/util/func_ref.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/alloc.hpp
cstddef
-

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/memory_access_listener.hpp
memory
-
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/ade/util/assert.hpp
ade/memory/memory_types.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/ade/memory/memory_types.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/memory_accessor.hpp
vector
-
list
-
functional
-
ade/memory/memory_types.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/ade/memory/memory_types.hpp
ade/memory/memory_access_listener.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/ade/memory/memory_access_listener.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/memory_descriptor.hpp
ade/util/memory_range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/ade/util/memory_range.hpp
ade/memory/memory_types.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/ade/memory/memory_types.hpp
ade/memory/memory_accessor.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/ade/memory/memory_accessor.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/memory_descriptor_ref.hpp
iosfwd
-
ade/memory/memory_types.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/ade/memory/memory_types.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/memory_descriptor_view.hpp
memory
-
ade/memory/memory_types.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/ade/memory/memory_types.hpp
ade/memory/memory_accessor.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/ade/memory/memory_accessor.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/memory_types.hpp
ade/util/md_size.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/ade/util/md_size.hpp
ade/util/md_span.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/ade/util/md_span.hpp
ade/util/md_view.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/memory/ade/util/md_view.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/metatypes/metatypes.hpp
functional
-
memory
-
string
-
vector
-
ade/memory/memory_descriptor_ref.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/metatypes/ade/memory/memory_descriptor_ref.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/node.hpp
vector
-
utility
-
memory
-
util/range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/range.hpp
util/map_range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/map_range.hpp
edge.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/edge.hpp
handle.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/handle.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/passes/check_cycles.hpp
exception
-
string
-
ade/passes/pass_base.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/passes/ade/passes/pass_base.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/passes/communications.hpp
ade/passes/pass_base.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/passes/ade/passes/pass_base.hpp
ade/metatypes/metatypes.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/passes/ade/metatypes/metatypes.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/passes/pass_base.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/passes/topological_sort.hpp
vector
-
utility
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/passes/utility
ade/node.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/passes/ade/node.hpp
ade/typed_graph.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/passes/ade/typed_graph.hpp
ade/passes/pass_base.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/passes/ade/passes/pass_base.hpp
ade/util/range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/passes/ade/util/range.hpp
ade/util/filter_range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/passes/ade/util/filter_range.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/passmanager.hpp
vector
-
list
-
unordered_map
-
memory
-
utility
-
algorithm
-
util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/assert.hpp
util/range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/range.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/typed_graph.hpp
unordered_set
-
string
-
array
-
util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/assert.hpp
util/memory_range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/memory_range.hpp
graph.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/graph.hpp
typed_metadata.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/typed_metadata.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/typed_metadata.hpp
array
-
memory
-
type_traits
-
unordered_map
-
ade/util/algorithm.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/ade/util/algorithm.hpp
ade/util/range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/ade/util/range.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/algorithm.hpp
string
-
sstream
-
algorithm
-
utility
-
iterator
-
ade/util/type_traits.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/type_traits.hpp
ade/util/checked_cast.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/checked_cast.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/assert.hpp
cassert
-
utility
-

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/chain_range.hpp
utility
-
type_traits
-
ade/util/range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/range.hpp
ade/util/type_traits.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/type_traits.hpp
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/assert.hpp
ade/util/range_iterator.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/range_iterator.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/checked_cast.hpp
limits
-
type_traits
-
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/assert.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/filter_range.hpp
ade/util/tuple.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/tuple.hpp
ade/util/range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/range.hpp
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/assert.hpp
ade/util/iota_range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/iota_range.hpp
ade/util/range_iterator.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/range_iterator.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/func_ref.hpp
cstdint
-
utility
-
ade/util/type_traits.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/type_traits.hpp
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/assert.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/hash.hpp
cstddef
-

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/iota_range.hpp
type_traits
-
cassert
-
cinttypes
-
limits
-

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/map_range.hpp
type_traits
-
utility
-
tuple
-
ade/util/type_traits.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/type_traits.hpp
ade/util/range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/range.hpp
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/assert.hpp
ade/util/range_iterator.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/range_iterator.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/math.hpp
type_traits
-
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/assert.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/md_io.hpp
ostream
-
ade/util/md_size.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/md_size.hpp
ade/util/md_span.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/md_span.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/md_size.hpp
algorithm
-
array
-
initializer_list
-
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/assert.hpp
ade/util/iota_range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/iota_range.hpp
ade/util/checked_cast.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/checked_cast.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/md_span.hpp
algorithm
-
array
-
initializer_list
-
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/assert.hpp
ade/util/iota_range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/iota_range.hpp
ade/util/md_size.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/md_size.hpp
ade/util/checked_cast.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/checked_cast.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/md_view.hpp
array
-
iterator
-
algorithm
-
numeric
-
ade/util/math.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/math.hpp
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/assert.hpp
ade/util/memory_range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/memory_range.hpp
ade/util/checked_cast.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/checked_cast.hpp
ade/util/md_size.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/md_size.hpp
ade/util/md_span.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/md_span.hpp
ade/util/iota_range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/iota_range.hpp
ade/util/algorithm.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/algorithm.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/memory_range.hpp
type_traits
-
cstring
-
algorithm
-
array
-
ade/util/type_traits.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/type_traits.hpp
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/assert.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/range.hpp
iterator
-
utility
-
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/assert.hpp
ade/util/type_traits.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/type_traits.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/range_iterator.hpp
iterator
-
type_traits
-
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/assert.hpp

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/tuple.hpp
tuple
-
utility
-
type_traits
-

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/type_traits.hpp
type_traits
-

3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/zip_range.hpp
ade/util/tuple.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/tuple.hpp
ade/util/range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/range.hpp
ade/util/assert.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/assert.hpp
ade/util/iota_range.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/iota_range.hpp
ade/util/range_iterator.hpp
3rdparty/ade/ade-0.1.1f/sources/ade/include/ade/util/ade/util/range_iterator.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp
ade/memory/alloc.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/memory/alloc.hpp
malloc.h
-
algorithm
-
stdlib.h
-
ade/util/math.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/math.hpp
ade/util/assert.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/assert.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp
ade/util/assert.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/assert.hpp
stdlib.h
-
stdio.h
-

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp
ade/passes/check_cycles.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/passes/check_cycles.hpp
unordered_map
-
ade/util/assert.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/assert.hpp
ade/util/map_range.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/map_range.hpp
ade/graph.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/graph.hpp
ade/node.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/node.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp
ade/edge.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/edge.hpp
ade/util/assert.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/assert.hpp
ade/node.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/node.hpp
ade/graph.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/graph.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp
ade/execution_engine/execution_engine.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/execution_engine/execution_engine.hpp
ade/execution_engine/backend.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/execution_engine/backend.hpp
ade/execution_engine/executable.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/execution_engine/executable.hpp
ade/graph.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/graph.hpp
ade/util/assert.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/assert.hpp
ade/util/range.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/range.hpp
ade/util/checked_cast.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/checked_cast.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp
ade/graph.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/graph.hpp
algorithm
-
ade/util/assert.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/assert.hpp
ade/util/algorithm.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/algorithm.hpp
ade/edge.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/edge.hpp
ade/node.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/node.hpp
ade/typed_metadata.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/typed_metadata.hpp
ade/graph_listener.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/graph_listener.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp
ade/memory/memory_accessor.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/memory/memory_accessor.hpp
algorithm
-
ade/util/zip_range.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/zip_range.hpp
ade/util/assert.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/assert.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp
ade/memory/memory_descriptor.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/memory/memory_descriptor.hpp
algorithm
-
ade/util/zip_range.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/zip_range.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp
ostream
-
ade/memory/memory_descriptor_ref.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/memory/memory_descriptor_ref.hpp
ade/memory/memory_descriptor_view.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/memory/memory_descriptor_view.hpp
ade/memory/memory_descriptor.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/memory/memory_descriptor.hpp
ade/util/md_io.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/md_io.hpp
ade/util/iota_range.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/iota_range.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp
ade/memory/memory_descriptor_view.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/memory/memory_descriptor_view.hpp
vector
-
ade/util/algorithm.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/algorithm.hpp
ade/util/range.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/range.hpp
ade/util/zip_range.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/zip_range.hpp
ade/memory/memory_descriptor.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/memory/memory_descriptor.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp
ade/typed_metadata.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/typed_metadata.hpp
ade/util/assert.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/assert.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp
ade/metatypes/metatypes.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/metatypes/metatypes.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp
ade/node.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/node.hpp
memory
-
ade/util/assert.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/assert.hpp
ade/util/algorithm.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/algorithm.hpp
ade/graph.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/graph.hpp
ade/edge.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/edge.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp
ade/passes/communications.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/ade/passes/communications.hpp
iterator
-
unordered_map
-
unordered_set
-
atomic
-
stdexcept
-
ade/typed_graph.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/ade/typed_graph.hpp
ade/communication/comm_buffer.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/ade/communication/comm_buffer.hpp
ade/communication/comm_interface.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/ade/communication/comm_interface.hpp
ade/communication/callback_connector.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/ade/communication/callback_connector.hpp
ade/memory/memory_descriptor.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/ade/memory/memory_descriptor.hpp
ade/memory/memory_descriptor_view.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/ade/memory/memory_descriptor_view.hpp
ade/util/algorithm.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/ade/util/algorithm.hpp
ade/util/chain_range.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/ade/util/chain_range.hpp
ade/memory/alloc.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/ade/memory/alloc.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp
ade/helpers/search.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/helpers/search.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp
ade/helpers/subgraphs.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/helpers/subgraphs.hpp
ade/util/hash.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/util/hash.hpp

D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp
ade/passes/topological_sort.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/passes/topological_sort.hpp
vector
-
unordered_set
-
ade/graph.hpp
D:/unet/opencv/opencv/mingw_build/3rdparty/ade/ade-0.1.1f/sources/ade/source/ade/graph.hpp

