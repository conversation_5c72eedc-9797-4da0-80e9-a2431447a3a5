# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\modules\imgcodecs\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/imgcodecs/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/imgcodecs/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/imgcodecs/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/imgcodecs/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/rule
.PHONY : modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/rule

# Convenience name for target.
opencv_imgcodecs: modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/rule

.PHONY : opencv_imgcodecs

# fast build rule for target.
opencv_imgcodecs/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/build
.PHONY : opencv_imgcodecs/fast

opencv_imgcodecs_main.obj: opencv_imgcodecs_main.cpp.obj

.PHONY : opencv_imgcodecs_main.obj

# target to build an object file
opencv_imgcodecs_main.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/opencv_imgcodecs_main.cpp.obj
.PHONY : opencv_imgcodecs_main.cpp.obj

opencv_imgcodecs_main.i: opencv_imgcodecs_main.cpp.i

.PHONY : opencv_imgcodecs_main.i

# target to preprocess a source file
opencv_imgcodecs_main.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/opencv_imgcodecs_main.cpp.i
.PHONY : opencv_imgcodecs_main.cpp.i

opencv_imgcodecs_main.s: opencv_imgcodecs_main.cpp.s

.PHONY : opencv_imgcodecs_main.s

# target to generate assembly for a file
opencv_imgcodecs_main.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/opencv_imgcodecs_main.cpp.s
.PHONY : opencv_imgcodecs_main.cpp.s

src/bitstrm.obj: src/bitstrm.cpp.obj

.PHONY : src/bitstrm.obj

# target to build an object file
src/bitstrm.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/bitstrm.cpp.obj
.PHONY : src/bitstrm.cpp.obj

src/bitstrm.i: src/bitstrm.cpp.i

.PHONY : src/bitstrm.i

# target to preprocess a source file
src/bitstrm.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/bitstrm.cpp.i
.PHONY : src/bitstrm.cpp.i

src/bitstrm.s: src/bitstrm.cpp.s

.PHONY : src/bitstrm.s

# target to generate assembly for a file
src/bitstrm.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/bitstrm.cpp.s
.PHONY : src/bitstrm.cpp.s

src/exif.obj: src/exif.cpp.obj

.PHONY : src/exif.obj

# target to build an object file
src/exif.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/exif.cpp.obj
.PHONY : src/exif.cpp.obj

src/exif.i: src/exif.cpp.i

.PHONY : src/exif.i

# target to preprocess a source file
src/exif.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/exif.cpp.i
.PHONY : src/exif.cpp.i

src/exif.s: src/exif.cpp.s

.PHONY : src/exif.s

# target to generate assembly for a file
src/exif.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/exif.cpp.s
.PHONY : src/exif.cpp.s

src/grfmt_base.obj: src/grfmt_base.cpp.obj

.PHONY : src/grfmt_base.obj

# target to build an object file
src/grfmt_base.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_base.cpp.obj
.PHONY : src/grfmt_base.cpp.obj

src/grfmt_base.i: src/grfmt_base.cpp.i

.PHONY : src/grfmt_base.i

# target to preprocess a source file
src/grfmt_base.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_base.cpp.i
.PHONY : src/grfmt_base.cpp.i

src/grfmt_base.s: src/grfmt_base.cpp.s

.PHONY : src/grfmt_base.s

# target to generate assembly for a file
src/grfmt_base.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_base.cpp.s
.PHONY : src/grfmt_base.cpp.s

src/grfmt_bmp.obj: src/grfmt_bmp.cpp.obj

.PHONY : src/grfmt_bmp.obj

# target to build an object file
src/grfmt_bmp.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_bmp.cpp.obj
.PHONY : src/grfmt_bmp.cpp.obj

src/grfmt_bmp.i: src/grfmt_bmp.cpp.i

.PHONY : src/grfmt_bmp.i

# target to preprocess a source file
src/grfmt_bmp.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_bmp.cpp.i
.PHONY : src/grfmt_bmp.cpp.i

src/grfmt_bmp.s: src/grfmt_bmp.cpp.s

.PHONY : src/grfmt_bmp.s

# target to generate assembly for a file
src/grfmt_bmp.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_bmp.cpp.s
.PHONY : src/grfmt_bmp.cpp.s

src/grfmt_exr.obj: src/grfmt_exr.cpp.obj

.PHONY : src/grfmt_exr.obj

# target to build an object file
src/grfmt_exr.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_exr.cpp.obj
.PHONY : src/grfmt_exr.cpp.obj

src/grfmt_exr.i: src/grfmt_exr.cpp.i

.PHONY : src/grfmt_exr.i

# target to preprocess a source file
src/grfmt_exr.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_exr.cpp.i
.PHONY : src/grfmt_exr.cpp.i

src/grfmt_exr.s: src/grfmt_exr.cpp.s

.PHONY : src/grfmt_exr.s

# target to generate assembly for a file
src/grfmt_exr.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_exr.cpp.s
.PHONY : src/grfmt_exr.cpp.s

src/grfmt_gdal.obj: src/grfmt_gdal.cpp.obj

.PHONY : src/grfmt_gdal.obj

# target to build an object file
src/grfmt_gdal.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_gdal.cpp.obj
.PHONY : src/grfmt_gdal.cpp.obj

src/grfmt_gdal.i: src/grfmt_gdal.cpp.i

.PHONY : src/grfmt_gdal.i

# target to preprocess a source file
src/grfmt_gdal.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_gdal.cpp.i
.PHONY : src/grfmt_gdal.cpp.i

src/grfmt_gdal.s: src/grfmt_gdal.cpp.s

.PHONY : src/grfmt_gdal.s

# target to generate assembly for a file
src/grfmt_gdal.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_gdal.cpp.s
.PHONY : src/grfmt_gdal.cpp.s

src/grfmt_gdcm.obj: src/grfmt_gdcm.cpp.obj

.PHONY : src/grfmt_gdcm.obj

# target to build an object file
src/grfmt_gdcm.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_gdcm.cpp.obj
.PHONY : src/grfmt_gdcm.cpp.obj

src/grfmt_gdcm.i: src/grfmt_gdcm.cpp.i

.PHONY : src/grfmt_gdcm.i

# target to preprocess a source file
src/grfmt_gdcm.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_gdcm.cpp.i
.PHONY : src/grfmt_gdcm.cpp.i

src/grfmt_gdcm.s: src/grfmt_gdcm.cpp.s

.PHONY : src/grfmt_gdcm.s

# target to generate assembly for a file
src/grfmt_gdcm.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_gdcm.cpp.s
.PHONY : src/grfmt_gdcm.cpp.s

src/grfmt_hdr.obj: src/grfmt_hdr.cpp.obj

.PHONY : src/grfmt_hdr.obj

# target to build an object file
src/grfmt_hdr.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_hdr.cpp.obj
.PHONY : src/grfmt_hdr.cpp.obj

src/grfmt_hdr.i: src/grfmt_hdr.cpp.i

.PHONY : src/grfmt_hdr.i

# target to preprocess a source file
src/grfmt_hdr.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_hdr.cpp.i
.PHONY : src/grfmt_hdr.cpp.i

src/grfmt_hdr.s: src/grfmt_hdr.cpp.s

.PHONY : src/grfmt_hdr.s

# target to generate assembly for a file
src/grfmt_hdr.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_hdr.cpp.s
.PHONY : src/grfmt_hdr.cpp.s

src/grfmt_jpeg.obj: src/grfmt_jpeg.cpp.obj

.PHONY : src/grfmt_jpeg.obj

# target to build an object file
src/grfmt_jpeg.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_jpeg.cpp.obj
.PHONY : src/grfmt_jpeg.cpp.obj

src/grfmt_jpeg.i: src/grfmt_jpeg.cpp.i

.PHONY : src/grfmt_jpeg.i

# target to preprocess a source file
src/grfmt_jpeg.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_jpeg.cpp.i
.PHONY : src/grfmt_jpeg.cpp.i

src/grfmt_jpeg.s: src/grfmt_jpeg.cpp.s

.PHONY : src/grfmt_jpeg.s

# target to generate assembly for a file
src/grfmt_jpeg.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_jpeg.cpp.s
.PHONY : src/grfmt_jpeg.cpp.s

src/grfmt_jpeg2000.obj: src/grfmt_jpeg2000.cpp.obj

.PHONY : src/grfmt_jpeg2000.obj

# target to build an object file
src/grfmt_jpeg2000.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_jpeg2000.cpp.obj
.PHONY : src/grfmt_jpeg2000.cpp.obj

src/grfmt_jpeg2000.i: src/grfmt_jpeg2000.cpp.i

.PHONY : src/grfmt_jpeg2000.i

# target to preprocess a source file
src/grfmt_jpeg2000.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_jpeg2000.cpp.i
.PHONY : src/grfmt_jpeg2000.cpp.i

src/grfmt_jpeg2000.s: src/grfmt_jpeg2000.cpp.s

.PHONY : src/grfmt_jpeg2000.s

# target to generate assembly for a file
src/grfmt_jpeg2000.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_jpeg2000.cpp.s
.PHONY : src/grfmt_jpeg2000.cpp.s

src/grfmt_pam.obj: src/grfmt_pam.cpp.obj

.PHONY : src/grfmt_pam.obj

# target to build an object file
src/grfmt_pam.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_pam.cpp.obj
.PHONY : src/grfmt_pam.cpp.obj

src/grfmt_pam.i: src/grfmt_pam.cpp.i

.PHONY : src/grfmt_pam.i

# target to preprocess a source file
src/grfmt_pam.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_pam.cpp.i
.PHONY : src/grfmt_pam.cpp.i

src/grfmt_pam.s: src/grfmt_pam.cpp.s

.PHONY : src/grfmt_pam.s

# target to generate assembly for a file
src/grfmt_pam.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_pam.cpp.s
.PHONY : src/grfmt_pam.cpp.s

src/grfmt_pfm.obj: src/grfmt_pfm.cpp.obj

.PHONY : src/grfmt_pfm.obj

# target to build an object file
src/grfmt_pfm.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_pfm.cpp.obj
.PHONY : src/grfmt_pfm.cpp.obj

src/grfmt_pfm.i: src/grfmt_pfm.cpp.i

.PHONY : src/grfmt_pfm.i

# target to preprocess a source file
src/grfmt_pfm.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_pfm.cpp.i
.PHONY : src/grfmt_pfm.cpp.i

src/grfmt_pfm.s: src/grfmt_pfm.cpp.s

.PHONY : src/grfmt_pfm.s

# target to generate assembly for a file
src/grfmt_pfm.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_pfm.cpp.s
.PHONY : src/grfmt_pfm.cpp.s

src/grfmt_png.obj: src/grfmt_png.cpp.obj

.PHONY : src/grfmt_png.obj

# target to build an object file
src/grfmt_png.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_png.cpp.obj
.PHONY : src/grfmt_png.cpp.obj

src/grfmt_png.i: src/grfmt_png.cpp.i

.PHONY : src/grfmt_png.i

# target to preprocess a source file
src/grfmt_png.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_png.cpp.i
.PHONY : src/grfmt_png.cpp.i

src/grfmt_png.s: src/grfmt_png.cpp.s

.PHONY : src/grfmt_png.s

# target to generate assembly for a file
src/grfmt_png.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_png.cpp.s
.PHONY : src/grfmt_png.cpp.s

src/grfmt_pxm.obj: src/grfmt_pxm.cpp.obj

.PHONY : src/grfmt_pxm.obj

# target to build an object file
src/grfmt_pxm.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_pxm.cpp.obj
.PHONY : src/grfmt_pxm.cpp.obj

src/grfmt_pxm.i: src/grfmt_pxm.cpp.i

.PHONY : src/grfmt_pxm.i

# target to preprocess a source file
src/grfmt_pxm.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_pxm.cpp.i
.PHONY : src/grfmt_pxm.cpp.i

src/grfmt_pxm.s: src/grfmt_pxm.cpp.s

.PHONY : src/grfmt_pxm.s

# target to generate assembly for a file
src/grfmt_pxm.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_pxm.cpp.s
.PHONY : src/grfmt_pxm.cpp.s

src/grfmt_sunras.obj: src/grfmt_sunras.cpp.obj

.PHONY : src/grfmt_sunras.obj

# target to build an object file
src/grfmt_sunras.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_sunras.cpp.obj
.PHONY : src/grfmt_sunras.cpp.obj

src/grfmt_sunras.i: src/grfmt_sunras.cpp.i

.PHONY : src/grfmt_sunras.i

# target to preprocess a source file
src/grfmt_sunras.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_sunras.cpp.i
.PHONY : src/grfmt_sunras.cpp.i

src/grfmt_sunras.s: src/grfmt_sunras.cpp.s

.PHONY : src/grfmt_sunras.s

# target to generate assembly for a file
src/grfmt_sunras.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_sunras.cpp.s
.PHONY : src/grfmt_sunras.cpp.s

src/grfmt_tiff.obj: src/grfmt_tiff.cpp.obj

.PHONY : src/grfmt_tiff.obj

# target to build an object file
src/grfmt_tiff.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_tiff.cpp.obj
.PHONY : src/grfmt_tiff.cpp.obj

src/grfmt_tiff.i: src/grfmt_tiff.cpp.i

.PHONY : src/grfmt_tiff.i

# target to preprocess a source file
src/grfmt_tiff.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_tiff.cpp.i
.PHONY : src/grfmt_tiff.cpp.i

src/grfmt_tiff.s: src/grfmt_tiff.cpp.s

.PHONY : src/grfmt_tiff.s

# target to generate assembly for a file
src/grfmt_tiff.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_tiff.cpp.s
.PHONY : src/grfmt_tiff.cpp.s

src/grfmt_webp.obj: src/grfmt_webp.cpp.obj

.PHONY : src/grfmt_webp.obj

# target to build an object file
src/grfmt_webp.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_webp.cpp.obj
.PHONY : src/grfmt_webp.cpp.obj

src/grfmt_webp.i: src/grfmt_webp.cpp.i

.PHONY : src/grfmt_webp.i

# target to preprocess a source file
src/grfmt_webp.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_webp.cpp.i
.PHONY : src/grfmt_webp.cpp.i

src/grfmt_webp.s: src/grfmt_webp.cpp.s

.PHONY : src/grfmt_webp.s

# target to generate assembly for a file
src/grfmt_webp.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_webp.cpp.s
.PHONY : src/grfmt_webp.cpp.s

src/loadsave.obj: src/loadsave.cpp.obj

.PHONY : src/loadsave.obj

# target to build an object file
src/loadsave.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/loadsave.cpp.obj
.PHONY : src/loadsave.cpp.obj

src/loadsave.i: src/loadsave.cpp.i

.PHONY : src/loadsave.i

# target to preprocess a source file
src/loadsave.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/loadsave.cpp.i
.PHONY : src/loadsave.cpp.i

src/loadsave.s: src/loadsave.cpp.s

.PHONY : src/loadsave.s

# target to generate assembly for a file
src/loadsave.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/loadsave.cpp.s
.PHONY : src/loadsave.cpp.s

src/rgbe.obj: src/rgbe.cpp.obj

.PHONY : src/rgbe.obj

# target to build an object file
src/rgbe.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/rgbe.cpp.obj
.PHONY : src/rgbe.cpp.obj

src/rgbe.i: src/rgbe.cpp.i

.PHONY : src/rgbe.i

# target to preprocess a source file
src/rgbe.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/rgbe.cpp.i
.PHONY : src/rgbe.cpp.i

src/rgbe.s: src/rgbe.cpp.s

.PHONY : src/rgbe.s

# target to generate assembly for a file
src/rgbe.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/rgbe.cpp.s
.PHONY : src/rgbe.cpp.s

src/utils.obj: src/utils.cpp.obj

.PHONY : src/utils.obj

# target to build an object file
src/utils.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/utils.cpp.obj
.PHONY : src/utils.cpp.obj

src/utils.i: src/utils.cpp.i

.PHONY : src/utils.i

# target to preprocess a source file
src/utils.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/utils.cpp.i
.PHONY : src/utils.cpp.i

src/utils.s: src/utils.cpp.s

.PHONY : src/utils.s

# target to generate assembly for a file
src/utils.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/utils.cpp.s
.PHONY : src/utils.cpp.s

vs_version.obj: vs_version.rc.obj

.PHONY : vs_version.obj

# target to build an object file
vs_version.rc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/vs_version.rc.obj
.PHONY : vs_version.rc.obj

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... opencv_imgcodecs
	@echo ... package
	@echo ... package_source
	@echo ... rebuild_cache
	@echo ... test
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... list_install_components
	@echo ... install/strip
	@echo ... opencv_imgcodecs_main.obj
	@echo ... opencv_imgcodecs_main.i
	@echo ... opencv_imgcodecs_main.s
	@echo ... src/bitstrm.obj
	@echo ... src/bitstrm.i
	@echo ... src/bitstrm.s
	@echo ... src/exif.obj
	@echo ... src/exif.i
	@echo ... src/exif.s
	@echo ... src/grfmt_base.obj
	@echo ... src/grfmt_base.i
	@echo ... src/grfmt_base.s
	@echo ... src/grfmt_bmp.obj
	@echo ... src/grfmt_bmp.i
	@echo ... src/grfmt_bmp.s
	@echo ... src/grfmt_exr.obj
	@echo ... src/grfmt_exr.i
	@echo ... src/grfmt_exr.s
	@echo ... src/grfmt_gdal.obj
	@echo ... src/grfmt_gdal.i
	@echo ... src/grfmt_gdal.s
	@echo ... src/grfmt_gdcm.obj
	@echo ... src/grfmt_gdcm.i
	@echo ... src/grfmt_gdcm.s
	@echo ... src/grfmt_hdr.obj
	@echo ... src/grfmt_hdr.i
	@echo ... src/grfmt_hdr.s
	@echo ... src/grfmt_jpeg.obj
	@echo ... src/grfmt_jpeg.i
	@echo ... src/grfmt_jpeg.s
	@echo ... src/grfmt_jpeg2000.obj
	@echo ... src/grfmt_jpeg2000.i
	@echo ... src/grfmt_jpeg2000.s
	@echo ... src/grfmt_pam.obj
	@echo ... src/grfmt_pam.i
	@echo ... src/grfmt_pam.s
	@echo ... src/grfmt_pfm.obj
	@echo ... src/grfmt_pfm.i
	@echo ... src/grfmt_pfm.s
	@echo ... src/grfmt_png.obj
	@echo ... src/grfmt_png.i
	@echo ... src/grfmt_png.s
	@echo ... src/grfmt_pxm.obj
	@echo ... src/grfmt_pxm.i
	@echo ... src/grfmt_pxm.s
	@echo ... src/grfmt_sunras.obj
	@echo ... src/grfmt_sunras.i
	@echo ... src/grfmt_sunras.s
	@echo ... src/grfmt_tiff.obj
	@echo ... src/grfmt_tiff.i
	@echo ... src/grfmt_tiff.s
	@echo ... src/grfmt_webp.obj
	@echo ... src/grfmt_webp.i
	@echo ... src/grfmt_webp.s
	@echo ... src/loadsave.obj
	@echo ... src/loadsave.i
	@echo ... src/loadsave.s
	@echo ... src/rgbe.obj
	@echo ... src/rgbe.i
	@echo ... src/rgbe.s
	@echo ... src/utils.obj
	@echo ... src/utils.i
	@echo ... src/utils.s
	@echo ... vs_version.obj
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

