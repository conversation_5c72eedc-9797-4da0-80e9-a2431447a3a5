#include <winver.h>

VS_VERSION_INFO         VERSIONINFO
  FILEVERSION           4,2,0,0
  PRODUCTVERSION        4,2,0,0
  FILEFLAGSMASK         VS_FFI_FILEFLAGSMASK
#ifdef _DEBUG
  FILEFLAGS             1
#else
  FILEFLAGS             0
#endif
  FILEOS                VOS__WINDOWS32
  FILETYPE              VFT_DLL
  FILESUBTYPE           0
BEGIN
  BLOCK "StringFileInfo"
  BEGIN
    BLOCK "040904E4"
    BEGIN
      VALUE "FileDescription",	"OpenCV module: Image I/O\0"
      VALUE "FileVersion",	"4.2.0\0"
      VALUE "InternalName",	"opencv_imgcodecs420\0"
#if 0
      VALUE "LegalCopyright",	"\0"
#endif
      VALUE "OriginalFilename",	"opencv_imgcodecs420.dll\0"
      VALUE "ProductName",	"OpenCV library\0"
      VALUE "ProductVersion",	"4.2.0\0"
#if 1
      VALUE "Comments",		"http://opencv.org/\0"
#endif
    END
  END
  BLOCK "VarFileInfo"
  BEGIN
    VALUE "Translation", 0x0409, 1252
  END
END
