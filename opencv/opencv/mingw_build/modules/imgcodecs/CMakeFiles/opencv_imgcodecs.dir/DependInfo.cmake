# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  "RC"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/opencv_imgcodecs_main.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/opencv_imgcodecs_main.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/bitstrm.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/bitstrm.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/exif.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/exif.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_base.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_base.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_bmp.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_bmp.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_exr.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_exr.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_gdal.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_gdal.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_gdcm.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_gdcm.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_hdr.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_hdr.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_jpeg.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_jpeg.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_jpeg2000.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_jpeg2000.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_pam.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_pam.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_pfm.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_pfm.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_png.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_png.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_pxm.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_pxm.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_sunras.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_sunras.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_tiff.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_tiff.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_webp.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/grfmt_webp.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/loadsave.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/loadsave.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/rgbe.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/rgbe.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/src/utils.cpp" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/src/utils.cpp.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "HAVE_IMGCODEC_HDR"
  "HAVE_IMGCODEC_PFM"
  "HAVE_IMGCODEC_PXM"
  "HAVE_IMGCODEC_SUNRASTER"
  "HAVE_WEBP"
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper"
  "3rdparty/libtiff"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src"
  "3rdparty/libjpeg-turbo"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src"
  "3rdparty/zlib"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/include"
  "modules/imgcodecs"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "."
  "D:/unet/opencv/opencv/sources/3rdparty/openexr/Half"
  "D:/unet/opencv/opencv/sources/3rdparty/openexr/Iex"
  "D:/unet/opencv/opencv/sources/3rdparty/openexr/IlmThread"
  "D:/unet/opencv/opencv/sources/3rdparty/openexr/Imath"
  "D:/unet/opencv/opencv/sources/3rdparty/openexr/IlmImf"
  "3rdparty/openexr"
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )
set(CMAKE_DEPENDS_CHECK_RC
  "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/vs_version.rc" "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/vs_version.rc.obj"
  )

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_RC
  "HAVE_IMGCODEC_HDR"
  "HAVE_IMGCODEC_PFM"
  "HAVE_IMGCODEC_PXM"
  "HAVE_IMGCODEC_SUNRASTER"
  "HAVE_WEBP"
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_RC_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper"
  "3rdparty/libtiff"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src"
  "3rdparty/libjpeg-turbo"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src"
  "3rdparty/zlib"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/include"
  "modules/imgcodecs"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "."
  "D:/unet/opencv/opencv/sources/3rdparty/openexr/Half"
  "D:/unet/opencv/opencv/sources/3rdparty/openexr/Iex"
  "D:/unet/opencv/opencv/sources/3rdparty/openexr/IlmThread"
  "D:/unet/opencv/opencv/sources/3rdparty/openexr/Imath"
  "D:/unet/opencv/opencv/sources/3rdparty/openexr/IlmImf"
  "3rdparty/openexr"
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "D:/unet/opencv/opencv/mingw_build/modules/imgproc/CMakeFiles/opencv_imgproc.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/openexr/CMakeFiles/IlmImf.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/core/CMakeFiles/opencv_core.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
