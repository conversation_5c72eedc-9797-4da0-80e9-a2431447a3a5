# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# compile CXX with E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/g++.exe
# compile RC with E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/windres.exe
CXX_FLAGS =    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wundef -Winit-self -Wpointer-arith -Wshadow -Wsign-promo -Wuninitialized -Winit-self -Wsuggest-override -Wno-delete-non-virtual-dtor -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -fvisibility-inlines-hidden -Wno-deprecated-declarations -O3 -DNDEBUG  -DNDEBUG   -std=c++11

CXX_DEFINES = -DCVAPI_EXPORTS -DHAVE_IMGCODEC_HDR -DHAVE_IMGCODEC_PFM -DHAVE_IMGCODEC_PXM -DHAVE_IMGCODEC_SUNRASTER -DHAVE_WEBP -D_USE_MATH_DEFINES -D_WIN32_WINNT=0x0601 -D__OPENCV_BUILD=1 -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS

CXX_INCLUDES = @CMakeFiles/opencv_imgcodecs.dir/includes_CXX.rsp

RC_FLAGS =  

RC_DEFINES = -DCVAPI_EXPORTS -DHAVE_IMGCODEC_HDR -DHAVE_IMGCODEC_PFM -DHAVE_IMGCODEC_PXM -DHAVE_IMGCODEC_SUNRASTER -DHAVE_WEBP -D_USE_MATH_DEFINES -D_WIN32_WINNT=0x0601 -D__OPENCV_BUILD=1 -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS

RC_INCLUDES = -ID:\unet\opencv\opencv\sources\3rdparty\libjasper -ID:\unet\opencv\opencv\mingw_build\3rdparty\libtiff -ID:\unet\opencv\opencv\sources\3rdparty\libtiff -ID:\unet\opencv\opencv\sources\3rdparty\libpng -ID:\unet\opencv\opencv\sources\3rdparty\libwebp\src -ID:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo -ID:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src -ID:\unet\opencv\opencv\mingw_build\3rdparty\zlib -ID:\unet\opencv\opencv\sources\3rdparty\zlib -ID:\unet\opencv\opencv\sources\modules\imgcodecs\include -ID:\unet\opencv\opencv\mingw_build\modules\imgcodecs -ID:\unet\opencv\opencv\sources\modules\core\include -ID:\unet\opencv\opencv\sources\modules\imgproc\include -ID:\unet\opencv\opencv\mingw_build -ID:\unet\opencv\opencv\sources\3rdparty\openexr\Half -ID:\unet\opencv\opencv\sources\3rdparty\openexr\Iex -ID:\unet\opencv\opencv\sources\3rdparty\openexr\IlmThread -ID:\unet\opencv\opencv\sources\3rdparty\openexr\Imath -ID:\unet\opencv\opencv\sources\3rdparty\openexr\IlmImf -ID:\unet\opencv\opencv\mingw_build\3rdparty\openexr -ID:\visp-ws\eigen-3.3.7\build-vc14\install\include\eigen3 

