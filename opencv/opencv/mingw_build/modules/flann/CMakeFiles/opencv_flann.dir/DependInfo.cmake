# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  "RC"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "D:/unet/opencv/opencv/mingw_build/modules/flann/opencv_flann_main.cpp" "D:/unet/opencv/opencv/mingw_build/modules/flann/CMakeFiles/opencv_flann.dir/opencv_flann_main.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/flann/src/flann.cpp" "D:/unet/opencv/opencv/mingw_build/modules/flann/CMakeFiles/opencv_flann.dir/src/flann.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/flann/src/miniflann.cpp" "D:/unet/opencv/opencv/mingw_build/modules/flann/CMakeFiles/opencv_flann.dir/src/miniflann.cpp.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/flann/include"
  "modules/flann"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )
set(CMAKE_DEPENDS_CHECK_RC
  "D:/unet/opencv/opencv/mingw_build/modules/flann/vs_version.rc" "D:/unet/opencv/opencv/mingw_build/modules/flann/CMakeFiles/opencv_flann.dir/vs_version.rc.obj"
  )

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_RC
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_RC_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/flann/include"
  "modules/flann"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "D:/unet/opencv/opencv/mingw_build/modules/core/CMakeFiles/opencv_core.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
