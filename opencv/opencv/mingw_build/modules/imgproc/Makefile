# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\modules\imgproc\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/imgproc/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/imgproc/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/imgproc/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/imgproc/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
modules/imgproc/CMakeFiles/opencv_imgproc.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/imgproc/CMakeFiles/opencv_imgproc.dir/rule
.PHONY : modules/imgproc/CMakeFiles/opencv_imgproc.dir/rule

# Convenience name for target.
opencv_imgproc: modules/imgproc/CMakeFiles/opencv_imgproc.dir/rule

.PHONY : opencv_imgproc

# fast build rule for target.
opencv_imgproc/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/build
.PHONY : opencv_imgproc/fast

accum.avx.obj: accum.avx.cpp.obj

.PHONY : accum.avx.obj

# target to build an object file
accum.avx.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/accum.avx.cpp.obj
.PHONY : accum.avx.cpp.obj

accum.avx.i: accum.avx.cpp.i

.PHONY : accum.avx.i

# target to preprocess a source file
accum.avx.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/accum.avx.cpp.i
.PHONY : accum.avx.cpp.i

accum.avx.s: accum.avx.cpp.s

.PHONY : accum.avx.s

# target to generate assembly for a file
accum.avx.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/accum.avx.cpp.s
.PHONY : accum.avx.cpp.s

accum.avx2.obj: accum.avx2.cpp.obj

.PHONY : accum.avx2.obj

# target to build an object file
accum.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/accum.avx2.cpp.obj
.PHONY : accum.avx2.cpp.obj

accum.avx2.i: accum.avx2.cpp.i

.PHONY : accum.avx2.i

# target to preprocess a source file
accum.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/accum.avx2.cpp.i
.PHONY : accum.avx2.cpp.i

accum.avx2.s: accum.avx2.cpp.s

.PHONY : accum.avx2.s

# target to generate assembly for a file
accum.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/accum.avx2.cpp.s
.PHONY : accum.avx2.cpp.s

accum.sse4_1.obj: accum.sse4_1.cpp.obj

.PHONY : accum.sse4_1.obj

# target to build an object file
accum.sse4_1.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/accum.sse4_1.cpp.obj
.PHONY : accum.sse4_1.cpp.obj

accum.sse4_1.i: accum.sse4_1.cpp.i

.PHONY : accum.sse4_1.i

# target to preprocess a source file
accum.sse4_1.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/accum.sse4_1.cpp.i
.PHONY : accum.sse4_1.cpp.i

accum.sse4_1.s: accum.sse4_1.cpp.s

.PHONY : accum.sse4_1.s

# target to generate assembly for a file
accum.sse4_1.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/accum.sse4_1.cpp.s
.PHONY : accum.sse4_1.cpp.s

bilateral_filter.avx2.obj: bilateral_filter.avx2.cpp.obj

.PHONY : bilateral_filter.avx2.obj

# target to build an object file
bilateral_filter.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/bilateral_filter.avx2.cpp.obj
.PHONY : bilateral_filter.avx2.cpp.obj

bilateral_filter.avx2.i: bilateral_filter.avx2.cpp.i

.PHONY : bilateral_filter.avx2.i

# target to preprocess a source file
bilateral_filter.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/bilateral_filter.avx2.cpp.i
.PHONY : bilateral_filter.avx2.cpp.i

bilateral_filter.avx2.s: bilateral_filter.avx2.cpp.s

.PHONY : bilateral_filter.avx2.s

# target to generate assembly for a file
bilateral_filter.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/bilateral_filter.avx2.cpp.s
.PHONY : bilateral_filter.avx2.cpp.s

box_filter.avx2.obj: box_filter.avx2.cpp.obj

.PHONY : box_filter.avx2.obj

# target to build an object file
box_filter.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/box_filter.avx2.cpp.obj
.PHONY : box_filter.avx2.cpp.obj

box_filter.avx2.i: box_filter.avx2.cpp.i

.PHONY : box_filter.avx2.i

# target to preprocess a source file
box_filter.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/box_filter.avx2.cpp.i
.PHONY : box_filter.avx2.cpp.i

box_filter.avx2.s: box_filter.avx2.cpp.s

.PHONY : box_filter.avx2.s

# target to generate assembly for a file
box_filter.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/box_filter.avx2.cpp.s
.PHONY : box_filter.avx2.cpp.s

box_filter.sse4_1.obj: box_filter.sse4_1.cpp.obj

.PHONY : box_filter.sse4_1.obj

# target to build an object file
box_filter.sse4_1.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/box_filter.sse4_1.cpp.obj
.PHONY : box_filter.sse4_1.cpp.obj

box_filter.sse4_1.i: box_filter.sse4_1.cpp.i

.PHONY : box_filter.sse4_1.i

# target to preprocess a source file
box_filter.sse4_1.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/box_filter.sse4_1.cpp.i
.PHONY : box_filter.sse4_1.cpp.i

box_filter.sse4_1.s: box_filter.sse4_1.cpp.s

.PHONY : box_filter.sse4_1.s

# target to generate assembly for a file
box_filter.sse4_1.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/box_filter.sse4_1.cpp.s
.PHONY : box_filter.sse4_1.cpp.s

color_hsv.avx2.obj: color_hsv.avx2.cpp.obj

.PHONY : color_hsv.avx2.obj

# target to build an object file
color_hsv.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_hsv.avx2.cpp.obj
.PHONY : color_hsv.avx2.cpp.obj

color_hsv.avx2.i: color_hsv.avx2.cpp.i

.PHONY : color_hsv.avx2.i

# target to preprocess a source file
color_hsv.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_hsv.avx2.cpp.i
.PHONY : color_hsv.avx2.cpp.i

color_hsv.avx2.s: color_hsv.avx2.cpp.s

.PHONY : color_hsv.avx2.s

# target to generate assembly for a file
color_hsv.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_hsv.avx2.cpp.s
.PHONY : color_hsv.avx2.cpp.s

color_hsv.sse4_1.obj: color_hsv.sse4_1.cpp.obj

.PHONY : color_hsv.sse4_1.obj

# target to build an object file
color_hsv.sse4_1.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_hsv.sse4_1.cpp.obj
.PHONY : color_hsv.sse4_1.cpp.obj

color_hsv.sse4_1.i: color_hsv.sse4_1.cpp.i

.PHONY : color_hsv.sse4_1.i

# target to preprocess a source file
color_hsv.sse4_1.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_hsv.sse4_1.cpp.i
.PHONY : color_hsv.sse4_1.cpp.i

color_hsv.sse4_1.s: color_hsv.sse4_1.cpp.s

.PHONY : color_hsv.sse4_1.s

# target to generate assembly for a file
color_hsv.sse4_1.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_hsv.sse4_1.cpp.s
.PHONY : color_hsv.sse4_1.cpp.s

color_rgb.avx2.obj: color_rgb.avx2.cpp.obj

.PHONY : color_rgb.avx2.obj

# target to build an object file
color_rgb.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_rgb.avx2.cpp.obj
.PHONY : color_rgb.avx2.cpp.obj

color_rgb.avx2.i: color_rgb.avx2.cpp.i

.PHONY : color_rgb.avx2.i

# target to preprocess a source file
color_rgb.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_rgb.avx2.cpp.i
.PHONY : color_rgb.avx2.cpp.i

color_rgb.avx2.s: color_rgb.avx2.cpp.s

.PHONY : color_rgb.avx2.s

# target to generate assembly for a file
color_rgb.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_rgb.avx2.cpp.s
.PHONY : color_rgb.avx2.cpp.s

color_rgb.sse4_1.obj: color_rgb.sse4_1.cpp.obj

.PHONY : color_rgb.sse4_1.obj

# target to build an object file
color_rgb.sse4_1.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_rgb.sse4_1.cpp.obj
.PHONY : color_rgb.sse4_1.cpp.obj

color_rgb.sse4_1.i: color_rgb.sse4_1.cpp.i

.PHONY : color_rgb.sse4_1.i

# target to preprocess a source file
color_rgb.sse4_1.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_rgb.sse4_1.cpp.i
.PHONY : color_rgb.sse4_1.cpp.i

color_rgb.sse4_1.s: color_rgb.sse4_1.cpp.s

.PHONY : color_rgb.sse4_1.s

# target to generate assembly for a file
color_rgb.sse4_1.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_rgb.sse4_1.cpp.s
.PHONY : color_rgb.sse4_1.cpp.s

color_yuv.avx2.obj: color_yuv.avx2.cpp.obj

.PHONY : color_yuv.avx2.obj

# target to build an object file
color_yuv.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_yuv.avx2.cpp.obj
.PHONY : color_yuv.avx2.cpp.obj

color_yuv.avx2.i: color_yuv.avx2.cpp.i

.PHONY : color_yuv.avx2.i

# target to preprocess a source file
color_yuv.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_yuv.avx2.cpp.i
.PHONY : color_yuv.avx2.cpp.i

color_yuv.avx2.s: color_yuv.avx2.cpp.s

.PHONY : color_yuv.avx2.s

# target to generate assembly for a file
color_yuv.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_yuv.avx2.cpp.s
.PHONY : color_yuv.avx2.cpp.s

color_yuv.sse4_1.obj: color_yuv.sse4_1.cpp.obj

.PHONY : color_yuv.sse4_1.obj

# target to build an object file
color_yuv.sse4_1.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_yuv.sse4_1.cpp.obj
.PHONY : color_yuv.sse4_1.cpp.obj

color_yuv.sse4_1.i: color_yuv.sse4_1.cpp.i

.PHONY : color_yuv.sse4_1.i

# target to preprocess a source file
color_yuv.sse4_1.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_yuv.sse4_1.cpp.i
.PHONY : color_yuv.sse4_1.cpp.i

color_yuv.sse4_1.s: color_yuv.sse4_1.cpp.s

.PHONY : color_yuv.sse4_1.s

# target to generate assembly for a file
color_yuv.sse4_1.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_yuv.sse4_1.cpp.s
.PHONY : color_yuv.sse4_1.cpp.s

filter.avx2.obj: filter.avx2.cpp.obj

.PHONY : filter.avx2.obj

# target to build an object file
filter.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/filter.avx2.cpp.obj
.PHONY : filter.avx2.cpp.obj

filter.avx2.i: filter.avx2.cpp.i

.PHONY : filter.avx2.i

# target to preprocess a source file
filter.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/filter.avx2.cpp.i
.PHONY : filter.avx2.cpp.i

filter.avx2.s: filter.avx2.cpp.s

.PHONY : filter.avx2.s

# target to generate assembly for a file
filter.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/filter.avx2.cpp.s
.PHONY : filter.avx2.cpp.s

filter.sse4_1.obj: filter.sse4_1.cpp.obj

.PHONY : filter.sse4_1.obj

# target to build an object file
filter.sse4_1.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/filter.sse4_1.cpp.obj
.PHONY : filter.sse4_1.cpp.obj

filter.sse4_1.i: filter.sse4_1.cpp.i

.PHONY : filter.sse4_1.i

# target to preprocess a source file
filter.sse4_1.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/filter.sse4_1.cpp.i
.PHONY : filter.sse4_1.cpp.i

filter.sse4_1.s: filter.sse4_1.cpp.s

.PHONY : filter.sse4_1.s

# target to generate assembly for a file
filter.sse4_1.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/filter.sse4_1.cpp.s
.PHONY : filter.sse4_1.cpp.s

median_blur.avx2.obj: median_blur.avx2.cpp.obj

.PHONY : median_blur.avx2.obj

# target to build an object file
median_blur.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/median_blur.avx2.cpp.obj
.PHONY : median_blur.avx2.cpp.obj

median_blur.avx2.i: median_blur.avx2.cpp.i

.PHONY : median_blur.avx2.i

# target to preprocess a source file
median_blur.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/median_blur.avx2.cpp.i
.PHONY : median_blur.avx2.cpp.i

median_blur.avx2.s: median_blur.avx2.cpp.s

.PHONY : median_blur.avx2.s

# target to generate assembly for a file
median_blur.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/median_blur.avx2.cpp.s
.PHONY : median_blur.avx2.cpp.s

median_blur.sse4_1.obj: median_blur.sse4_1.cpp.obj

.PHONY : median_blur.sse4_1.obj

# target to build an object file
median_blur.sse4_1.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/median_blur.sse4_1.cpp.obj
.PHONY : median_blur.sse4_1.cpp.obj

median_blur.sse4_1.i: median_blur.sse4_1.cpp.i

.PHONY : median_blur.sse4_1.i

# target to preprocess a source file
median_blur.sse4_1.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/median_blur.sse4_1.cpp.i
.PHONY : median_blur.sse4_1.cpp.i

median_blur.sse4_1.s: median_blur.sse4_1.cpp.s

.PHONY : median_blur.sse4_1.s

# target to generate assembly for a file
median_blur.sse4_1.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/median_blur.sse4_1.cpp.s
.PHONY : median_blur.sse4_1.cpp.s

morph.avx2.obj: morph.avx2.cpp.obj

.PHONY : morph.avx2.obj

# target to build an object file
morph.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/morph.avx2.cpp.obj
.PHONY : morph.avx2.cpp.obj

morph.avx2.i: morph.avx2.cpp.i

.PHONY : morph.avx2.i

# target to preprocess a source file
morph.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/morph.avx2.cpp.i
.PHONY : morph.avx2.cpp.i

morph.avx2.s: morph.avx2.cpp.s

.PHONY : morph.avx2.s

# target to generate assembly for a file
morph.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/morph.avx2.cpp.s
.PHONY : morph.avx2.cpp.s

morph.sse4_1.obj: morph.sse4_1.cpp.obj

.PHONY : morph.sse4_1.obj

# target to build an object file
morph.sse4_1.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/morph.sse4_1.cpp.obj
.PHONY : morph.sse4_1.cpp.obj

morph.sse4_1.i: morph.sse4_1.cpp.i

.PHONY : morph.sse4_1.i

# target to preprocess a source file
morph.sse4_1.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/morph.sse4_1.cpp.i
.PHONY : morph.sse4_1.cpp.i

morph.sse4_1.s: morph.sse4_1.cpp.s

.PHONY : morph.sse4_1.s

# target to generate assembly for a file
morph.sse4_1.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/morph.sse4_1.cpp.s
.PHONY : morph.sse4_1.cpp.s

opencl_kernels_imgproc.obj: opencl_kernels_imgproc.cpp.obj

.PHONY : opencl_kernels_imgproc.obj

# target to build an object file
opencl_kernels_imgproc.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/opencl_kernels_imgproc.cpp.obj
.PHONY : opencl_kernels_imgproc.cpp.obj

opencl_kernels_imgproc.i: opencl_kernels_imgproc.cpp.i

.PHONY : opencl_kernels_imgproc.i

# target to preprocess a source file
opencl_kernels_imgproc.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/opencl_kernels_imgproc.cpp.i
.PHONY : opencl_kernels_imgproc.cpp.i

opencl_kernels_imgproc.s: opencl_kernels_imgproc.cpp.s

.PHONY : opencl_kernels_imgproc.s

# target to generate assembly for a file
opencl_kernels_imgproc.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/opencl_kernels_imgproc.cpp.s
.PHONY : opencl_kernels_imgproc.cpp.s

opencv_imgproc_main.obj: opencv_imgproc_main.cpp.obj

.PHONY : opencv_imgproc_main.obj

# target to build an object file
opencv_imgproc_main.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/opencv_imgproc_main.cpp.obj
.PHONY : opencv_imgproc_main.cpp.obj

opencv_imgproc_main.i: opencv_imgproc_main.cpp.i

.PHONY : opencv_imgproc_main.i

# target to preprocess a source file
opencv_imgproc_main.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/opencv_imgproc_main.cpp.i
.PHONY : opencv_imgproc_main.cpp.i

opencv_imgproc_main.s: opencv_imgproc_main.cpp.s

.PHONY : opencv_imgproc_main.s

# target to generate assembly for a file
opencv_imgproc_main.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/opencv_imgproc_main.cpp.s
.PHONY : opencv_imgproc_main.cpp.s

smooth.avx2.obj: smooth.avx2.cpp.obj

.PHONY : smooth.avx2.obj

# target to build an object file
smooth.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/smooth.avx2.cpp.obj
.PHONY : smooth.avx2.cpp.obj

smooth.avx2.i: smooth.avx2.cpp.i

.PHONY : smooth.avx2.i

# target to preprocess a source file
smooth.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/smooth.avx2.cpp.i
.PHONY : smooth.avx2.cpp.i

smooth.avx2.s: smooth.avx2.cpp.s

.PHONY : smooth.avx2.s

# target to generate assembly for a file
smooth.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/smooth.avx2.cpp.s
.PHONY : smooth.avx2.cpp.s

smooth.sse4_1.obj: smooth.sse4_1.cpp.obj

.PHONY : smooth.sse4_1.obj

# target to build an object file
smooth.sse4_1.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/smooth.sse4_1.cpp.obj
.PHONY : smooth.sse4_1.cpp.obj

smooth.sse4_1.i: smooth.sse4_1.cpp.i

.PHONY : smooth.sse4_1.i

# target to preprocess a source file
smooth.sse4_1.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/smooth.sse4_1.cpp.i
.PHONY : smooth.sse4_1.cpp.i

smooth.sse4_1.s: smooth.sse4_1.cpp.s

.PHONY : smooth.sse4_1.s

# target to generate assembly for a file
smooth.sse4_1.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/smooth.sse4_1.cpp.s
.PHONY : smooth.sse4_1.cpp.s

src/accum.obj: src/accum.cpp.obj

.PHONY : src/accum.obj

# target to build an object file
src/accum.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/accum.cpp.obj
.PHONY : src/accum.cpp.obj

src/accum.i: src/accum.cpp.i

.PHONY : src/accum.i

# target to preprocess a source file
src/accum.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/accum.cpp.i
.PHONY : src/accum.cpp.i

src/accum.s: src/accum.cpp.s

.PHONY : src/accum.s

# target to generate assembly for a file
src/accum.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/accum.cpp.s
.PHONY : src/accum.cpp.s

src/accum.dispatch.obj: src/accum.dispatch.cpp.obj

.PHONY : src/accum.dispatch.obj

# target to build an object file
src/accum.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/accum.dispatch.cpp.obj
.PHONY : src/accum.dispatch.cpp.obj

src/accum.dispatch.i: src/accum.dispatch.cpp.i

.PHONY : src/accum.dispatch.i

# target to preprocess a source file
src/accum.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/accum.dispatch.cpp.i
.PHONY : src/accum.dispatch.cpp.i

src/accum.dispatch.s: src/accum.dispatch.cpp.s

.PHONY : src/accum.dispatch.s

# target to generate assembly for a file
src/accum.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/accum.dispatch.cpp.s
.PHONY : src/accum.dispatch.cpp.s

src/approx.obj: src/approx.cpp.obj

.PHONY : src/approx.obj

# target to build an object file
src/approx.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/approx.cpp.obj
.PHONY : src/approx.cpp.obj

src/approx.i: src/approx.cpp.i

.PHONY : src/approx.i

# target to preprocess a source file
src/approx.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/approx.cpp.i
.PHONY : src/approx.cpp.i

src/approx.s: src/approx.cpp.s

.PHONY : src/approx.s

# target to generate assembly for a file
src/approx.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/approx.cpp.s
.PHONY : src/approx.cpp.s

src/bilateral_filter.dispatch.obj: src/bilateral_filter.dispatch.cpp.obj

.PHONY : src/bilateral_filter.dispatch.obj

# target to build an object file
src/bilateral_filter.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/bilateral_filter.dispatch.cpp.obj
.PHONY : src/bilateral_filter.dispatch.cpp.obj

src/bilateral_filter.dispatch.i: src/bilateral_filter.dispatch.cpp.i

.PHONY : src/bilateral_filter.dispatch.i

# target to preprocess a source file
src/bilateral_filter.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/bilateral_filter.dispatch.cpp.i
.PHONY : src/bilateral_filter.dispatch.cpp.i

src/bilateral_filter.dispatch.s: src/bilateral_filter.dispatch.cpp.s

.PHONY : src/bilateral_filter.dispatch.s

# target to generate assembly for a file
src/bilateral_filter.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/bilateral_filter.dispatch.cpp.s
.PHONY : src/bilateral_filter.dispatch.cpp.s

src/blend.obj: src/blend.cpp.obj

.PHONY : src/blend.obj

# target to build an object file
src/blend.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/blend.cpp.obj
.PHONY : src/blend.cpp.obj

src/blend.i: src/blend.cpp.i

.PHONY : src/blend.i

# target to preprocess a source file
src/blend.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/blend.cpp.i
.PHONY : src/blend.cpp.i

src/blend.s: src/blend.cpp.s

.PHONY : src/blend.s

# target to generate assembly for a file
src/blend.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/blend.cpp.s
.PHONY : src/blend.cpp.s

src/box_filter.dispatch.obj: src/box_filter.dispatch.cpp.obj

.PHONY : src/box_filter.dispatch.obj

# target to build an object file
src/box_filter.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/box_filter.dispatch.cpp.obj
.PHONY : src/box_filter.dispatch.cpp.obj

src/box_filter.dispatch.i: src/box_filter.dispatch.cpp.i

.PHONY : src/box_filter.dispatch.i

# target to preprocess a source file
src/box_filter.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/box_filter.dispatch.cpp.i
.PHONY : src/box_filter.dispatch.cpp.i

src/box_filter.dispatch.s: src/box_filter.dispatch.cpp.s

.PHONY : src/box_filter.dispatch.s

# target to generate assembly for a file
src/box_filter.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/box_filter.dispatch.cpp.s
.PHONY : src/box_filter.dispatch.cpp.s

src/canny.obj: src/canny.cpp.obj

.PHONY : src/canny.obj

# target to build an object file
src/canny.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/canny.cpp.obj
.PHONY : src/canny.cpp.obj

src/canny.i: src/canny.cpp.i

.PHONY : src/canny.i

# target to preprocess a source file
src/canny.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/canny.cpp.i
.PHONY : src/canny.cpp.i

src/canny.s: src/canny.cpp.s

.PHONY : src/canny.s

# target to generate assembly for a file
src/canny.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/canny.cpp.s
.PHONY : src/canny.cpp.s

src/clahe.obj: src/clahe.cpp.obj

.PHONY : src/clahe.obj

# target to build an object file
src/clahe.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/clahe.cpp.obj
.PHONY : src/clahe.cpp.obj

src/clahe.i: src/clahe.cpp.i

.PHONY : src/clahe.i

# target to preprocess a source file
src/clahe.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/clahe.cpp.i
.PHONY : src/clahe.cpp.i

src/clahe.s: src/clahe.cpp.s

.PHONY : src/clahe.s

# target to generate assembly for a file
src/clahe.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/clahe.cpp.s
.PHONY : src/clahe.cpp.s

src/color.obj: src/color.cpp.obj

.PHONY : src/color.obj

# target to build an object file
src/color.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/color.cpp.obj
.PHONY : src/color.cpp.obj

src/color.i: src/color.cpp.i

.PHONY : src/color.i

# target to preprocess a source file
src/color.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/color.cpp.i
.PHONY : src/color.cpp.i

src/color.s: src/color.cpp.s

.PHONY : src/color.s

# target to generate assembly for a file
src/color.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/color.cpp.s
.PHONY : src/color.cpp.s

src/color_hsv.dispatch.obj: src/color_hsv.dispatch.cpp.obj

.PHONY : src/color_hsv.dispatch.obj

# target to build an object file
src/color_hsv.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/color_hsv.dispatch.cpp.obj
.PHONY : src/color_hsv.dispatch.cpp.obj

src/color_hsv.dispatch.i: src/color_hsv.dispatch.cpp.i

.PHONY : src/color_hsv.dispatch.i

# target to preprocess a source file
src/color_hsv.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/color_hsv.dispatch.cpp.i
.PHONY : src/color_hsv.dispatch.cpp.i

src/color_hsv.dispatch.s: src/color_hsv.dispatch.cpp.s

.PHONY : src/color_hsv.dispatch.s

# target to generate assembly for a file
src/color_hsv.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/color_hsv.dispatch.cpp.s
.PHONY : src/color_hsv.dispatch.cpp.s

src/color_lab.obj: src/color_lab.cpp.obj

.PHONY : src/color_lab.obj

# target to build an object file
src/color_lab.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/color_lab.cpp.obj
.PHONY : src/color_lab.cpp.obj

src/color_lab.i: src/color_lab.cpp.i

.PHONY : src/color_lab.i

# target to preprocess a source file
src/color_lab.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/color_lab.cpp.i
.PHONY : src/color_lab.cpp.i

src/color_lab.s: src/color_lab.cpp.s

.PHONY : src/color_lab.s

# target to generate assembly for a file
src/color_lab.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/color_lab.cpp.s
.PHONY : src/color_lab.cpp.s

src/color_rgb.dispatch.obj: src/color_rgb.dispatch.cpp.obj

.PHONY : src/color_rgb.dispatch.obj

# target to build an object file
src/color_rgb.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/color_rgb.dispatch.cpp.obj
.PHONY : src/color_rgb.dispatch.cpp.obj

src/color_rgb.dispatch.i: src/color_rgb.dispatch.cpp.i

.PHONY : src/color_rgb.dispatch.i

# target to preprocess a source file
src/color_rgb.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/color_rgb.dispatch.cpp.i
.PHONY : src/color_rgb.dispatch.cpp.i

src/color_rgb.dispatch.s: src/color_rgb.dispatch.cpp.s

.PHONY : src/color_rgb.dispatch.s

# target to generate assembly for a file
src/color_rgb.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/color_rgb.dispatch.cpp.s
.PHONY : src/color_rgb.dispatch.cpp.s

src/color_yuv.dispatch.obj: src/color_yuv.dispatch.cpp.obj

.PHONY : src/color_yuv.dispatch.obj

# target to build an object file
src/color_yuv.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/color_yuv.dispatch.cpp.obj
.PHONY : src/color_yuv.dispatch.cpp.obj

src/color_yuv.dispatch.i: src/color_yuv.dispatch.cpp.i

.PHONY : src/color_yuv.dispatch.i

# target to preprocess a source file
src/color_yuv.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/color_yuv.dispatch.cpp.i
.PHONY : src/color_yuv.dispatch.cpp.i

src/color_yuv.dispatch.s: src/color_yuv.dispatch.cpp.s

.PHONY : src/color_yuv.dispatch.s

# target to generate assembly for a file
src/color_yuv.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/color_yuv.dispatch.cpp.s
.PHONY : src/color_yuv.dispatch.cpp.s

src/colormap.obj: src/colormap.cpp.obj

.PHONY : src/colormap.obj

# target to build an object file
src/colormap.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/colormap.cpp.obj
.PHONY : src/colormap.cpp.obj

src/colormap.i: src/colormap.cpp.i

.PHONY : src/colormap.i

# target to preprocess a source file
src/colormap.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/colormap.cpp.i
.PHONY : src/colormap.cpp.i

src/colormap.s: src/colormap.cpp.s

.PHONY : src/colormap.s

# target to generate assembly for a file
src/colormap.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/colormap.cpp.s
.PHONY : src/colormap.cpp.s

src/connectedcomponents.obj: src/connectedcomponents.cpp.obj

.PHONY : src/connectedcomponents.obj

# target to build an object file
src/connectedcomponents.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/connectedcomponents.cpp.obj
.PHONY : src/connectedcomponents.cpp.obj

src/connectedcomponents.i: src/connectedcomponents.cpp.i

.PHONY : src/connectedcomponents.i

# target to preprocess a source file
src/connectedcomponents.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/connectedcomponents.cpp.i
.PHONY : src/connectedcomponents.cpp.i

src/connectedcomponents.s: src/connectedcomponents.cpp.s

.PHONY : src/connectedcomponents.s

# target to generate assembly for a file
src/connectedcomponents.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/connectedcomponents.cpp.s
.PHONY : src/connectedcomponents.cpp.s

src/contours.obj: src/contours.cpp.obj

.PHONY : src/contours.obj

# target to build an object file
src/contours.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/contours.cpp.obj
.PHONY : src/contours.cpp.obj

src/contours.i: src/contours.cpp.i

.PHONY : src/contours.i

# target to preprocess a source file
src/contours.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/contours.cpp.i
.PHONY : src/contours.cpp.i

src/contours.s: src/contours.cpp.s

.PHONY : src/contours.s

# target to generate assembly for a file
src/contours.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/contours.cpp.s
.PHONY : src/contours.cpp.s

src/convhull.obj: src/convhull.cpp.obj

.PHONY : src/convhull.obj

# target to build an object file
src/convhull.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/convhull.cpp.obj
.PHONY : src/convhull.cpp.obj

src/convhull.i: src/convhull.cpp.i

.PHONY : src/convhull.i

# target to preprocess a source file
src/convhull.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/convhull.cpp.i
.PHONY : src/convhull.cpp.i

src/convhull.s: src/convhull.cpp.s

.PHONY : src/convhull.s

# target to generate assembly for a file
src/convhull.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/convhull.cpp.s
.PHONY : src/convhull.cpp.s

src/corner.avx.obj: src/corner.avx.cpp.obj

.PHONY : src/corner.avx.obj

# target to build an object file
src/corner.avx.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/corner.avx.cpp.obj
.PHONY : src/corner.avx.cpp.obj

src/corner.avx.i: src/corner.avx.cpp.i

.PHONY : src/corner.avx.i

# target to preprocess a source file
src/corner.avx.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/corner.avx.cpp.i
.PHONY : src/corner.avx.cpp.i

src/corner.avx.s: src/corner.avx.cpp.s

.PHONY : src/corner.avx.s

# target to generate assembly for a file
src/corner.avx.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/corner.avx.cpp.s
.PHONY : src/corner.avx.cpp.s

src/corner.obj: src/corner.cpp.obj

.PHONY : src/corner.obj

# target to build an object file
src/corner.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/corner.cpp.obj
.PHONY : src/corner.cpp.obj

src/corner.i: src/corner.cpp.i

.PHONY : src/corner.i

# target to preprocess a source file
src/corner.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/corner.cpp.i
.PHONY : src/corner.cpp.i

src/corner.s: src/corner.cpp.s

.PHONY : src/corner.s

# target to generate assembly for a file
src/corner.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/corner.cpp.s
.PHONY : src/corner.cpp.s

src/cornersubpix.obj: src/cornersubpix.cpp.obj

.PHONY : src/cornersubpix.obj

# target to build an object file
src/cornersubpix.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/cornersubpix.cpp.obj
.PHONY : src/cornersubpix.cpp.obj

src/cornersubpix.i: src/cornersubpix.cpp.i

.PHONY : src/cornersubpix.i

# target to preprocess a source file
src/cornersubpix.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/cornersubpix.cpp.i
.PHONY : src/cornersubpix.cpp.i

src/cornersubpix.s: src/cornersubpix.cpp.s

.PHONY : src/cornersubpix.s

# target to generate assembly for a file
src/cornersubpix.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/cornersubpix.cpp.s
.PHONY : src/cornersubpix.cpp.s

src/demosaicing.obj: src/demosaicing.cpp.obj

.PHONY : src/demosaicing.obj

# target to build an object file
src/demosaicing.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/demosaicing.cpp.obj
.PHONY : src/demosaicing.cpp.obj

src/demosaicing.i: src/demosaicing.cpp.i

.PHONY : src/demosaicing.i

# target to preprocess a source file
src/demosaicing.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/demosaicing.cpp.i
.PHONY : src/demosaicing.cpp.i

src/demosaicing.s: src/demosaicing.cpp.s

.PHONY : src/demosaicing.s

# target to generate assembly for a file
src/demosaicing.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/demosaicing.cpp.s
.PHONY : src/demosaicing.cpp.s

src/deriv.obj: src/deriv.cpp.obj

.PHONY : src/deriv.obj

# target to build an object file
src/deriv.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/deriv.cpp.obj
.PHONY : src/deriv.cpp.obj

src/deriv.i: src/deriv.cpp.i

.PHONY : src/deriv.i

# target to preprocess a source file
src/deriv.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/deriv.cpp.i
.PHONY : src/deriv.cpp.i

src/deriv.s: src/deriv.cpp.s

.PHONY : src/deriv.s

# target to generate assembly for a file
src/deriv.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/deriv.cpp.s
.PHONY : src/deriv.cpp.s

src/distransform.obj: src/distransform.cpp.obj

.PHONY : src/distransform.obj

# target to build an object file
src/distransform.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/distransform.cpp.obj
.PHONY : src/distransform.cpp.obj

src/distransform.i: src/distransform.cpp.i

.PHONY : src/distransform.i

# target to preprocess a source file
src/distransform.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/distransform.cpp.i
.PHONY : src/distransform.cpp.i

src/distransform.s: src/distransform.cpp.s

.PHONY : src/distransform.s

# target to generate assembly for a file
src/distransform.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/distransform.cpp.s
.PHONY : src/distransform.cpp.s

src/drawing.obj: src/drawing.cpp.obj

.PHONY : src/drawing.obj

# target to build an object file
src/drawing.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/drawing.cpp.obj
.PHONY : src/drawing.cpp.obj

src/drawing.i: src/drawing.cpp.i

.PHONY : src/drawing.i

# target to preprocess a source file
src/drawing.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/drawing.cpp.i
.PHONY : src/drawing.cpp.i

src/drawing.s: src/drawing.cpp.s

.PHONY : src/drawing.s

# target to generate assembly for a file
src/drawing.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/drawing.cpp.s
.PHONY : src/drawing.cpp.s

src/emd.obj: src/emd.cpp.obj

.PHONY : src/emd.obj

# target to build an object file
src/emd.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/emd.cpp.obj
.PHONY : src/emd.cpp.obj

src/emd.i: src/emd.cpp.i

.PHONY : src/emd.i

# target to preprocess a source file
src/emd.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/emd.cpp.i
.PHONY : src/emd.cpp.i

src/emd.s: src/emd.cpp.s

.PHONY : src/emd.s

# target to generate assembly for a file
src/emd.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/emd.cpp.s
.PHONY : src/emd.cpp.s

src/featureselect.obj: src/featureselect.cpp.obj

.PHONY : src/featureselect.obj

# target to build an object file
src/featureselect.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/featureselect.cpp.obj
.PHONY : src/featureselect.cpp.obj

src/featureselect.i: src/featureselect.cpp.i

.PHONY : src/featureselect.i

# target to preprocess a source file
src/featureselect.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/featureselect.cpp.i
.PHONY : src/featureselect.cpp.i

src/featureselect.s: src/featureselect.cpp.s

.PHONY : src/featureselect.s

# target to generate assembly for a file
src/featureselect.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/featureselect.cpp.s
.PHONY : src/featureselect.cpp.s

src/filter.dispatch.obj: src/filter.dispatch.cpp.obj

.PHONY : src/filter.dispatch.obj

# target to build an object file
src/filter.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/filter.dispatch.cpp.obj
.PHONY : src/filter.dispatch.cpp.obj

src/filter.dispatch.i: src/filter.dispatch.cpp.i

.PHONY : src/filter.dispatch.i

# target to preprocess a source file
src/filter.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/filter.dispatch.cpp.i
.PHONY : src/filter.dispatch.cpp.i

src/filter.dispatch.s: src/filter.dispatch.cpp.s

.PHONY : src/filter.dispatch.s

# target to generate assembly for a file
src/filter.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/filter.dispatch.cpp.s
.PHONY : src/filter.dispatch.cpp.s

src/floodfill.obj: src/floodfill.cpp.obj

.PHONY : src/floodfill.obj

# target to build an object file
src/floodfill.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/floodfill.cpp.obj
.PHONY : src/floodfill.cpp.obj

src/floodfill.i: src/floodfill.cpp.i

.PHONY : src/floodfill.i

# target to preprocess a source file
src/floodfill.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/floodfill.cpp.i
.PHONY : src/floodfill.cpp.i

src/floodfill.s: src/floodfill.cpp.s

.PHONY : src/floodfill.s

# target to generate assembly for a file
src/floodfill.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/floodfill.cpp.s
.PHONY : src/floodfill.cpp.s

src/gabor.obj: src/gabor.cpp.obj

.PHONY : src/gabor.obj

# target to build an object file
src/gabor.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/gabor.cpp.obj
.PHONY : src/gabor.cpp.obj

src/gabor.i: src/gabor.cpp.i

.PHONY : src/gabor.i

# target to preprocess a source file
src/gabor.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/gabor.cpp.i
.PHONY : src/gabor.cpp.i

src/gabor.s: src/gabor.cpp.s

.PHONY : src/gabor.s

# target to generate assembly for a file
src/gabor.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/gabor.cpp.s
.PHONY : src/gabor.cpp.s

src/generalized_hough.obj: src/generalized_hough.cpp.obj

.PHONY : src/generalized_hough.obj

# target to build an object file
src/generalized_hough.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/generalized_hough.cpp.obj
.PHONY : src/generalized_hough.cpp.obj

src/generalized_hough.i: src/generalized_hough.cpp.i

.PHONY : src/generalized_hough.i

# target to preprocess a source file
src/generalized_hough.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/generalized_hough.cpp.i
.PHONY : src/generalized_hough.cpp.i

src/generalized_hough.s: src/generalized_hough.cpp.s

.PHONY : src/generalized_hough.s

# target to generate assembly for a file
src/generalized_hough.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/generalized_hough.cpp.s
.PHONY : src/generalized_hough.cpp.s

src/geometry.obj: src/geometry.cpp.obj

.PHONY : src/geometry.obj

# target to build an object file
src/geometry.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/geometry.cpp.obj
.PHONY : src/geometry.cpp.obj

src/geometry.i: src/geometry.cpp.i

.PHONY : src/geometry.i

# target to preprocess a source file
src/geometry.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/geometry.cpp.i
.PHONY : src/geometry.cpp.i

src/geometry.s: src/geometry.cpp.s

.PHONY : src/geometry.s

# target to generate assembly for a file
src/geometry.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/geometry.cpp.s
.PHONY : src/geometry.cpp.s

src/grabcut.obj: src/grabcut.cpp.obj

.PHONY : src/grabcut.obj

# target to build an object file
src/grabcut.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/grabcut.cpp.obj
.PHONY : src/grabcut.cpp.obj

src/grabcut.i: src/grabcut.cpp.i

.PHONY : src/grabcut.i

# target to preprocess a source file
src/grabcut.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/grabcut.cpp.i
.PHONY : src/grabcut.cpp.i

src/grabcut.s: src/grabcut.cpp.s

.PHONY : src/grabcut.s

# target to generate assembly for a file
src/grabcut.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/grabcut.cpp.s
.PHONY : src/grabcut.cpp.s

src/hershey_fonts.obj: src/hershey_fonts.cpp.obj

.PHONY : src/hershey_fonts.obj

# target to build an object file
src/hershey_fonts.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/hershey_fonts.cpp.obj
.PHONY : src/hershey_fonts.cpp.obj

src/hershey_fonts.i: src/hershey_fonts.cpp.i

.PHONY : src/hershey_fonts.i

# target to preprocess a source file
src/hershey_fonts.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/hershey_fonts.cpp.i
.PHONY : src/hershey_fonts.cpp.i

src/hershey_fonts.s: src/hershey_fonts.cpp.s

.PHONY : src/hershey_fonts.s

# target to generate assembly for a file
src/hershey_fonts.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/hershey_fonts.cpp.s
.PHONY : src/hershey_fonts.cpp.s

src/histogram.obj: src/histogram.cpp.obj

.PHONY : src/histogram.obj

# target to build an object file
src/histogram.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/histogram.cpp.obj
.PHONY : src/histogram.cpp.obj

src/histogram.i: src/histogram.cpp.i

.PHONY : src/histogram.i

# target to preprocess a source file
src/histogram.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/histogram.cpp.i
.PHONY : src/histogram.cpp.i

src/histogram.s: src/histogram.cpp.s

.PHONY : src/histogram.s

# target to generate assembly for a file
src/histogram.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/histogram.cpp.s
.PHONY : src/histogram.cpp.s

src/hough.obj: src/hough.cpp.obj

.PHONY : src/hough.obj

# target to build an object file
src/hough.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/hough.cpp.obj
.PHONY : src/hough.cpp.obj

src/hough.i: src/hough.cpp.i

.PHONY : src/hough.i

# target to preprocess a source file
src/hough.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/hough.cpp.i
.PHONY : src/hough.cpp.i

src/hough.s: src/hough.cpp.s

.PHONY : src/hough.s

# target to generate assembly for a file
src/hough.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/hough.cpp.s
.PHONY : src/hough.cpp.s

src/imgwarp.avx2.obj: src/imgwarp.avx2.cpp.obj

.PHONY : src/imgwarp.avx2.obj

# target to build an object file
src/imgwarp.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/imgwarp.avx2.cpp.obj
.PHONY : src/imgwarp.avx2.cpp.obj

src/imgwarp.avx2.i: src/imgwarp.avx2.cpp.i

.PHONY : src/imgwarp.avx2.i

# target to preprocess a source file
src/imgwarp.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/imgwarp.avx2.cpp.i
.PHONY : src/imgwarp.avx2.cpp.i

src/imgwarp.avx2.s: src/imgwarp.avx2.cpp.s

.PHONY : src/imgwarp.avx2.s

# target to generate assembly for a file
src/imgwarp.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/imgwarp.avx2.cpp.s
.PHONY : src/imgwarp.avx2.cpp.s

src/imgwarp.obj: src/imgwarp.cpp.obj

.PHONY : src/imgwarp.obj

# target to build an object file
src/imgwarp.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/imgwarp.cpp.obj
.PHONY : src/imgwarp.cpp.obj

src/imgwarp.i: src/imgwarp.cpp.i

.PHONY : src/imgwarp.i

# target to preprocess a source file
src/imgwarp.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/imgwarp.cpp.i
.PHONY : src/imgwarp.cpp.i

src/imgwarp.s: src/imgwarp.cpp.s

.PHONY : src/imgwarp.s

# target to generate assembly for a file
src/imgwarp.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/imgwarp.cpp.s
.PHONY : src/imgwarp.cpp.s

src/imgwarp.sse4_1.obj: src/imgwarp.sse4_1.cpp.obj

.PHONY : src/imgwarp.sse4_1.obj

# target to build an object file
src/imgwarp.sse4_1.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/imgwarp.sse4_1.cpp.obj
.PHONY : src/imgwarp.sse4_1.cpp.obj

src/imgwarp.sse4_1.i: src/imgwarp.sse4_1.cpp.i

.PHONY : src/imgwarp.sse4_1.i

# target to preprocess a source file
src/imgwarp.sse4_1.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/imgwarp.sse4_1.cpp.i
.PHONY : src/imgwarp.sse4_1.cpp.i

src/imgwarp.sse4_1.s: src/imgwarp.sse4_1.cpp.s

.PHONY : src/imgwarp.sse4_1.s

# target to generate assembly for a file
src/imgwarp.sse4_1.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/imgwarp.sse4_1.cpp.s
.PHONY : src/imgwarp.sse4_1.cpp.s

src/intersection.obj: src/intersection.cpp.obj

.PHONY : src/intersection.obj

# target to build an object file
src/intersection.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/intersection.cpp.obj
.PHONY : src/intersection.cpp.obj

src/intersection.i: src/intersection.cpp.i

.PHONY : src/intersection.i

# target to preprocess a source file
src/intersection.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/intersection.cpp.i
.PHONY : src/intersection.cpp.i

src/intersection.s: src/intersection.cpp.s

.PHONY : src/intersection.s

# target to generate assembly for a file
src/intersection.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/intersection.cpp.s
.PHONY : src/intersection.cpp.s

src/linefit.obj: src/linefit.cpp.obj

.PHONY : src/linefit.obj

# target to build an object file
src/linefit.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/linefit.cpp.obj
.PHONY : src/linefit.cpp.obj

src/linefit.i: src/linefit.cpp.i

.PHONY : src/linefit.i

# target to preprocess a source file
src/linefit.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/linefit.cpp.i
.PHONY : src/linefit.cpp.i

src/linefit.s: src/linefit.cpp.s

.PHONY : src/linefit.s

# target to generate assembly for a file
src/linefit.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/linefit.cpp.s
.PHONY : src/linefit.cpp.s

src/lsd.obj: src/lsd.cpp.obj

.PHONY : src/lsd.obj

# target to build an object file
src/lsd.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/lsd.cpp.obj
.PHONY : src/lsd.cpp.obj

src/lsd.i: src/lsd.cpp.i

.PHONY : src/lsd.i

# target to preprocess a source file
src/lsd.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/lsd.cpp.i
.PHONY : src/lsd.cpp.i

src/lsd.s: src/lsd.cpp.s

.PHONY : src/lsd.s

# target to generate assembly for a file
src/lsd.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/lsd.cpp.s
.PHONY : src/lsd.cpp.s

src/main.obj: src/main.cpp.obj

.PHONY : src/main.obj

# target to build an object file
src/main.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/main.cpp.obj
.PHONY : src/main.cpp.obj

src/main.i: src/main.cpp.i

.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s

.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/matchcontours.obj: src/matchcontours.cpp.obj

.PHONY : src/matchcontours.obj

# target to build an object file
src/matchcontours.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/matchcontours.cpp.obj
.PHONY : src/matchcontours.cpp.obj

src/matchcontours.i: src/matchcontours.cpp.i

.PHONY : src/matchcontours.i

# target to preprocess a source file
src/matchcontours.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/matchcontours.cpp.i
.PHONY : src/matchcontours.cpp.i

src/matchcontours.s: src/matchcontours.cpp.s

.PHONY : src/matchcontours.s

# target to generate assembly for a file
src/matchcontours.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/matchcontours.cpp.s
.PHONY : src/matchcontours.cpp.s

src/median_blur.dispatch.obj: src/median_blur.dispatch.cpp.obj

.PHONY : src/median_blur.dispatch.obj

# target to build an object file
src/median_blur.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/median_blur.dispatch.cpp.obj
.PHONY : src/median_blur.dispatch.cpp.obj

src/median_blur.dispatch.i: src/median_blur.dispatch.cpp.i

.PHONY : src/median_blur.dispatch.i

# target to preprocess a source file
src/median_blur.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/median_blur.dispatch.cpp.i
.PHONY : src/median_blur.dispatch.cpp.i

src/median_blur.dispatch.s: src/median_blur.dispatch.cpp.s

.PHONY : src/median_blur.dispatch.s

# target to generate assembly for a file
src/median_blur.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/median_blur.dispatch.cpp.s
.PHONY : src/median_blur.dispatch.cpp.s

src/min_enclosing_triangle.obj: src/min_enclosing_triangle.cpp.obj

.PHONY : src/min_enclosing_triangle.obj

# target to build an object file
src/min_enclosing_triangle.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/min_enclosing_triangle.cpp.obj
.PHONY : src/min_enclosing_triangle.cpp.obj

src/min_enclosing_triangle.i: src/min_enclosing_triangle.cpp.i

.PHONY : src/min_enclosing_triangle.i

# target to preprocess a source file
src/min_enclosing_triangle.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/min_enclosing_triangle.cpp.i
.PHONY : src/min_enclosing_triangle.cpp.i

src/min_enclosing_triangle.s: src/min_enclosing_triangle.cpp.s

.PHONY : src/min_enclosing_triangle.s

# target to generate assembly for a file
src/min_enclosing_triangle.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/min_enclosing_triangle.cpp.s
.PHONY : src/min_enclosing_triangle.cpp.s

src/moments.obj: src/moments.cpp.obj

.PHONY : src/moments.obj

# target to build an object file
src/moments.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/moments.cpp.obj
.PHONY : src/moments.cpp.obj

src/moments.i: src/moments.cpp.i

.PHONY : src/moments.i

# target to preprocess a source file
src/moments.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/moments.cpp.i
.PHONY : src/moments.cpp.i

src/moments.s: src/moments.cpp.s

.PHONY : src/moments.s

# target to generate assembly for a file
src/moments.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/moments.cpp.s
.PHONY : src/moments.cpp.s

src/morph.dispatch.obj: src/morph.dispatch.cpp.obj

.PHONY : src/morph.dispatch.obj

# target to build an object file
src/morph.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/morph.dispatch.cpp.obj
.PHONY : src/morph.dispatch.cpp.obj

src/morph.dispatch.i: src/morph.dispatch.cpp.i

.PHONY : src/morph.dispatch.i

# target to preprocess a source file
src/morph.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/morph.dispatch.cpp.i
.PHONY : src/morph.dispatch.cpp.i

src/morph.dispatch.s: src/morph.dispatch.cpp.s

.PHONY : src/morph.dispatch.s

# target to generate assembly for a file
src/morph.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/morph.dispatch.cpp.s
.PHONY : src/morph.dispatch.cpp.s

src/phasecorr.obj: src/phasecorr.cpp.obj

.PHONY : src/phasecorr.obj

# target to build an object file
src/phasecorr.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/phasecorr.cpp.obj
.PHONY : src/phasecorr.cpp.obj

src/phasecorr.i: src/phasecorr.cpp.i

.PHONY : src/phasecorr.i

# target to preprocess a source file
src/phasecorr.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/phasecorr.cpp.i
.PHONY : src/phasecorr.cpp.i

src/phasecorr.s: src/phasecorr.cpp.s

.PHONY : src/phasecorr.s

# target to generate assembly for a file
src/phasecorr.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/phasecorr.cpp.s
.PHONY : src/phasecorr.cpp.s

src/pyramids.obj: src/pyramids.cpp.obj

.PHONY : src/pyramids.obj

# target to build an object file
src/pyramids.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/pyramids.cpp.obj
.PHONY : src/pyramids.cpp.obj

src/pyramids.i: src/pyramids.cpp.i

.PHONY : src/pyramids.i

# target to preprocess a source file
src/pyramids.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/pyramids.cpp.i
.PHONY : src/pyramids.cpp.i

src/pyramids.s: src/pyramids.cpp.s

.PHONY : src/pyramids.s

# target to generate assembly for a file
src/pyramids.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/pyramids.cpp.s
.PHONY : src/pyramids.cpp.s

src/resize.avx2.obj: src/resize.avx2.cpp.obj

.PHONY : src/resize.avx2.obj

# target to build an object file
src/resize.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/resize.avx2.cpp.obj
.PHONY : src/resize.avx2.cpp.obj

src/resize.avx2.i: src/resize.avx2.cpp.i

.PHONY : src/resize.avx2.i

# target to preprocess a source file
src/resize.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/resize.avx2.cpp.i
.PHONY : src/resize.avx2.cpp.i

src/resize.avx2.s: src/resize.avx2.cpp.s

.PHONY : src/resize.avx2.s

# target to generate assembly for a file
src/resize.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/resize.avx2.cpp.s
.PHONY : src/resize.avx2.cpp.s

src/resize.obj: src/resize.cpp.obj

.PHONY : src/resize.obj

# target to build an object file
src/resize.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/resize.cpp.obj
.PHONY : src/resize.cpp.obj

src/resize.i: src/resize.cpp.i

.PHONY : src/resize.i

# target to preprocess a source file
src/resize.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/resize.cpp.i
.PHONY : src/resize.cpp.i

src/resize.s: src/resize.cpp.s

.PHONY : src/resize.s

# target to generate assembly for a file
src/resize.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/resize.cpp.s
.PHONY : src/resize.cpp.s

src/resize.sse4_1.obj: src/resize.sse4_1.cpp.obj

.PHONY : src/resize.sse4_1.obj

# target to build an object file
src/resize.sse4_1.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/resize.sse4_1.cpp.obj
.PHONY : src/resize.sse4_1.cpp.obj

src/resize.sse4_1.i: src/resize.sse4_1.cpp.i

.PHONY : src/resize.sse4_1.i

# target to preprocess a source file
src/resize.sse4_1.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/resize.sse4_1.cpp.i
.PHONY : src/resize.sse4_1.cpp.i

src/resize.sse4_1.s: src/resize.sse4_1.cpp.s

.PHONY : src/resize.sse4_1.s

# target to generate assembly for a file
src/resize.sse4_1.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/resize.sse4_1.cpp.s
.PHONY : src/resize.sse4_1.cpp.s

src/rotcalipers.obj: src/rotcalipers.cpp.obj

.PHONY : src/rotcalipers.obj

# target to build an object file
src/rotcalipers.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/rotcalipers.cpp.obj
.PHONY : src/rotcalipers.cpp.obj

src/rotcalipers.i: src/rotcalipers.cpp.i

.PHONY : src/rotcalipers.i

# target to preprocess a source file
src/rotcalipers.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/rotcalipers.cpp.i
.PHONY : src/rotcalipers.cpp.i

src/rotcalipers.s: src/rotcalipers.cpp.s

.PHONY : src/rotcalipers.s

# target to generate assembly for a file
src/rotcalipers.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/rotcalipers.cpp.s
.PHONY : src/rotcalipers.cpp.s

src/samplers.obj: src/samplers.cpp.obj

.PHONY : src/samplers.obj

# target to build an object file
src/samplers.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/samplers.cpp.obj
.PHONY : src/samplers.cpp.obj

src/samplers.i: src/samplers.cpp.i

.PHONY : src/samplers.i

# target to preprocess a source file
src/samplers.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/samplers.cpp.i
.PHONY : src/samplers.cpp.i

src/samplers.s: src/samplers.cpp.s

.PHONY : src/samplers.s

# target to generate assembly for a file
src/samplers.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/samplers.cpp.s
.PHONY : src/samplers.cpp.s

src/segmentation.obj: src/segmentation.cpp.obj

.PHONY : src/segmentation.obj

# target to build an object file
src/segmentation.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/segmentation.cpp.obj
.PHONY : src/segmentation.cpp.obj

src/segmentation.i: src/segmentation.cpp.i

.PHONY : src/segmentation.i

# target to preprocess a source file
src/segmentation.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/segmentation.cpp.i
.PHONY : src/segmentation.cpp.i

src/segmentation.s: src/segmentation.cpp.s

.PHONY : src/segmentation.s

# target to generate assembly for a file
src/segmentation.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/segmentation.cpp.s
.PHONY : src/segmentation.cpp.s

src/shapedescr.obj: src/shapedescr.cpp.obj

.PHONY : src/shapedescr.obj

# target to build an object file
src/shapedescr.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/shapedescr.cpp.obj
.PHONY : src/shapedescr.cpp.obj

src/shapedescr.i: src/shapedescr.cpp.i

.PHONY : src/shapedescr.i

# target to preprocess a source file
src/shapedescr.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/shapedescr.cpp.i
.PHONY : src/shapedescr.cpp.i

src/shapedescr.s: src/shapedescr.cpp.s

.PHONY : src/shapedescr.s

# target to generate assembly for a file
src/shapedescr.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/shapedescr.cpp.s
.PHONY : src/shapedescr.cpp.s

src/smooth.dispatch.obj: src/smooth.dispatch.cpp.obj

.PHONY : src/smooth.dispatch.obj

# target to build an object file
src/smooth.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/smooth.dispatch.cpp.obj
.PHONY : src/smooth.dispatch.cpp.obj

src/smooth.dispatch.i: src/smooth.dispatch.cpp.i

.PHONY : src/smooth.dispatch.i

# target to preprocess a source file
src/smooth.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/smooth.dispatch.cpp.i
.PHONY : src/smooth.dispatch.cpp.i

src/smooth.dispatch.s: src/smooth.dispatch.cpp.s

.PHONY : src/smooth.dispatch.s

# target to generate assembly for a file
src/smooth.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/smooth.dispatch.cpp.s
.PHONY : src/smooth.dispatch.cpp.s

src/spatialgradient.obj: src/spatialgradient.cpp.obj

.PHONY : src/spatialgradient.obj

# target to build an object file
src/spatialgradient.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/spatialgradient.cpp.obj
.PHONY : src/spatialgradient.cpp.obj

src/spatialgradient.i: src/spatialgradient.cpp.i

.PHONY : src/spatialgradient.i

# target to preprocess a source file
src/spatialgradient.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/spatialgradient.cpp.i
.PHONY : src/spatialgradient.cpp.i

src/spatialgradient.s: src/spatialgradient.cpp.s

.PHONY : src/spatialgradient.s

# target to generate assembly for a file
src/spatialgradient.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/spatialgradient.cpp.s
.PHONY : src/spatialgradient.cpp.s

src/subdivision2d.obj: src/subdivision2d.cpp.obj

.PHONY : src/subdivision2d.obj

# target to build an object file
src/subdivision2d.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/subdivision2d.cpp.obj
.PHONY : src/subdivision2d.cpp.obj

src/subdivision2d.i: src/subdivision2d.cpp.i

.PHONY : src/subdivision2d.i

# target to preprocess a source file
src/subdivision2d.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/subdivision2d.cpp.i
.PHONY : src/subdivision2d.cpp.i

src/subdivision2d.s: src/subdivision2d.cpp.s

.PHONY : src/subdivision2d.s

# target to generate assembly for a file
src/subdivision2d.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/subdivision2d.cpp.s
.PHONY : src/subdivision2d.cpp.s

src/sumpixels.obj: src/sumpixels.cpp.obj

.PHONY : src/sumpixels.obj

# target to build an object file
src/sumpixels.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/sumpixels.cpp.obj
.PHONY : src/sumpixels.cpp.obj

src/sumpixels.i: src/sumpixels.cpp.i

.PHONY : src/sumpixels.i

# target to preprocess a source file
src/sumpixels.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/sumpixels.cpp.i
.PHONY : src/sumpixels.cpp.i

src/sumpixels.s: src/sumpixels.cpp.s

.PHONY : src/sumpixels.s

# target to generate assembly for a file
src/sumpixels.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/sumpixels.cpp.s
.PHONY : src/sumpixels.cpp.s

src/tables.obj: src/tables.cpp.obj

.PHONY : src/tables.obj

# target to build an object file
src/tables.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/tables.cpp.obj
.PHONY : src/tables.cpp.obj

src/tables.i: src/tables.cpp.i

.PHONY : src/tables.i

# target to preprocess a source file
src/tables.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/tables.cpp.i
.PHONY : src/tables.cpp.i

src/tables.s: src/tables.cpp.s

.PHONY : src/tables.s

# target to generate assembly for a file
src/tables.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/tables.cpp.s
.PHONY : src/tables.cpp.s

src/templmatch.obj: src/templmatch.cpp.obj

.PHONY : src/templmatch.obj

# target to build an object file
src/templmatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/templmatch.cpp.obj
.PHONY : src/templmatch.cpp.obj

src/templmatch.i: src/templmatch.cpp.i

.PHONY : src/templmatch.i

# target to preprocess a source file
src/templmatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/templmatch.cpp.i
.PHONY : src/templmatch.cpp.i

src/templmatch.s: src/templmatch.cpp.s

.PHONY : src/templmatch.s

# target to generate assembly for a file
src/templmatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/templmatch.cpp.s
.PHONY : src/templmatch.cpp.s

src/thresh.obj: src/thresh.cpp.obj

.PHONY : src/thresh.obj

# target to build an object file
src/thresh.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/thresh.cpp.obj
.PHONY : src/thresh.cpp.obj

src/thresh.i: src/thresh.cpp.i

.PHONY : src/thresh.i

# target to preprocess a source file
src/thresh.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/thresh.cpp.i
.PHONY : src/thresh.cpp.i

src/thresh.s: src/thresh.cpp.s

.PHONY : src/thresh.s

# target to generate assembly for a file
src/thresh.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/thresh.cpp.s
.PHONY : src/thresh.cpp.s

src/utils.obj: src/utils.cpp.obj

.PHONY : src/utils.obj

# target to build an object file
src/utils.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/utils.cpp.obj
.PHONY : src/utils.cpp.obj

src/utils.i: src/utils.cpp.i

.PHONY : src/utils.i

# target to preprocess a source file
src/utils.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/utils.cpp.i
.PHONY : src/utils.cpp.i

src/utils.s: src/utils.cpp.s

.PHONY : src/utils.s

# target to generate assembly for a file
src/utils.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/utils.cpp.s
.PHONY : src/utils.cpp.s

vs_version.obj: vs_version.rc.obj

.PHONY : vs_version.obj

# target to build an object file
vs_version.rc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/vs_version.rc.obj
.PHONY : vs_version.rc.obj

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... opencv_imgproc
	@echo ... package
	@echo ... package_source
	@echo ... rebuild_cache
	@echo ... test
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... list_install_components
	@echo ... install/strip
	@echo ... accum.avx.obj
	@echo ... accum.avx.i
	@echo ... accum.avx.s
	@echo ... accum.avx2.obj
	@echo ... accum.avx2.i
	@echo ... accum.avx2.s
	@echo ... accum.sse4_1.obj
	@echo ... accum.sse4_1.i
	@echo ... accum.sse4_1.s
	@echo ... bilateral_filter.avx2.obj
	@echo ... bilateral_filter.avx2.i
	@echo ... bilateral_filter.avx2.s
	@echo ... box_filter.avx2.obj
	@echo ... box_filter.avx2.i
	@echo ... box_filter.avx2.s
	@echo ... box_filter.sse4_1.obj
	@echo ... box_filter.sse4_1.i
	@echo ... box_filter.sse4_1.s
	@echo ... color_hsv.avx2.obj
	@echo ... color_hsv.avx2.i
	@echo ... color_hsv.avx2.s
	@echo ... color_hsv.sse4_1.obj
	@echo ... color_hsv.sse4_1.i
	@echo ... color_hsv.sse4_1.s
	@echo ... color_rgb.avx2.obj
	@echo ... color_rgb.avx2.i
	@echo ... color_rgb.avx2.s
	@echo ... color_rgb.sse4_1.obj
	@echo ... color_rgb.sse4_1.i
	@echo ... color_rgb.sse4_1.s
	@echo ... color_yuv.avx2.obj
	@echo ... color_yuv.avx2.i
	@echo ... color_yuv.avx2.s
	@echo ... color_yuv.sse4_1.obj
	@echo ... color_yuv.sse4_1.i
	@echo ... color_yuv.sse4_1.s
	@echo ... filter.avx2.obj
	@echo ... filter.avx2.i
	@echo ... filter.avx2.s
	@echo ... filter.sse4_1.obj
	@echo ... filter.sse4_1.i
	@echo ... filter.sse4_1.s
	@echo ... median_blur.avx2.obj
	@echo ... median_blur.avx2.i
	@echo ... median_blur.avx2.s
	@echo ... median_blur.sse4_1.obj
	@echo ... median_blur.sse4_1.i
	@echo ... median_blur.sse4_1.s
	@echo ... morph.avx2.obj
	@echo ... morph.avx2.i
	@echo ... morph.avx2.s
	@echo ... morph.sse4_1.obj
	@echo ... morph.sse4_1.i
	@echo ... morph.sse4_1.s
	@echo ... opencl_kernels_imgproc.obj
	@echo ... opencl_kernels_imgproc.i
	@echo ... opencl_kernels_imgproc.s
	@echo ... opencv_imgproc_main.obj
	@echo ... opencv_imgproc_main.i
	@echo ... opencv_imgproc_main.s
	@echo ... smooth.avx2.obj
	@echo ... smooth.avx2.i
	@echo ... smooth.avx2.s
	@echo ... smooth.sse4_1.obj
	@echo ... smooth.sse4_1.i
	@echo ... smooth.sse4_1.s
	@echo ... src/accum.obj
	@echo ... src/accum.i
	@echo ... src/accum.s
	@echo ... src/accum.dispatch.obj
	@echo ... src/accum.dispatch.i
	@echo ... src/accum.dispatch.s
	@echo ... src/approx.obj
	@echo ... src/approx.i
	@echo ... src/approx.s
	@echo ... src/bilateral_filter.dispatch.obj
	@echo ... src/bilateral_filter.dispatch.i
	@echo ... src/bilateral_filter.dispatch.s
	@echo ... src/blend.obj
	@echo ... src/blend.i
	@echo ... src/blend.s
	@echo ... src/box_filter.dispatch.obj
	@echo ... src/box_filter.dispatch.i
	@echo ... src/box_filter.dispatch.s
	@echo ... src/canny.obj
	@echo ... src/canny.i
	@echo ... src/canny.s
	@echo ... src/clahe.obj
	@echo ... src/clahe.i
	@echo ... src/clahe.s
	@echo ... src/color.obj
	@echo ... src/color.i
	@echo ... src/color.s
	@echo ... src/color_hsv.dispatch.obj
	@echo ... src/color_hsv.dispatch.i
	@echo ... src/color_hsv.dispatch.s
	@echo ... src/color_lab.obj
	@echo ... src/color_lab.i
	@echo ... src/color_lab.s
	@echo ... src/color_rgb.dispatch.obj
	@echo ... src/color_rgb.dispatch.i
	@echo ... src/color_rgb.dispatch.s
	@echo ... src/color_yuv.dispatch.obj
	@echo ... src/color_yuv.dispatch.i
	@echo ... src/color_yuv.dispatch.s
	@echo ... src/colormap.obj
	@echo ... src/colormap.i
	@echo ... src/colormap.s
	@echo ... src/connectedcomponents.obj
	@echo ... src/connectedcomponents.i
	@echo ... src/connectedcomponents.s
	@echo ... src/contours.obj
	@echo ... src/contours.i
	@echo ... src/contours.s
	@echo ... src/convhull.obj
	@echo ... src/convhull.i
	@echo ... src/convhull.s
	@echo ... src/corner.avx.obj
	@echo ... src/corner.avx.i
	@echo ... src/corner.avx.s
	@echo ... src/corner.obj
	@echo ... src/corner.i
	@echo ... src/corner.s
	@echo ... src/cornersubpix.obj
	@echo ... src/cornersubpix.i
	@echo ... src/cornersubpix.s
	@echo ... src/demosaicing.obj
	@echo ... src/demosaicing.i
	@echo ... src/demosaicing.s
	@echo ... src/deriv.obj
	@echo ... src/deriv.i
	@echo ... src/deriv.s
	@echo ... src/distransform.obj
	@echo ... src/distransform.i
	@echo ... src/distransform.s
	@echo ... src/drawing.obj
	@echo ... src/drawing.i
	@echo ... src/drawing.s
	@echo ... src/emd.obj
	@echo ... src/emd.i
	@echo ... src/emd.s
	@echo ... src/featureselect.obj
	@echo ... src/featureselect.i
	@echo ... src/featureselect.s
	@echo ... src/filter.dispatch.obj
	@echo ... src/filter.dispatch.i
	@echo ... src/filter.dispatch.s
	@echo ... src/floodfill.obj
	@echo ... src/floodfill.i
	@echo ... src/floodfill.s
	@echo ... src/gabor.obj
	@echo ... src/gabor.i
	@echo ... src/gabor.s
	@echo ... src/generalized_hough.obj
	@echo ... src/generalized_hough.i
	@echo ... src/generalized_hough.s
	@echo ... src/geometry.obj
	@echo ... src/geometry.i
	@echo ... src/geometry.s
	@echo ... src/grabcut.obj
	@echo ... src/grabcut.i
	@echo ... src/grabcut.s
	@echo ... src/hershey_fonts.obj
	@echo ... src/hershey_fonts.i
	@echo ... src/hershey_fonts.s
	@echo ... src/histogram.obj
	@echo ... src/histogram.i
	@echo ... src/histogram.s
	@echo ... src/hough.obj
	@echo ... src/hough.i
	@echo ... src/hough.s
	@echo ... src/imgwarp.avx2.obj
	@echo ... src/imgwarp.avx2.i
	@echo ... src/imgwarp.avx2.s
	@echo ... src/imgwarp.obj
	@echo ... src/imgwarp.i
	@echo ... src/imgwarp.s
	@echo ... src/imgwarp.sse4_1.obj
	@echo ... src/imgwarp.sse4_1.i
	@echo ... src/imgwarp.sse4_1.s
	@echo ... src/intersection.obj
	@echo ... src/intersection.i
	@echo ... src/intersection.s
	@echo ... src/linefit.obj
	@echo ... src/linefit.i
	@echo ... src/linefit.s
	@echo ... src/lsd.obj
	@echo ... src/lsd.i
	@echo ... src/lsd.s
	@echo ... src/main.obj
	@echo ... src/main.i
	@echo ... src/main.s
	@echo ... src/matchcontours.obj
	@echo ... src/matchcontours.i
	@echo ... src/matchcontours.s
	@echo ... src/median_blur.dispatch.obj
	@echo ... src/median_blur.dispatch.i
	@echo ... src/median_blur.dispatch.s
	@echo ... src/min_enclosing_triangle.obj
	@echo ... src/min_enclosing_triangle.i
	@echo ... src/min_enclosing_triangle.s
	@echo ... src/moments.obj
	@echo ... src/moments.i
	@echo ... src/moments.s
	@echo ... src/morph.dispatch.obj
	@echo ... src/morph.dispatch.i
	@echo ... src/morph.dispatch.s
	@echo ... src/phasecorr.obj
	@echo ... src/phasecorr.i
	@echo ... src/phasecorr.s
	@echo ... src/pyramids.obj
	@echo ... src/pyramids.i
	@echo ... src/pyramids.s
	@echo ... src/resize.avx2.obj
	@echo ... src/resize.avx2.i
	@echo ... src/resize.avx2.s
	@echo ... src/resize.obj
	@echo ... src/resize.i
	@echo ... src/resize.s
	@echo ... src/resize.sse4_1.obj
	@echo ... src/resize.sse4_1.i
	@echo ... src/resize.sse4_1.s
	@echo ... src/rotcalipers.obj
	@echo ... src/rotcalipers.i
	@echo ... src/rotcalipers.s
	@echo ... src/samplers.obj
	@echo ... src/samplers.i
	@echo ... src/samplers.s
	@echo ... src/segmentation.obj
	@echo ... src/segmentation.i
	@echo ... src/segmentation.s
	@echo ... src/shapedescr.obj
	@echo ... src/shapedescr.i
	@echo ... src/shapedescr.s
	@echo ... src/smooth.dispatch.obj
	@echo ... src/smooth.dispatch.i
	@echo ... src/smooth.dispatch.s
	@echo ... src/spatialgradient.obj
	@echo ... src/spatialgradient.i
	@echo ... src/spatialgradient.s
	@echo ... src/subdivision2d.obj
	@echo ... src/subdivision2d.i
	@echo ... src/subdivision2d.s
	@echo ... src/sumpixels.obj
	@echo ... src/sumpixels.i
	@echo ... src/sumpixels.s
	@echo ... src/tables.obj
	@echo ... src/tables.i
	@echo ... src/tables.s
	@echo ... src/templmatch.obj
	@echo ... src/templmatch.i
	@echo ... src/templmatch.s
	@echo ... src/thresh.obj
	@echo ... src/thresh.i
	@echo ... src/thresh.s
	@echo ... src/utils.obj
	@echo ... src/utils.i
	@echo ... src/utils.s
	@echo ... vs_version.obj
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

