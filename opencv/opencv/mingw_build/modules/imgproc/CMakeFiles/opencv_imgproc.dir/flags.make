# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# compile CXX with E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/g++.exe
# compile RC with E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/windres.exe
CXX_FLAGS =    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wundef -Winit-self -Wpointer-arith -Wshadow -Wsign-promo -Wuninitialized -Winit-self -Wsuggest-override -Wno-delete-non-virtual-dtor -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -fvisibility-inlines-hidden -O3 -DNDEBUG  -DNDEBUG   -std=c++11

CXX_DEFINES = -DCVAPI_EXPORTS -D_USE_MATH_DEFINES -D_WIN32_WINNT=0x0601 -D__OPENCV_BUILD=1 -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS

CXX_INCLUDES = @CMakeFiles/opencv_imgproc.dir/includes_CXX.rsp

RC_FLAGS =  

RC_DEFINES = -DCVAPI_EXPORTS -D_USE_MATH_DEFINES -D_WIN32_WINNT=0x0601 -D__OPENCV_BUILD=1 -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS

RC_INCLUDES = -ID:\unet\opencv\opencv\sources\modules\imgproc\include -ID:\unet\opencv\opencv\mingw_build\modules\imgproc -ID:\unet\opencv\opencv\sources\modules\core\include -ID:\unet\opencv\opencv\mingw_build -ID:\visp-ws\eigen-3.3.7\build-vc14\install\include\eigen3 

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/imgwarp.sse4_1.cpp.obj_FLAGS =  -mssse3 -msse4.1

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/imgwarp.sse4_1.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=SSE4_1;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/resize.sse4_1.cpp.obj_FLAGS =  -mssse3 -msse4.1

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/resize.sse4_1.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=SSE4_1;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/accum.sse4_1.cpp.obj_FLAGS =  -mssse3 -msse4.1

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/accum.sse4_1.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=SSE4_1;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/box_filter.sse4_1.cpp.obj_FLAGS =  -mssse3 -msse4.1

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/box_filter.sse4_1.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=SSE4_1;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/filter.sse4_1.cpp.obj_FLAGS =  -mssse3 -msse4.1

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/filter.sse4_1.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=SSE4_1;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_hsv.sse4_1.cpp.obj_FLAGS =  -mssse3 -msse4.1

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_hsv.sse4_1.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=SSE4_1;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_rgb.sse4_1.cpp.obj_FLAGS =  -mssse3 -msse4.1

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_rgb.sse4_1.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=SSE4_1;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_yuv.sse4_1.cpp.obj_FLAGS =  -mssse3 -msse4.1

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_yuv.sse4_1.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=SSE4_1;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/median_blur.sse4_1.cpp.obj_FLAGS =  -mssse3 -msse4.1

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/median_blur.sse4_1.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=SSE4_1;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/morph.sse4_1.cpp.obj_FLAGS =  -mssse3 -msse4.1

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/morph.sse4_1.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=SSE4_1;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/smooth.sse4_1.cpp.obj_FLAGS =  -mssse3 -msse4.1

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/smooth.sse4_1.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=SSE4_1;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/corner.avx.cpp.obj_FLAGS =  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mavx

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/corner.avx.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=AVX;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;CV_CPU_COMPILE_POPCNT=1;CV_CPU_COMPILE_SSE4_2=1;CV_CPU_COMPILE_AVX=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/accum.avx.cpp.obj_FLAGS =  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mavx

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/accum.avx.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=AVX;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;CV_CPU_COMPILE_POPCNT=1;CV_CPU_COMPILE_SSE4_2=1;CV_CPU_COMPILE_AVX=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/imgwarp.avx2.cpp.obj_FLAGS =  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/imgwarp.avx2.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=AVX2;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;CV_CPU_COMPILE_POPCNT=1;CV_CPU_COMPILE_SSE4_2=1;CV_CPU_COMPILE_FP16=1;CV_CPU_COMPILE_FMA3=1;CV_CPU_COMPILE_AVX=1;CV_CPU_COMPILE_AVX2=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/resize.avx2.cpp.obj_FLAGS =  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/src/resize.avx2.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=AVX2;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;CV_CPU_COMPILE_POPCNT=1;CV_CPU_COMPILE_SSE4_2=1;CV_CPU_COMPILE_FP16=1;CV_CPU_COMPILE_FMA3=1;CV_CPU_COMPILE_AVX=1;CV_CPU_COMPILE_AVX2=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/accum.avx2.cpp.obj_FLAGS =  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/accum.avx2.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=AVX2;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;CV_CPU_COMPILE_POPCNT=1;CV_CPU_COMPILE_SSE4_2=1;CV_CPU_COMPILE_FP16=1;CV_CPU_COMPILE_FMA3=1;CV_CPU_COMPILE_AVX=1;CV_CPU_COMPILE_AVX2=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/bilateral_filter.avx2.cpp.obj_FLAGS =  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/bilateral_filter.avx2.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=AVX2;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;CV_CPU_COMPILE_POPCNT=1;CV_CPU_COMPILE_SSE4_2=1;CV_CPU_COMPILE_FP16=1;CV_CPU_COMPILE_FMA3=1;CV_CPU_COMPILE_AVX=1;CV_CPU_COMPILE_AVX2=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/box_filter.avx2.cpp.obj_FLAGS =  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/box_filter.avx2.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=AVX2;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;CV_CPU_COMPILE_POPCNT=1;CV_CPU_COMPILE_SSE4_2=1;CV_CPU_COMPILE_FP16=1;CV_CPU_COMPILE_FMA3=1;CV_CPU_COMPILE_AVX=1;CV_CPU_COMPILE_AVX2=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/filter.avx2.cpp.obj_FLAGS =  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/filter.avx2.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=AVX2;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;CV_CPU_COMPILE_POPCNT=1;CV_CPU_COMPILE_SSE4_2=1;CV_CPU_COMPILE_FP16=1;CV_CPU_COMPILE_FMA3=1;CV_CPU_COMPILE_AVX=1;CV_CPU_COMPILE_AVX2=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_hsv.avx2.cpp.obj_FLAGS =  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_hsv.avx2.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=AVX2;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;CV_CPU_COMPILE_POPCNT=1;CV_CPU_COMPILE_SSE4_2=1;CV_CPU_COMPILE_FP16=1;CV_CPU_COMPILE_FMA3=1;CV_CPU_COMPILE_AVX=1;CV_CPU_COMPILE_AVX2=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_rgb.avx2.cpp.obj_FLAGS =  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_rgb.avx2.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=AVX2;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;CV_CPU_COMPILE_POPCNT=1;CV_CPU_COMPILE_SSE4_2=1;CV_CPU_COMPILE_FP16=1;CV_CPU_COMPILE_FMA3=1;CV_CPU_COMPILE_AVX=1;CV_CPU_COMPILE_AVX2=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_yuv.avx2.cpp.obj_FLAGS =  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/color_yuv.avx2.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=AVX2;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;CV_CPU_COMPILE_POPCNT=1;CV_CPU_COMPILE_SSE4_2=1;CV_CPU_COMPILE_FP16=1;CV_CPU_COMPILE_FMA3=1;CV_CPU_COMPILE_AVX=1;CV_CPU_COMPILE_AVX2=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/median_blur.avx2.cpp.obj_FLAGS =  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/median_blur.avx2.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=AVX2;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;CV_CPU_COMPILE_POPCNT=1;CV_CPU_COMPILE_SSE4_2=1;CV_CPU_COMPILE_FP16=1;CV_CPU_COMPILE_FMA3=1;CV_CPU_COMPILE_AVX=1;CV_CPU_COMPILE_AVX2=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/morph.avx2.cpp.obj_FLAGS =  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/morph.avx2.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=AVX2;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;CV_CPU_COMPILE_POPCNT=1;CV_CPU_COMPILE_SSE4_2=1;CV_CPU_COMPILE_FP16=1;CV_CPU_COMPILE_FMA3=1;CV_CPU_COMPILE_AVX=1;CV_CPU_COMPILE_AVX2=1

# Custom flags: modules/imgproc/CMakeFiles/opencv_imgproc.dir/smooth.avx2.cpp.obj_FLAGS =  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2

# Custom defines: modules/imgproc/CMakeFiles/opencv_imgproc.dir/smooth.avx2.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=AVX2;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;CV_CPU_COMPILE_POPCNT=1;CV_CPU_COMPILE_SSE4_2=1;CV_CPU_COMPILE_FP16=1;CV_CPU_COMPILE_FMA3=1;CV_CPU_COMPILE_AVX=1;CV_CPU_COMPILE_AVX2=1

