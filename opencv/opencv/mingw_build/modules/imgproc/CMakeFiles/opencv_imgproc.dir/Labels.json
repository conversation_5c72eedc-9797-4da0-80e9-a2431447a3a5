{"sources": [{"file": "D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/hal/hal.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/hal/interface.h", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/imgproc.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/imgproc_c.h", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/types_c.h", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/detail/gcgraph.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/accum.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/accum.dispatch.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/approx.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/bilateral_filter.dispatch.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/blend.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/box_filter.dispatch.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/canny.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/clahe.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/color.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/color_hsv.dispatch.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/color_lab.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/color_rgb.dispatch.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/color_yuv.dispatch.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/colormap.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/connectedcomponents.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/contours.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/convhull.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/corner.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/cornersubpix.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/demosaicing.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/deriv.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/distransform.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/drawing.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/emd.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/featureselect.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/filter.dispatch.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/floodfill.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/gabor.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/generalized_hough.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/geometry.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/grabcut.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/hershey_fonts.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/histogram.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/hough.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/imgwarp.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/intersection.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/linefit.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/lsd.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/main.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/matchcontours.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/median_blur.dispatch.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/min_enclosing_triangle.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/moments.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/morph.dispatch.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/phasecorr.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/pyramids.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/resize.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/rotcalipers.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/samplers.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/segmentation.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/shapedescr.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/smooth.dispatch.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/spatialgradient.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/subdivision2d.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/sumpixels.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/tables.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/templmatch.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/thresh.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/utils.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/accumulate.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/bilateral.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/blend_linear.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/boxFilter.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/boxFilter3x3.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/calc_back_project.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/canny.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/clahe.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/color_hsv.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/color_lab.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/color_rgb.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/color_yuv.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/corner.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/covardata.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/filter2D.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/filter2DSmall.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/filterSepCol.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/filterSepRow.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/filterSep_singlePass.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/filterSmall.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/gaussianBlur3x3.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/gaussianBlur5x5.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/gftt.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/histogram.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/hough_lines.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/integral_sum.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/laplacian3.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/laplacian5.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/linearPolar.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/logPolar.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/match_template.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/medianFilter.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/moments.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/morph.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/morph3x3.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/precornerdetect.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/pyr_down.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/pyr_up.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/pyramid_up.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/remap.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/resize.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/sepFilter3x3.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/threshold.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/warp_affine.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/warp_perspective.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/warp_transform.cl", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/opencl_kernels_imgproc.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/opencl_kernels_imgproc.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/_geom.h", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/accum.simd.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/bilateral_filter.simd.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/box_filter.simd.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/color.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/color.simd_helpers.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/color_hsv.simd.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/color_rgb.simd.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/color_yuv.simd.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/corner.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/filter.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/filter.simd.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/filterengine.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/fixedpoint.inl.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/hal_replacement.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/imgwarp.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/median_blur.simd.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/morph.simd.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/precomp.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/resize.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/smooth.simd.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/sumpixels.hpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/imgwarp.sse4_1.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/resize.sse4_1.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/accum.sse4_1.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/box_filter.sse4_1.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/filter.sse4_1.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/color_hsv.sse4_1.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/color_rgb.sse4_1.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/color_yuv.sse4_1.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/median_blur.sse4_1.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/morph.sse4_1.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/smooth.sse4_1.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/corner.avx.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/accum.avx.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/imgwarp.avx2.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/imgproc/src/resize.avx2.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/accum.avx2.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/bilateral_filter.avx2.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/box_filter.avx2.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/filter.avx2.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/color_hsv.avx2.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/color_rgb.avx2.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/color_yuv.avx2.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/median_blur.avx2.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/morph.avx2.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/smooth.avx2.cpp", "labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/cvconfig.h"}, {"file": "D:/unet/opencv/opencv/mingw_build/opencv2/opencv_modules.hpp"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/vs_version.rc"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/opencv_imgproc_main.cpp"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/imgproc/opencl_kernels_imgproc.cpp.rule"}], "target": {"labels": ["Main", "opencv_imgproc", "<PERSON><PERSON><PERSON>"], "name": "opencv_imgproc"}}