// This file is auto-generated. Do not edit!

#include "opencv2/core/ocl.hpp"
#include "opencv2/core/ocl_genbase.hpp"
#include "opencv2/core/opencl/ocl_defs.hpp"

#ifdef HAVE_OPENCL

namespace cv
{
namespace ocl
{
namespace imgproc
{

extern struct cv::ocl::internal::ProgramEntry accumulate_oclsrc;
extern struct cv::ocl::internal::ProgramEntry bilateral_oclsrc;
extern struct cv::ocl::internal::ProgramEntry blend_linear_oclsrc;
extern struct cv::ocl::internal::ProgramEntry boxFilter_oclsrc;
extern struct cv::ocl::internal::ProgramEntry boxFilter3x3_oclsrc;
extern struct cv::ocl::internal::ProgramEntry calc_back_project_oclsrc;
extern struct cv::ocl::internal::ProgramEntry canny_oclsrc;
extern struct cv::ocl::internal::ProgramEntry clahe_oclsrc;
extern struct cv::ocl::internal::ProgramEntry color_hsv_oclsrc;
extern struct cv::ocl::internal::ProgramEntry color_lab_oclsrc;
extern struct cv::ocl::internal::ProgramEntry color_rgb_oclsrc;
extern struct cv::ocl::internal::ProgramEntry color_yuv_oclsrc;
extern struct cv::ocl::internal::ProgramEntry corner_oclsrc;
extern struct cv::ocl::internal::ProgramEntry covardata_oclsrc;
extern struct cv::ocl::internal::ProgramEntry filter2D_oclsrc;
extern struct cv::ocl::internal::ProgramEntry filter2DSmall_oclsrc;
extern struct cv::ocl::internal::ProgramEntry filterSepCol_oclsrc;
extern struct cv::ocl::internal::ProgramEntry filterSepRow_oclsrc;
extern struct cv::ocl::internal::ProgramEntry filterSep_singlePass_oclsrc;
extern struct cv::ocl::internal::ProgramEntry filterSmall_oclsrc;
extern struct cv::ocl::internal::ProgramEntry gaussianBlur3x3_oclsrc;
extern struct cv::ocl::internal::ProgramEntry gaussianBlur5x5_oclsrc;
extern struct cv::ocl::internal::ProgramEntry gftt_oclsrc;
extern struct cv::ocl::internal::ProgramEntry histogram_oclsrc;
extern struct cv::ocl::internal::ProgramEntry hough_lines_oclsrc;
extern struct cv::ocl::internal::ProgramEntry integral_sum_oclsrc;
extern struct cv::ocl::internal::ProgramEntry laplacian3_oclsrc;
extern struct cv::ocl::internal::ProgramEntry laplacian5_oclsrc;
extern struct cv::ocl::internal::ProgramEntry linearPolar_oclsrc;
extern struct cv::ocl::internal::ProgramEntry logPolar_oclsrc;
extern struct cv::ocl::internal::ProgramEntry match_template_oclsrc;
extern struct cv::ocl::internal::ProgramEntry medianFilter_oclsrc;
extern struct cv::ocl::internal::ProgramEntry moments_oclsrc;
extern struct cv::ocl::internal::ProgramEntry morph_oclsrc;
extern struct cv::ocl::internal::ProgramEntry morph3x3_oclsrc;
extern struct cv::ocl::internal::ProgramEntry precornerdetect_oclsrc;
extern struct cv::ocl::internal::ProgramEntry pyr_down_oclsrc;
extern struct cv::ocl::internal::ProgramEntry pyr_up_oclsrc;
extern struct cv::ocl::internal::ProgramEntry pyramid_up_oclsrc;
extern struct cv::ocl::internal::ProgramEntry remap_oclsrc;
extern struct cv::ocl::internal::ProgramEntry resize_oclsrc;
extern struct cv::ocl::internal::ProgramEntry sepFilter3x3_oclsrc;
extern struct cv::ocl::internal::ProgramEntry threshold_oclsrc;
extern struct cv::ocl::internal::ProgramEntry warp_affine_oclsrc;
extern struct cv::ocl::internal::ProgramEntry warp_perspective_oclsrc;
extern struct cv::ocl::internal::ProgramEntry warp_transform_oclsrc;

}}}
#endif
