# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

# Include any dependencies generated for this target.
include modules/python3/CMakeFiles/opencv_python3.dir/depend.make

# Include the progress variables for this target.
include modules/python3/CMakeFiles/opencv_python3.dir/progress.make

# Include the compile flags for this target's objects.
include modules/python3/CMakeFiles/opencv_python3.dir/flags.make

modules/python3/CMakeFiles/opencv_python3.dir/__/src2/cv2.cpp.obj: modules/python3/CMakeFiles/opencv_python3.dir/flags.make
modules/python3/CMakeFiles/opencv_python3.dir/__/src2/cv2.cpp.obj: modules/python3/CMakeFiles/opencv_python3.dir/includes_CXX.rsp
modules/python3/CMakeFiles/opencv_python3.dir/__/src2/cv2.cpp.obj: D:/unet/opencv/opencv/sources/modules/python/src2/cv2.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object modules/python3/CMakeFiles/opencv_python3.dir/__/src2/cv2.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\python3 && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_python3.dir\__\src2\cv2.cpp.obj -c D:\unet\opencv\opencv\sources\modules\python\src2\cv2.cpp

modules/python3/CMakeFiles/opencv_python3.dir/__/src2/cv2.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_python3.dir/__/src2/cv2.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\python3 && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\python\src2\cv2.cpp > CMakeFiles\opencv_python3.dir\__\src2\cv2.cpp.i

modules/python3/CMakeFiles/opencv_python3.dir/__/src2/cv2.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_python3.dir/__/src2/cv2.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\python3 && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\python\src2\cv2.cpp -o CMakeFiles\opencv_python3.dir\__\src2\cv2.cpp.s

# Object files for target opencv_python3
opencv_python3_OBJECTS = \
"CMakeFiles/opencv_python3.dir/__/src2/cv2.cpp.obj"

# External object files for target opencv_python3
opencv_python3_EXTERNAL_OBJECTS =

lib/python3/cv2.cp38-win_amd64.pyd: modules/python3/CMakeFiles/opencv_python3.dir/__/src2/cv2.cpp.obj
lib/python3/cv2.cp38-win_amd64.pyd: modules/python3/CMakeFiles/opencv_python3.dir/build.make
lib/python3/cv2.cp38-win_amd64.pyd: D:/anaconda/python38.dll
lib/python3/cv2.cp38-win_amd64.pyd: lib/libopencv_ml420.dll.a
lib/python3/cv2.cp38-win_amd64.pyd: lib/libopencv_photo420.dll.a
lib/python3/cv2.cp38-win_amd64.pyd: lib/libopencv_dnn420.dll.a
lib/python3/cv2.cp38-win_amd64.pyd: lib/libopencv_highgui420.dll.a
lib/python3/cv2.cp38-win_amd64.pyd: lib/libopencv_objdetect420.dll.a
lib/python3/cv2.cp38-win_amd64.pyd: lib/libopencv_stitching420.dll.a
lib/python3/cv2.cp38-win_amd64.pyd: lib/libopencv_video420.dll.a
lib/python3/cv2.cp38-win_amd64.pyd: lib/libopencv_videoio420.dll.a
lib/python3/cv2.cp38-win_amd64.pyd: lib/libopencv_imgcodecs420.dll.a
lib/python3/cv2.cp38-win_amd64.pyd: lib/libopencv_calib3d420.dll.a
lib/python3/cv2.cp38-win_amd64.pyd: lib/libopencv_features2d420.dll.a
lib/python3/cv2.cp38-win_amd64.pyd: lib/libopencv_flann420.dll.a
lib/python3/cv2.cp38-win_amd64.pyd: lib/libopencv_imgproc420.dll.a
lib/python3/cv2.cp38-win_amd64.pyd: lib/libopencv_core420.dll.a
lib/python3/cv2.cp38-win_amd64.pyd: modules/python3/CMakeFiles/opencv_python3.dir/linklibs.rsp
lib/python3/cv2.cp38-win_amd64.pyd: modules/python3/CMakeFiles/opencv_python3.dir/objects1.rsp
lib/python3/cv2.cp38-win_amd64.pyd: modules/python3/CMakeFiles/opencv_python3.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX shared module ..\..\lib\python3\cv2.cp38-win_amd64.pyd"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\python3 && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\opencv_python3.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
modules/python3/CMakeFiles/opencv_python3.dir/build: lib/python3/cv2.cp38-win_amd64.pyd

.PHONY : modules/python3/CMakeFiles/opencv_python3.dir/build

modules/python3/CMakeFiles/opencv_python3.dir/clean:
	cd /d D:\unet\opencv\opencv\mingw_build\modules\python3 && $(CMAKE_COMMAND) -P CMakeFiles\opencv_python3.dir\cmake_clean.cmake
.PHONY : modules/python3/CMakeFiles/opencv_python3.dir/clean

modules/python3/CMakeFiles/opencv_python3.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\unet\opencv\opencv\sources D:\unet\opencv\opencv\sources\modules\python\python3 D:\unet\opencv\opencv\mingw_build D:\unet\opencv\opencv\mingw_build\modules\python3 D:\unet\opencv\opencv\mingw_build\modules\python3\CMakeFiles\opencv_python3.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : modules/python3/CMakeFiles/opencv_python3.dir/depend

