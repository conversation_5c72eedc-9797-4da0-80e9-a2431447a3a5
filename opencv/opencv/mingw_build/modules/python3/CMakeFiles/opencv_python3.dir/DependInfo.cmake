# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "D:/unet/opencv/opencv/sources/modules/python/src2/cv2.cpp" "D:/unet/opencv/opencv/mingw_build/modules/python3/CMakeFiles/opencv_python3.dir/__/src2/cv2.cpp.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/python/python3/include"
  "modules/python3"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/flann/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "D:/unet/opencv/opencv/sources/modules/ml/include"
  "D:/unet/opencv/opencv/sources/modules/photo/include"
  "D:/unet/opencv/opencv/sources/modules/dnn/include"
  "D:/unet/opencv/opencv/sources/modules/features2d/include"
  "D:/unet/opencv/opencv/sources/modules/imgcodecs/include"
  "D:/unet/opencv/opencv/sources/modules/videoio/include"
  "D:/unet/opencv/opencv/sources/modules/calib3d/include"
  "D:/unet/opencv/opencv/sources/modules/highgui/include"
  "D:/unet/opencv/opencv/sources/modules/objdetect/include"
  "D:/unet/opencv/opencv/sources/modules/stitching/include"
  "D:/unet/opencv/opencv/sources/modules/video/include"
  "D:/unet/opencv/opencv/sources/modules/python/src2"
  "modules/python_bindings_generator"
  "."
  "D:/anaconda/include"
  "D:/anaconda/lib/site-packages/numpy/core/include"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "D:/unet/opencv/opencv/mingw_build/modules/ml/CMakeFiles/opencv_ml.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/highgui/CMakeFiles/opencv_highgui.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/objdetect/CMakeFiles/opencv_objdetect.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/stitching/CMakeFiles/opencv_stitching.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/video/CMakeFiles/opencv_video.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/videoio/CMakeFiles/opencv_videoio.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/flann/CMakeFiles/opencv_flann.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/imgproc/CMakeFiles/opencv_imgproc.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/core/CMakeFiles/opencv_core.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
