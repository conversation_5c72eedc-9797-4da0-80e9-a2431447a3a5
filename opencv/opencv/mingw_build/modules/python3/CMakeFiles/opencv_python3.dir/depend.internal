# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

modules/python3/CMakeFiles/opencv_python3.dir/__/src2/cv2.cpp.obj
 D:/anaconda/include/Python.h
 D:/anaconda/include/abstract.h
 D:/anaconda/include/bltinmodule.h
 D:/anaconda/include/boolobject.h
 D:/anaconda/include/bytearrayobject.h
 D:/anaconda/include/bytesobject.h
 D:/anaconda/include/cellobject.h
 D:/anaconda/include/ceval.h
 D:/anaconda/include/classobject.h
 D:/anaconda/include/code.h
 D:/anaconda/include/codecs.h
 D:/anaconda/include/compile.h
 D:/anaconda/include/complexobject.h
 D:/anaconda/include/context.h
 D:/anaconda/include/cpython/abstract.h
 D:/anaconda/include/cpython/dictobject.h
 D:/anaconda/include/cpython/fileobject.h
 D:/anaconda/include/cpython/initconfig.h
 D:/anaconda/include/cpython/object.h
 D:/anaconda/include/cpython/objimpl.h
 D:/anaconda/include/cpython/pyerrors.h
 D:/anaconda/include/cpython/pylifecycle.h
 D:/anaconda/include/cpython/pymem.h
 D:/anaconda/include/cpython/pystate.h
 D:/anaconda/include/cpython/sysmodule.h
 D:/anaconda/include/cpython/traceback.h
 D:/anaconda/include/cpython/tupleobject.h
 D:/anaconda/include/cpython/unicodeobject.h
 D:/anaconda/include/descrobject.h
 D:/anaconda/include/dictobject.h
 D:/anaconda/include/dtoa.h
 D:/anaconda/include/enumobject.h
 D:/anaconda/include/eval.h
 D:/anaconda/include/fileobject.h
 D:/anaconda/include/fileutils.h
 D:/anaconda/include/floatobject.h
 D:/anaconda/include/funcobject.h
 D:/anaconda/include/genobject.h
 D:/anaconda/include/import.h
 D:/anaconda/include/intrcheck.h
 D:/anaconda/include/iterobject.h
 D:/anaconda/include/listobject.h
 D:/anaconda/include/longintrepr.h
 D:/anaconda/include/longobject.h
 D:/anaconda/include/memoryobject.h
 D:/anaconda/include/methodobject.h
 D:/anaconda/include/modsupport.h
 D:/anaconda/include/moduleobject.h
 D:/anaconda/include/namespaceobject.h
 D:/anaconda/include/object.h
 D:/anaconda/include/objimpl.h
 D:/anaconda/include/odictobject.h
 D:/anaconda/include/osmodule.h
 D:/anaconda/include/patchlevel.h
 D:/anaconda/include/picklebufobject.h
 D:/anaconda/include/pyarena.h
 D:/anaconda/include/pycapsule.h
 D:/anaconda/include/pyconfig.h
 D:/anaconda/include/pyctype.h
 D:/anaconda/include/pydebug.h
 D:/anaconda/include/pyerrors.h
 D:/anaconda/include/pyfpe.h
 D:/anaconda/include/pyhash.h
 D:/anaconda/include/pylifecycle.h
 D:/anaconda/include/pymacconfig.h
 D:/anaconda/include/pymacro.h
 D:/anaconda/include/pymath.h
 D:/anaconda/include/pymem.h
 D:/anaconda/include/pyport.h
 D:/anaconda/include/pystate.h
 D:/anaconda/include/pystrcmp.h
 D:/anaconda/include/pystrtod.h
 D:/anaconda/include/pythonrun.h
 D:/anaconda/include/pythread.h
 D:/anaconda/include/pytime.h
 D:/anaconda/include/rangeobject.h
 D:/anaconda/include/setobject.h
 D:/anaconda/include/sliceobject.h
 D:/anaconda/include/structseq.h
 D:/anaconda/include/sysmodule.h
 D:/anaconda/include/traceback.h
 D:/anaconda/include/tracemalloc.h
 D:/anaconda/include/tupleobject.h
 D:/anaconda/include/typeslots.h
 D:/anaconda/include/unicodeobject.h
 D:/anaconda/include/warnings.h
 D:/anaconda/include/weakrefobject.h
 D:/anaconda/lib/site-packages/numpy/core/include/numpy/__multiarray_api.h
 D:/anaconda/lib/site-packages/numpy/core/include/numpy/_neighborhood_iterator_imp.h
 D:/anaconda/lib/site-packages/numpy/core/include/numpy/_numpyconfig.h
 D:/anaconda/lib/site-packages/numpy/core/include/numpy/ndarrayobject.h
 D:/anaconda/lib/site-packages/numpy/core/include/numpy/ndarraytypes.h
 D:/anaconda/lib/site-packages/numpy/core/include/numpy/npy_1_7_deprecated_api.h
 D:/anaconda/lib/site-packages/numpy/core/include/numpy/npy_common.h
 D:/anaconda/lib/site-packages/numpy/core/include/numpy/npy_cpu.h
 D:/anaconda/lib/site-packages/numpy/core/include/numpy/npy_endian.h
 D:/anaconda/lib/site-packages/numpy/core/include/numpy/numpyconfig.h
 D:/anaconda/lib/site-packages/numpy/core/include/numpy/old_defines.h
 D:/anaconda/lib/site-packages/numpy/core/include/numpy/utils.h
 D:/unet/opencv/opencv/sources/modules/calib3d/include/opencv2/calib3d.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/affine.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/async.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/base.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/bindings_utils.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/bufferpool.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/check.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda.inl.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda_types.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_dispatch.h
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_helper.h
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvdef.h
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.inl.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd_wrapper.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/detail/async_promise.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/detail/exception_ptr.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/fast_math.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/interface.h
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/msa_macros.h
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.inl.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/matx.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/neon_utils.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opengl.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/operations.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/optim.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ovx.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/persistence.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/saturate.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/traits.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/types.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/types_c.h
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utility.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/configuration.private.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/instrumentation.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/logger.defines.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/logger.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/logtag.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/tls.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/version.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/vsx_utils.hpp
 D:/unet/opencv/opencv/sources/modules/core/misc/python/pyopencv_async.hpp
 D:/unet/opencv/opencv/sources/modules/core/misc/python/pyopencv_cuda.hpp
 D:/unet/opencv/opencv/sources/modules/core/misc/python/pyopencv_umat.hpp
 D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn.hpp
 D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/dict.hpp
 D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/dnn.hpp
 D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/dnn.inl.hpp
 D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/layer.hpp
 D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/utils/inference_engine.hpp
 D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/version.hpp
 D:/unet/opencv/opencv/sources/modules/dnn/misc/python/pyopencv_dnn.hpp
 D:/unet/opencv/opencv/sources/modules/features2d/include/opencv2/features2d.hpp
 D:/unet/opencv/opencv/sources/modules/features2d/misc/python/pyopencv_features2d.hpp
 D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/config.h
 D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/defines.h
 D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/miniflann.hpp
 D:/unet/opencv/opencv/sources/modules/flann/misc/python/pyopencv_flann.hpp
 D:/unet/opencv/opencv/sources/modules/highgui/include/opencv2/highgui.hpp
 D:/unet/opencv/opencv/sources/modules/imgcodecs/include/opencv2/imgcodecs.hpp
 D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc.hpp
 D:/unet/opencv/opencv/sources/modules/ml/include/opencv2/ml.hpp
 D:/unet/opencv/opencv/sources/modules/ml/include/opencv2/ml/ml.inl.hpp
 D:/unet/opencv/opencv/sources/modules/ml/misc/python/pyopencv_ml.hpp
 D:/unet/opencv/opencv/sources/modules/objdetect/include/opencv2/objdetect.hpp
 D:/unet/opencv/opencv/sources/modules/objdetect/include/opencv2/objdetect/detection_based_tracker.hpp
 D:/unet/opencv/opencv/sources/modules/objdetect/misc/python/pyopencv_objdetect.hpp
 D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo.hpp
 D:/unet/opencv/opencv/sources/modules/python/src2/cv2.cpp
 D:/unet/opencv/opencv/sources/modules/python/src2/pycompat.hpp
 D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching.hpp
 D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/autocalib.hpp
 D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/blenders.hpp
 D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/camera.hpp
 D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/exposure_compensate.hpp
 D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/matchers.hpp
 D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/motion_estimators.hpp
 D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/seam_finders.hpp
 D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/timelapsers.hpp
 D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/util.hpp
 D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/util_inl.hpp
 D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/warpers.hpp
 D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/warpers_inl.hpp
 D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/warpers.hpp
 D:/unet/opencv/opencv/sources/modules/stitching/misc/python/pyopencv_stitching.hpp
 D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video/background_segm.hpp
 D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video/tracking.hpp
 D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio.hpp
 D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio/registry.hpp
 D:/unet/opencv/opencv/sources/modules/videoio/misc/python/pyopencv_videoio.hpp
 cv_cpu_config.h
 cvconfig.h
 modules/python_bindings_generator/pyopencv_custom_headers.h
 modules/python_bindings_generator/pyopencv_generated_enums.h
 modules/python_bindings_generator/pyopencv_generated_funcs.h
 modules/python_bindings_generator/pyopencv_generated_include.h
 modules/python_bindings_generator/pyopencv_generated_modules.h
 modules/python_bindings_generator/pyopencv_generated_modules_content.h
 modules/python_bindings_generator/pyopencv_generated_types.h
 modules/python_bindings_generator/pyopencv_generated_types_content.h
 opencv2/opencv_modules.hpp
