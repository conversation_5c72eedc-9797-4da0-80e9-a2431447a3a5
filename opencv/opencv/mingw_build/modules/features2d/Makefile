# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\modules\features2d\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/features2d/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/features2d/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/features2d/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/features2d/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
modules/features2d/CMakeFiles/opencv_features2d.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/features2d/CMakeFiles/opencv_features2d.dir/rule
.PHONY : modules/features2d/CMakeFiles/opencv_features2d.dir/rule

# Convenience name for target.
opencv_features2d: modules/features2d/CMakeFiles/opencv_features2d.dir/rule

.PHONY : opencv_features2d

# fast build rule for target.
opencv_features2d/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/build
.PHONY : opencv_features2d/fast

opencl_kernels_features2d.obj: opencl_kernels_features2d.cpp.obj

.PHONY : opencl_kernels_features2d.obj

# target to build an object file
opencl_kernels_features2d.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/opencl_kernels_features2d.cpp.obj
.PHONY : opencl_kernels_features2d.cpp.obj

opencl_kernels_features2d.i: opencl_kernels_features2d.cpp.i

.PHONY : opencl_kernels_features2d.i

# target to preprocess a source file
opencl_kernels_features2d.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/opencl_kernels_features2d.cpp.i
.PHONY : opencl_kernels_features2d.cpp.i

opencl_kernels_features2d.s: opencl_kernels_features2d.cpp.s

.PHONY : opencl_kernels_features2d.s

# target to generate assembly for a file
opencl_kernels_features2d.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/opencl_kernels_features2d.cpp.s
.PHONY : opencl_kernels_features2d.cpp.s

opencv_features2d_main.obj: opencv_features2d_main.cpp.obj

.PHONY : opencv_features2d_main.obj

# target to build an object file
opencv_features2d_main.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/opencv_features2d_main.cpp.obj
.PHONY : opencv_features2d_main.cpp.obj

opencv_features2d_main.i: opencv_features2d_main.cpp.i

.PHONY : opencv_features2d_main.i

# target to preprocess a source file
opencv_features2d_main.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/opencv_features2d_main.cpp.i
.PHONY : opencv_features2d_main.cpp.i

opencv_features2d_main.s: opencv_features2d_main.cpp.s

.PHONY : opencv_features2d_main.s

# target to generate assembly for a file
opencv_features2d_main.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/opencv_features2d_main.cpp.s
.PHONY : opencv_features2d_main.cpp.s

src/agast.obj: src/agast.cpp.obj

.PHONY : src/agast.obj

# target to build an object file
src/agast.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/agast.cpp.obj
.PHONY : src/agast.cpp.obj

src/agast.i: src/agast.cpp.i

.PHONY : src/agast.i

# target to preprocess a source file
src/agast.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/agast.cpp.i
.PHONY : src/agast.cpp.i

src/agast.s: src/agast.cpp.s

.PHONY : src/agast.s

# target to generate assembly for a file
src/agast.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/agast.cpp.s
.PHONY : src/agast.cpp.s

src/agast_score.obj: src/agast_score.cpp.obj

.PHONY : src/agast_score.obj

# target to build an object file
src/agast_score.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/agast_score.cpp.obj
.PHONY : src/agast_score.cpp.obj

src/agast_score.i: src/agast_score.cpp.i

.PHONY : src/agast_score.i

# target to preprocess a source file
src/agast_score.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/agast_score.cpp.i
.PHONY : src/agast_score.cpp.i

src/agast_score.s: src/agast_score.cpp.s

.PHONY : src/agast_score.s

# target to generate assembly for a file
src/agast_score.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/agast_score.cpp.s
.PHONY : src/agast_score.cpp.s

src/akaze.obj: src/akaze.cpp.obj

.PHONY : src/akaze.obj

# target to build an object file
src/akaze.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/akaze.cpp.obj
.PHONY : src/akaze.cpp.obj

src/akaze.i: src/akaze.cpp.i

.PHONY : src/akaze.i

# target to preprocess a source file
src/akaze.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/akaze.cpp.i
.PHONY : src/akaze.cpp.i

src/akaze.s: src/akaze.cpp.s

.PHONY : src/akaze.s

# target to generate assembly for a file
src/akaze.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/akaze.cpp.s
.PHONY : src/akaze.cpp.s

src/bagofwords.obj: src/bagofwords.cpp.obj

.PHONY : src/bagofwords.obj

# target to build an object file
src/bagofwords.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/bagofwords.cpp.obj
.PHONY : src/bagofwords.cpp.obj

src/bagofwords.i: src/bagofwords.cpp.i

.PHONY : src/bagofwords.i

# target to preprocess a source file
src/bagofwords.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/bagofwords.cpp.i
.PHONY : src/bagofwords.cpp.i

src/bagofwords.s: src/bagofwords.cpp.s

.PHONY : src/bagofwords.s

# target to generate assembly for a file
src/bagofwords.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/bagofwords.cpp.s
.PHONY : src/bagofwords.cpp.s

src/blobdetector.obj: src/blobdetector.cpp.obj

.PHONY : src/blobdetector.obj

# target to build an object file
src/blobdetector.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/blobdetector.cpp.obj
.PHONY : src/blobdetector.cpp.obj

src/blobdetector.i: src/blobdetector.cpp.i

.PHONY : src/blobdetector.i

# target to preprocess a source file
src/blobdetector.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/blobdetector.cpp.i
.PHONY : src/blobdetector.cpp.i

src/blobdetector.s: src/blobdetector.cpp.s

.PHONY : src/blobdetector.s

# target to generate assembly for a file
src/blobdetector.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/blobdetector.cpp.s
.PHONY : src/blobdetector.cpp.s

src/brisk.obj: src/brisk.cpp.obj

.PHONY : src/brisk.obj

# target to build an object file
src/brisk.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/brisk.cpp.obj
.PHONY : src/brisk.cpp.obj

src/brisk.i: src/brisk.cpp.i

.PHONY : src/brisk.i

# target to preprocess a source file
src/brisk.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/brisk.cpp.i
.PHONY : src/brisk.cpp.i

src/brisk.s: src/brisk.cpp.s

.PHONY : src/brisk.s

# target to generate assembly for a file
src/brisk.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/brisk.cpp.s
.PHONY : src/brisk.cpp.s

src/draw.obj: src/draw.cpp.obj

.PHONY : src/draw.obj

# target to build an object file
src/draw.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/draw.cpp.obj
.PHONY : src/draw.cpp.obj

src/draw.i: src/draw.cpp.i

.PHONY : src/draw.i

# target to preprocess a source file
src/draw.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/draw.cpp.i
.PHONY : src/draw.cpp.i

src/draw.s: src/draw.cpp.s

.PHONY : src/draw.s

# target to generate assembly for a file
src/draw.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/draw.cpp.s
.PHONY : src/draw.cpp.s

src/dynamic.obj: src/dynamic.cpp.obj

.PHONY : src/dynamic.obj

# target to build an object file
src/dynamic.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/dynamic.cpp.obj
.PHONY : src/dynamic.cpp.obj

src/dynamic.i: src/dynamic.cpp.i

.PHONY : src/dynamic.i

# target to preprocess a source file
src/dynamic.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/dynamic.cpp.i
.PHONY : src/dynamic.cpp.i

src/dynamic.s: src/dynamic.cpp.s

.PHONY : src/dynamic.s

# target to generate assembly for a file
src/dynamic.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/dynamic.cpp.s
.PHONY : src/dynamic.cpp.s

src/evaluation.obj: src/evaluation.cpp.obj

.PHONY : src/evaluation.obj

# target to build an object file
src/evaluation.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/evaluation.cpp.obj
.PHONY : src/evaluation.cpp.obj

src/evaluation.i: src/evaluation.cpp.i

.PHONY : src/evaluation.i

# target to preprocess a source file
src/evaluation.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/evaluation.cpp.i
.PHONY : src/evaluation.cpp.i

src/evaluation.s: src/evaluation.cpp.s

.PHONY : src/evaluation.s

# target to generate assembly for a file
src/evaluation.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/evaluation.cpp.s
.PHONY : src/evaluation.cpp.s

src/fast.avx2.obj: src/fast.avx2.cpp.obj

.PHONY : src/fast.avx2.obj

# target to build an object file
src/fast.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/fast.avx2.cpp.obj
.PHONY : src/fast.avx2.cpp.obj

src/fast.avx2.i: src/fast.avx2.cpp.i

.PHONY : src/fast.avx2.i

# target to preprocess a source file
src/fast.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/fast.avx2.cpp.i
.PHONY : src/fast.avx2.cpp.i

src/fast.avx2.s: src/fast.avx2.cpp.s

.PHONY : src/fast.avx2.s

# target to generate assembly for a file
src/fast.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/fast.avx2.cpp.s
.PHONY : src/fast.avx2.cpp.s

src/fast.obj: src/fast.cpp.obj

.PHONY : src/fast.obj

# target to build an object file
src/fast.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/fast.cpp.obj
.PHONY : src/fast.cpp.obj

src/fast.i: src/fast.cpp.i

.PHONY : src/fast.i

# target to preprocess a source file
src/fast.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/fast.cpp.i
.PHONY : src/fast.cpp.i

src/fast.s: src/fast.cpp.s

.PHONY : src/fast.s

# target to generate assembly for a file
src/fast.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/fast.cpp.s
.PHONY : src/fast.cpp.s

src/fast_score.obj: src/fast_score.cpp.obj

.PHONY : src/fast_score.obj

# target to build an object file
src/fast_score.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/fast_score.cpp.obj
.PHONY : src/fast_score.cpp.obj

src/fast_score.i: src/fast_score.cpp.i

.PHONY : src/fast_score.i

# target to preprocess a source file
src/fast_score.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/fast_score.cpp.i
.PHONY : src/fast_score.cpp.i

src/fast_score.s: src/fast_score.cpp.s

.PHONY : src/fast_score.s

# target to generate assembly for a file
src/fast_score.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/fast_score.cpp.s
.PHONY : src/fast_score.cpp.s

src/feature2d.obj: src/feature2d.cpp.obj

.PHONY : src/feature2d.obj

# target to build an object file
src/feature2d.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/feature2d.cpp.obj
.PHONY : src/feature2d.cpp.obj

src/feature2d.i: src/feature2d.cpp.i

.PHONY : src/feature2d.i

# target to preprocess a source file
src/feature2d.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/feature2d.cpp.i
.PHONY : src/feature2d.cpp.i

src/feature2d.s: src/feature2d.cpp.s

.PHONY : src/feature2d.s

# target to generate assembly for a file
src/feature2d.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/feature2d.cpp.s
.PHONY : src/feature2d.cpp.s

src/gftt.obj: src/gftt.cpp.obj

.PHONY : src/gftt.obj

# target to build an object file
src/gftt.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/gftt.cpp.obj
.PHONY : src/gftt.cpp.obj

src/gftt.i: src/gftt.cpp.i

.PHONY : src/gftt.i

# target to preprocess a source file
src/gftt.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/gftt.cpp.i
.PHONY : src/gftt.cpp.i

src/gftt.s: src/gftt.cpp.s

.PHONY : src/gftt.s

# target to generate assembly for a file
src/gftt.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/gftt.cpp.s
.PHONY : src/gftt.cpp.s

src/kaze.obj: src/kaze.cpp.obj

.PHONY : src/kaze.obj

# target to build an object file
src/kaze.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze.cpp.obj
.PHONY : src/kaze.cpp.obj

src/kaze.i: src/kaze.cpp.i

.PHONY : src/kaze.i

# target to preprocess a source file
src/kaze.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze.cpp.i
.PHONY : src/kaze.cpp.i

src/kaze.s: src/kaze.cpp.s

.PHONY : src/kaze.s

# target to generate assembly for a file
src/kaze.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze.cpp.s
.PHONY : src/kaze.cpp.s

src/kaze/AKAZEFeatures.obj: src/kaze/AKAZEFeatures.cpp.obj

.PHONY : src/kaze/AKAZEFeatures.obj

# target to build an object file
src/kaze/AKAZEFeatures.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/AKAZEFeatures.cpp.obj
.PHONY : src/kaze/AKAZEFeatures.cpp.obj

src/kaze/AKAZEFeatures.i: src/kaze/AKAZEFeatures.cpp.i

.PHONY : src/kaze/AKAZEFeatures.i

# target to preprocess a source file
src/kaze/AKAZEFeatures.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/AKAZEFeatures.cpp.i
.PHONY : src/kaze/AKAZEFeatures.cpp.i

src/kaze/AKAZEFeatures.s: src/kaze/AKAZEFeatures.cpp.s

.PHONY : src/kaze/AKAZEFeatures.s

# target to generate assembly for a file
src/kaze/AKAZEFeatures.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/AKAZEFeatures.cpp.s
.PHONY : src/kaze/AKAZEFeatures.cpp.s

src/kaze/KAZEFeatures.obj: src/kaze/KAZEFeatures.cpp.obj

.PHONY : src/kaze/KAZEFeatures.obj

# target to build an object file
src/kaze/KAZEFeatures.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/KAZEFeatures.cpp.obj
.PHONY : src/kaze/KAZEFeatures.cpp.obj

src/kaze/KAZEFeatures.i: src/kaze/KAZEFeatures.cpp.i

.PHONY : src/kaze/KAZEFeatures.i

# target to preprocess a source file
src/kaze/KAZEFeatures.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/KAZEFeatures.cpp.i
.PHONY : src/kaze/KAZEFeatures.cpp.i

src/kaze/KAZEFeatures.s: src/kaze/KAZEFeatures.cpp.s

.PHONY : src/kaze/KAZEFeatures.s

# target to generate assembly for a file
src/kaze/KAZEFeatures.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/KAZEFeatures.cpp.s
.PHONY : src/kaze/KAZEFeatures.cpp.s

src/kaze/fed.obj: src/kaze/fed.cpp.obj

.PHONY : src/kaze/fed.obj

# target to build an object file
src/kaze/fed.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/fed.cpp.obj
.PHONY : src/kaze/fed.cpp.obj

src/kaze/fed.i: src/kaze/fed.cpp.i

.PHONY : src/kaze/fed.i

# target to preprocess a source file
src/kaze/fed.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/fed.cpp.i
.PHONY : src/kaze/fed.cpp.i

src/kaze/fed.s: src/kaze/fed.cpp.s

.PHONY : src/kaze/fed.s

# target to generate assembly for a file
src/kaze/fed.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/fed.cpp.s
.PHONY : src/kaze/fed.cpp.s

src/kaze/nldiffusion_functions.obj: src/kaze/nldiffusion_functions.cpp.obj

.PHONY : src/kaze/nldiffusion_functions.obj

# target to build an object file
src/kaze/nldiffusion_functions.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/nldiffusion_functions.cpp.obj
.PHONY : src/kaze/nldiffusion_functions.cpp.obj

src/kaze/nldiffusion_functions.i: src/kaze/nldiffusion_functions.cpp.i

.PHONY : src/kaze/nldiffusion_functions.i

# target to preprocess a source file
src/kaze/nldiffusion_functions.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/nldiffusion_functions.cpp.i
.PHONY : src/kaze/nldiffusion_functions.cpp.i

src/kaze/nldiffusion_functions.s: src/kaze/nldiffusion_functions.cpp.s

.PHONY : src/kaze/nldiffusion_functions.s

# target to generate assembly for a file
src/kaze/nldiffusion_functions.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/nldiffusion_functions.cpp.s
.PHONY : src/kaze/nldiffusion_functions.cpp.s

src/keypoint.obj: src/keypoint.cpp.obj

.PHONY : src/keypoint.obj

# target to build an object file
src/keypoint.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/keypoint.cpp.obj
.PHONY : src/keypoint.cpp.obj

src/keypoint.i: src/keypoint.cpp.i

.PHONY : src/keypoint.i

# target to preprocess a source file
src/keypoint.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/keypoint.cpp.i
.PHONY : src/keypoint.cpp.i

src/keypoint.s: src/keypoint.cpp.s

.PHONY : src/keypoint.s

# target to generate assembly for a file
src/keypoint.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/keypoint.cpp.s
.PHONY : src/keypoint.cpp.s

src/main.obj: src/main.cpp.obj

.PHONY : src/main.obj

# target to build an object file
src/main.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/main.cpp.obj
.PHONY : src/main.cpp.obj

src/main.i: src/main.cpp.i

.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s

.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

src/matchers.obj: src/matchers.cpp.obj

.PHONY : src/matchers.obj

# target to build an object file
src/matchers.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/matchers.cpp.obj
.PHONY : src/matchers.cpp.obj

src/matchers.i: src/matchers.cpp.i

.PHONY : src/matchers.i

# target to preprocess a source file
src/matchers.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/matchers.cpp.i
.PHONY : src/matchers.cpp.i

src/matchers.s: src/matchers.cpp.s

.PHONY : src/matchers.s

# target to generate assembly for a file
src/matchers.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/matchers.cpp.s
.PHONY : src/matchers.cpp.s

src/mser.obj: src/mser.cpp.obj

.PHONY : src/mser.obj

# target to build an object file
src/mser.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/mser.cpp.obj
.PHONY : src/mser.cpp.obj

src/mser.i: src/mser.cpp.i

.PHONY : src/mser.i

# target to preprocess a source file
src/mser.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/mser.cpp.i
.PHONY : src/mser.cpp.i

src/mser.s: src/mser.cpp.s

.PHONY : src/mser.s

# target to generate assembly for a file
src/mser.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/mser.cpp.s
.PHONY : src/mser.cpp.s

src/orb.obj: src/orb.cpp.obj

.PHONY : src/orb.obj

# target to build an object file
src/orb.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/orb.cpp.obj
.PHONY : src/orb.cpp.obj

src/orb.i: src/orb.cpp.i

.PHONY : src/orb.i

# target to preprocess a source file
src/orb.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/orb.cpp.i
.PHONY : src/orb.cpp.i

src/orb.s: src/orb.cpp.s

.PHONY : src/orb.s

# target to generate assembly for a file
src/orb.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/src/orb.cpp.s
.PHONY : src/orb.cpp.s

vs_version.obj: vs_version.rc.obj

.PHONY : vs_version.obj

# target to build an object file
vs_version.rc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/vs_version.rc.obj
.PHONY : vs_version.rc.obj

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... opencv_features2d
	@echo ... package
	@echo ... package_source
	@echo ... rebuild_cache
	@echo ... test
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... list_install_components
	@echo ... install/strip
	@echo ... opencl_kernels_features2d.obj
	@echo ... opencl_kernels_features2d.i
	@echo ... opencl_kernels_features2d.s
	@echo ... opencv_features2d_main.obj
	@echo ... opencv_features2d_main.i
	@echo ... opencv_features2d_main.s
	@echo ... src/agast.obj
	@echo ... src/agast.i
	@echo ... src/agast.s
	@echo ... src/agast_score.obj
	@echo ... src/agast_score.i
	@echo ... src/agast_score.s
	@echo ... src/akaze.obj
	@echo ... src/akaze.i
	@echo ... src/akaze.s
	@echo ... src/bagofwords.obj
	@echo ... src/bagofwords.i
	@echo ... src/bagofwords.s
	@echo ... src/blobdetector.obj
	@echo ... src/blobdetector.i
	@echo ... src/blobdetector.s
	@echo ... src/brisk.obj
	@echo ... src/brisk.i
	@echo ... src/brisk.s
	@echo ... src/draw.obj
	@echo ... src/draw.i
	@echo ... src/draw.s
	@echo ... src/dynamic.obj
	@echo ... src/dynamic.i
	@echo ... src/dynamic.s
	@echo ... src/evaluation.obj
	@echo ... src/evaluation.i
	@echo ... src/evaluation.s
	@echo ... src/fast.avx2.obj
	@echo ... src/fast.avx2.i
	@echo ... src/fast.avx2.s
	@echo ... src/fast.obj
	@echo ... src/fast.i
	@echo ... src/fast.s
	@echo ... src/fast_score.obj
	@echo ... src/fast_score.i
	@echo ... src/fast_score.s
	@echo ... src/feature2d.obj
	@echo ... src/feature2d.i
	@echo ... src/feature2d.s
	@echo ... src/gftt.obj
	@echo ... src/gftt.i
	@echo ... src/gftt.s
	@echo ... src/kaze.obj
	@echo ... src/kaze.i
	@echo ... src/kaze.s
	@echo ... src/kaze/AKAZEFeatures.obj
	@echo ... src/kaze/AKAZEFeatures.i
	@echo ... src/kaze/AKAZEFeatures.s
	@echo ... src/kaze/KAZEFeatures.obj
	@echo ... src/kaze/KAZEFeatures.i
	@echo ... src/kaze/KAZEFeatures.s
	@echo ... src/kaze/fed.obj
	@echo ... src/kaze/fed.i
	@echo ... src/kaze/fed.s
	@echo ... src/kaze/nldiffusion_functions.obj
	@echo ... src/kaze/nldiffusion_functions.i
	@echo ... src/kaze/nldiffusion_functions.s
	@echo ... src/keypoint.obj
	@echo ... src/keypoint.i
	@echo ... src/keypoint.s
	@echo ... src/main.obj
	@echo ... src/main.i
	@echo ... src/main.s
	@echo ... src/matchers.obj
	@echo ... src/matchers.i
	@echo ... src/matchers.s
	@echo ... src/mser.obj
	@echo ... src/mser.i
	@echo ... src/mser.s
	@echo ... src/orb.obj
	@echo ... src/orb.i
	@echo ... src/orb.s
	@echo ... vs_version.obj
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

