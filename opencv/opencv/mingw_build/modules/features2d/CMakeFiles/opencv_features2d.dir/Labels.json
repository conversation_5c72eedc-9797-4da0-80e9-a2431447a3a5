{"sources": [{"file": "D:/unet/opencv/opencv/sources/modules/features2d/include/opencv2/features2d.hpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/include/opencv2/features2d/features2d.hpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/include/opencv2/features2d/hal/interface.h", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/agast.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/agast_score.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/akaze.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/bagofwords.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/blobdetector.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/brisk.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/draw.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/dynamic.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/evaluation.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/fast.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/fast_score.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/feature2d.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/gftt.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/AKAZEFeatures.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/KAZEFeatures.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/fed.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/nldiffusion_functions.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/keypoint.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/main.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/matchers.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/mser.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/orb.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/opencl/akaze.cl", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/opencl/brute_force_match.cl", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/opencl/fast.cl", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/opencl/orb.cl", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/features2d/opencl_kernels_features2d.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/features2d/opencl_kernels_features2d.hpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/agast_score.hpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/fast.hpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/fast_score.hpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/hal_replacement.hpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/AKAZEConfig.h", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/AKAZEFeatures.h", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/KAZEConfig.h", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/KAZEFeatures.h", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/TEvolution.h", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/fed.h", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/nldiffusion_functions.h", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/utils.h", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/precomp.hpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/features2d/src/fast.avx2.cpp", "labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/cvconfig.h"}, {"file": "D:/unet/opencv/opencv/mingw_build/opencv2/opencv_modules.hpp"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/features2d/vs_version.rc"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/features2d/opencv_features2d_main.cpp"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/features2d/opencl_kernels_features2d.cpp.rule"}], "target": {"labels": ["Main", "opencv_features2d", "<PERSON><PERSON><PERSON>"], "name": "opencv_features2d"}}