# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  "RC"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "D:/unet/opencv/opencv/mingw_build/modules/features2d/opencl_kernels_features2d.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/opencl_kernels_features2d.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/modules/features2d/opencv_features2d_main.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/opencv_features2d_main.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/agast.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/agast.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/agast_score.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/agast_score.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/akaze.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/akaze.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/bagofwords.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/bagofwords.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/blobdetector.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/blobdetector.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/brisk.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/brisk.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/draw.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/draw.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/dynamic.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/dynamic.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/evaluation.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/evaluation.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/fast.avx2.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/fast.avx2.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/fast.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/fast.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/fast_score.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/fast_score.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/feature2d.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/feature2d.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/gftt.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/gftt.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/AKAZEFeatures.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/AKAZEFeatures.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/KAZEFeatures.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/KAZEFeatures.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/fed.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/fed.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/nldiffusion_functions.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/kaze/nldiffusion_functions.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/keypoint.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/keypoint.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/main.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/main.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/matchers.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/matchers.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/mser.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/mser.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/features2d/src/orb.cpp" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/src/orb.cpp.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/features2d/include"
  "modules/features2d"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/flann/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )
set(CMAKE_DEPENDS_CHECK_RC
  "D:/unet/opencv/opencv/mingw_build/modules/features2d/vs_version.rc" "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/vs_version.rc.obj"
  )

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_RC
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_RC_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/features2d/include"
  "modules/features2d"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/flann/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "D:/unet/opencv/opencv/mingw_build/modules/flann/CMakeFiles/opencv_flann.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/imgproc/CMakeFiles/opencv_imgproc.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/core/CMakeFiles/opencv_core.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
