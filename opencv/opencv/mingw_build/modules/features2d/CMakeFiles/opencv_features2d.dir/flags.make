# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# compile CXX with E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/g++.exe
# compile RC with E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/windres.exe
CXX_FLAGS =    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wundef -Winit-self -Wpointer-arith -Wshadow -Wsign-promo -Wuninitialized -Winit-self -Wsuggest-override -Wno-delete-non-virtual-dtor -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -fvisibility-inlines-hidden -O3 -DNDEBUG  -DNDEBUG   -std=c++11

CXX_DEFINES = -DCVAPI_EXPORTS -D_USE_MATH_DEFINES -D_WIN32_WINNT=0x0601 -D__OPENCV_BUILD=1 -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS

CXX_INCLUDES = @CMakeFiles/opencv_features2d.dir/includes_CXX.rsp

RC_FLAGS =  

RC_DEFINES = -DCVAPI_EXPORTS -D_USE_MATH_DEFINES -D_WIN32_WINNT=0x0601 -D__OPENCV_BUILD=1 -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D__STDC_LIMIT_MACROS

RC_INCLUDES = -ID:\unet\opencv\opencv\sources\modules\features2d\include -ID:\unet\opencv\opencv\mingw_build\modules\features2d -ID:\unet\opencv\opencv\sources\modules\core\include -ID:\unet\opencv\opencv\sources\modules\flann\include -ID:\unet\opencv\opencv\sources\modules\imgproc\include -ID:\unet\opencv\opencv\mingw_build -ID:\visp-ws\eigen-3.3.7\build-vc14\install\include\eigen3 

# Custom flags: modules/features2d/CMakeFiles/opencv_features2d.dir/src/fast.avx2.cpp.obj_FLAGS =  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2

# Custom defines: modules/features2d/CMakeFiles/opencv_features2d.dir/src/fast.avx2.cpp.obj_DEFINES = CV_CPU_DISPATCH_MODE=AVX2;CV_CPU_COMPILE_SSSE3=1;CV_CPU_COMPILE_SSE4_1=1;CV_CPU_COMPILE_POPCNT=1;CV_CPU_COMPILE_SSE4_2=1;CV_CPU_COMPILE_FP16=1;CV_CPU_COMPILE_FMA3=1;CV_CPU_COMPILE_AVX=1;CV_CPU_COMPILE_AVX2=1

