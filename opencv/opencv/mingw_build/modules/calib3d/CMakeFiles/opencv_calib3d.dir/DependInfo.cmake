# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  "RC"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "D:/unet/opencv/opencv/mingw_build/modules/calib3d/opencl_kernels_calib3d.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/opencl_kernels_calib3d.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/modules/calib3d/opencv_calib3d_main.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/opencv_calib3d_main.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/ap3p.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/ap3p.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/calibinit.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/calibinit.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/calibration.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/calibration.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/calibration_handeye.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/calibration_handeye.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/checkchessboard.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/checkchessboard.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/chessboard.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/chessboard.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/circlesgrid.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/circlesgrid.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/compat_ptsetreg.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/compat_ptsetreg.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/dls.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/dls.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/epnp.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/epnp.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/fisheye.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/fisheye.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/five-point.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/five-point.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/fundam.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/fundam.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/homography_decomp.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/homography_decomp.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/ippe.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/ippe.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/levmarq.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/levmarq.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/main.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/main.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/p3p.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/p3p.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/polynom_solver.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/polynom_solver.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/posit.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/posit.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/ptsetreg.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/ptsetreg.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/quadsubpix.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/quadsubpix.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/rho.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/rho.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/solvepnp.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/solvepnp.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/stereobm.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/stereobm.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/stereosgbm.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/stereosgbm.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/triangulate.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/triangulate.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/undistort.dispatch.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/undistort.dispatch.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/calib3d/src/upnp.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/src/upnp.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/modules/calib3d/undistort.avx2.cpp" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/undistort.avx2.cpp.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/calib3d/include"
  "modules/calib3d"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/flann/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "D:/unet/opencv/opencv/sources/modules/features2d/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )
set(CMAKE_DEPENDS_CHECK_RC
  "D:/unet/opencv/opencv/mingw_build/modules/calib3d/vs_version.rc" "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/vs_version.rc.obj"
  )

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_RC
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_RC_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/calib3d/include"
  "modules/calib3d"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/flann/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "D:/unet/opencv/opencv/sources/modules/features2d/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/flann/CMakeFiles/opencv_flann.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/imgproc/CMakeFiles/opencv_imgproc.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/core/CMakeFiles/opencv_core.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
