# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\modules\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
modules/CMakeFiles/ade.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/CMakeFiles/ade.dir/rule
.PHONY : modules/CMakeFiles/ade.dir/rule

# Convenience name for target.
ade: modules/CMakeFiles/ade.dir/rule

.PHONY : ade

# fast build rule for target.
ade/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/build
.PHONY : ade/fast

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.cpp.s

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.obj: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.obj

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.obj

# target to build an object file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.obj
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.obj

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.i: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.i

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.i

# target to preprocess a source file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.i
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.i

__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.s: __/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.s

.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.s

# target to generate assembly for a file
__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/__/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.s
.PHONY : __/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... ade
	@echo ... package
	@echo ... package_source
	@echo ... rebuild_cache
	@echo ... test
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... list_install_components
	@echo ... install/strip
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/alloc.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/assert.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/check_cycles.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/edge.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/execution_engine.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/graph.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_accessor.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_ref.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/memory_descriptor_view.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metadata.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/metatypes.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/node.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/passes/communications.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/search.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/subgraphs.s
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.obj
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.i
	@echo ... __/3rdparty/ade/ade-0.1.1f/sources/ade/source/topological_sort.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

