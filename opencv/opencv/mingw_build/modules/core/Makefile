# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\modules\core\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/core/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/core/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/core/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/core/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
modules/core/CMakeFiles/opencv_core.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/core/CMakeFiles/opencv_core.dir/rule
.PHONY : modules/core/CMakeFiles/opencv_core.dir/rule

# Convenience name for target.
opencv_core: modules/core/CMakeFiles/opencv_core.dir/rule

.PHONY : opencv_core

# fast build rule for target.
opencv_core/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/build
.PHONY : opencv_core/fast

arithm.avx2.obj: arithm.avx2.cpp.obj

.PHONY : arithm.avx2.obj

# target to build an object file
arithm.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/arithm.avx2.cpp.obj
.PHONY : arithm.avx2.cpp.obj

arithm.avx2.i: arithm.avx2.cpp.i

.PHONY : arithm.avx2.i

# target to preprocess a source file
arithm.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/arithm.avx2.cpp.i
.PHONY : arithm.avx2.cpp.i

arithm.avx2.s: arithm.avx2.cpp.s

.PHONY : arithm.avx2.s

# target to generate assembly for a file
arithm.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/arithm.avx2.cpp.s
.PHONY : arithm.avx2.cpp.s

arithm.sse4_1.obj: arithm.sse4_1.cpp.obj

.PHONY : arithm.sse4_1.obj

# target to build an object file
arithm.sse4_1.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/arithm.sse4_1.cpp.obj
.PHONY : arithm.sse4_1.cpp.obj

arithm.sse4_1.i: arithm.sse4_1.cpp.i

.PHONY : arithm.sse4_1.i

# target to preprocess a source file
arithm.sse4_1.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/arithm.sse4_1.cpp.i
.PHONY : arithm.sse4_1.cpp.i

arithm.sse4_1.s: arithm.sse4_1.cpp.s

.PHONY : arithm.sse4_1.s

# target to generate assembly for a file
arithm.sse4_1.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/arithm.sse4_1.cpp.s
.PHONY : arithm.sse4_1.cpp.s

convert.avx2.obj: convert.avx2.cpp.obj

.PHONY : convert.avx2.obj

# target to build an object file
convert.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/convert.avx2.cpp.obj
.PHONY : convert.avx2.cpp.obj

convert.avx2.i: convert.avx2.cpp.i

.PHONY : convert.avx2.i

# target to preprocess a source file
convert.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/convert.avx2.cpp.i
.PHONY : convert.avx2.cpp.i

convert.avx2.s: convert.avx2.cpp.s

.PHONY : convert.avx2.s

# target to generate assembly for a file
convert.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/convert.avx2.cpp.s
.PHONY : convert.avx2.cpp.s

convert_scale.avx2.obj: convert_scale.avx2.cpp.obj

.PHONY : convert_scale.avx2.obj

# target to build an object file
convert_scale.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/convert_scale.avx2.cpp.obj
.PHONY : convert_scale.avx2.cpp.obj

convert_scale.avx2.i: convert_scale.avx2.cpp.i

.PHONY : convert_scale.avx2.i

# target to preprocess a source file
convert_scale.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/convert_scale.avx2.cpp.i
.PHONY : convert_scale.avx2.cpp.i

convert_scale.avx2.s: convert_scale.avx2.cpp.s

.PHONY : convert_scale.avx2.s

# target to generate assembly for a file
convert_scale.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/convert_scale.avx2.cpp.s
.PHONY : convert_scale.avx2.cpp.s

count_non_zero.avx2.obj: count_non_zero.avx2.cpp.obj

.PHONY : count_non_zero.avx2.obj

# target to build an object file
count_non_zero.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/count_non_zero.avx2.cpp.obj
.PHONY : count_non_zero.avx2.cpp.obj

count_non_zero.avx2.i: count_non_zero.avx2.cpp.i

.PHONY : count_non_zero.avx2.i

# target to preprocess a source file
count_non_zero.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/count_non_zero.avx2.cpp.i
.PHONY : count_non_zero.avx2.cpp.i

count_non_zero.avx2.s: count_non_zero.avx2.cpp.s

.PHONY : count_non_zero.avx2.s

# target to generate assembly for a file
count_non_zero.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/count_non_zero.avx2.cpp.s
.PHONY : count_non_zero.avx2.cpp.s

mathfuncs_core.avx.obj: mathfuncs_core.avx.cpp.obj

.PHONY : mathfuncs_core.avx.obj

# target to build an object file
mathfuncs_core.avx.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/mathfuncs_core.avx.cpp.obj
.PHONY : mathfuncs_core.avx.cpp.obj

mathfuncs_core.avx.i: mathfuncs_core.avx.cpp.i

.PHONY : mathfuncs_core.avx.i

# target to preprocess a source file
mathfuncs_core.avx.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/mathfuncs_core.avx.cpp.i
.PHONY : mathfuncs_core.avx.cpp.i

mathfuncs_core.avx.s: mathfuncs_core.avx.cpp.s

.PHONY : mathfuncs_core.avx.s

# target to generate assembly for a file
mathfuncs_core.avx.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/mathfuncs_core.avx.cpp.s
.PHONY : mathfuncs_core.avx.cpp.s

mathfuncs_core.avx2.obj: mathfuncs_core.avx2.cpp.obj

.PHONY : mathfuncs_core.avx2.obj

# target to build an object file
mathfuncs_core.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/mathfuncs_core.avx2.cpp.obj
.PHONY : mathfuncs_core.avx2.cpp.obj

mathfuncs_core.avx2.i: mathfuncs_core.avx2.cpp.i

.PHONY : mathfuncs_core.avx2.i

# target to preprocess a source file
mathfuncs_core.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/mathfuncs_core.avx2.cpp.i
.PHONY : mathfuncs_core.avx2.cpp.i

mathfuncs_core.avx2.s: mathfuncs_core.avx2.cpp.s

.PHONY : mathfuncs_core.avx2.s

# target to generate assembly for a file
mathfuncs_core.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/mathfuncs_core.avx2.cpp.s
.PHONY : mathfuncs_core.avx2.cpp.s

matmul.avx2.obj: matmul.avx2.cpp.obj

.PHONY : matmul.avx2.obj

# target to build an object file
matmul.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/matmul.avx2.cpp.obj
.PHONY : matmul.avx2.cpp.obj

matmul.avx2.i: matmul.avx2.cpp.i

.PHONY : matmul.avx2.i

# target to preprocess a source file
matmul.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/matmul.avx2.cpp.i
.PHONY : matmul.avx2.cpp.i

matmul.avx2.s: matmul.avx2.cpp.s

.PHONY : matmul.avx2.s

# target to generate assembly for a file
matmul.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/matmul.avx2.cpp.s
.PHONY : matmul.avx2.cpp.s

matmul.sse4_1.obj: matmul.sse4_1.cpp.obj

.PHONY : matmul.sse4_1.obj

# target to build an object file
matmul.sse4_1.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/matmul.sse4_1.cpp.obj
.PHONY : matmul.sse4_1.cpp.obj

matmul.sse4_1.i: matmul.sse4_1.cpp.i

.PHONY : matmul.sse4_1.i

# target to preprocess a source file
matmul.sse4_1.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/matmul.sse4_1.cpp.i
.PHONY : matmul.sse4_1.cpp.i

matmul.sse4_1.s: matmul.sse4_1.cpp.s

.PHONY : matmul.sse4_1.s

# target to generate assembly for a file
matmul.sse4_1.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/matmul.sse4_1.cpp.s
.PHONY : matmul.sse4_1.cpp.s

mean.avx2.obj: mean.avx2.cpp.obj

.PHONY : mean.avx2.obj

# target to build an object file
mean.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/mean.avx2.cpp.obj
.PHONY : mean.avx2.cpp.obj

mean.avx2.i: mean.avx2.cpp.i

.PHONY : mean.avx2.i

# target to preprocess a source file
mean.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/mean.avx2.cpp.i
.PHONY : mean.avx2.cpp.i

mean.avx2.s: mean.avx2.cpp.s

.PHONY : mean.avx2.s

# target to generate assembly for a file
mean.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/mean.avx2.cpp.s
.PHONY : mean.avx2.cpp.s

merge.avx2.obj: merge.avx2.cpp.obj

.PHONY : merge.avx2.obj

# target to build an object file
merge.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/merge.avx2.cpp.obj
.PHONY : merge.avx2.cpp.obj

merge.avx2.i: merge.avx2.cpp.i

.PHONY : merge.avx2.i

# target to preprocess a source file
merge.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/merge.avx2.cpp.i
.PHONY : merge.avx2.cpp.i

merge.avx2.s: merge.avx2.cpp.s

.PHONY : merge.avx2.s

# target to generate assembly for a file
merge.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/merge.avx2.cpp.s
.PHONY : merge.avx2.cpp.s

opencl_kernels_core.obj: opencl_kernels_core.cpp.obj

.PHONY : opencl_kernels_core.obj

# target to build an object file
opencl_kernels_core.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/opencl_kernels_core.cpp.obj
.PHONY : opencl_kernels_core.cpp.obj

opencl_kernels_core.i: opencl_kernels_core.cpp.i

.PHONY : opencl_kernels_core.i

# target to preprocess a source file
opencl_kernels_core.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/opencl_kernels_core.cpp.i
.PHONY : opencl_kernels_core.cpp.i

opencl_kernels_core.s: opencl_kernels_core.cpp.s

.PHONY : opencl_kernels_core.s

# target to generate assembly for a file
opencl_kernels_core.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/opencl_kernels_core.cpp.s
.PHONY : opencl_kernels_core.cpp.s

split.avx2.obj: split.avx2.cpp.obj

.PHONY : split.avx2.obj

# target to build an object file
split.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/split.avx2.cpp.obj
.PHONY : split.avx2.cpp.obj

split.avx2.i: split.avx2.cpp.i

.PHONY : split.avx2.i

# target to preprocess a source file
split.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/split.avx2.cpp.i
.PHONY : split.avx2.cpp.i

split.avx2.s: split.avx2.cpp.s

.PHONY : split.avx2.s

# target to generate assembly for a file
split.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/split.avx2.cpp.s
.PHONY : split.avx2.cpp.s

src/algorithm.obj: src/algorithm.cpp.obj

.PHONY : src/algorithm.obj

# target to build an object file
src/algorithm.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/algorithm.cpp.obj
.PHONY : src/algorithm.cpp.obj

src/algorithm.i: src/algorithm.cpp.i

.PHONY : src/algorithm.i

# target to preprocess a source file
src/algorithm.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/algorithm.cpp.i
.PHONY : src/algorithm.cpp.i

src/algorithm.s: src/algorithm.cpp.s

.PHONY : src/algorithm.s

# target to generate assembly for a file
src/algorithm.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/algorithm.cpp.s
.PHONY : src/algorithm.cpp.s

src/alloc.obj: src/alloc.cpp.obj

.PHONY : src/alloc.obj

# target to build an object file
src/alloc.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/alloc.cpp.obj
.PHONY : src/alloc.cpp.obj

src/alloc.i: src/alloc.cpp.i

.PHONY : src/alloc.i

# target to preprocess a source file
src/alloc.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/alloc.cpp.i
.PHONY : src/alloc.cpp.i

src/alloc.s: src/alloc.cpp.s

.PHONY : src/alloc.s

# target to generate assembly for a file
src/alloc.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/alloc.cpp.s
.PHONY : src/alloc.cpp.s

src/arithm.obj: src/arithm.cpp.obj

.PHONY : src/arithm.obj

# target to build an object file
src/arithm.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/arithm.cpp.obj
.PHONY : src/arithm.cpp.obj

src/arithm.i: src/arithm.cpp.i

.PHONY : src/arithm.i

# target to preprocess a source file
src/arithm.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/arithm.cpp.i
.PHONY : src/arithm.cpp.i

src/arithm.s: src/arithm.cpp.s

.PHONY : src/arithm.s

# target to generate assembly for a file
src/arithm.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/arithm.cpp.s
.PHONY : src/arithm.cpp.s

src/arithm.dispatch.obj: src/arithm.dispatch.cpp.obj

.PHONY : src/arithm.dispatch.obj

# target to build an object file
src/arithm.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/arithm.dispatch.cpp.obj
.PHONY : src/arithm.dispatch.cpp.obj

src/arithm.dispatch.i: src/arithm.dispatch.cpp.i

.PHONY : src/arithm.dispatch.i

# target to preprocess a source file
src/arithm.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/arithm.dispatch.cpp.i
.PHONY : src/arithm.dispatch.cpp.i

src/arithm.dispatch.s: src/arithm.dispatch.cpp.s

.PHONY : src/arithm.dispatch.s

# target to generate assembly for a file
src/arithm.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/arithm.dispatch.cpp.s
.PHONY : src/arithm.dispatch.cpp.s

src/array.obj: src/array.cpp.obj

.PHONY : src/array.obj

# target to build an object file
src/array.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/array.cpp.obj
.PHONY : src/array.cpp.obj

src/array.i: src/array.cpp.i

.PHONY : src/array.i

# target to preprocess a source file
src/array.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/array.cpp.i
.PHONY : src/array.cpp.i

src/array.s: src/array.cpp.s

.PHONY : src/array.s

# target to generate assembly for a file
src/array.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/array.cpp.s
.PHONY : src/array.cpp.s

src/async.obj: src/async.cpp.obj

.PHONY : src/async.obj

# target to build an object file
src/async.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/async.cpp.obj
.PHONY : src/async.cpp.obj

src/async.i: src/async.cpp.i

.PHONY : src/async.i

# target to preprocess a source file
src/async.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/async.cpp.i
.PHONY : src/async.cpp.i

src/async.s: src/async.cpp.s

.PHONY : src/async.s

# target to generate assembly for a file
src/async.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/async.cpp.s
.PHONY : src/async.cpp.s

src/batch_distance.obj: src/batch_distance.cpp.obj

.PHONY : src/batch_distance.obj

# target to build an object file
src/batch_distance.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/batch_distance.cpp.obj
.PHONY : src/batch_distance.cpp.obj

src/batch_distance.i: src/batch_distance.cpp.i

.PHONY : src/batch_distance.i

# target to preprocess a source file
src/batch_distance.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/batch_distance.cpp.i
.PHONY : src/batch_distance.cpp.i

src/batch_distance.s: src/batch_distance.cpp.s

.PHONY : src/batch_distance.s

# target to generate assembly for a file
src/batch_distance.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/batch_distance.cpp.s
.PHONY : src/batch_distance.cpp.s

src/bindings_utils.obj: src/bindings_utils.cpp.obj

.PHONY : src/bindings_utils.obj

# target to build an object file
src/bindings_utils.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/bindings_utils.cpp.obj
.PHONY : src/bindings_utils.cpp.obj

src/bindings_utils.i: src/bindings_utils.cpp.i

.PHONY : src/bindings_utils.i

# target to preprocess a source file
src/bindings_utils.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/bindings_utils.cpp.i
.PHONY : src/bindings_utils.cpp.i

src/bindings_utils.s: src/bindings_utils.cpp.s

.PHONY : src/bindings_utils.s

# target to generate assembly for a file
src/bindings_utils.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/bindings_utils.cpp.s
.PHONY : src/bindings_utils.cpp.s

src/channels.obj: src/channels.cpp.obj

.PHONY : src/channels.obj

# target to build an object file
src/channels.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/channels.cpp.obj
.PHONY : src/channels.cpp.obj

src/channels.i: src/channels.cpp.i

.PHONY : src/channels.i

# target to preprocess a source file
src/channels.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/channels.cpp.i
.PHONY : src/channels.cpp.i

src/channels.s: src/channels.cpp.s

.PHONY : src/channels.s

# target to generate assembly for a file
src/channels.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/channels.cpp.s
.PHONY : src/channels.cpp.s

src/check.obj: src/check.cpp.obj

.PHONY : src/check.obj

# target to build an object file
src/check.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/check.cpp.obj
.PHONY : src/check.cpp.obj

src/check.i: src/check.cpp.i

.PHONY : src/check.i

# target to preprocess a source file
src/check.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/check.cpp.i
.PHONY : src/check.cpp.i

src/check.s: src/check.cpp.s

.PHONY : src/check.s

# target to generate assembly for a file
src/check.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/check.cpp.s
.PHONY : src/check.cpp.s

src/command_line_parser.obj: src/command_line_parser.cpp.obj

.PHONY : src/command_line_parser.obj

# target to build an object file
src/command_line_parser.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/command_line_parser.cpp.obj
.PHONY : src/command_line_parser.cpp.obj

src/command_line_parser.i: src/command_line_parser.cpp.i

.PHONY : src/command_line_parser.i

# target to preprocess a source file
src/command_line_parser.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/command_line_parser.cpp.i
.PHONY : src/command_line_parser.cpp.i

src/command_line_parser.s: src/command_line_parser.cpp.s

.PHONY : src/command_line_parser.s

# target to generate assembly for a file
src/command_line_parser.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/command_line_parser.cpp.s
.PHONY : src/command_line_parser.cpp.s

src/conjugate_gradient.obj: src/conjugate_gradient.cpp.obj

.PHONY : src/conjugate_gradient.obj

# target to build an object file
src/conjugate_gradient.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/conjugate_gradient.cpp.obj
.PHONY : src/conjugate_gradient.cpp.obj

src/conjugate_gradient.i: src/conjugate_gradient.cpp.i

.PHONY : src/conjugate_gradient.i

# target to preprocess a source file
src/conjugate_gradient.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/conjugate_gradient.cpp.i
.PHONY : src/conjugate_gradient.cpp.i

src/conjugate_gradient.s: src/conjugate_gradient.cpp.s

.PHONY : src/conjugate_gradient.s

# target to generate assembly for a file
src/conjugate_gradient.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/conjugate_gradient.cpp.s
.PHONY : src/conjugate_gradient.cpp.s

src/convert.dispatch.obj: src/convert.dispatch.cpp.obj

.PHONY : src/convert.dispatch.obj

# target to build an object file
src/convert.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/convert.dispatch.cpp.obj
.PHONY : src/convert.dispatch.cpp.obj

src/convert.dispatch.i: src/convert.dispatch.cpp.i

.PHONY : src/convert.dispatch.i

# target to preprocess a source file
src/convert.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/convert.dispatch.cpp.i
.PHONY : src/convert.dispatch.cpp.i

src/convert.dispatch.s: src/convert.dispatch.cpp.s

.PHONY : src/convert.dispatch.s

# target to generate assembly for a file
src/convert.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/convert.dispatch.cpp.s
.PHONY : src/convert.dispatch.cpp.s

src/convert_c.obj: src/convert_c.cpp.obj

.PHONY : src/convert_c.obj

# target to build an object file
src/convert_c.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/convert_c.cpp.obj
.PHONY : src/convert_c.cpp.obj

src/convert_c.i: src/convert_c.cpp.i

.PHONY : src/convert_c.i

# target to preprocess a source file
src/convert_c.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/convert_c.cpp.i
.PHONY : src/convert_c.cpp.i

src/convert_c.s: src/convert_c.cpp.s

.PHONY : src/convert_c.s

# target to generate assembly for a file
src/convert_c.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/convert_c.cpp.s
.PHONY : src/convert_c.cpp.s

src/convert_scale.dispatch.obj: src/convert_scale.dispatch.cpp.obj

.PHONY : src/convert_scale.dispatch.obj

# target to build an object file
src/convert_scale.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/convert_scale.dispatch.cpp.obj
.PHONY : src/convert_scale.dispatch.cpp.obj

src/convert_scale.dispatch.i: src/convert_scale.dispatch.cpp.i

.PHONY : src/convert_scale.dispatch.i

# target to preprocess a source file
src/convert_scale.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/convert_scale.dispatch.cpp.i
.PHONY : src/convert_scale.dispatch.cpp.i

src/convert_scale.dispatch.s: src/convert_scale.dispatch.cpp.s

.PHONY : src/convert_scale.dispatch.s

# target to generate assembly for a file
src/convert_scale.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/convert_scale.dispatch.cpp.s
.PHONY : src/convert_scale.dispatch.cpp.s

src/copy.obj: src/copy.cpp.obj

.PHONY : src/copy.obj

# target to build an object file
src/copy.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/copy.cpp.obj
.PHONY : src/copy.cpp.obj

src/copy.i: src/copy.cpp.i

.PHONY : src/copy.i

# target to preprocess a source file
src/copy.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/copy.cpp.i
.PHONY : src/copy.cpp.i

src/copy.s: src/copy.cpp.s

.PHONY : src/copy.s

# target to generate assembly for a file
src/copy.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/copy.cpp.s
.PHONY : src/copy.cpp.s

src/count_non_zero.dispatch.obj: src/count_non_zero.dispatch.cpp.obj

.PHONY : src/count_non_zero.dispatch.obj

# target to build an object file
src/count_non_zero.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/count_non_zero.dispatch.cpp.obj
.PHONY : src/count_non_zero.dispatch.cpp.obj

src/count_non_zero.dispatch.i: src/count_non_zero.dispatch.cpp.i

.PHONY : src/count_non_zero.dispatch.i

# target to preprocess a source file
src/count_non_zero.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/count_non_zero.dispatch.cpp.i
.PHONY : src/count_non_zero.dispatch.cpp.i

src/count_non_zero.dispatch.s: src/count_non_zero.dispatch.cpp.s

.PHONY : src/count_non_zero.dispatch.s

# target to generate assembly for a file
src/count_non_zero.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/count_non_zero.dispatch.cpp.s
.PHONY : src/count_non_zero.dispatch.cpp.s

src/cuda_gpu_mat.obj: src/cuda_gpu_mat.cpp.obj

.PHONY : src/cuda_gpu_mat.obj

# target to build an object file
src/cuda_gpu_mat.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/cuda_gpu_mat.cpp.obj
.PHONY : src/cuda_gpu_mat.cpp.obj

src/cuda_gpu_mat.i: src/cuda_gpu_mat.cpp.i

.PHONY : src/cuda_gpu_mat.i

# target to preprocess a source file
src/cuda_gpu_mat.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/cuda_gpu_mat.cpp.i
.PHONY : src/cuda_gpu_mat.cpp.i

src/cuda_gpu_mat.s: src/cuda_gpu_mat.cpp.s

.PHONY : src/cuda_gpu_mat.s

# target to generate assembly for a file
src/cuda_gpu_mat.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/cuda_gpu_mat.cpp.s
.PHONY : src/cuda_gpu_mat.cpp.s

src/cuda_host_mem.obj: src/cuda_host_mem.cpp.obj

.PHONY : src/cuda_host_mem.obj

# target to build an object file
src/cuda_host_mem.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/cuda_host_mem.cpp.obj
.PHONY : src/cuda_host_mem.cpp.obj

src/cuda_host_mem.i: src/cuda_host_mem.cpp.i

.PHONY : src/cuda_host_mem.i

# target to preprocess a source file
src/cuda_host_mem.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/cuda_host_mem.cpp.i
.PHONY : src/cuda_host_mem.cpp.i

src/cuda_host_mem.s: src/cuda_host_mem.cpp.s

.PHONY : src/cuda_host_mem.s

# target to generate assembly for a file
src/cuda_host_mem.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/cuda_host_mem.cpp.s
.PHONY : src/cuda_host_mem.cpp.s

src/cuda_info.obj: src/cuda_info.cpp.obj

.PHONY : src/cuda_info.obj

# target to build an object file
src/cuda_info.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/cuda_info.cpp.obj
.PHONY : src/cuda_info.cpp.obj

src/cuda_info.i: src/cuda_info.cpp.i

.PHONY : src/cuda_info.i

# target to preprocess a source file
src/cuda_info.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/cuda_info.cpp.i
.PHONY : src/cuda_info.cpp.i

src/cuda_info.s: src/cuda_info.cpp.s

.PHONY : src/cuda_info.s

# target to generate assembly for a file
src/cuda_info.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/cuda_info.cpp.s
.PHONY : src/cuda_info.cpp.s

src/cuda_stream.obj: src/cuda_stream.cpp.obj

.PHONY : src/cuda_stream.obj

# target to build an object file
src/cuda_stream.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/cuda_stream.cpp.obj
.PHONY : src/cuda_stream.cpp.obj

src/cuda_stream.i: src/cuda_stream.cpp.i

.PHONY : src/cuda_stream.i

# target to preprocess a source file
src/cuda_stream.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/cuda_stream.cpp.i
.PHONY : src/cuda_stream.cpp.i

src/cuda_stream.s: src/cuda_stream.cpp.s

.PHONY : src/cuda_stream.s

# target to generate assembly for a file
src/cuda_stream.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/cuda_stream.cpp.s
.PHONY : src/cuda_stream.cpp.s

src/datastructs.obj: src/datastructs.cpp.obj

.PHONY : src/datastructs.obj

# target to build an object file
src/datastructs.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/datastructs.cpp.obj
.PHONY : src/datastructs.cpp.obj

src/datastructs.i: src/datastructs.cpp.i

.PHONY : src/datastructs.i

# target to preprocess a source file
src/datastructs.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/datastructs.cpp.i
.PHONY : src/datastructs.cpp.i

src/datastructs.s: src/datastructs.cpp.s

.PHONY : src/datastructs.s

# target to generate assembly for a file
src/datastructs.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/datastructs.cpp.s
.PHONY : src/datastructs.cpp.s

src/directx.obj: src/directx.cpp.obj

.PHONY : src/directx.obj

# target to build an object file
src/directx.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/directx.cpp.obj
.PHONY : src/directx.cpp.obj

src/directx.i: src/directx.cpp.i

.PHONY : src/directx.i

# target to preprocess a source file
src/directx.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/directx.cpp.i
.PHONY : src/directx.cpp.i

src/directx.s: src/directx.cpp.s

.PHONY : src/directx.s

# target to generate assembly for a file
src/directx.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/directx.cpp.s
.PHONY : src/directx.cpp.s

src/downhill_simplex.obj: src/downhill_simplex.cpp.obj

.PHONY : src/downhill_simplex.obj

# target to build an object file
src/downhill_simplex.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/downhill_simplex.cpp.obj
.PHONY : src/downhill_simplex.cpp.obj

src/downhill_simplex.i: src/downhill_simplex.cpp.i

.PHONY : src/downhill_simplex.i

# target to preprocess a source file
src/downhill_simplex.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/downhill_simplex.cpp.i
.PHONY : src/downhill_simplex.cpp.i

src/downhill_simplex.s: src/downhill_simplex.cpp.s

.PHONY : src/downhill_simplex.s

# target to generate assembly for a file
src/downhill_simplex.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/downhill_simplex.cpp.s
.PHONY : src/downhill_simplex.cpp.s

src/dxt.obj: src/dxt.cpp.obj

.PHONY : src/dxt.obj

# target to build an object file
src/dxt.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/dxt.cpp.obj
.PHONY : src/dxt.cpp.obj

src/dxt.i: src/dxt.cpp.i

.PHONY : src/dxt.i

# target to preprocess a source file
src/dxt.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/dxt.cpp.i
.PHONY : src/dxt.cpp.i

src/dxt.s: src/dxt.cpp.s

.PHONY : src/dxt.s

# target to generate assembly for a file
src/dxt.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/dxt.cpp.s
.PHONY : src/dxt.cpp.s

src/gl_core_3_1.obj: src/gl_core_3_1.cpp.obj

.PHONY : src/gl_core_3_1.obj

# target to build an object file
src/gl_core_3_1.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/gl_core_3_1.cpp.obj
.PHONY : src/gl_core_3_1.cpp.obj

src/gl_core_3_1.i: src/gl_core_3_1.cpp.i

.PHONY : src/gl_core_3_1.i

# target to preprocess a source file
src/gl_core_3_1.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/gl_core_3_1.cpp.i
.PHONY : src/gl_core_3_1.cpp.i

src/gl_core_3_1.s: src/gl_core_3_1.cpp.s

.PHONY : src/gl_core_3_1.s

# target to generate assembly for a file
src/gl_core_3_1.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/gl_core_3_1.cpp.s
.PHONY : src/gl_core_3_1.cpp.s

src/glob.obj: src/glob.cpp.obj

.PHONY : src/glob.obj

# target to build an object file
src/glob.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/glob.cpp.obj
.PHONY : src/glob.cpp.obj

src/glob.i: src/glob.cpp.i

.PHONY : src/glob.i

# target to preprocess a source file
src/glob.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/glob.cpp.i
.PHONY : src/glob.cpp.i

src/glob.s: src/glob.cpp.s

.PHONY : src/glob.s

# target to generate assembly for a file
src/glob.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/glob.cpp.s
.PHONY : src/glob.cpp.s

src/hal_internal.obj: src/hal_internal.cpp.obj

.PHONY : src/hal_internal.obj

# target to build an object file
src/hal_internal.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/hal_internal.cpp.obj
.PHONY : src/hal_internal.cpp.obj

src/hal_internal.i: src/hal_internal.cpp.i

.PHONY : src/hal_internal.i

# target to preprocess a source file
src/hal_internal.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/hal_internal.cpp.i
.PHONY : src/hal_internal.cpp.i

src/hal_internal.s: src/hal_internal.cpp.s

.PHONY : src/hal_internal.s

# target to generate assembly for a file
src/hal_internal.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/hal_internal.cpp.s
.PHONY : src/hal_internal.cpp.s

src/kmeans.obj: src/kmeans.cpp.obj

.PHONY : src/kmeans.obj

# target to build an object file
src/kmeans.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/kmeans.cpp.obj
.PHONY : src/kmeans.cpp.obj

src/kmeans.i: src/kmeans.cpp.i

.PHONY : src/kmeans.i

# target to preprocess a source file
src/kmeans.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/kmeans.cpp.i
.PHONY : src/kmeans.cpp.i

src/kmeans.s: src/kmeans.cpp.s

.PHONY : src/kmeans.s

# target to generate assembly for a file
src/kmeans.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/kmeans.cpp.s
.PHONY : src/kmeans.cpp.s

src/lapack.obj: src/lapack.cpp.obj

.PHONY : src/lapack.obj

# target to build an object file
src/lapack.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/lapack.cpp.obj
.PHONY : src/lapack.cpp.obj

src/lapack.i: src/lapack.cpp.i

.PHONY : src/lapack.i

# target to preprocess a source file
src/lapack.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/lapack.cpp.i
.PHONY : src/lapack.cpp.i

src/lapack.s: src/lapack.cpp.s

.PHONY : src/lapack.s

# target to generate assembly for a file
src/lapack.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/lapack.cpp.s
.PHONY : src/lapack.cpp.s

src/lda.obj: src/lda.cpp.obj

.PHONY : src/lda.obj

# target to build an object file
src/lda.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/lda.cpp.obj
.PHONY : src/lda.cpp.obj

src/lda.i: src/lda.cpp.i

.PHONY : src/lda.i

# target to preprocess a source file
src/lda.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/lda.cpp.i
.PHONY : src/lda.cpp.i

src/lda.s: src/lda.cpp.s

.PHONY : src/lda.s

# target to generate assembly for a file
src/lda.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/lda.cpp.s
.PHONY : src/lda.cpp.s

src/logger.obj: src/logger.cpp.obj

.PHONY : src/logger.obj

# target to build an object file
src/logger.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/logger.cpp.obj
.PHONY : src/logger.cpp.obj

src/logger.i: src/logger.cpp.i

.PHONY : src/logger.i

# target to preprocess a source file
src/logger.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/logger.cpp.i
.PHONY : src/logger.cpp.i

src/logger.s: src/logger.cpp.s

.PHONY : src/logger.s

# target to generate assembly for a file
src/logger.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/logger.cpp.s
.PHONY : src/logger.cpp.s

src/lpsolver.obj: src/lpsolver.cpp.obj

.PHONY : src/lpsolver.obj

# target to build an object file
src/lpsolver.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/lpsolver.cpp.obj
.PHONY : src/lpsolver.cpp.obj

src/lpsolver.i: src/lpsolver.cpp.i

.PHONY : src/lpsolver.i

# target to preprocess a source file
src/lpsolver.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/lpsolver.cpp.i
.PHONY : src/lpsolver.cpp.i

src/lpsolver.s: src/lpsolver.cpp.s

.PHONY : src/lpsolver.s

# target to generate assembly for a file
src/lpsolver.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/lpsolver.cpp.s
.PHONY : src/lpsolver.cpp.s

src/lut.obj: src/lut.cpp.obj

.PHONY : src/lut.obj

# target to build an object file
src/lut.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/lut.cpp.obj
.PHONY : src/lut.cpp.obj

src/lut.i: src/lut.cpp.i

.PHONY : src/lut.i

# target to preprocess a source file
src/lut.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/lut.cpp.i
.PHONY : src/lut.cpp.i

src/lut.s: src/lut.cpp.s

.PHONY : src/lut.s

# target to generate assembly for a file
src/lut.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/lut.cpp.s
.PHONY : src/lut.cpp.s

src/mathfuncs.obj: src/mathfuncs.cpp.obj

.PHONY : src/mathfuncs.obj

# target to build an object file
src/mathfuncs.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/mathfuncs.cpp.obj
.PHONY : src/mathfuncs.cpp.obj

src/mathfuncs.i: src/mathfuncs.cpp.i

.PHONY : src/mathfuncs.i

# target to preprocess a source file
src/mathfuncs.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/mathfuncs.cpp.i
.PHONY : src/mathfuncs.cpp.i

src/mathfuncs.s: src/mathfuncs.cpp.s

.PHONY : src/mathfuncs.s

# target to generate assembly for a file
src/mathfuncs.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/mathfuncs.cpp.s
.PHONY : src/mathfuncs.cpp.s

src/mathfuncs_core.dispatch.obj: src/mathfuncs_core.dispatch.cpp.obj

.PHONY : src/mathfuncs_core.dispatch.obj

# target to build an object file
src/mathfuncs_core.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/mathfuncs_core.dispatch.cpp.obj
.PHONY : src/mathfuncs_core.dispatch.cpp.obj

src/mathfuncs_core.dispatch.i: src/mathfuncs_core.dispatch.cpp.i

.PHONY : src/mathfuncs_core.dispatch.i

# target to preprocess a source file
src/mathfuncs_core.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/mathfuncs_core.dispatch.cpp.i
.PHONY : src/mathfuncs_core.dispatch.cpp.i

src/mathfuncs_core.dispatch.s: src/mathfuncs_core.dispatch.cpp.s

.PHONY : src/mathfuncs_core.dispatch.s

# target to generate assembly for a file
src/mathfuncs_core.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/mathfuncs_core.dispatch.cpp.s
.PHONY : src/mathfuncs_core.dispatch.cpp.s

src/matmul.dispatch.obj: src/matmul.dispatch.cpp.obj

.PHONY : src/matmul.dispatch.obj

# target to build an object file
src/matmul.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matmul.dispatch.cpp.obj
.PHONY : src/matmul.dispatch.cpp.obj

src/matmul.dispatch.i: src/matmul.dispatch.cpp.i

.PHONY : src/matmul.dispatch.i

# target to preprocess a source file
src/matmul.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matmul.dispatch.cpp.i
.PHONY : src/matmul.dispatch.cpp.i

src/matmul.dispatch.s: src/matmul.dispatch.cpp.s

.PHONY : src/matmul.dispatch.s

# target to generate assembly for a file
src/matmul.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matmul.dispatch.cpp.s
.PHONY : src/matmul.dispatch.cpp.s

src/matrix.obj: src/matrix.cpp.obj

.PHONY : src/matrix.obj

# target to build an object file
src/matrix.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix.cpp.obj
.PHONY : src/matrix.cpp.obj

src/matrix.i: src/matrix.cpp.i

.PHONY : src/matrix.i

# target to preprocess a source file
src/matrix.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix.cpp.i
.PHONY : src/matrix.cpp.i

src/matrix.s: src/matrix.cpp.s

.PHONY : src/matrix.s

# target to generate assembly for a file
src/matrix.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix.cpp.s
.PHONY : src/matrix.cpp.s

src/matrix_c.obj: src/matrix_c.cpp.obj

.PHONY : src/matrix_c.obj

# target to build an object file
src/matrix_c.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_c.cpp.obj
.PHONY : src/matrix_c.cpp.obj

src/matrix_c.i: src/matrix_c.cpp.i

.PHONY : src/matrix_c.i

# target to preprocess a source file
src/matrix_c.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_c.cpp.i
.PHONY : src/matrix_c.cpp.i

src/matrix_c.s: src/matrix_c.cpp.s

.PHONY : src/matrix_c.s

# target to generate assembly for a file
src/matrix_c.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_c.cpp.s
.PHONY : src/matrix_c.cpp.s

src/matrix_decomp.obj: src/matrix_decomp.cpp.obj

.PHONY : src/matrix_decomp.obj

# target to build an object file
src/matrix_decomp.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_decomp.cpp.obj
.PHONY : src/matrix_decomp.cpp.obj

src/matrix_decomp.i: src/matrix_decomp.cpp.i

.PHONY : src/matrix_decomp.i

# target to preprocess a source file
src/matrix_decomp.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_decomp.cpp.i
.PHONY : src/matrix_decomp.cpp.i

src/matrix_decomp.s: src/matrix_decomp.cpp.s

.PHONY : src/matrix_decomp.s

# target to generate assembly for a file
src/matrix_decomp.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_decomp.cpp.s
.PHONY : src/matrix_decomp.cpp.s

src/matrix_expressions.obj: src/matrix_expressions.cpp.obj

.PHONY : src/matrix_expressions.obj

# target to build an object file
src/matrix_expressions.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_expressions.cpp.obj
.PHONY : src/matrix_expressions.cpp.obj

src/matrix_expressions.i: src/matrix_expressions.cpp.i

.PHONY : src/matrix_expressions.i

# target to preprocess a source file
src/matrix_expressions.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_expressions.cpp.i
.PHONY : src/matrix_expressions.cpp.i

src/matrix_expressions.s: src/matrix_expressions.cpp.s

.PHONY : src/matrix_expressions.s

# target to generate assembly for a file
src/matrix_expressions.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_expressions.cpp.s
.PHONY : src/matrix_expressions.cpp.s

src/matrix_iterator.obj: src/matrix_iterator.cpp.obj

.PHONY : src/matrix_iterator.obj

# target to build an object file
src/matrix_iterator.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_iterator.cpp.obj
.PHONY : src/matrix_iterator.cpp.obj

src/matrix_iterator.i: src/matrix_iterator.cpp.i

.PHONY : src/matrix_iterator.i

# target to preprocess a source file
src/matrix_iterator.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_iterator.cpp.i
.PHONY : src/matrix_iterator.cpp.i

src/matrix_iterator.s: src/matrix_iterator.cpp.s

.PHONY : src/matrix_iterator.s

# target to generate assembly for a file
src/matrix_iterator.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_iterator.cpp.s
.PHONY : src/matrix_iterator.cpp.s

src/matrix_operations.obj: src/matrix_operations.cpp.obj

.PHONY : src/matrix_operations.obj

# target to build an object file
src/matrix_operations.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_operations.cpp.obj
.PHONY : src/matrix_operations.cpp.obj

src/matrix_operations.i: src/matrix_operations.cpp.i

.PHONY : src/matrix_operations.i

# target to preprocess a source file
src/matrix_operations.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_operations.cpp.i
.PHONY : src/matrix_operations.cpp.i

src/matrix_operations.s: src/matrix_operations.cpp.s

.PHONY : src/matrix_operations.s

# target to generate assembly for a file
src/matrix_operations.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_operations.cpp.s
.PHONY : src/matrix_operations.cpp.s

src/matrix_sparse.obj: src/matrix_sparse.cpp.obj

.PHONY : src/matrix_sparse.obj

# target to build an object file
src/matrix_sparse.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_sparse.cpp.obj
.PHONY : src/matrix_sparse.cpp.obj

src/matrix_sparse.i: src/matrix_sparse.cpp.i

.PHONY : src/matrix_sparse.i

# target to preprocess a source file
src/matrix_sparse.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_sparse.cpp.i
.PHONY : src/matrix_sparse.cpp.i

src/matrix_sparse.s: src/matrix_sparse.cpp.s

.PHONY : src/matrix_sparse.s

# target to generate assembly for a file
src/matrix_sparse.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_sparse.cpp.s
.PHONY : src/matrix_sparse.cpp.s

src/matrix_wrap.obj: src/matrix_wrap.cpp.obj

.PHONY : src/matrix_wrap.obj

# target to build an object file
src/matrix_wrap.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_wrap.cpp.obj
.PHONY : src/matrix_wrap.cpp.obj

src/matrix_wrap.i: src/matrix_wrap.cpp.i

.PHONY : src/matrix_wrap.i

# target to preprocess a source file
src/matrix_wrap.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_wrap.cpp.i
.PHONY : src/matrix_wrap.cpp.i

src/matrix_wrap.s: src/matrix_wrap.cpp.s

.PHONY : src/matrix_wrap.s

# target to generate assembly for a file
src/matrix_wrap.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/matrix_wrap.cpp.s
.PHONY : src/matrix_wrap.cpp.s

src/mean.dispatch.obj: src/mean.dispatch.cpp.obj

.PHONY : src/mean.dispatch.obj

# target to build an object file
src/mean.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/mean.dispatch.cpp.obj
.PHONY : src/mean.dispatch.cpp.obj

src/mean.dispatch.i: src/mean.dispatch.cpp.i

.PHONY : src/mean.dispatch.i

# target to preprocess a source file
src/mean.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/mean.dispatch.cpp.i
.PHONY : src/mean.dispatch.cpp.i

src/mean.dispatch.s: src/mean.dispatch.cpp.s

.PHONY : src/mean.dispatch.s

# target to generate assembly for a file
src/mean.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/mean.dispatch.cpp.s
.PHONY : src/mean.dispatch.cpp.s

src/merge.dispatch.obj: src/merge.dispatch.cpp.obj

.PHONY : src/merge.dispatch.obj

# target to build an object file
src/merge.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/merge.dispatch.cpp.obj
.PHONY : src/merge.dispatch.cpp.obj

src/merge.dispatch.i: src/merge.dispatch.cpp.i

.PHONY : src/merge.dispatch.i

# target to preprocess a source file
src/merge.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/merge.dispatch.cpp.i
.PHONY : src/merge.dispatch.cpp.i

src/merge.dispatch.s: src/merge.dispatch.cpp.s

.PHONY : src/merge.dispatch.s

# target to generate assembly for a file
src/merge.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/merge.dispatch.cpp.s
.PHONY : src/merge.dispatch.cpp.s

src/minmax.obj: src/minmax.cpp.obj

.PHONY : src/minmax.obj

# target to build an object file
src/minmax.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/minmax.cpp.obj
.PHONY : src/minmax.cpp.obj

src/minmax.i: src/minmax.cpp.i

.PHONY : src/minmax.i

# target to preprocess a source file
src/minmax.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/minmax.cpp.i
.PHONY : src/minmax.cpp.i

src/minmax.s: src/minmax.cpp.s

.PHONY : src/minmax.s

# target to generate assembly for a file
src/minmax.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/minmax.cpp.s
.PHONY : src/minmax.cpp.s

src/norm.obj: src/norm.cpp.obj

.PHONY : src/norm.obj

# target to build an object file
src/norm.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/norm.cpp.obj
.PHONY : src/norm.cpp.obj

src/norm.i: src/norm.cpp.i

.PHONY : src/norm.i

# target to preprocess a source file
src/norm.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/norm.cpp.i
.PHONY : src/norm.cpp.i

src/norm.s: src/norm.cpp.s

.PHONY : src/norm.s

# target to generate assembly for a file
src/norm.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/norm.cpp.s
.PHONY : src/norm.cpp.s

src/ocl.obj: src/ocl.cpp.obj

.PHONY : src/ocl.obj

# target to build an object file
src/ocl.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/ocl.cpp.obj
.PHONY : src/ocl.cpp.obj

src/ocl.i: src/ocl.cpp.i

.PHONY : src/ocl.i

# target to preprocess a source file
src/ocl.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/ocl.cpp.i
.PHONY : src/ocl.cpp.i

src/ocl.s: src/ocl.cpp.s

.PHONY : src/ocl.s

# target to generate assembly for a file
src/ocl.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/ocl.cpp.s
.PHONY : src/ocl.cpp.s

src/opencl/runtime/opencl_clamdblas.obj: src/opencl/runtime/opencl_clamdblas.cpp.obj

.PHONY : src/opencl/runtime/opencl_clamdblas.obj

# target to build an object file
src/opencl/runtime/opencl_clamdblas.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/opencl/runtime/opencl_clamdblas.cpp.obj
.PHONY : src/opencl/runtime/opencl_clamdblas.cpp.obj

src/opencl/runtime/opencl_clamdblas.i: src/opencl/runtime/opencl_clamdblas.cpp.i

.PHONY : src/opencl/runtime/opencl_clamdblas.i

# target to preprocess a source file
src/opencl/runtime/opencl_clamdblas.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/opencl/runtime/opencl_clamdblas.cpp.i
.PHONY : src/opencl/runtime/opencl_clamdblas.cpp.i

src/opencl/runtime/opencl_clamdblas.s: src/opencl/runtime/opencl_clamdblas.cpp.s

.PHONY : src/opencl/runtime/opencl_clamdblas.s

# target to generate assembly for a file
src/opencl/runtime/opencl_clamdblas.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/opencl/runtime/opencl_clamdblas.cpp.s
.PHONY : src/opencl/runtime/opencl_clamdblas.cpp.s

src/opencl/runtime/opencl_clamdfft.obj: src/opencl/runtime/opencl_clamdfft.cpp.obj

.PHONY : src/opencl/runtime/opencl_clamdfft.obj

# target to build an object file
src/opencl/runtime/opencl_clamdfft.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/opencl/runtime/opencl_clamdfft.cpp.obj
.PHONY : src/opencl/runtime/opencl_clamdfft.cpp.obj

src/opencl/runtime/opencl_clamdfft.i: src/opencl/runtime/opencl_clamdfft.cpp.i

.PHONY : src/opencl/runtime/opencl_clamdfft.i

# target to preprocess a source file
src/opencl/runtime/opencl_clamdfft.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/opencl/runtime/opencl_clamdfft.cpp.i
.PHONY : src/opencl/runtime/opencl_clamdfft.cpp.i

src/opencl/runtime/opencl_clamdfft.s: src/opencl/runtime/opencl_clamdfft.cpp.s

.PHONY : src/opencl/runtime/opencl_clamdfft.s

# target to generate assembly for a file
src/opencl/runtime/opencl_clamdfft.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/opencl/runtime/opencl_clamdfft.cpp.s
.PHONY : src/opencl/runtime/opencl_clamdfft.cpp.s

src/opencl/runtime/opencl_core.obj: src/opencl/runtime/opencl_core.cpp.obj

.PHONY : src/opencl/runtime/opencl_core.obj

# target to build an object file
src/opencl/runtime/opencl_core.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/opencl/runtime/opencl_core.cpp.obj
.PHONY : src/opencl/runtime/opencl_core.cpp.obj

src/opencl/runtime/opencl_core.i: src/opencl/runtime/opencl_core.cpp.i

.PHONY : src/opencl/runtime/opencl_core.i

# target to preprocess a source file
src/opencl/runtime/opencl_core.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/opencl/runtime/opencl_core.cpp.i
.PHONY : src/opencl/runtime/opencl_core.cpp.i

src/opencl/runtime/opencl_core.s: src/opencl/runtime/opencl_core.cpp.s

.PHONY : src/opencl/runtime/opencl_core.s

# target to generate assembly for a file
src/opencl/runtime/opencl_core.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/opencl/runtime/opencl_core.cpp.s
.PHONY : src/opencl/runtime/opencl_core.cpp.s

src/opengl.obj: src/opengl.cpp.obj

.PHONY : src/opengl.obj

# target to build an object file
src/opengl.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/opengl.cpp.obj
.PHONY : src/opengl.cpp.obj

src/opengl.i: src/opengl.cpp.i

.PHONY : src/opengl.i

# target to preprocess a source file
src/opengl.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/opengl.cpp.i
.PHONY : src/opengl.cpp.i

src/opengl.s: src/opengl.cpp.s

.PHONY : src/opengl.s

# target to generate assembly for a file
src/opengl.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/opengl.cpp.s
.PHONY : src/opengl.cpp.s

src/out.obj: src/out.cpp.obj

.PHONY : src/out.obj

# target to build an object file
src/out.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/out.cpp.obj
.PHONY : src/out.cpp.obj

src/out.i: src/out.cpp.i

.PHONY : src/out.i

# target to preprocess a source file
src/out.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/out.cpp.i
.PHONY : src/out.cpp.i

src/out.s: src/out.cpp.s

.PHONY : src/out.s

# target to generate assembly for a file
src/out.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/out.cpp.s
.PHONY : src/out.cpp.s

src/ovx.obj: src/ovx.cpp.obj

.PHONY : src/ovx.obj

# target to build an object file
src/ovx.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/ovx.cpp.obj
.PHONY : src/ovx.cpp.obj

src/ovx.i: src/ovx.cpp.i

.PHONY : src/ovx.i

# target to preprocess a source file
src/ovx.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/ovx.cpp.i
.PHONY : src/ovx.cpp.i

src/ovx.s: src/ovx.cpp.s

.PHONY : src/ovx.s

# target to generate assembly for a file
src/ovx.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/ovx.cpp.s
.PHONY : src/ovx.cpp.s

src/parallel.obj: src/parallel.cpp.obj

.PHONY : src/parallel.obj

# target to build an object file
src/parallel.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/parallel.cpp.obj
.PHONY : src/parallel.cpp.obj

src/parallel.i: src/parallel.cpp.i

.PHONY : src/parallel.i

# target to preprocess a source file
src/parallel.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/parallel.cpp.i
.PHONY : src/parallel.cpp.i

src/parallel.s: src/parallel.cpp.s

.PHONY : src/parallel.s

# target to generate assembly for a file
src/parallel.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/parallel.cpp.s
.PHONY : src/parallel.cpp.s

src/parallel_impl.obj: src/parallel_impl.cpp.obj

.PHONY : src/parallel_impl.obj

# target to build an object file
src/parallel_impl.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/parallel_impl.cpp.obj
.PHONY : src/parallel_impl.cpp.obj

src/parallel_impl.i: src/parallel_impl.cpp.i

.PHONY : src/parallel_impl.i

# target to preprocess a source file
src/parallel_impl.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/parallel_impl.cpp.i
.PHONY : src/parallel_impl.cpp.i

src/parallel_impl.s: src/parallel_impl.cpp.s

.PHONY : src/parallel_impl.s

# target to generate assembly for a file
src/parallel_impl.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/parallel_impl.cpp.s
.PHONY : src/parallel_impl.cpp.s

src/pca.obj: src/pca.cpp.obj

.PHONY : src/pca.obj

# target to build an object file
src/pca.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/pca.cpp.obj
.PHONY : src/pca.cpp.obj

src/pca.i: src/pca.cpp.i

.PHONY : src/pca.i

# target to preprocess a source file
src/pca.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/pca.cpp.i
.PHONY : src/pca.cpp.i

src/pca.s: src/pca.cpp.s

.PHONY : src/pca.s

# target to generate assembly for a file
src/pca.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/pca.cpp.s
.PHONY : src/pca.cpp.s

src/persistence.obj: src/persistence.cpp.obj

.PHONY : src/persistence.obj

# target to build an object file
src/persistence.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/persistence.cpp.obj
.PHONY : src/persistence.cpp.obj

src/persistence.i: src/persistence.cpp.i

.PHONY : src/persistence.i

# target to preprocess a source file
src/persistence.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/persistence.cpp.i
.PHONY : src/persistence.cpp.i

src/persistence.s: src/persistence.cpp.s

.PHONY : src/persistence.s

# target to generate assembly for a file
src/persistence.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/persistence.cpp.s
.PHONY : src/persistence.cpp.s

src/persistence_json.obj: src/persistence_json.cpp.obj

.PHONY : src/persistence_json.obj

# target to build an object file
src/persistence_json.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/persistence_json.cpp.obj
.PHONY : src/persistence_json.cpp.obj

src/persistence_json.i: src/persistence_json.cpp.i

.PHONY : src/persistence_json.i

# target to preprocess a source file
src/persistence_json.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/persistence_json.cpp.i
.PHONY : src/persistence_json.cpp.i

src/persistence_json.s: src/persistence_json.cpp.s

.PHONY : src/persistence_json.s

# target to generate assembly for a file
src/persistence_json.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/persistence_json.cpp.s
.PHONY : src/persistence_json.cpp.s

src/persistence_types.obj: src/persistence_types.cpp.obj

.PHONY : src/persistence_types.obj

# target to build an object file
src/persistence_types.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/persistence_types.cpp.obj
.PHONY : src/persistence_types.cpp.obj

src/persistence_types.i: src/persistence_types.cpp.i

.PHONY : src/persistence_types.i

# target to preprocess a source file
src/persistence_types.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/persistence_types.cpp.i
.PHONY : src/persistence_types.cpp.i

src/persistence_types.s: src/persistence_types.cpp.s

.PHONY : src/persistence_types.s

# target to generate assembly for a file
src/persistence_types.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/persistence_types.cpp.s
.PHONY : src/persistence_types.cpp.s

src/persistence_xml.obj: src/persistence_xml.cpp.obj

.PHONY : src/persistence_xml.obj

# target to build an object file
src/persistence_xml.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/persistence_xml.cpp.obj
.PHONY : src/persistence_xml.cpp.obj

src/persistence_xml.i: src/persistence_xml.cpp.i

.PHONY : src/persistence_xml.i

# target to preprocess a source file
src/persistence_xml.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/persistence_xml.cpp.i
.PHONY : src/persistence_xml.cpp.i

src/persistence_xml.s: src/persistence_xml.cpp.s

.PHONY : src/persistence_xml.s

# target to generate assembly for a file
src/persistence_xml.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/persistence_xml.cpp.s
.PHONY : src/persistence_xml.cpp.s

src/persistence_yml.obj: src/persistence_yml.cpp.obj

.PHONY : src/persistence_yml.obj

# target to build an object file
src/persistence_yml.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/persistence_yml.cpp.obj
.PHONY : src/persistence_yml.cpp.obj

src/persistence_yml.i: src/persistence_yml.cpp.i

.PHONY : src/persistence_yml.i

# target to preprocess a source file
src/persistence_yml.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/persistence_yml.cpp.i
.PHONY : src/persistence_yml.cpp.i

src/persistence_yml.s: src/persistence_yml.cpp.s

.PHONY : src/persistence_yml.s

# target to generate assembly for a file
src/persistence_yml.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/persistence_yml.cpp.s
.PHONY : src/persistence_yml.cpp.s

src/rand.obj: src/rand.cpp.obj

.PHONY : src/rand.obj

# target to build an object file
src/rand.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/rand.cpp.obj
.PHONY : src/rand.cpp.obj

src/rand.i: src/rand.cpp.i

.PHONY : src/rand.i

# target to preprocess a source file
src/rand.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/rand.cpp.i
.PHONY : src/rand.cpp.i

src/rand.s: src/rand.cpp.s

.PHONY : src/rand.s

# target to generate assembly for a file
src/rand.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/rand.cpp.s
.PHONY : src/rand.cpp.s

src/softfloat.obj: src/softfloat.cpp.obj

.PHONY : src/softfloat.obj

# target to build an object file
src/softfloat.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/softfloat.cpp.obj
.PHONY : src/softfloat.cpp.obj

src/softfloat.i: src/softfloat.cpp.i

.PHONY : src/softfloat.i

# target to preprocess a source file
src/softfloat.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/softfloat.cpp.i
.PHONY : src/softfloat.cpp.i

src/softfloat.s: src/softfloat.cpp.s

.PHONY : src/softfloat.s

# target to generate assembly for a file
src/softfloat.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/softfloat.cpp.s
.PHONY : src/softfloat.cpp.s

src/split.dispatch.obj: src/split.dispatch.cpp.obj

.PHONY : src/split.dispatch.obj

# target to build an object file
src/split.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/split.dispatch.cpp.obj
.PHONY : src/split.dispatch.cpp.obj

src/split.dispatch.i: src/split.dispatch.cpp.i

.PHONY : src/split.dispatch.i

# target to preprocess a source file
src/split.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/split.dispatch.cpp.i
.PHONY : src/split.dispatch.cpp.i

src/split.dispatch.s: src/split.dispatch.cpp.s

.PHONY : src/split.dispatch.s

# target to generate assembly for a file
src/split.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/split.dispatch.cpp.s
.PHONY : src/split.dispatch.cpp.s

src/stat.dispatch.obj: src/stat.dispatch.cpp.obj

.PHONY : src/stat.dispatch.obj

# target to build an object file
src/stat.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/stat.dispatch.cpp.obj
.PHONY : src/stat.dispatch.cpp.obj

src/stat.dispatch.i: src/stat.dispatch.cpp.i

.PHONY : src/stat.dispatch.i

# target to preprocess a source file
src/stat.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/stat.dispatch.cpp.i
.PHONY : src/stat.dispatch.cpp.i

src/stat.dispatch.s: src/stat.dispatch.cpp.s

.PHONY : src/stat.dispatch.s

# target to generate assembly for a file
src/stat.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/stat.dispatch.cpp.s
.PHONY : src/stat.dispatch.cpp.s

src/stat_c.obj: src/stat_c.cpp.obj

.PHONY : src/stat_c.obj

# target to build an object file
src/stat_c.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/stat_c.cpp.obj
.PHONY : src/stat_c.cpp.obj

src/stat_c.i: src/stat_c.cpp.i

.PHONY : src/stat_c.i

# target to preprocess a source file
src/stat_c.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/stat_c.cpp.i
.PHONY : src/stat_c.cpp.i

src/stat_c.s: src/stat_c.cpp.s

.PHONY : src/stat_c.s

# target to generate assembly for a file
src/stat_c.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/stat_c.cpp.s
.PHONY : src/stat_c.cpp.s

src/stl.obj: src/stl.cpp.obj

.PHONY : src/stl.obj

# target to build an object file
src/stl.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/stl.cpp.obj
.PHONY : src/stl.cpp.obj

src/stl.i: src/stl.cpp.i

.PHONY : src/stl.i

# target to preprocess a source file
src/stl.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/stl.cpp.i
.PHONY : src/stl.cpp.i

src/stl.s: src/stl.cpp.s

.PHONY : src/stl.s

# target to generate assembly for a file
src/stl.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/stl.cpp.s
.PHONY : src/stl.cpp.s

src/sum.dispatch.obj: src/sum.dispatch.cpp.obj

.PHONY : src/sum.dispatch.obj

# target to build an object file
src/sum.dispatch.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/sum.dispatch.cpp.obj
.PHONY : src/sum.dispatch.cpp.obj

src/sum.dispatch.i: src/sum.dispatch.cpp.i

.PHONY : src/sum.dispatch.i

# target to preprocess a source file
src/sum.dispatch.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/sum.dispatch.cpp.i
.PHONY : src/sum.dispatch.cpp.i

src/sum.dispatch.s: src/sum.dispatch.cpp.s

.PHONY : src/sum.dispatch.s

# target to generate assembly for a file
src/sum.dispatch.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/sum.dispatch.cpp.s
.PHONY : src/sum.dispatch.cpp.s

src/system.obj: src/system.cpp.obj

.PHONY : src/system.obj

# target to build an object file
src/system.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/system.cpp.obj
.PHONY : src/system.cpp.obj

src/system.i: src/system.cpp.i

.PHONY : src/system.i

# target to preprocess a source file
src/system.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/system.cpp.i
.PHONY : src/system.cpp.i

src/system.s: src/system.cpp.s

.PHONY : src/system.s

# target to generate assembly for a file
src/system.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/system.cpp.s
.PHONY : src/system.cpp.s

src/tables.obj: src/tables.cpp.obj

.PHONY : src/tables.obj

# target to build an object file
src/tables.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/tables.cpp.obj
.PHONY : src/tables.cpp.obj

src/tables.i: src/tables.cpp.i

.PHONY : src/tables.i

# target to preprocess a source file
src/tables.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/tables.cpp.i
.PHONY : src/tables.cpp.i

src/tables.s: src/tables.cpp.s

.PHONY : src/tables.s

# target to generate assembly for a file
src/tables.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/tables.cpp.s
.PHONY : src/tables.cpp.s

src/trace.obj: src/trace.cpp.obj

.PHONY : src/trace.obj

# target to build an object file
src/trace.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/trace.cpp.obj
.PHONY : src/trace.cpp.obj

src/trace.i: src/trace.cpp.i

.PHONY : src/trace.i

# target to preprocess a source file
src/trace.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/trace.cpp.i
.PHONY : src/trace.cpp.i

src/trace.s: src/trace.cpp.s

.PHONY : src/trace.s

# target to generate assembly for a file
src/trace.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/trace.cpp.s
.PHONY : src/trace.cpp.s

src/types.obj: src/types.cpp.obj

.PHONY : src/types.obj

# target to build an object file
src/types.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/types.cpp.obj
.PHONY : src/types.cpp.obj

src/types.i: src/types.cpp.i

.PHONY : src/types.i

# target to preprocess a source file
src/types.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/types.cpp.i
.PHONY : src/types.cpp.i

src/types.s: src/types.cpp.s

.PHONY : src/types.s

# target to generate assembly for a file
src/types.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/types.cpp.s
.PHONY : src/types.cpp.s

src/umatrix.obj: src/umatrix.cpp.obj

.PHONY : src/umatrix.obj

# target to build an object file
src/umatrix.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/umatrix.cpp.obj
.PHONY : src/umatrix.cpp.obj

src/umatrix.i: src/umatrix.cpp.i

.PHONY : src/umatrix.i

# target to preprocess a source file
src/umatrix.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/umatrix.cpp.i
.PHONY : src/umatrix.cpp.i

src/umatrix.s: src/umatrix.cpp.s

.PHONY : src/umatrix.s

# target to generate assembly for a file
src/umatrix.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/umatrix.cpp.s
.PHONY : src/umatrix.cpp.s

src/utils/datafile.obj: src/utils/datafile.cpp.obj

.PHONY : src/utils/datafile.obj

# target to build an object file
src/utils/datafile.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/utils/datafile.cpp.obj
.PHONY : src/utils/datafile.cpp.obj

src/utils/datafile.i: src/utils/datafile.cpp.i

.PHONY : src/utils/datafile.i

# target to preprocess a source file
src/utils/datafile.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/utils/datafile.cpp.i
.PHONY : src/utils/datafile.cpp.i

src/utils/datafile.s: src/utils/datafile.cpp.s

.PHONY : src/utils/datafile.s

# target to generate assembly for a file
src/utils/datafile.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/utils/datafile.cpp.s
.PHONY : src/utils/datafile.cpp.s

src/utils/filesystem.obj: src/utils/filesystem.cpp.obj

.PHONY : src/utils/filesystem.obj

# target to build an object file
src/utils/filesystem.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/utils/filesystem.cpp.obj
.PHONY : src/utils/filesystem.cpp.obj

src/utils/filesystem.i: src/utils/filesystem.cpp.i

.PHONY : src/utils/filesystem.i

# target to preprocess a source file
src/utils/filesystem.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/utils/filesystem.cpp.i
.PHONY : src/utils/filesystem.cpp.i

src/utils/filesystem.s: src/utils/filesystem.cpp.s

.PHONY : src/utils/filesystem.s

# target to generate assembly for a file
src/utils/filesystem.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/utils/filesystem.cpp.s
.PHONY : src/utils/filesystem.cpp.s

src/utils/logtagconfigparser.obj: src/utils/logtagconfigparser.cpp.obj

.PHONY : src/utils/logtagconfigparser.obj

# target to build an object file
src/utils/logtagconfigparser.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/utils/logtagconfigparser.cpp.obj
.PHONY : src/utils/logtagconfigparser.cpp.obj

src/utils/logtagconfigparser.i: src/utils/logtagconfigparser.cpp.i

.PHONY : src/utils/logtagconfigparser.i

# target to preprocess a source file
src/utils/logtagconfigparser.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/utils/logtagconfigparser.cpp.i
.PHONY : src/utils/logtagconfigparser.cpp.i

src/utils/logtagconfigparser.s: src/utils/logtagconfigparser.cpp.s

.PHONY : src/utils/logtagconfigparser.s

# target to generate assembly for a file
src/utils/logtagconfigparser.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/utils/logtagconfigparser.cpp.s
.PHONY : src/utils/logtagconfigparser.cpp.s

src/utils/logtagmanager.obj: src/utils/logtagmanager.cpp.obj

.PHONY : src/utils/logtagmanager.obj

# target to build an object file
src/utils/logtagmanager.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/utils/logtagmanager.cpp.obj
.PHONY : src/utils/logtagmanager.cpp.obj

src/utils/logtagmanager.i: src/utils/logtagmanager.cpp.i

.PHONY : src/utils/logtagmanager.i

# target to preprocess a source file
src/utils/logtagmanager.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/utils/logtagmanager.cpp.i
.PHONY : src/utils/logtagmanager.cpp.i

src/utils/logtagmanager.s: src/utils/logtagmanager.cpp.s

.PHONY : src/utils/logtagmanager.s

# target to generate assembly for a file
src/utils/logtagmanager.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/utils/logtagmanager.cpp.s
.PHONY : src/utils/logtagmanager.cpp.s

src/utils/samples.obj: src/utils/samples.cpp.obj

.PHONY : src/utils/samples.obj

# target to build an object file
src/utils/samples.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/utils/samples.cpp.obj
.PHONY : src/utils/samples.cpp.obj

src/utils/samples.i: src/utils/samples.cpp.i

.PHONY : src/utils/samples.i

# target to preprocess a source file
src/utils/samples.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/utils/samples.cpp.i
.PHONY : src/utils/samples.cpp.i

src/utils/samples.s: src/utils/samples.cpp.s

.PHONY : src/utils/samples.s

# target to generate assembly for a file
src/utils/samples.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/utils/samples.cpp.s
.PHONY : src/utils/samples.cpp.s

src/va_intel.obj: src/va_intel.cpp.obj

.PHONY : src/va_intel.obj

# target to build an object file
src/va_intel.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/va_intel.cpp.obj
.PHONY : src/va_intel.cpp.obj

src/va_intel.i: src/va_intel.cpp.i

.PHONY : src/va_intel.i

# target to preprocess a source file
src/va_intel.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/va_intel.cpp.i
.PHONY : src/va_intel.cpp.i

src/va_intel.s: src/va_intel.cpp.s

.PHONY : src/va_intel.s

# target to generate assembly for a file
src/va_intel.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/src/va_intel.cpp.s
.PHONY : src/va_intel.cpp.s

stat.avx2.obj: stat.avx2.cpp.obj

.PHONY : stat.avx2.obj

# target to build an object file
stat.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/stat.avx2.cpp.obj
.PHONY : stat.avx2.cpp.obj

stat.avx2.i: stat.avx2.cpp.i

.PHONY : stat.avx2.i

# target to preprocess a source file
stat.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/stat.avx2.cpp.i
.PHONY : stat.avx2.cpp.i

stat.avx2.s: stat.avx2.cpp.s

.PHONY : stat.avx2.s

# target to generate assembly for a file
stat.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/stat.avx2.cpp.s
.PHONY : stat.avx2.cpp.s

stat.sse4_2.obj: stat.sse4_2.cpp.obj

.PHONY : stat.sse4_2.obj

# target to build an object file
stat.sse4_2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/stat.sse4_2.cpp.obj
.PHONY : stat.sse4_2.cpp.obj

stat.sse4_2.i: stat.sse4_2.cpp.i

.PHONY : stat.sse4_2.i

# target to preprocess a source file
stat.sse4_2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/stat.sse4_2.cpp.i
.PHONY : stat.sse4_2.cpp.i

stat.sse4_2.s: stat.sse4_2.cpp.s

.PHONY : stat.sse4_2.s

# target to generate assembly for a file
stat.sse4_2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/stat.sse4_2.cpp.s
.PHONY : stat.sse4_2.cpp.s

sum.avx2.obj: sum.avx2.cpp.obj

.PHONY : sum.avx2.obj

# target to build an object file
sum.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/sum.avx2.cpp.obj
.PHONY : sum.avx2.cpp.obj

sum.avx2.i: sum.avx2.cpp.i

.PHONY : sum.avx2.i

# target to preprocess a source file
sum.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/sum.avx2.cpp.i
.PHONY : sum.avx2.cpp.i

sum.avx2.s: sum.avx2.cpp.s

.PHONY : sum.avx2.s

# target to generate assembly for a file
sum.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/sum.avx2.cpp.s
.PHONY : sum.avx2.cpp.s

vs_version.obj: vs_version.rc.obj

.PHONY : vs_version.obj

# target to build an object file
vs_version.rc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/vs_version.rc.obj
.PHONY : vs_version.rc.obj

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... list_install_components
	@echo ... opencv_core
	@echo ... package
	@echo ... package_source
	@echo ... rebuild_cache
	@echo ... test
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... install/strip
	@echo ... arithm.avx2.obj
	@echo ... arithm.avx2.i
	@echo ... arithm.avx2.s
	@echo ... arithm.sse4_1.obj
	@echo ... arithm.sse4_1.i
	@echo ... arithm.sse4_1.s
	@echo ... convert.avx2.obj
	@echo ... convert.avx2.i
	@echo ... convert.avx2.s
	@echo ... convert_scale.avx2.obj
	@echo ... convert_scale.avx2.i
	@echo ... convert_scale.avx2.s
	@echo ... count_non_zero.avx2.obj
	@echo ... count_non_zero.avx2.i
	@echo ... count_non_zero.avx2.s
	@echo ... mathfuncs_core.avx.obj
	@echo ... mathfuncs_core.avx.i
	@echo ... mathfuncs_core.avx.s
	@echo ... mathfuncs_core.avx2.obj
	@echo ... mathfuncs_core.avx2.i
	@echo ... mathfuncs_core.avx2.s
	@echo ... matmul.avx2.obj
	@echo ... matmul.avx2.i
	@echo ... matmul.avx2.s
	@echo ... matmul.sse4_1.obj
	@echo ... matmul.sse4_1.i
	@echo ... matmul.sse4_1.s
	@echo ... mean.avx2.obj
	@echo ... mean.avx2.i
	@echo ... mean.avx2.s
	@echo ... merge.avx2.obj
	@echo ... merge.avx2.i
	@echo ... merge.avx2.s
	@echo ... opencl_kernels_core.obj
	@echo ... opencl_kernels_core.i
	@echo ... opencl_kernels_core.s
	@echo ... split.avx2.obj
	@echo ... split.avx2.i
	@echo ... split.avx2.s
	@echo ... src/algorithm.obj
	@echo ... src/algorithm.i
	@echo ... src/algorithm.s
	@echo ... src/alloc.obj
	@echo ... src/alloc.i
	@echo ... src/alloc.s
	@echo ... src/arithm.obj
	@echo ... src/arithm.i
	@echo ... src/arithm.s
	@echo ... src/arithm.dispatch.obj
	@echo ... src/arithm.dispatch.i
	@echo ... src/arithm.dispatch.s
	@echo ... src/array.obj
	@echo ... src/array.i
	@echo ... src/array.s
	@echo ... src/async.obj
	@echo ... src/async.i
	@echo ... src/async.s
	@echo ... src/batch_distance.obj
	@echo ... src/batch_distance.i
	@echo ... src/batch_distance.s
	@echo ... src/bindings_utils.obj
	@echo ... src/bindings_utils.i
	@echo ... src/bindings_utils.s
	@echo ... src/channels.obj
	@echo ... src/channels.i
	@echo ... src/channels.s
	@echo ... src/check.obj
	@echo ... src/check.i
	@echo ... src/check.s
	@echo ... src/command_line_parser.obj
	@echo ... src/command_line_parser.i
	@echo ... src/command_line_parser.s
	@echo ... src/conjugate_gradient.obj
	@echo ... src/conjugate_gradient.i
	@echo ... src/conjugate_gradient.s
	@echo ... src/convert.dispatch.obj
	@echo ... src/convert.dispatch.i
	@echo ... src/convert.dispatch.s
	@echo ... src/convert_c.obj
	@echo ... src/convert_c.i
	@echo ... src/convert_c.s
	@echo ... src/convert_scale.dispatch.obj
	@echo ... src/convert_scale.dispatch.i
	@echo ... src/convert_scale.dispatch.s
	@echo ... src/copy.obj
	@echo ... src/copy.i
	@echo ... src/copy.s
	@echo ... src/count_non_zero.dispatch.obj
	@echo ... src/count_non_zero.dispatch.i
	@echo ... src/count_non_zero.dispatch.s
	@echo ... src/cuda_gpu_mat.obj
	@echo ... src/cuda_gpu_mat.i
	@echo ... src/cuda_gpu_mat.s
	@echo ... src/cuda_host_mem.obj
	@echo ... src/cuda_host_mem.i
	@echo ... src/cuda_host_mem.s
	@echo ... src/cuda_info.obj
	@echo ... src/cuda_info.i
	@echo ... src/cuda_info.s
	@echo ... src/cuda_stream.obj
	@echo ... src/cuda_stream.i
	@echo ... src/cuda_stream.s
	@echo ... src/datastructs.obj
	@echo ... src/datastructs.i
	@echo ... src/datastructs.s
	@echo ... src/directx.obj
	@echo ... src/directx.i
	@echo ... src/directx.s
	@echo ... src/downhill_simplex.obj
	@echo ... src/downhill_simplex.i
	@echo ... src/downhill_simplex.s
	@echo ... src/dxt.obj
	@echo ... src/dxt.i
	@echo ... src/dxt.s
	@echo ... src/gl_core_3_1.obj
	@echo ... src/gl_core_3_1.i
	@echo ... src/gl_core_3_1.s
	@echo ... src/glob.obj
	@echo ... src/glob.i
	@echo ... src/glob.s
	@echo ... src/hal_internal.obj
	@echo ... src/hal_internal.i
	@echo ... src/hal_internal.s
	@echo ... src/kmeans.obj
	@echo ... src/kmeans.i
	@echo ... src/kmeans.s
	@echo ... src/lapack.obj
	@echo ... src/lapack.i
	@echo ... src/lapack.s
	@echo ... src/lda.obj
	@echo ... src/lda.i
	@echo ... src/lda.s
	@echo ... src/logger.obj
	@echo ... src/logger.i
	@echo ... src/logger.s
	@echo ... src/lpsolver.obj
	@echo ... src/lpsolver.i
	@echo ... src/lpsolver.s
	@echo ... src/lut.obj
	@echo ... src/lut.i
	@echo ... src/lut.s
	@echo ... src/mathfuncs.obj
	@echo ... src/mathfuncs.i
	@echo ... src/mathfuncs.s
	@echo ... src/mathfuncs_core.dispatch.obj
	@echo ... src/mathfuncs_core.dispatch.i
	@echo ... src/mathfuncs_core.dispatch.s
	@echo ... src/matmul.dispatch.obj
	@echo ... src/matmul.dispatch.i
	@echo ... src/matmul.dispatch.s
	@echo ... src/matrix.obj
	@echo ... src/matrix.i
	@echo ... src/matrix.s
	@echo ... src/matrix_c.obj
	@echo ... src/matrix_c.i
	@echo ... src/matrix_c.s
	@echo ... src/matrix_decomp.obj
	@echo ... src/matrix_decomp.i
	@echo ... src/matrix_decomp.s
	@echo ... src/matrix_expressions.obj
	@echo ... src/matrix_expressions.i
	@echo ... src/matrix_expressions.s
	@echo ... src/matrix_iterator.obj
	@echo ... src/matrix_iterator.i
	@echo ... src/matrix_iterator.s
	@echo ... src/matrix_operations.obj
	@echo ... src/matrix_operations.i
	@echo ... src/matrix_operations.s
	@echo ... src/matrix_sparse.obj
	@echo ... src/matrix_sparse.i
	@echo ... src/matrix_sparse.s
	@echo ... src/matrix_wrap.obj
	@echo ... src/matrix_wrap.i
	@echo ... src/matrix_wrap.s
	@echo ... src/mean.dispatch.obj
	@echo ... src/mean.dispatch.i
	@echo ... src/mean.dispatch.s
	@echo ... src/merge.dispatch.obj
	@echo ... src/merge.dispatch.i
	@echo ... src/merge.dispatch.s
	@echo ... src/minmax.obj
	@echo ... src/minmax.i
	@echo ... src/minmax.s
	@echo ... src/norm.obj
	@echo ... src/norm.i
	@echo ... src/norm.s
	@echo ... src/ocl.obj
	@echo ... src/ocl.i
	@echo ... src/ocl.s
	@echo ... src/opencl/runtime/opencl_clamdblas.obj
	@echo ... src/opencl/runtime/opencl_clamdblas.i
	@echo ... src/opencl/runtime/opencl_clamdblas.s
	@echo ... src/opencl/runtime/opencl_clamdfft.obj
	@echo ... src/opencl/runtime/opencl_clamdfft.i
	@echo ... src/opencl/runtime/opencl_clamdfft.s
	@echo ... src/opencl/runtime/opencl_core.obj
	@echo ... src/opencl/runtime/opencl_core.i
	@echo ... src/opencl/runtime/opencl_core.s
	@echo ... src/opengl.obj
	@echo ... src/opengl.i
	@echo ... src/opengl.s
	@echo ... src/out.obj
	@echo ... src/out.i
	@echo ... src/out.s
	@echo ... src/ovx.obj
	@echo ... src/ovx.i
	@echo ... src/ovx.s
	@echo ... src/parallel.obj
	@echo ... src/parallel.i
	@echo ... src/parallel.s
	@echo ... src/parallel_impl.obj
	@echo ... src/parallel_impl.i
	@echo ... src/parallel_impl.s
	@echo ... src/pca.obj
	@echo ... src/pca.i
	@echo ... src/pca.s
	@echo ... src/persistence.obj
	@echo ... src/persistence.i
	@echo ... src/persistence.s
	@echo ... src/persistence_json.obj
	@echo ... src/persistence_json.i
	@echo ... src/persistence_json.s
	@echo ... src/persistence_types.obj
	@echo ... src/persistence_types.i
	@echo ... src/persistence_types.s
	@echo ... src/persistence_xml.obj
	@echo ... src/persistence_xml.i
	@echo ... src/persistence_xml.s
	@echo ... src/persistence_yml.obj
	@echo ... src/persistence_yml.i
	@echo ... src/persistence_yml.s
	@echo ... src/rand.obj
	@echo ... src/rand.i
	@echo ... src/rand.s
	@echo ... src/softfloat.obj
	@echo ... src/softfloat.i
	@echo ... src/softfloat.s
	@echo ... src/split.dispatch.obj
	@echo ... src/split.dispatch.i
	@echo ... src/split.dispatch.s
	@echo ... src/stat.dispatch.obj
	@echo ... src/stat.dispatch.i
	@echo ... src/stat.dispatch.s
	@echo ... src/stat_c.obj
	@echo ... src/stat_c.i
	@echo ... src/stat_c.s
	@echo ... src/stl.obj
	@echo ... src/stl.i
	@echo ... src/stl.s
	@echo ... src/sum.dispatch.obj
	@echo ... src/sum.dispatch.i
	@echo ... src/sum.dispatch.s
	@echo ... src/system.obj
	@echo ... src/system.i
	@echo ... src/system.s
	@echo ... src/tables.obj
	@echo ... src/tables.i
	@echo ... src/tables.s
	@echo ... src/trace.obj
	@echo ... src/trace.i
	@echo ... src/trace.s
	@echo ... src/types.obj
	@echo ... src/types.i
	@echo ... src/types.s
	@echo ... src/umatrix.obj
	@echo ... src/umatrix.i
	@echo ... src/umatrix.s
	@echo ... src/utils/datafile.obj
	@echo ... src/utils/datafile.i
	@echo ... src/utils/datafile.s
	@echo ... src/utils/filesystem.obj
	@echo ... src/utils/filesystem.i
	@echo ... src/utils/filesystem.s
	@echo ... src/utils/logtagconfigparser.obj
	@echo ... src/utils/logtagconfigparser.i
	@echo ... src/utils/logtagconfigparser.s
	@echo ... src/utils/logtagmanager.obj
	@echo ... src/utils/logtagmanager.i
	@echo ... src/utils/logtagmanager.s
	@echo ... src/utils/samples.obj
	@echo ... src/utils/samples.i
	@echo ... src/utils/samples.s
	@echo ... src/va_intel.obj
	@echo ... src/va_intel.i
	@echo ... src/va_intel.s
	@echo ... stat.avx2.obj
	@echo ... stat.avx2.i
	@echo ... stat.avx2.s
	@echo ... stat.sse4_2.obj
	@echo ... stat.sse4_2.i
	@echo ... stat.sse4_2.s
	@echo ... sum.avx2.obj
	@echo ... sum.avx2.i
	@echo ... sum.avx2.s
	@echo ... vs_version.obj
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

