#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

D:/unet/opencv/opencv/mingw_build/modules/core/arithm.avx2.cpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
-
D:/unet/opencv/opencv/sources/modules/core/src/arithm.simd.hpp
-

D:/unet/opencv/opencv/mingw_build/modules/core/arithm.sse4_1.cpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
-
D:/unet/opencv/opencv/sources/modules/core/src/arithm.simd.hpp
-

D:/unet/opencv/opencv/mingw_build/modules/core/convert.avx2.cpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
-
D:/unet/opencv/opencv/sources/modules/core/src/convert.simd.hpp
-

D:/unet/opencv/opencv/mingw_build/modules/core/convert_scale.avx2.cpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
-
D:/unet/opencv/opencv/sources/modules/core/src/convert_scale.simd.hpp
-

D:/unet/opencv/opencv/mingw_build/modules/core/count_non_zero.avx2.cpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
-
D:/unet/opencv/opencv/sources/modules/core/src/count_non_zero.simd.hpp
-

D:/unet/opencv/opencv/mingw_build/modules/core/mathfuncs_core.avx.cpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
-
D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs_core.simd.hpp
-

D:/unet/opencv/opencv/mingw_build/modules/core/mathfuncs_core.avx2.cpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
-
D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs_core.simd.hpp
-

D:/unet/opencv/opencv/mingw_build/modules/core/matmul.avx2.cpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
-
D:/unet/opencv/opencv/sources/modules/core/src/matmul.simd.hpp
-

D:/unet/opencv/opencv/mingw_build/modules/core/matmul.sse4_1.cpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
-
D:/unet/opencv/opencv/sources/modules/core/src/matmul.simd.hpp
-

D:/unet/opencv/opencv/mingw_build/modules/core/mean.avx2.cpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
-
D:/unet/opencv/opencv/sources/modules/core/src/mean.simd.hpp
-

D:/unet/opencv/opencv/mingw_build/modules/core/merge.avx2.cpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
-
D:/unet/opencv/opencv/sources/modules/core/src/merge.simd.hpp
-

D:/unet/opencv/opencv/mingw_build/modules/core/opencl_kernels_core.cpp
opencv2/core.hpp
D:/unet/opencv/opencv/mingw_build/modules/core/opencv2/core.hpp
cvconfig.h
D:/unet/opencv/opencv/mingw_build/modules/core/cvconfig.h
opencl_kernels_core.hpp
D:/unet/opencv/opencv/mingw_build/modules/core/opencl_kernels_core.hpp

D:/unet/opencv/opencv/mingw_build/modules/core/opencl_kernels_core.hpp
opencv2/core/ocl.hpp
D:/unet/opencv/opencv/mingw_build/modules/core/opencv2/core/ocl.hpp
opencv2/core/ocl_genbase.hpp
D:/unet/opencv/opencv/mingw_build/modules/core/opencv2/core/ocl_genbase.hpp
opencv2/core/opencl/ocl_defs.hpp
D:/unet/opencv/opencv/mingw_build/modules/core/opencv2/core/opencl/ocl_defs.hpp

D:/unet/opencv/opencv/mingw_build/modules/core/split.avx2.cpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
-
D:/unet/opencv/opencv/sources/modules/core/src/split.simd.hpp
-

D:/unet/opencv/opencv/mingw_build/modules/core/stat.avx2.cpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
-
D:/unet/opencv/opencv/sources/modules/core/src/stat.simd.hpp
-

D:/unet/opencv/opencv/mingw_build/modules/core/stat.sse4_2.cpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
-
D:/unet/opencv/opencv/sources/modules/core/src/stat.simd.hpp
-

D:/unet/opencv/opencv/mingw_build/modules/core/sum.avx2.cpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
-
D:/unet/opencv/opencv/sources/modules/core/src/sum.simd.hpp
-

D:/unet/opencv/opencv/sources/3rdparty/include/opencl/1.2/CL/cl.h
OpenCL/cl_platform.h
-
CL/cl_platform.h
-

D:/unet/opencv/opencv/sources/3rdparty/include/opencl/1.2/CL/cl_d3d10.h
d3d10.h
-
CL/cl.h
-
CL/cl_platform.h
-

D:/unet/opencv/opencv/sources/3rdparty/include/opencl/1.2/CL/cl_d3d11.h
d3d11.h
-
CL/cl.h
-
CL/cl_platform.h
-

D:/unet/opencv/opencv/sources/3rdparty/include/opencl/1.2/CL/cl_d3d11_ext.h
d3d11.h
-
CL/cl.h
-
CL/cl_platform.h
-

D:/unet/opencv/opencv/sources/3rdparty/include/opencl/1.2/CL/cl_dx9_media_sharing.h
CL/cl.h
-
CL/cl_platform.h
-
d3d9.h
-

D:/unet/opencv/opencv/sources/3rdparty/include/opencl/1.2/CL/cl_ext.h
OpenCL/cl.h
-
AvailabilityMacros.h
-
CL/cl.h
-

D:/unet/opencv/opencv/sources/3rdparty/include/opencl/1.2/CL/cl_gl.h
OpenCL/cl.h
-
CL/cl.h
-

D:/unet/opencv/opencv/sources/3rdparty/include/opencl/1.2/CL/cl_gl_ext.h
OpenCL/cl_gl.h
-
CL/cl_gl.h
-

D:/unet/opencv/opencv/sources/3rdparty/include/opencl/1.2/CL/cl_platform.h
AvailabilityMacros.h
-
stdint.h
-
stddef.h
-
altivec.h
-
intrin.h
-
xmmintrin.h
-
intrin.h
-
emmintrin.h
-
mmintrin.h
-
intrin.h
-
immintrin.h
-

D:/unet/opencv/opencv/sources/3rdparty/include/opencl/1.2/CL/opencl.h
OpenCL/cl.h
-
OpenCL/cl_gl.h
-
OpenCL/cl_gl_ext.h
-
OpenCL/cl_ext.h
-
CL/cl.h
-
CL/cl_gl.h
-
CL/cl_gl_ext.h
-
CL/cl_ext.h
-

D:/unet/opencv/opencv/sources/3rdparty/zlib/zconf.h
stddef.h
-
windows.h
-
limits.h
-
sys/types.h
-
stdarg.h
-
stddef.h
-
unistd.h
-
unixio.h
-

D:/unet/opencv/opencv/sources/3rdparty/zlib/zlib.h
zconf.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zconf.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/cvdef.h
opencv2/core/version.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/version.hpp
opencv2/core/base.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/base.hpp
opencv2/core/cvstd.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/cvstd.hpp
opencv2/core/traits.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/traits.hpp
opencv2/core/matx.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/matx.hpp
opencv2/core/types.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/types.hpp
opencv2/core/mat.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/mat.hpp
opencv2/core/persistence.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/persistence.hpp
opencv2/core/operations.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/operations.hpp
opencv2/core/cvstd.inl.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/cvstd.inl.hpp
opencv2/core/utility.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/utility.hpp
opencv2/core/optim.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/optim.hpp
opencv2/core/ovx.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/ovx.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/async.hpp
opencv2/core/mat.hpp
-
chrono
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/base.hpp
opencv2/opencv_modules.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/opencv_modules.hpp
climits
-
algorithm
-
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
opencv2/core/cvstd.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvstd.hpp
opencv2/core/neon_utils.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/neon_utils.hpp
opencv2/core/vsx_utils.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/vsx_utils.hpp
opencv2/core/check.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/check.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/bindings_utils.hpp
opencv2/core/async.hpp
-
opencv2/core/detail/async_promise.hpp
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/bufferpool.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/check.hpp
opencv2/core/base.hpp
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/core_c.h
opencv2/core/types_c.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/types_c.h
cxcore.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cxcore.h
cxcore.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cxcore.h
opencv2/core/utility.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/utility.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp
opencv2/core/cuda_types.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cuda_types.hpp
opencv2/opencv.hpp
-
opencv2/core/cuda.inl.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cuda.inl.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda.inl.hpp
opencv2/core/cuda.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cuda.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/common.hpp
cuda_runtime.h
-
opencv2/core/cuda_types.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/opencv2/core/cuda_types.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/opencv2/core/cvdef.h
opencv2/core/base.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/opencv2/core/base.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda_stream_accessor.hpp
cuda_runtime.h
-
opencv2/core/cuda.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cuda.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda_types.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_dispatch.h
cv_cpu_config.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_config.h
cv_cpu_helper.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_helper.h
emmintrin.h
-
pmmintrin.h
-
tmmintrin.h
-
smmintrin.h
-
nmmintrin.h
-
nmmintrin.h
-
popcntintrin.h
-
immintrin.h
-
arm_neon.h
-
immintrin.h
-
immintrin.h
-
immintrin.h
-
Intrin.h
-
arm_neon.h
-
arm_neon.h
-
arm_neon.h
-
altivec.h
-
hal/msa_macros.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/msa_macros.h
wasm_simd128.h
-
emmintrin.h
-
Intrin.h
-
arm_neon.h
-
arm_neon.h
-
altivec.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_helper.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvdef.h
cvconfig.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvconfig.h
limits.h
-
opencv2/core/hal/interface.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/hal/interface.h
cv_cpu_dispatch.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_dispatch.h
intrin.h
-
array
-
cstdint
-
stdint.h
-
stdint.h
-
opencv2/core/fast_math.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/fast_math.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
cstddef
-
cstring
-
cctype
-
string
-
algorithm
-
utility
-
cstdlib
-
cmath
-
cvstd_wrapper.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd_wrapper.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.inl.hpp
complex
-
ostream
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd_wrapper.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
string
-
memory
-
type_traits
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/detail/async_promise.hpp
../async.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/async.hpp
exception_ptr.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/detail/exception_ptr.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/detail/exception_ptr.hpp
exception
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/directx.hpp
mat.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.hpp
ocl.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/eigen.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/fast_math.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
cmath
-
fastmath.h
-
math.h
-
emmintrin.h
-
altivec.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/hal.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/cvdef.h
opencv2/core/cvstd.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/cvstd.hpp
opencv2/core/hal/interface.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/hal/interface.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/interface.h
cstddef
-
stddef.h
-
stdbool.h
-
cstdint
-
stdint.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin.hpp
cmath
-
float.h
-
stdlib.h
-
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/cvdef.h
opencv2/core/hal/intrin_forward.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/hal/intrin_forward.hpp
opencv2/core/hal/intrin_sse_em.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/hal/intrin_sse_em.hpp
opencv2/core/hal/intrin_sse.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/hal/intrin_sse.hpp
opencv2/core/hal/intrin_neon.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/hal/intrin_neon.hpp
opencv2/core/hal/intrin_vsx.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/hal/intrin_vsx.hpp
opencv2/core/hal/intrin_msa.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/hal/intrin_msa.hpp
opencv2/core/hal/intrin_wasm.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/hal/intrin_wasm.hpp
opencv2/core/hal/intrin_cpp.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/hal/intrin_cpp.hpp
opencv2/core/hal/intrin_forward.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/hal/intrin_forward.hpp
opencv2/core/hal/intrin_avx.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/hal/intrin_avx.hpp
opencv2/core/hal/intrin_forward.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/hal/intrin_forward.hpp
opencv2/core/hal/intrin_avx512.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/hal/intrin_avx512.hpp
simd_utils.impl.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/simd_utils.impl.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_avx.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_avx512.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_cpp.hpp
limits
-
cstring
-
algorithm
-
opencv2/core/saturate.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/saturate.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_forward.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_msa.hpp
algorithm
-
opencv2/core/utility.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/utility.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_neon.hpp
algorithm
-
opencv2/core/utility.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/utility.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_sse.hpp
algorithm
-
opencv2/core/utility.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/utility.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_sse_em.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_vsx.hpp
algorithm
-
opencv2/core/utility.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/utility.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_wasm.hpp
limits
-
cstring
-
algorithm
-
opencv2/core/saturate.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/opencv2/core/saturate.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/msa_macros.h
msa.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/msa.h
stdint.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/simd_utils.impl.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.hpp
opencv2/core/matx.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/matx.hpp
opencv2/core/types.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/types.hpp
opencv2/core/bufferpool.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/bufferpool.hpp
type_traits
-
opencv2/core/mat.inl.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/mat.inl.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.inl.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/matx.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
opencv2/core/base.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/base.hpp
opencv2/core/traits.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/traits.hpp
opencv2/core/saturate.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/saturate.hpp
initializer_list
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/neon_utils.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl_genbase.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/ocl_defs.hpp
opencv2/core/utility.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/opencv2/core/utility.hpp
cvconfig.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/cvconfig.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/opencl_svm.hpp
runtime/opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_core.hpp
runtime/opencl_svm_20.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_svm_20.hpp
runtime/opencl_svm_hsa_extension.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_svm_hsa_extension.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_clamdblas.hpp
clAmdBlas.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_clamdfft.hpp
clAmdFft.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_core.hpp
OpenCL/cl.h
-
CL/cl.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_gl.hpp
OpenCL/cl_gl.h
-
CL/cl_gl.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_clamdblas.hpp
opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_core.hpp
autogenerated/opencl_clamdblas.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_clamdblas.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_clamdfft.hpp
opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_core.hpp
autogenerated/opencl_clamdfft.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_clamdfft.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_core.hpp
autogenerated/opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_core.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_gl.hpp
opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_core.hpp
autogenerated/opencl_gl.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_gl.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_svm_20.hpp
opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_core.hpp
opencl_svm_definitions.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_svm_definitions.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_svm_definitions.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_svm_hsa_extension.hpp
opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_core.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opengl.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp
ocl.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/openvx/ovx_defs.hpp
cvconfig.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/openvx/cvconfig.h
ivx.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/openvx/ivx.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/operations.hpp
cstdio
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/optim.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ovx.hpp
cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvdef.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/persistence.hpp
opencv2/core/types.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/types.hpp
opencv2/core/mat.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/mat.hpp
opencv2/opencv.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/opencv.hpp
time.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/private.cuda.hpp
cvconfig.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvconfig.h
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
opencv2/core/base.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/base.hpp
opencv2/core/cuda.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cuda.hpp
cuda.h
-
cuda_runtime.h
-
cuda_fp16.h
-
cuda_fp16.h
-
npp.h
-
opencv2/core/cuda_stream_accessor.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cuda_stream_accessor.hpp
opencv2/core/cuda/common.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cuda/common.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/private.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp
cvconfig.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvconfig.h
opencv2/core/utils/trace.hpp
-
opencv2/core/utils/instrumentation.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/utils/instrumentation.hpp
Eigen/Core
-
opencv2/core/eigen.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/eigen.hpp
ippversion.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ippversion.h
ippicv.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ippicv.h
ipp.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ipp.h
ipp.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ipp.h
iw++/iw.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/iw++/iw.hpp
iw/iw_ll.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/iw/iw_ll.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/private/cv_cpu_include_simd_declarations.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/saturate.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
opencv2/core/fast_math.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/fast_math.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/softfloat.hpp
cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvdef.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/sse_utils.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/traits.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/types.hpp
climits
-
cfloat
-
vector
-
limits
-
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
opencv2/core/cvstd.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvstd.hpp
opencv2/core/matx.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/matx.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/types_c.h
ipl.h
-
ipl/ipl.h
-
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
assert.h
-
stdlib.h
-
string.h
-
float.h
-
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utility.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp
ostream
-
functional
-
mutex
-
opencv2/core/utils/instrumentation.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/utils/instrumentation.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/./allocator_stats.hpp
../cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/cvdef.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/allocator_stats.impl.hpp
./allocator_stats.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/./allocator_stats.hpp
atomic
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/configuration.private.hpp
opencv2/core/cvstd.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/opencv2/core/cvstd.hpp
vector
-
string
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/filesystem.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/filesystem.private.hpp
TargetConditionals.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/instrumentation.hpp
opencv2/core/utility.hpp
-
opencv2/core/utils/tls.hpp
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/lock.private.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/logger.defines.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/logger.hpp
iostream
-
sstream
-
limits.h
-
logger.defines.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/logger.defines.hpp
logtag.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/logtag.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/logtag.hpp
opencv2/core/cvstd.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/opencv2/core/cvstd.hpp
logger.defines.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/logger.defines.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/tls.hpp
opencv2/core/utility.hpp
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/trace.hpp
opencv2/core/cvdef.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/trace.private.hpp
opencv2/core/utils/logger.hpp
-
opencv2/core/utils/tls.hpp
-
trace.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/trace.hpp
deque
-
ostream
-
ittnotify.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/ittnotify.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/va_intel.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp
ocl.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl.hpp
va/va.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/va/va.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/version.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/vsx_utils.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
assert.h
-

D:/unet/opencv/opencv/sources/modules/core/src/algorithm.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/alloc.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core/utils/logger.defines.hpp
-
opencv2/core/utils/logger.hpp
-
opencv2/core/utils/configuration.private.hpp
-
opencv2/core/utils/allocator_stats.impl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/utils/allocator_stats.impl.hpp
stdlib.h
-
malloc.h
-
map
-

D:/unet/opencv/opencv/sources/modules/core/src/arithm.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp

D:/unet/opencv/opencv/sources/modules/core/src/arithm.dispatch.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
arithm_ipp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/arithm_ipp.hpp
arithm.simd.hpp
D:/unet/opencv/opencv/sources/modules/core/src/arithm.simd.hpp
arithm.simd_declarations.hpp
D:/unet/opencv/opencv/sources/modules/core/src/arithm.simd_declarations.hpp
arithm.simd.hpp
D:/unet/opencv/opencv/sources/modules/core/src/arithm.simd.hpp

D:/unet/opencv/opencv/sources/modules/core/src/arithm.simd.hpp
opencv2/core/hal/intrin.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/hal/intrin.hpp

D:/unet/opencv/opencv/sources/modules/core/src/arithm_ipp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/array.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/async.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core/async.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/async.hpp
opencv2/core/detail/async_promise.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/detail/async_promise.hpp
opencv2/core/cvstd.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/cvstd.hpp
opencv2/core/utils/logger.defines.hpp
-
opencv2/core/utils/logger.hpp
-
mutex
-
condition_variable
-
chrono
-

D:/unet/opencv/opencv/sources/modules/core/src/batch_distance.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
stat.hpp
D:/unet/opencv/opencv/sources/modules/core/src/stat.hpp
opencv2/core/hal/hal.hpp
-

D:/unet/opencv/opencv/sources/modules/core/src/bindings_utils.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core/bindings_utils.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/bindings_utils.hpp
sstream
-

D:/unet/opencv/opencv/sources/modules/core/src/bufferpool.impl.hpp
opencv2/core/bufferpool.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/bufferpool.hpp

D:/unet/opencv/opencv/sources/modules/core/src/channels.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
convert.hpp
D:/unet/opencv/opencv/sources/modules/core/src/convert.hpp

D:/unet/opencv/opencv/sources/modules/core/src/check.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core/check.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/check.hpp

D:/unet/opencv/opencv/sources/modules/core/src/command_line_parser.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
sstream
-

D:/unet/opencv/opencv/sources/modules/core/src/conjugate_gradient.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/convert.dispatch.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
convert.simd.hpp
D:/unet/opencv/opencv/sources/modules/core/src/convert.simd.hpp
convert.simd_declarations.hpp
D:/unet/opencv/opencv/sources/modules/core/src/convert.simd_declarations.hpp

D:/unet/opencv/opencv/sources/modules/core/src/convert.hpp
opencv2/core/types.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/types.hpp

D:/unet/opencv/opencv/sources/modules/core/src/convert.simd.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
convert.hpp
D:/unet/opencv/opencv/sources/modules/core/src/convert.hpp

D:/unet/opencv/opencv/sources/modules/core/src/convert_c.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/convert_scale.dispatch.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
convert_scale.simd.hpp
D:/unet/opencv/opencv/sources/modules/core/src/convert_scale.simd.hpp
convert_scale.simd_declarations.hpp
D:/unet/opencv/opencv/sources/modules/core/src/convert_scale.simd_declarations.hpp

D:/unet/opencv/opencv/sources/modules/core/src/convert_scale.simd.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
convert.hpp
D:/unet/opencv/opencv/sources/modules/core/src/convert.hpp

D:/unet/opencv/opencv/sources/modules/core/src/copy.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp

D:/unet/opencv/opencv/sources/modules/core/src/count_non_zero.dispatch.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
stat.hpp
D:/unet/opencv/opencv/sources/modules/core/src/stat.hpp
count_non_zero.simd.hpp
D:/unet/opencv/opencv/sources/modules/core/src/count_non_zero.simd.hpp
count_non_zero.simd_declarations.hpp
D:/unet/opencv/opencv/sources/modules/core/src/count_non_zero.simd_declarations.hpp

D:/unet/opencv/opencv/sources/modules/core/src/count_non_zero.simd.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/cuda_gpu_mat.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/cuda_host_mem.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
map
-

D:/unet/opencv/opencv/sources/modules/core/src/cuda_info.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/cuda_stream.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/datastructs.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/directx.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core.hpp
opencv2/core/ocl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/ocl.hpp
opencv2/core/directx.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/directx.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
vector
-
directx.inc.hpp
D:/unet/opencv/opencv/sources/modules/core/src/directx.inc.hpp

D:/unet/opencv/opencv/sources/modules/core/src/directx.inc.hpp
d3d11.h
-
d3d10.h
-
d3d9.h
-
opencv2/core/opencl/runtime/opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opencl/runtime/opencl_core.hpp
CL/cl_d3d11.h
-
CL/cl_d3d11_ext.h
-
CL/cl_d3d10.h
-
CL/cl_dx9_media_sharing.h
-

D:/unet/opencv/opencv/sources/modules/core/src/downhill_simplex.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
iostream
-
cstdlib
-
cmath
-
algorithm
-
opencv2/optim/optim.hpp
-

D:/unet/opencv/opencv/sources/modules/core/src/dxt.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core/opencl/runtime/opencl_clamdfft.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opencl/runtime/opencl_clamdfft.hpp
opencv2/core/opencl/runtime/opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opencl/runtime/opencl_core.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
map
-

D:/unet/opencv/opencv/sources/modules/core/src/gl_core_3_1.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
gl_core_3_1.hpp
D:/unet/opencv/opencv/sources/modules/core/src/gl_core_3_1.hpp
dlfcn.h
-
dlfcn.h
-
stdio.h
-
GL/glx.h
-

D:/unet/opencv/opencv/sources/modules/core/src/gl_core_3_1.hpp
windows.h
-
windows.h
-
stddef.h
-
inttypes.h
-
inttypes.h
-
inttypes.h
-
stdint.h
-
stdint.h
-
inttypes.h
-

D:/unet/opencv/opencv/sources/modules/core/src/glob.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core/utils/filesystem.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/utils/filesystem.hpp
windows.h
-
dirent.h
-
sys/stat.h
-

D:/unet/opencv/opencv/sources/modules/core/src/hal_internal.cpp
hal_internal.hpp
D:/unet/opencv/opencv/sources/modules/core/src/hal_internal.hpp
complex.h
-
opencv_lapack.h
D:/unet/opencv/opencv/sources/modules/core/src/opencv_lapack.h
cmath
-
algorithm
-
typeinfo
-
limits
-
complex
-
vector
-

D:/unet/opencv/opencv/sources/modules/core/src/hal_internal.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/hal_replacement.hpp
opencv2/core/hal/interface.h
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/hal/interface.h
hal_internal.hpp
D:/unet/opencv/opencv/sources/modules/core/src/hal_internal.hpp
custom_hal.hpp
D:/unet/opencv/opencv/sources/modules/core/src/custom_hal.hpp

D:/unet/opencv/opencv/sources/modules/core/src/intel_gpu_gemm.inl.hpp
sstream
-
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
opencv2/core/opencl/runtime/opencl_clamdblas.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opencl/runtime/opencl_clamdblas.hpp
opencv2/core/opencl/runtime/opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opencl/runtime/opencl_core.hpp

D:/unet/opencv/opencv/sources/modules/core/src/kmeans.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core/utils/configuration.private.hpp
-
opencv2/core/hal/hal.hpp
-

D:/unet/opencv/opencv/sources/modules/core/src/lapack.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
limits
-
Eigen/Core
-
Eigen/Eigenvalues
-
opencv2/core/eigen.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/eigen.hpp

D:/unet/opencv/opencv/sources/modules/core/src/lda.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
iostream
-
map
-
set
-

D:/unet/opencv/opencv/sources/modules/core/src/logger.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core/utils/configuration.private.hpp
-
opencv2/core/utils/logger.hpp
-
utils/logtagmanager.hpp
D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagmanager.hpp
utils/logtagconfigparser.hpp
D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagconfigparser.hpp
sstream
-
iostream
-
fstream
-
android/log.h
-

D:/unet/opencv/opencv/sources/modules/core/src/lpsolver.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
climits
-
algorithm
-

D:/unet/opencv/opencv/sources/modules/core/src/lut.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
convert.hpp
D:/unet/opencv/opencv/sources/modules/core/src/convert.hpp
opencv2/core/openvx/ovx_defs.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/openvx/ovx_defs.hpp

D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
atomic
-
limits
-
iostream
-
mathfuncs.hpp
D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs.hpp

D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs.hpp

D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs_core.dispatch.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
mathfuncs_core.simd.hpp
D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs_core.simd.hpp
mathfuncs_core.simd_declarations.hpp
D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs_core.simd_declarations.hpp

D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs_core.simd.hpp
mathfuncs.hpp
D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs.hpp

D:/unet/opencv/opencv/sources/modules/core/src/matmul.dispatch.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
opencv2/core/opencl/runtime/opencl_clamdblas.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opencl/runtime/opencl_clamdblas.hpp
opencv2/core/opencl/runtime/opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opencl/runtime/opencl_core.hpp
intel_gpu_gemm.inl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/intel_gpu_gemm.inl.hpp
matmul.simd.hpp
D:/unet/opencv/opencv/sources/modules/core/src/matmul.simd.hpp
matmul.simd_declarations.hpp
D:/unet/opencv/opencv/sources/modules/core/src/matmul.simd_declarations.hpp

D:/unet/opencv/opencv/sources/modules/core/src/matmul.simd.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/matrix.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
bufferpool.impl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/bufferpool.impl.hpp

D:/unet/opencv/opencv/sources/modules/core/src/matrix_c.cpp
opencv2/core/mat.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/mat.hpp
opencv2/core/types_c.h
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/types_c.h
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/matrix_decomp.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/matrix_expressions.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/matrix_iterator.cpp
opencv2/core/mat.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/mat.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/matrix_operations.cpp
opencv2/core/mat.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/mat.hpp
opencv2/core/types_c.h
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/types_c.h
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/matrix_sparse.cpp
opencv2/core/mat.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/mat.hpp
opencv2/core/types_c.h
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/types_c.h
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/matrix_wrap.cpp
opencv2/core/mat.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/mat.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/mean.dispatch.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
opencv2/core/openvx/ovx_defs.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/openvx/ovx_defs.hpp
stat.hpp
D:/unet/opencv/opencv/sources/modules/core/src/stat.hpp
mean.simd.hpp
D:/unet/opencv/opencv/sources/modules/core/src/mean.simd.hpp
mean.simd_declarations.hpp
D:/unet/opencv/opencv/sources/modules/core/src/mean.simd_declarations.hpp

D:/unet/opencv/opencv/sources/modules/core/src/mean.simd.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
stat.hpp
D:/unet/opencv/opencv/sources/modules/core/src/stat.hpp

D:/unet/opencv/opencv/sources/modules/core/src/merge.dispatch.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
merge.simd.hpp
D:/unet/opencv/opencv/sources/modules/core/src/merge.simd.hpp
merge.simd_declarations.hpp
D:/unet/opencv/opencv/sources/modules/core/src/merge.simd_declarations.hpp

D:/unet/opencv/opencv/sources/modules/core/src/merge.simd.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/minmax.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
opencv2/core/openvx/ovx_defs.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/openvx/ovx_defs.hpp
stat.hpp
D:/unet/opencv/opencv/sources/modules/core/src/stat.hpp

D:/unet/opencv/opencv/sources/modules/core/src/norm.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
stat.hpp
D:/unet/opencv/opencv/sources/modules/core/src/stat.hpp

D:/unet/opencv/opencv/sources/modules/core/src/ocl.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
list
-
map
-
deque
-
set
-
string
-
sstream
-
iostream
-
fstream
-
inttypes.h
-
opencv2/core/utils/configuration.private.hpp
-
opencv2/core/utils/logger.defines.hpp
-
opencv2/core/utils/logger.hpp
-
opencv2/core/ocl_genbase.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/ocl_genbase.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
opencv2/core/utils/lock.private.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/utils/lock.private.hpp
opencv2/core/utils/filesystem.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/utils/filesystem.hpp
opencv2/core/utils/filesystem.private.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/utils/filesystem.private.hpp
opencv2/core/utils/allocator_stats.impl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/utils/allocator_stats.impl.hpp
opencv2/core/bufferpool.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/bufferpool.hpp
opencv2/core/opencl/runtime/opencl_clamdblas.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opencl/runtime/opencl_clamdblas.hpp
opencv2/core/opencl/runtime/opencl_clamdfft.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opencl/runtime/opencl_clamdfft.hpp
opencv2/core/opencl/runtime/opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opencl/runtime/opencl_core.hpp
ocl_deprecated.hpp
D:/unet/opencv/opencv/sources/modules/core/src/ocl_deprecated.hpp
opencv2/core/opencl/runtime/opencl_svm_20.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opencl/runtime/opencl_svm_20.hpp
opencv2/core/opencl/runtime/opencl_svm_hsa_extension.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opencl/runtime/opencl_svm_hsa_extension.hpp
opencv2/core/opencl/opencl_svm.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opencl/opencl_svm.hpp
umatrix.hpp
D:/unet/opencv/opencv/sources/modules/core/src/umatrix.hpp

D:/unet/opencv/opencv/sources/modules/core/src/ocl_deprecated.hpp
OpenCL/opencl.h
-
CL/opencl.h
-
dlfcn.h
-
windows.h
-
synchapi.h
-
dlfcn.h
-
stdio.h
-

D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_clamdblas_impl.hpp

D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_clamdfft_impl.hpp

D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_core_impl.hpp

D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_core_static_impl.hpp

D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_gl_impl.hpp

D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_gl_static_impl.hpp

D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencl_clamdblas.cpp
../../precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core/opencl/runtime/opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencv2/core/opencl/runtime/opencl_core.hpp
opencv2/core/opencl/runtime/opencl_clamdblas.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencv2/core/opencl/runtime/opencl_clamdblas.hpp
windows.h
-
dlfcn.h
-
stdio.h
-
runtime_common.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/runtime_common.hpp
autogenerated/opencl_clamdblas_impl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_clamdblas_impl.hpp

D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencl_clamdfft.cpp
../../precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core/opencl/runtime/opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencv2/core/opencl/runtime/opencl_core.hpp
opencv2/core/opencl/runtime/opencl_clamdfft.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencv2/core/opencl/runtime/opencl_clamdfft.hpp
windows.h
-
dlfcn.h
-
stdio.h
-
runtime_common.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/runtime_common.hpp
autogenerated/opencl_clamdfft_impl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_clamdfft_impl.hpp

D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencl_core.cpp
../../precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencv2/core.hpp
OpenCL/cl.h
-
CL/cl.h
-
autogenerated/opencl_core_static_impl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_core_static_impl.hpp
opencv2/core/opencl/runtime/opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencv2/core/opencl/runtime/opencl_core.hpp
opencv2/core/opencl/runtime/opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencv2/core/opencl/runtime/opencl_core.hpp
dlfcn.h
-
windows.h
-
dlfcn.h
-
stdio.h
-
runtime_common.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/runtime_common.hpp
autogenerated/opencl_core_impl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_core_impl.hpp
opencv2/core/opencl/runtime/opencl_svm_20.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencv2/core/opencl/runtime/opencl_svm_20.hpp
OpenCL/cl_gl.h
-
CL/cl_gl.h
-
autogenerated/opencl_gl_static_impl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_gl_static_impl.hpp
opencv2/core/opencl/runtime/opencl_gl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencv2/core/opencl/runtime/opencl_gl.hpp
opencv2/core/opencl/runtime/opencl_gl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencv2/core/opencl/runtime/opencl_gl.hpp
autogenerated/opencl_gl_impl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_gl_impl.hpp

D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/runtime_common.hpp

D:/unet/opencv/opencv/sources/modules/core/src/opengl.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
gl_core_3_1.hpp
D:/unet/opencv/opencv/sources/modules/core/src/gl_core_3_1.hpp
GL/gl.h
-
cuda_gl_interop.h
-
opencv2/core/opencl/runtime/opencl_gl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opencl/runtime/opencl_gl.hpp
EGL/egl.h
-
GL/glx.h
-

D:/unet/opencv/opencv/sources/modules/core/src/out.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/ovx.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core/ovx.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/ovx.hpp
opencv2/core/openvx/ovx_defs.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/openvx/ovx_defs.hpp

D:/unet/opencv/opencv/sources/modules/core/src/parallel.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core/utils/configuration.private.hpp
-
opencv2/core/utils/trace.private.hpp
-
windows.h
-
unistd.h
-
stdio.h
-
sys/types.h
-
sys/sysconf.h
-
sys/sysctl.h
-
tbb/tbb.h
D:/unet/opencv/opencv/sources/modules/core/src/tbb/tbb.h
tbb/task.h
D:/unet/opencv/opencv/sources/modules/core/src/tbb/task.h
tbb/tbb_stddef.h
D:/unet/opencv/opencv/sources/modules/core/src/tbb/tbb_stddef.h
tbb/task_arena.h
D:/unet/opencv/opencv/sources/modules/core/src/tbb/task_arena.h
hpx/parallel/algorithms/for_loop.hpp
-
hpx/parallel/execution.hpp
-
hpx/hpx_start.hpp
-
hpx/hpx_suspend.hpp
-
hpx/include/apply.hpp
-
hpx/util/yield_while.hpp
-
hpx/include/threadmanager.hpp
-
omp.h
-
dispatch/dispatch.h
-
pthread.h
-
ppltasks.h
-
ppl.h
-
atomic
-
parallel_impl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/parallel_impl.hpp
opencv2/core/detail/exception_ptr.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/detail/exception_ptr.hpp

D:/unet/opencv/opencv/sources/modules/core/src/parallel_impl.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
parallel_impl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/parallel_impl.hpp
pthread.h
-
opencv2/core/utils/configuration.private.hpp
-
opencv2/core/utils/logger.defines.hpp
-
opencv2/core/utils/logger.hpp
-
opencv2/core/utils/trace.private.hpp
-
atomic
-
thread
-

D:/unet/opencv/opencv/sources/modules/core/src/parallel_impl.hpp

D:/unet/opencv/opencv/sources/modules/core/src/pca.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/persistence.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
persistence.hpp
D:/unet/opencv/opencv/sources/modules/core/src/persistence.hpp
unordered_map
-
iterator
-

D:/unet/opencv/opencv/sources/modules/core/src/persistence.hpp
opencv2/core/types_c.h
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/types_c.h
deque
-
sstream
-
string
-
iterator
-
zlib.h
-

D:/unet/opencv/opencv/sources/modules/core/src/persistence_json.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
persistence.hpp
D:/unet/opencv/opencv/sources/modules/core/src/persistence.hpp

D:/unet/opencv/opencv/sources/modules/core/src/persistence_types.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
persistence.hpp
D:/unet/opencv/opencv/sources/modules/core/src/persistence.hpp

D:/unet/opencv/opencv/sources/modules/core/src/persistence_xml.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
persistence.hpp
D:/unet/opencv/opencv/sources/modules/core/src/persistence.hpp

D:/unet/opencv/opencv/sources/modules/core/src/persistence_yml.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
persistence.hpp
D:/unet/opencv/opencv/sources/modules/core/src/persistence.hpp

D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/opencv_modules.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/opencv_modules.hpp
cvconfig.h
D:/unet/opencv/opencv/sources/modules/core/src/cvconfig.h
opencv2/core/utility.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/utility.hpp
opencv2/core/core_c.h
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/core_c.h
opencv2/core/cuda.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/cuda.hpp
opencv2/core/opengl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opengl.hpp
opencv2/core/va_intel.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/va_intel.hpp
opencv2/core/private.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/private.hpp
opencv2/core/private.cuda.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/private.cuda.hpp
opencv2/core/ocl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/ocl.hpp
assert.h
-
ctype.h
-
float.h
-
limits.h
-
math.h
-
stdarg.h
-
stdio.h
-
stdlib.h
-
string.h
-
algorithm
-
cmath
-
cstdlib
-
limits
-
float.h
-
cstring
-
cassert
-
opencv2/core/hal/hal.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/hal/hal.hpp
opencv2/core/hal/intrin.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/hal/intrin.hpp
opencv2/core/sse_utils.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/sse_utils.hpp
opencv2/core/neon_utils.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/neon_utils.hpp
opencv2/core/vsx_utils.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/vsx_utils.hpp
hal_replacement.hpp
D:/unet/opencv/opencv/sources/modules/core/src/hal_replacement.hpp

D:/unet/opencv/opencv/sources/modules/core/src/rand.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/softfloat.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core/softfloat.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/softfloat.hpp

D:/unet/opencv/opencv/sources/modules/core/src/split.dispatch.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
split.simd.hpp
D:/unet/opencv/opencv/sources/modules/core/src/split.simd.hpp
split.simd_declarations.hpp
D:/unet/opencv/opencv/sources/modules/core/src/split.simd_declarations.hpp

D:/unet/opencv/opencv/sources/modules/core/src/split.simd.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/stat.dispatch.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
stat.simd.hpp
D:/unet/opencv/opencv/sources/modules/core/src/stat.simd.hpp
stat.simd_declarations.hpp
D:/unet/opencv/opencv/sources/modules/core/src/stat.simd_declarations.hpp

D:/unet/opencv/opencv/sources/modules/core/src/stat.hpp
opencv2/core/mat.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/mat.hpp

D:/unet/opencv/opencv/sources/modules/core/src/stat.simd.hpp
opencv2/core/hal/intrin.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/hal/intrin.hpp

D:/unet/opencv/opencv/sources/modules/core/src/stat_c.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/stl.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/sum.dispatch.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
stat.hpp
D:/unet/opencv/opencv/sources/modules/core/src/stat.hpp
sum.simd.hpp
D:/unet/opencv/opencv/sources/modules/core/src/sum.simd.hpp
sum.simd_declarations.hpp
D:/unet/opencv/opencv/sources/modules/core/src/sum.simd_declarations.hpp

D:/unet/opencv/opencv/sources/modules/core/src/sum.simd.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
stat.hpp
D:/unet/opencv/opencv/sources/modules/core/src/stat.hpp

D:/unet/opencv/opencv/sources/modules/core/src/system.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
iostream
-
ostream
-
opencv2/core/utils/configuration.private.hpp
-
opencv2/core/utils/trace.private.hpp
-
opencv2/core/utils/logger.hpp
-
opencv2/core/utils/tls.hpp
-
opencv2/core/utils/instrumentation.hpp
-
exception
-
cstdlib
-
unistd.h
-
fcntl.h
-
elf.h
-
linux/auxvec.h
-
cpu-features.h
-
sys/auxv.h
D:/unet/opencv/opencv/sources/modules/core/src/sys/auxv.h
windows.h
-
synchapi.h
-
fibersapi.h
-
tchar.h
-
wrl/client.h
-
windows.storage.h
-
pthread.h
-
sys/time.h
-
time.h
-
mach/mach.h
-
mach/mach_time.h
-
omp.h
D:/unet/opencv/opencv/sources/modules/core/src/omp.h
unistd.h
-
stdio.h
-
sys/types.h
-
sys/sysconf.h
-
android/log.h
-
intrin.h
-
version_string.inc
D:/unet/opencv/opencv/sources/modules/core/src/version_string.inc

D:/unet/opencv/opencv/sources/modules/core/src/tables.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/trace.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core/utils/trace.hpp
-
opencv2/core/utils/trace.private.hpp
-
opencv2/core/utils/configuration.private.hpp
-
opencv2/core/opencl/ocl_defs.hpp
-
cstdarg
-
sstream
-
ostream
-
fstream
-

D:/unet/opencv/opencv/sources/modules/core/src/types.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp

D:/unet/opencv/opencv/sources/modules/core/src/umatrix.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
umatrix.hpp
D:/unet/opencv/opencv/sources/modules/core/src/umatrix.hpp
opencv2/core/utils/tls.hpp
-

D:/unet/opencv/opencv/sources/modules/core/src/umatrix.hpp

D:/unet/opencv/opencv/sources/modules/core/src/utils/datafile.cpp
../precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv_data_config.hpp
D:/unet/opencv/opencv/sources/modules/core/src/utils/opencv_data_config.hpp
vector
-
fstream
-
opencv2/core/utils/logger.defines.hpp
-
opencv2/core/utils/logger.hpp
D:/unet/opencv/opencv/sources/modules/core/src/utils/opencv2/core/utils/logger.hpp
opencv2/core/utils/filesystem.hpp
D:/unet/opencv/opencv/sources/modules/core/src/utils/opencv2/core/utils/filesystem.hpp
opencv2/core/utils/configuration.private.hpp
-
windows.h
-
dlfcn.h
-
TargetConditionals.h
-
dlfcn.h
-

D:/unet/opencv/opencv/sources/modules/core/src/utils/filesystem.cpp
../precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
opencv2/core/utils/configuration.private.hpp
-
opencv2/core/utils/logger.hpp
-
opencv2/core/utils/filesystem.private.hpp
D:/unet/opencv/opencv/sources/modules/core/src/utils/opencv2/core/utils/filesystem.private.hpp
opencv2/core/utils/filesystem.hpp
D:/unet/opencv/opencv/sources/modules/core/src/utils/opencv2/core/utils/filesystem.hpp
iostream
-
windows.h
-
direct.h
-
sys/types.h
-
sys/stat.h
-
errno.h
-
io.h
-
stdio.h
-
sys/types.h
-
sys/stat.h
-
fcntl.h
-
unistd.h
-
errno.h
-

D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagconfig.hpp
opencv2/core/utils/logger.defines.hpp
-
string
-

D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagconfigparser.cpp
../precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
logtagconfigparser.hpp
D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagconfigparser.hpp

D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagconfigparser.hpp
string
-
vector
-
functional
-
opencv2/core/utils/logtag.hpp
-
logtagconfig.hpp
D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagconfig.hpp

D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagmanager.cpp
../precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
logtagmanager.hpp
D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagmanager.hpp
logtagconfigparser.hpp
D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagconfigparser.hpp

D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagmanager.hpp
string
-
algorithm
-
functional
-
unordered_map
-
mutex
-
memory
-
opencv2/core/utils/logtag.hpp
-
logtagconfig.hpp
D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagconfig.hpp

D:/unet/opencv/opencv/sources/modules/core/src/utils/samples.cpp
../precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
vector
-
opencv2/core/utils/logger.defines.hpp
-
opencv2/core/utils/logger.hpp
D:/unet/opencv/opencv/sources/modules/core/src/utils/opencv2/core/utils/logger.hpp
opencv2/core/utils/filesystem.hpp
D:/unet/opencv/opencv/sources/modules/core/src/utils/opencv2/core/utils/filesystem.hpp

D:/unet/opencv/opencv/sources/modules/core/src/va_intel.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp
va/va.h
-
opencv2/core/opencl/runtime/opencl_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/opencl/runtime/opencl_core.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core.hpp
opencv2/core/ocl.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencv2/core/ocl.hpp
opencl_kernels_core.hpp
D:/unet/opencv/opencv/sources/modules/core/src/opencl_kernels_core.hpp
CL/va_ext.h
-

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Cholesky
Core
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Core
Jacobi
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Jacobi
src/Core/util/DisableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/DisableStupidWarnings.h
src/Cholesky/LLT.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Cholesky/LLT.h
src/Cholesky/LDLT.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Cholesky/LDLT.h
mkl_lapacke.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/mkl_lapacke.h
src/misc/lapacke.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/lapacke.h
src/Cholesky/LLT_LAPACKE.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Cholesky/LLT_LAPACKE.h
src/Core/util/ReenableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ReenableStupidWarnings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Core
src/Core/util/DisableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/DisableStupidWarnings.h
cuda_runtime.h
-
new
-
src/Core/util/Macros.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Macros.h
complex
-
src/Core/util/MKL_support.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/MKL_support.h
malloc.h
-
immintrin.h
-
mmintrin.h
-
emmintrin.h
-
xmmintrin.h
-
pmmintrin.h
-
tmmintrin.h
-
smmintrin.h
-
nmmintrin.h
-
immintrin.h
-
altivec.h
-
altivec.h
-
arm_neon.h
-
vecintrin.h
-
vector_types.h
-
host_defines.h
-
cuda_fp16.h
-
omp.h
-
cerrno
-
cstddef
-
cstdlib
-
cmath
-
cassert
-
functional
-
iosfwd
-
cstring
-
string
-
limits
-
climits
-
algorithm
-
type_traits
-
iostream
-
intrin.h
-
src/Core/util/Constants.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Constants.h
src/Core/util/Meta.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Meta.h
src/Core/util/ForwardDeclarations.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ForwardDeclarations.h
src/Core/util/StaticAssert.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/StaticAssert.h
src/Core/util/XprHelper.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/XprHelper.h
src/Core/util/Memory.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Memory.h
src/Core/NumTraits.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/NumTraits.h
src/Core/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MathFunctions.h
src/Core/GenericPacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/GenericPacketMath.h
src/Core/MathFunctionsImpl.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MathFunctionsImpl.h
src/Core/arch/Default/ConjHelper.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/Default/ConjHelper.h
src/Core/arch/SSE/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/PacketMath.h
src/Core/arch/AVX/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/PacketMath.h
src/Core/arch/AVX512/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX512/PacketMath.h
src/Core/arch/AVX512/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX512/MathFunctions.h
src/Core/arch/SSE/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/PacketMath.h
src/Core/arch/SSE/Complex.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/Complex.h
src/Core/arch/SSE/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/MathFunctions.h
src/Core/arch/AVX/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/PacketMath.h
src/Core/arch/AVX/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/MathFunctions.h
src/Core/arch/AVX/Complex.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/Complex.h
src/Core/arch/AVX/TypeCasting.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/TypeCasting.h
src/Core/arch/SSE/TypeCasting.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/TypeCasting.h
src/Core/arch/SSE/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/PacketMath.h
src/Core/arch/SSE/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/MathFunctions.h
src/Core/arch/SSE/Complex.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/Complex.h
src/Core/arch/SSE/TypeCasting.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/TypeCasting.h
src/Core/arch/AltiVec/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AltiVec/PacketMath.h
src/Core/arch/AltiVec/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AltiVec/MathFunctions.h
src/Core/arch/AltiVec/Complex.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AltiVec/Complex.h
src/Core/arch/NEON/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/NEON/PacketMath.h
src/Core/arch/NEON/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/NEON/MathFunctions.h
src/Core/arch/NEON/Complex.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/NEON/Complex.h
src/Core/arch/ZVector/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/ZVector/PacketMath.h
src/Core/arch/ZVector/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/ZVector/MathFunctions.h
src/Core/arch/ZVector/Complex.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/ZVector/Complex.h
src/Core/arch/CUDA/Half.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/Half.h
src/Core/arch/CUDA/PacketMathHalf.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/PacketMathHalf.h
src/Core/arch/CUDA/TypeCasting.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/TypeCasting.h
src/Core/arch/CUDA/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/PacketMath.h
src/Core/arch/CUDA/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/MathFunctions.h
src/Core/arch/Default/Settings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/Default/Settings.h
src/Core/functors/TernaryFunctors.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/TernaryFunctors.h
src/Core/functors/BinaryFunctors.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/BinaryFunctors.h
src/Core/functors/UnaryFunctors.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/UnaryFunctors.h
src/Core/functors/NullaryFunctors.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/NullaryFunctors.h
src/Core/functors/StlFunctors.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/StlFunctors.h
src/Core/functors/AssignmentFunctors.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/AssignmentFunctors.h
src/Core/arch/CUDA/Complex.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/Complex.h
src/Core/IO.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/IO.h
src/Core/DenseCoeffsBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DenseCoeffsBase.h
src/Core/DenseBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DenseBase.h
src/Core/MatrixBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MatrixBase.h
src/Core/EigenBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/EigenBase.h
src/Core/Product.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Product.h
src/Core/CoreEvaluators.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CoreEvaluators.h
src/Core/AssignEvaluator.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/AssignEvaluator.h
src/Core/Assign.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Assign.h
src/Core/ArrayBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ArrayBase.h
src/Core/util/BlasUtil.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/BlasUtil.h
src/Core/DenseStorage.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DenseStorage.h
src/Core/NestByValue.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/NestByValue.h
src/Core/ReturnByValue.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ReturnByValue.h
src/Core/NoAlias.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/NoAlias.h
src/Core/PlainObjectBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/PlainObjectBase.h
src/Core/Matrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Matrix.h
src/Core/Array.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Array.h
src/Core/CwiseTernaryOp.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseTernaryOp.h
src/Core/CwiseBinaryOp.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseBinaryOp.h
src/Core/CwiseUnaryOp.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseUnaryOp.h
src/Core/CwiseNullaryOp.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseNullaryOp.h
src/Core/CwiseUnaryView.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseUnaryView.h
src/Core/SelfCwiseBinaryOp.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SelfCwiseBinaryOp.h
src/Core/Dot.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Dot.h
src/Core/StableNorm.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/StableNorm.h
src/Core/Stride.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Stride.h
src/Core/MapBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MapBase.h
src/Core/Map.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Map.h
src/Core/Ref.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Ref.h
src/Core/Block.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Block.h
src/Core/VectorBlock.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/VectorBlock.h
src/Core/Transpose.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Transpose.h
src/Core/DiagonalMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DiagonalMatrix.h
src/Core/Diagonal.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Diagonal.h
src/Core/DiagonalProduct.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DiagonalProduct.h
src/Core/Redux.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Redux.h
src/Core/Visitor.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Visitor.h
src/Core/Fuzzy.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Fuzzy.h
src/Core/Swap.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Swap.h
src/Core/CommaInitializer.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CommaInitializer.h
src/Core/GeneralProduct.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/GeneralProduct.h
src/Core/Solve.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Solve.h
src/Core/Inverse.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Inverse.h
src/Core/SolverBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SolverBase.h
src/Core/PermutationMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/PermutationMatrix.h
src/Core/Transpositions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Transpositions.h
src/Core/TriangularMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/TriangularMatrix.h
src/Core/SelfAdjointView.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SelfAdjointView.h
src/Core/products/GeneralBlockPanelKernel.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralBlockPanelKernel.h
src/Core/products/Parallelizer.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/Parallelizer.h
src/Core/ProductEvaluators.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ProductEvaluators.h
src/Core/products/GeneralMatrixVector.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixVector.h
src/Core/products/GeneralMatrixMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrix.h
src/Core/SolveTriangular.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SolveTriangular.h
src/Core/products/GeneralMatrixMatrixTriangular.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrixTriangular.h
src/Core/products/SelfadjointMatrixVector.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixVector.h
src/Core/products/SelfadjointMatrixMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixMatrix.h
src/Core/products/SelfadjointProduct.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointProduct.h
src/Core/products/SelfadjointRank2Update.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointRank2Update.h
src/Core/products/TriangularMatrixVector.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixVector.h
src/Core/products/TriangularMatrixMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixMatrix.h
src/Core/products/TriangularSolverMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularSolverMatrix.h
src/Core/products/TriangularSolverVector.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularSolverVector.h
src/Core/BandMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/BandMatrix.h
src/Core/CoreIterators.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CoreIterators.h
src/Core/ConditionEstimator.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ConditionEstimator.h
src/Core/BooleanRedux.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/BooleanRedux.h
src/Core/Select.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Select.h
src/Core/VectorwiseOp.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/VectorwiseOp.h
src/Core/Random.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Random.h
src/Core/Replicate.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Replicate.h
src/Core/Reverse.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Reverse.h
src/Core/ArrayWrapper.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ArrayWrapper.h
src/Core/products/GeneralMatrixMatrix_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrix_BLAS.h
src/Core/products/GeneralMatrixVector_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixVector_BLAS.h
src/Core/products/GeneralMatrixMatrixTriangular_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrixTriangular_BLAS.h
src/Core/products/SelfadjointMatrixMatrix_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixMatrix_BLAS.h
src/Core/products/SelfadjointMatrixVector_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixVector_BLAS.h
src/Core/products/TriangularMatrixMatrix_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixMatrix_BLAS.h
src/Core/products/TriangularMatrixVector_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixVector_BLAS.h
src/Core/products/TriangularSolverMatrix_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularSolverMatrix_BLAS.h
src/Core/Assign_MKL.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Assign_MKL.h
src/Core/GlobalFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/GlobalFunctions.h
src/Core/util/ReenableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ReenableStupidWarnings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Eigenvalues
Core
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Core
src/Core/util/DisableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/DisableStupidWarnings.h
Cholesky
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Cholesky
Jacobi
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Jacobi
Householder
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Householder
LU
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/LU
Geometry
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Geometry
src/misc/RealSvd2x2.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/RealSvd2x2.h
src/Eigenvalues/Tridiagonalization.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/Tridiagonalization.h
src/Eigenvalues/RealSchur.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/RealSchur.h
src/Eigenvalues/EigenSolver.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/EigenSolver.h
src/Eigenvalues/SelfAdjointEigenSolver.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/SelfAdjointEigenSolver.h
src/Eigenvalues/GeneralizedSelfAdjointEigenSolver.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/GeneralizedSelfAdjointEigenSolver.h
src/Eigenvalues/HessenbergDecomposition.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/HessenbergDecomposition.h
src/Eigenvalues/ComplexSchur.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/ComplexSchur.h
src/Eigenvalues/ComplexEigenSolver.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/ComplexEigenSolver.h
src/Eigenvalues/RealQZ.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/RealQZ.h
src/Eigenvalues/GeneralizedEigenSolver.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/GeneralizedEigenSolver.h
src/Eigenvalues/MatrixBaseEigenvalues.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/MatrixBaseEigenvalues.h
mkl_lapacke.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/mkl_lapacke.h
src/misc/lapacke.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/lapacke.h
src/Eigenvalues/RealSchur_LAPACKE.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/RealSchur_LAPACKE.h
src/Eigenvalues/ComplexSchur_LAPACKE.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/ComplexSchur_LAPACKE.h
src/Eigenvalues/SelfAdjointEigenSolver_LAPACKE.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/SelfAdjointEigenSolver_LAPACKE.h
src/Core/util/ReenableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ReenableStupidWarnings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Geometry
Core
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Core
src/Core/util/DisableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/DisableStupidWarnings.h
SVD
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/SVD
LU
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/LU
limits
-
src/Geometry/OrthoMethods.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/OrthoMethods.h
src/Geometry/EulerAngles.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/EulerAngles.h
src/Geometry/Homogeneous.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Homogeneous.h
src/Geometry/RotationBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/RotationBase.h
src/Geometry/Rotation2D.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Rotation2D.h
src/Geometry/Quaternion.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Quaternion.h
src/Geometry/AngleAxis.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/AngleAxis.h
src/Geometry/Transform.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Transform.h
src/Geometry/Translation.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Translation.h
src/Geometry/Scaling.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Scaling.h
src/Geometry/Hyperplane.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Hyperplane.h
src/Geometry/ParametrizedLine.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/ParametrizedLine.h
src/Geometry/AlignedBox.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/AlignedBox.h
src/Geometry/Umeyama.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Umeyama.h
src/Geometry/arch/Geometry_SSE.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/arch/Geometry_SSE.h
src/Core/util/ReenableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ReenableStupidWarnings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Householder
Core
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Core
src/Core/util/DisableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/DisableStupidWarnings.h
src/Householder/Householder.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Householder/Householder.h
src/Householder/HouseholderSequence.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Householder/HouseholderSequence.h
src/Householder/BlockHouseholder.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Householder/BlockHouseholder.h
src/Core/util/ReenableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ReenableStupidWarnings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Jacobi
Core
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Core
src/Core/util/DisableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/DisableStupidWarnings.h
src/Jacobi/Jacobi.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Jacobi/Jacobi.h
src/Core/util/ReenableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ReenableStupidWarnings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/LU
Core
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Core
src/Core/util/DisableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/DisableStupidWarnings.h
src/misc/Kernel.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/Kernel.h
src/misc/Image.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/Image.h
src/LU/FullPivLU.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/LU/FullPivLU.h
src/LU/PartialPivLU.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/LU/PartialPivLU.h
mkl_lapacke.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/mkl_lapacke.h
src/misc/lapacke.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/lapacke.h
src/LU/PartialPivLU_LAPACKE.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/LU/PartialPivLU_LAPACKE.h
src/LU/Determinant.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/LU/Determinant.h
src/LU/InverseImpl.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/LU/InverseImpl.h
src/LU/arch/Inverse_SSE.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/LU/arch/Inverse_SSE.h
src/Core/util/ReenableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ReenableStupidWarnings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/QR
Core
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Core
src/Core/util/DisableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/DisableStupidWarnings.h
Cholesky
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Cholesky
Jacobi
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Jacobi
Householder
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Householder
src/QR/HouseholderQR.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/QR/HouseholderQR.h
src/QR/FullPivHouseholderQR.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/QR/FullPivHouseholderQR.h
src/QR/ColPivHouseholderQR.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/QR/ColPivHouseholderQR.h
src/QR/CompleteOrthogonalDecomposition.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/QR/CompleteOrthogonalDecomposition.h
mkl_lapacke.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/mkl_lapacke.h
src/misc/lapacke.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/lapacke.h
src/QR/HouseholderQR_LAPACKE.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/QR/HouseholderQR_LAPACKE.h
src/QR/ColPivHouseholderQR_LAPACKE.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/QR/ColPivHouseholderQR_LAPACKE.h
src/Core/util/ReenableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ReenableStupidWarnings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/SVD
QR
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/QR
Householder
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Householder
Jacobi
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Jacobi
src/Core/util/DisableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/DisableStupidWarnings.h
src/misc/RealSvd2x2.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/RealSvd2x2.h
src/SVD/UpperBidiagonalization.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/SVD/UpperBidiagonalization.h
src/SVD/SVDBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/SVD/SVDBase.h
src/SVD/JacobiSVD.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/SVD/JacobiSVD.h
src/SVD/BDCSVD.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/SVD/BDCSVD.h
mkl_lapacke.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/mkl_lapacke.h
src/misc/lapacke.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/lapacke.h
src/SVD/JacobiSVD_LAPACKE.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/SVD/JacobiSVD_LAPACKE.h
src/Core/util/ReenableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ReenableStupidWarnings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Cholesky/LDLT.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Cholesky/LLT.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Cholesky/LLT_LAPACKE.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Array.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ArrayBase.h
../plugins/CommonCwiseUnaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/CommonCwiseUnaryOps.h
../plugins/MatrixCwiseUnaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/MatrixCwiseUnaryOps.h
../plugins/ArrayCwiseUnaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/ArrayCwiseUnaryOps.h
../plugins/CommonCwiseBinaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/CommonCwiseBinaryOps.h
../plugins/MatrixCwiseBinaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/MatrixCwiseBinaryOps.h
../plugins/ArrayCwiseBinaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/ArrayCwiseBinaryOps.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ArrayWrapper.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Assign.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/AssignEvaluator.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Assign_MKL.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/BandMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Block.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/BooleanRedux.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CommaInitializer.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ConditionEstimator.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CoreEvaluators.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CoreIterators.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseBinaryOp.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseNullaryOp.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseTernaryOp.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseUnaryOp.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseUnaryView.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DenseBase.h
../plugins/BlockMethods.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/BlockMethods.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DenseCoeffsBase.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DenseStorage.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Diagonal.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DiagonalMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DiagonalProduct.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Dot.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/EigenBase.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Fuzzy.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/GeneralProduct.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/GenericPacketMath.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/GlobalFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/IO.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Inverse.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Map.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MapBase.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MathFunctionsImpl.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Matrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MatrixBase.h
../plugins/CommonCwiseUnaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/CommonCwiseUnaryOps.h
../plugins/CommonCwiseBinaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/CommonCwiseBinaryOps.h
../plugins/MatrixCwiseUnaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/MatrixCwiseUnaryOps.h
../plugins/MatrixCwiseBinaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/MatrixCwiseBinaryOps.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/NestByValue.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/NoAlias.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/NumTraits.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/PermutationMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/PlainObjectBase.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Product.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ProductEvaluators.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Random.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Redux.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Ref.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Replicate.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ReturnByValue.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Reverse.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Select.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SelfAdjointView.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SelfCwiseBinaryOp.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Solve.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SolveTriangular.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SolverBase.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/StableNorm.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Stride.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Swap.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Transpose.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Transpositions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/TriangularMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/VectorBlock.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/VectorwiseOp.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Visitor.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/Complex.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/PacketMath.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/TypeCasting.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX512/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX512/PacketMath.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AltiVec/Complex.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AltiVec/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AltiVec/PacketMath.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/Complex.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/Half.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/PacketMath.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/PacketMathHalf.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/TypeCasting.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/Default/ConjHelper.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/Default/Settings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/NEON/Complex.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/NEON/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/NEON/PacketMath.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/Complex.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/PacketMath.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/TypeCasting.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/ZVector/Complex.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/ZVector/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/ZVector/PacketMath.h
stdint.h
-

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/AssignmentFunctors.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/BinaryFunctors.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/NullaryFunctors.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/StlFunctors.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/TernaryFunctors.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/UnaryFunctors.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralBlockPanelKernel.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrixTriangular.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrixTriangular_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrix_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixVector.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixVector_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/Parallelizer.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixMatrix_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixVector.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixVector_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointProduct.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointRank2Update.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixMatrix_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixVector.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixVector_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularSolverMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularSolverMatrix_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularSolverVector.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/BlasUtil.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Constants.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/DisableStupidWarnings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ForwardDeclarations.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/MKL_support.h
mkl.h
-
../../misc/blas.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/blas.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Macros.h
cstdlib
-
iostream
-

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Memory.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Meta.h
cfloat
-
math_constants.h
-
cstdint
-

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ReenableStupidWarnings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/StaticAssert.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/XprHelper.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/./ComplexSchur.h
./HessenbergDecomposition.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/././HessenbergDecomposition.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/./HessenbergDecomposition.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/./RealQZ.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/./RealSchur.h
./HessenbergDecomposition.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/././HessenbergDecomposition.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/./Tridiagonalization.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/ComplexEigenSolver.h
./ComplexSchur.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/./ComplexSchur.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/ComplexSchur.h
./HessenbergDecomposition.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/./HessenbergDecomposition.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/ComplexSchur_LAPACKE.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/EigenSolver.h
./RealSchur.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/./RealSchur.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/GeneralizedEigenSolver.h
./RealQZ.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/./RealQZ.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/GeneralizedSelfAdjointEigenSolver.h
./Tridiagonalization.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/./Tridiagonalization.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/HessenbergDecomposition.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/MatrixBaseEigenvalues.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/RealQZ.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/RealSchur.h
./HessenbergDecomposition.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/./HessenbergDecomposition.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/RealSchur_LAPACKE.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/SelfAdjointEigenSolver.h
./Tridiagonalization.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/./Tridiagonalization.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/SelfAdjointEigenSolver_LAPACKE.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Eigenvalues/Tridiagonalization.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/AlignedBox.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/AngleAxis.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/EulerAngles.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Homogeneous.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Hyperplane.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/OrthoMethods.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/ParametrizedLine.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Quaternion.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Rotation2D.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/RotationBase.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Scaling.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Transform.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Translation.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/Umeyama.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Geometry/arch/Geometry_SSE.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Householder/BlockHouseholder.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Householder/Householder.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Householder/HouseholderSequence.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Jacobi/Jacobi.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/LU/Determinant.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/LU/FullPivLU.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/LU/InverseImpl.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/LU/PartialPivLU.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/LU/PartialPivLU_LAPACKE.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/LU/arch/Inverse_SSE.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/QR/ColPivHouseholderQR.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/QR/ColPivHouseholderQR_LAPACKE.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/QR/CompleteOrthogonalDecomposition.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/QR/FullPivHouseholderQR.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/QR/HouseholderQR.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/QR/HouseholderQR_LAPACKE.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/SVD/BDCSVD.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/SVD/JacobiSVD.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/SVD/JacobiSVD_LAPACKE.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/SVD/SVDBase.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/SVD/UpperBidiagonalization.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/Image.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/Kernel.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/RealSvd2x2.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/blas.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/lapacke.h
lapacke_config.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/lapacke_config.h
stdlib.h
-
complex.h
-
complex.h
-
lapacke_mangling.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/lapacke_mangling.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/lapacke_mangling.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/ArrayCwiseBinaryOps.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/ArrayCwiseUnaryOps.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/BlockMethods.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/CommonCwiseBinaryOps.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/CommonCwiseUnaryOps.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/MatrixCwiseBinaryOps.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/MatrixCwiseUnaryOps.h

custom_hal.hpp

cv_cpu_config.h

cvconfig.h

modules/core/arithm.simd_declarations.hpp
opencv2/core/private/cv_cpu_include_simd_declarations.hpp
modules/core/opencv2/core/private/cv_cpu_include_simd_declarations.hpp
opencv2/core/private/cv_cpu_include_simd_declarations.hpp
modules/core/opencv2/core/private/cv_cpu_include_simd_declarations.hpp

modules/core/convert.simd_declarations.hpp
opencv2/core/private/cv_cpu_include_simd_declarations.hpp
modules/core/opencv2/core/private/cv_cpu_include_simd_declarations.hpp

modules/core/convert_scale.simd_declarations.hpp
opencv2/core/private/cv_cpu_include_simd_declarations.hpp
modules/core/opencv2/core/private/cv_cpu_include_simd_declarations.hpp

modules/core/count_non_zero.simd_declarations.hpp
opencv2/core/private/cv_cpu_include_simd_declarations.hpp
modules/core/opencv2/core/private/cv_cpu_include_simd_declarations.hpp

modules/core/mathfuncs_core.simd_declarations.hpp
opencv2/core/private/cv_cpu_include_simd_declarations.hpp
modules/core/opencv2/core/private/cv_cpu_include_simd_declarations.hpp
opencv2/core/private/cv_cpu_include_simd_declarations.hpp
modules/core/opencv2/core/private/cv_cpu_include_simd_declarations.hpp

modules/core/matmul.simd_declarations.hpp
opencv2/core/private/cv_cpu_include_simd_declarations.hpp
modules/core/opencv2/core/private/cv_cpu_include_simd_declarations.hpp
opencv2/core/private/cv_cpu_include_simd_declarations.hpp
modules/core/opencv2/core/private/cv_cpu_include_simd_declarations.hpp

modules/core/mean.simd_declarations.hpp
opencv2/core/private/cv_cpu_include_simd_declarations.hpp
modules/core/opencv2/core/private/cv_cpu_include_simd_declarations.hpp

modules/core/merge.simd_declarations.hpp
opencv2/core/private/cv_cpu_include_simd_declarations.hpp
modules/core/opencv2/core/private/cv_cpu_include_simd_declarations.hpp

modules/core/opencl_kernels_core.hpp
opencv2/core/ocl.hpp
modules/core/opencv2/core/ocl.hpp
opencv2/core/ocl_genbase.hpp
modules/core/opencv2/core/ocl_genbase.hpp
opencv2/core/opencl/ocl_defs.hpp
modules/core/opencv2/core/opencl/ocl_defs.hpp

modules/core/split.simd_declarations.hpp
opencv2/core/private/cv_cpu_include_simd_declarations.hpp
modules/core/opencv2/core/private/cv_cpu_include_simd_declarations.hpp

modules/core/stat.simd_declarations.hpp
opencv2/core/private/cv_cpu_include_simd_declarations.hpp
modules/core/opencv2/core/private/cv_cpu_include_simd_declarations.hpp
opencv2/core/private/cv_cpu_include_simd_declarations.hpp
modules/core/opencv2/core/private/cv_cpu_include_simd_declarations.hpp

modules/core/sum.simd_declarations.hpp
opencv2/core/private/cv_cpu_include_simd_declarations.hpp
modules/core/opencv2/core/private/cv_cpu_include_simd_declarations.hpp

modules/core/version_string.inc

opencv2/opencv_modules.hpp

opencv_data_config.hpp

