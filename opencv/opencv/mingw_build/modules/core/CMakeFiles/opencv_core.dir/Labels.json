{"sources": [{"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/ocl_defs.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/opencl_info.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/opencl_svm.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_clamdblas.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_clamdfft.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_core.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_core_wrappers.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_gl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_gl_wrappers.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_clamdblas.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_clamdfft.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_core.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_core_wrappers.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_gl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_gl_wrappers.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_svm_20.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_svm_definitions.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_svm_hsa_extension.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/block.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/border_interpolate.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/color.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/common.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/datamov_utils.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/dynamic_smem.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/emulation.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/filters.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/funcattrib.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/functional.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/limits.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/reduce.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/saturate_cast.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/scan.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/simd_functions.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/transform.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/type_traits.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/utility.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/vec_distance.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/vec_math.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/vec_traits.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/warp.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/warp_reduce.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/warp_shuffle.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/detail/color_detail.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/detail/reduce.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/detail/reduce_key_val.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/detail/transform_detail.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/detail/type_traits_detail.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/detail/vec_distance_detail.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/affine.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/async.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/base.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/bindings_utils.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/bufferpool.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/check.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/core.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/core_c.h", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda.inl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda_stream_accessor.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda_types.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_dispatch.h", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_helper.h", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvdef.h", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.inl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd_wrapper.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/directx.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/eigen.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/fast_math.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/hal.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/interface.h", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_avx.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_avx512.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_cpp.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_forward.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_msa.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_neon.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_sse.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_sse_em.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_vsx.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_wasm.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/msa_macros.h", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/simd_utils.impl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.inl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/matx.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/neon_utils.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl_genbase.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opengl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/operations.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/optim.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ovx.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/persistence.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/private.cuda.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/private.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/saturate.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/simd_intrinsics.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/softfloat.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/sse_utils.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/traits.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/types.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/types_c.h", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utility.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/allocator_stats.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/allocator_stats.impl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/configuration.private.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/filesystem.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/filesystem.private.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/instrumentation.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/lock.private.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/logger.defines.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/logger.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/logtag.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/tls.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/trace.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/trace.private.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/va_intel.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/version.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/vsx_utils.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/detail/async_promise.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/detail/exception_ptr.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/version_string.inc", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/algorithm.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/alloc.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/arithm.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/arithm.dispatch.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/array.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/async.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/batch_distance.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/bindings_utils.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/channels.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/check.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/command_line_parser.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/conjugate_gradient.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/convert.dispatch.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/convert_c.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/convert_scale.dispatch.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/copy.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/count_non_zero.dispatch.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/cuda_gpu_mat.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/cuda_host_mem.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/cuda_info.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/cuda_stream.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/datastructs.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/directx.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/downhill_simplex.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/dxt.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/gl_core_3_1.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/glob.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/hal_internal.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/kmeans.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/lapack.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/lda.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/logger.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/lpsolver.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/lut.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs_core.dispatch.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/matmul.dispatch.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/matrix.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/matrix_c.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/matrix_decomp.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/matrix_expressions.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/matrix_iterator.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/matrix_operations.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/matrix_sparse.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/matrix_wrap.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/mean.dispatch.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/merge.dispatch.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/minmax.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/norm.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/ocl.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencl_clamdblas.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencl_clamdfft.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencl_core.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opengl.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/out.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/ovx.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/parallel.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/parallel_impl.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/pca.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/persistence.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/persistence_json.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/persistence_types.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/persistence_xml.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/persistence_yml.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/rand.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/softfloat.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/split.dispatch.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/stat.dispatch.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/stat_c.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/stl.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/sum.dispatch.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/system.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/tables.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/trace.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/types.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/umatrix.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/utils/datafile.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/utils/filesystem.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagconfigparser.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagmanager.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/utils/samples.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/va_intel.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/arithm.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/convert.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/copymakeborder.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/copyset.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/cvtclr_dx.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/fft.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/flip.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/gemm.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/halfconvert.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/inrange.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/intel_gemm.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/lut.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/meanstddev.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/minmaxloc.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/mixchannels.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/mulspectrums.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/normalize.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/reduce.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/reduce2.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/repeat.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/set_identity.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/split_merge.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/transpose.cl", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/opencl_kernels_core.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/opencl_kernels_core.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/arithm.simd.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/arithm_ipp.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/bufferpool.impl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/convert.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/convert.simd.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/convert_scale.simd.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/count_non_zero.simd.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/directx.inc.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/gl_core_3_1.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/hal_internal.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/hal_replacement.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/intel_gpu_gemm.inl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs_core.simd.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/matmul.simd.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/mean.simd.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/merge.simd.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/ocl_deprecated.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_clamdblas_impl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_clamdfft_impl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_core_impl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_core_static_impl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_gl_impl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_gl_static_impl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/runtime_common.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/parallel_impl.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/persistence.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/split.simd.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/stat.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/stat.simd.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/sum.simd.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/umatrix.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagconfig.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagconfigparser.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagmanager.hpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/arithm.sse4_1.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/matmul.sse4_1.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/stat.sse4_2.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/mathfuncs_core.avx.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/mathfuncs_core.avx2.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/stat.avx2.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/arithm.avx2.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/convert.avx2.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/convert_scale.avx2.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/count_non_zero.avx2.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/matmul.avx2.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/mean.avx2.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/merge.avx2.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/split.avx2.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/sum.avx2.cpp", "labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/cvconfig.h"}, {"file": "D:/unet/opencv/opencv/mingw_build/opencv2/opencv_modules.hpp"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/vs_version.rc"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/core/opencl_kernels_core.cpp.rule"}], "target": {"labels": ["Main", "opencv_core", "<PERSON><PERSON><PERSON>"], "name": "opencv_core"}}