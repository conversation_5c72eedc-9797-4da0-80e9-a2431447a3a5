#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

D:/unet/opencv/opencv/mingw_build/modules/photo/opencl_kernels_photo.cpp
opencv2/core.hpp
D:/unet/opencv/opencv/mingw_build/modules/photo/opencv2/core.hpp
cvconfig.h
D:/unet/opencv/opencv/mingw_build/modules/photo/cvconfig.h
opencl_kernels_photo.hpp
D:/unet/opencv/opencv/mingw_build/modules/photo/opencl_kernels_photo.hpp

D:/unet/opencv/opencv/mingw_build/modules/photo/opencl_kernels_photo.hpp
opencv2/core/ocl.hpp
D:/unet/opencv/opencv/mingw_build/modules/photo/opencv2/core/ocl.hpp
opencv2/core/ocl_genbase.hpp
D:/unet/opencv/opencv/mingw_build/modules/photo/opencv2/core/ocl_genbase.hpp
opencv2/core/opencl/ocl_defs.hpp
D:/unet/opencv/opencv/mingw_build/modules/photo/opencv2/core/opencl/ocl_defs.hpp

D:/unet/opencv/opencv/mingw_build/modules/photo/opencv_photo_main.cpp
windows.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/cvdef.h
opencv2/core/version.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/version.hpp
opencv2/core/base.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/base.hpp
opencv2/core/cvstd.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/cvstd.hpp
opencv2/core/traits.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/traits.hpp
opencv2/core/matx.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/matx.hpp
opencv2/core/types.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/types.hpp
opencv2/core/mat.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/mat.hpp
opencv2/core/persistence.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/persistence.hpp
opencv2/core/operations.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/operations.hpp
opencv2/core/cvstd.inl.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/cvstd.inl.hpp
opencv2/core/utility.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/utility.hpp
opencv2/core/optim.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/optim.hpp
opencv2/core/ovx.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/ovx.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/base.hpp
opencv2/opencv_modules.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/opencv_modules.hpp
climits
-
algorithm
-
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
opencv2/core/cvstd.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvstd.hpp
opencv2/core/neon_utils.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/neon_utils.hpp
opencv2/core/vsx_utils.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/vsx_utils.hpp
opencv2/core/check.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/check.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/bufferpool.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/check.hpp
opencv2/core/base.hpp
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/core_c.h
opencv2/core/types_c.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/types_c.h
cxcore.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cxcore.h
cxcore.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cxcore.h
opencv2/core/utility.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/utility.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp
opencv2/core/cuda_types.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cuda_types.hpp
opencv2/opencv.hpp
-
opencv2/core/cuda.inl.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cuda.inl.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda.inl.hpp
opencv2/core/cuda.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cuda.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/common.hpp
cuda_runtime.h
-
opencv2/core/cuda_types.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/opencv2/core/cuda_types.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/opencv2/core/cvdef.h
opencv2/core/base.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/opencv2/core/base.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda_stream_accessor.hpp
cuda_runtime.h
-
opencv2/core/cuda.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cuda.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda_types.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_dispatch.h
cv_cpu_config.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_config.h
cv_cpu_helper.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_helper.h
emmintrin.h
-
pmmintrin.h
-
tmmintrin.h
-
smmintrin.h
-
nmmintrin.h
-
nmmintrin.h
-
popcntintrin.h
-
immintrin.h
-
arm_neon.h
-
immintrin.h
-
immintrin.h
-
immintrin.h
-
Intrin.h
-
arm_neon.h
-
arm_neon.h
-
arm_neon.h
-
altivec.h
-
hal/msa_macros.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/msa_macros.h
wasm_simd128.h
-
emmintrin.h
-
Intrin.h
-
arm_neon.h
-
arm_neon.h
-
altivec.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_helper.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvdef.h
cvconfig.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvconfig.h
limits.h
-
opencv2/core/hal/interface.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/hal/interface.h
cv_cpu_dispatch.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_dispatch.h
intrin.h
-
array
-
cstdint
-
stdint.h
-
stdint.h
-
opencv2/core/fast_math.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/fast_math.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
cstddef
-
cstring
-
cctype
-
string
-
algorithm
-
utility
-
cstdlib
-
cmath
-
cvstd_wrapper.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd_wrapper.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.inl.hpp
complex
-
ostream
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd_wrapper.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
string
-
memory
-
type_traits
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/eigen.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/fast_math.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
cmath
-
fastmath.h
-
math.h
-
emmintrin.h
-
altivec.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/interface.h
cstddef
-
stddef.h
-
stdbool.h
-
cstdint
-
stdint.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/msa_macros.h
msa.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/msa.h
stdint.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.hpp
opencv2/core/matx.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/matx.hpp
opencv2/core/types.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/types.hpp
opencv2/core/bufferpool.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/bufferpool.hpp
type_traits
-
opencv2/core/mat.inl.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/mat.inl.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.inl.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/matx.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
opencv2/core/base.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/base.hpp
opencv2/core/traits.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/traits.hpp
opencv2/core/saturate.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/saturate.hpp
initializer_list
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/neon_utils.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl_genbase.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/ocl_defs.hpp
opencv2/core/utility.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/opencv2/core/utility.hpp
cvconfig.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/cvconfig.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/operations.hpp
cstdio
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/optim.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ovx.hpp
cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvdef.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/persistence.hpp
opencv2/core/types.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/types.hpp
opencv2/core/mat.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/mat.hpp
opencv2/opencv.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/opencv.hpp
time.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/private.cuda.hpp
cvconfig.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvconfig.h
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
opencv2/core/base.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/base.hpp
opencv2/core/cuda.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cuda.hpp
cuda.h
-
cuda_runtime.h
-
cuda_fp16.h
-
cuda_fp16.h
-
npp.h
-
opencv2/core/cuda_stream_accessor.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cuda_stream_accessor.hpp
opencv2/core/cuda/common.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cuda/common.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/private.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp
cvconfig.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvconfig.h
opencv2/core/utils/trace.hpp
-
opencv2/core/utils/instrumentation.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/utils/instrumentation.hpp
Eigen/Core
-
opencv2/core/eigen.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/eigen.hpp
ippversion.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ippversion.h
ippicv.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ippicv.h
ipp.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ipp.h
ipp.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ipp.h
iw++/iw.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/iw++/iw.hpp
iw/iw_ll.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/iw/iw_ll.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/saturate.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
opencv2/core/fast_math.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/fast_math.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/traits.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/types.hpp
climits
-
cfloat
-
vector
-
limits
-
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
opencv2/core/cvstd.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvstd.hpp
opencv2/core/matx.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/matx.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/types_c.h
ipl.h
-
ipl/ipl.h
-
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
assert.h
-
stdlib.h
-
string.h
-
float.h
-
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utility.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp
ostream
-
functional
-
mutex
-
opencv2/core/utils/instrumentation.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/utils/instrumentation.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/instrumentation.hpp
opencv2/core/utility.hpp
-
opencv2/core/utils/tls.hpp
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/tls.hpp
opencv2/core/utility.hpp
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/trace.hpp
opencv2/core/cvdef.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/version.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/vsx_utils.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
assert.h
-

D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/opencv2/core.hpp

D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/imgproc_c.h
opencv2/imgproc/types_c.h
D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/opencv2/imgproc/types_c.h

D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/types_c.h
opencv2/core/core_c.h
D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/opencv2/core/core_c.h

D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/opencv2/core.hpp
opencv2/imgproc.hpp
D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/opencv2/imgproc.hpp

D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo/cuda.hpp
opencv2/core/cuda.hpp
D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo/opencv2/core/cuda.hpp

D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo/legacy/constants_c.h

D:/unet/opencv/opencv/sources/modules/photo/src/align.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencv2/photo.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/photo.hpp
opencv2/imgproc.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/imgproc.hpp
hdr_common.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/hdr_common.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/arrays.hpp
opencv2/core/base.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/core/base.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/calibrate.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencv2/photo.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/photo.hpp
opencv2/imgproc.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/imgproc.hpp
hdr_common.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/hdr_common.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/contrast_preserve.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencv2/photo.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/photo.hpp
cmath
-
vector
-
limits
-
contrast_preserve.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/contrast_preserve.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/contrast_preserve.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencv2/photo.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/photo.hpp
cmath
-
vector
-

D:/unet/opencv/opencv/sources/modules/photo/src/denoise_tvl1.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
vector
-
algorithm
-

D:/unet/opencv/opencv/sources/modules/photo/src/denoising.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
fast_nlmeans_denoising_invoker.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_denoising_invoker.hpp
fast_nlmeans_multi_denoising_invoker.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_multi_denoising_invoker.hpp
fast_nlmeans_denoising_opencl.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_denoising_opencl.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/denoising.cuda.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencv2/photo/cuda.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/photo/cuda.hpp
opencv2/core/private.cuda.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/core/private.cuda.hpp
opencv2/opencv_modules.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/opencv_modules.hpp
opencv2/cudaarithm.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/cudaarithm.hpp
opencv2/cudaimgproc.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/cudaimgproc.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_denoising_invoker.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
limits
-
fast_nlmeans_denoising_invoker_commons.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_denoising_invoker_commons.hpp
arrays.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/arrays.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_denoising_invoker_commons.hpp
float.h
-

D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_denoising_opencl.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencl_kernels_photo.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencl_kernels_photo.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_multi_denoising_invoker.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
limits
-
fast_nlmeans_denoising_invoker_commons.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_denoising_invoker_commons.hpp
arrays.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/arrays.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/hdr_common.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencv2/photo.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/photo.hpp
hdr_common.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/hdr_common.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/hdr_common.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencv2/photo.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/photo.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/inpaint.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencv2/imgproc/imgproc_c.h
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/imgproc/imgproc_c.h
opencv2/photo/legacy/constants_c.h
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/photo/legacy/constants_c.h

D:/unet/opencv/opencv/sources/modules/photo/src/merge.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencv2/photo.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/photo.hpp
opencv2/imgproc.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/imgproc.hpp
hdr_common.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/hdr_common.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/npr.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencv2/photo.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/photo.hpp
iostream
-
stdlib.h
-
npr.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/npr.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/npr.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencv2/photo.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/photo.hpp
iostream
-
stdlib.h
-
limits
-
math.h
D:/unet/opencv/opencv/sources/modules/photo/src/math.h

D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencv2/core/private.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/core/private.hpp
opencv2/core/utility.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/core/utility.hpp
opencv2/photo.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/photo.hpp
opencv2/core/ocl.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/core/ocl.hpp
opencv2/imgproc.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/imgproc.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/seamless_cloning.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencv2/photo.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/photo.hpp
seamless_cloning.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/seamless_cloning.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/seamless_cloning.hpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencv2/photo.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/photo.hpp
vector
-

D:/unet/opencv/opencv/sources/modules/photo/src/seamless_cloning_impl.cpp
seamless_cloning.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/seamless_cloning.hpp

D:/unet/opencv/opencv/sources/modules/photo/src/tonemap.cpp
precomp.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp
opencv2/photo.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/photo.hpp
opencv2/imgproc.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/opencv2/imgproc.hpp
hdr_common.hpp
D:/unet/opencv/opencv/sources/modules/photo/src/hdr_common.hpp

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/Core
src/Core/util/DisableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/DisableStupidWarnings.h
cuda_runtime.h
-
new
-
src/Core/util/Macros.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Macros.h
complex
-
src/Core/util/MKL_support.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/MKL_support.h
malloc.h
-
immintrin.h
-
mmintrin.h
-
emmintrin.h
-
xmmintrin.h
-
pmmintrin.h
-
tmmintrin.h
-
smmintrin.h
-
nmmintrin.h
-
immintrin.h
-
altivec.h
-
altivec.h
-
arm_neon.h
-
vecintrin.h
-
vector_types.h
-
host_defines.h
-
cuda_fp16.h
-
omp.h
-
cerrno
-
cstddef
-
cstdlib
-
cmath
-
cassert
-
functional
-
iosfwd
-
cstring
-
string
-
limits
-
climits
-
algorithm
-
type_traits
-
iostream
-
intrin.h
-
src/Core/util/Constants.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Constants.h
src/Core/util/Meta.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Meta.h
src/Core/util/ForwardDeclarations.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ForwardDeclarations.h
src/Core/util/StaticAssert.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/StaticAssert.h
src/Core/util/XprHelper.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/XprHelper.h
src/Core/util/Memory.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Memory.h
src/Core/NumTraits.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/NumTraits.h
src/Core/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MathFunctions.h
src/Core/GenericPacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/GenericPacketMath.h
src/Core/MathFunctionsImpl.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MathFunctionsImpl.h
src/Core/arch/Default/ConjHelper.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/Default/ConjHelper.h
src/Core/arch/SSE/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/PacketMath.h
src/Core/arch/AVX/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/PacketMath.h
src/Core/arch/AVX512/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX512/PacketMath.h
src/Core/arch/AVX512/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX512/MathFunctions.h
src/Core/arch/SSE/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/PacketMath.h
src/Core/arch/SSE/Complex.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/Complex.h
src/Core/arch/SSE/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/MathFunctions.h
src/Core/arch/AVX/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/PacketMath.h
src/Core/arch/AVX/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/MathFunctions.h
src/Core/arch/AVX/Complex.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/Complex.h
src/Core/arch/AVX/TypeCasting.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/TypeCasting.h
src/Core/arch/SSE/TypeCasting.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/TypeCasting.h
src/Core/arch/SSE/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/PacketMath.h
src/Core/arch/SSE/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/MathFunctions.h
src/Core/arch/SSE/Complex.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/Complex.h
src/Core/arch/SSE/TypeCasting.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/TypeCasting.h
src/Core/arch/AltiVec/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AltiVec/PacketMath.h
src/Core/arch/AltiVec/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AltiVec/MathFunctions.h
src/Core/arch/AltiVec/Complex.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AltiVec/Complex.h
src/Core/arch/NEON/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/NEON/PacketMath.h
src/Core/arch/NEON/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/NEON/MathFunctions.h
src/Core/arch/NEON/Complex.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/NEON/Complex.h
src/Core/arch/ZVector/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/ZVector/PacketMath.h
src/Core/arch/ZVector/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/ZVector/MathFunctions.h
src/Core/arch/ZVector/Complex.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/ZVector/Complex.h
src/Core/arch/CUDA/Half.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/Half.h
src/Core/arch/CUDA/PacketMathHalf.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/PacketMathHalf.h
src/Core/arch/CUDA/TypeCasting.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/TypeCasting.h
src/Core/arch/CUDA/PacketMath.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/PacketMath.h
src/Core/arch/CUDA/MathFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/MathFunctions.h
src/Core/arch/Default/Settings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/Default/Settings.h
src/Core/functors/TernaryFunctors.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/TernaryFunctors.h
src/Core/functors/BinaryFunctors.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/BinaryFunctors.h
src/Core/functors/UnaryFunctors.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/UnaryFunctors.h
src/Core/functors/NullaryFunctors.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/NullaryFunctors.h
src/Core/functors/StlFunctors.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/StlFunctors.h
src/Core/functors/AssignmentFunctors.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/AssignmentFunctors.h
src/Core/arch/CUDA/Complex.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/Complex.h
src/Core/IO.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/IO.h
src/Core/DenseCoeffsBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DenseCoeffsBase.h
src/Core/DenseBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DenseBase.h
src/Core/MatrixBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MatrixBase.h
src/Core/EigenBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/EigenBase.h
src/Core/Product.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Product.h
src/Core/CoreEvaluators.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CoreEvaluators.h
src/Core/AssignEvaluator.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/AssignEvaluator.h
src/Core/Assign.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Assign.h
src/Core/ArrayBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ArrayBase.h
src/Core/util/BlasUtil.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/BlasUtil.h
src/Core/DenseStorage.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DenseStorage.h
src/Core/NestByValue.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/NestByValue.h
src/Core/ReturnByValue.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ReturnByValue.h
src/Core/NoAlias.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/NoAlias.h
src/Core/PlainObjectBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/PlainObjectBase.h
src/Core/Matrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Matrix.h
src/Core/Array.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Array.h
src/Core/CwiseTernaryOp.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseTernaryOp.h
src/Core/CwiseBinaryOp.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseBinaryOp.h
src/Core/CwiseUnaryOp.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseUnaryOp.h
src/Core/CwiseNullaryOp.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseNullaryOp.h
src/Core/CwiseUnaryView.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseUnaryView.h
src/Core/SelfCwiseBinaryOp.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SelfCwiseBinaryOp.h
src/Core/Dot.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Dot.h
src/Core/StableNorm.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/StableNorm.h
src/Core/Stride.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Stride.h
src/Core/MapBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MapBase.h
src/Core/Map.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Map.h
src/Core/Ref.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Ref.h
src/Core/Block.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Block.h
src/Core/VectorBlock.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/VectorBlock.h
src/Core/Transpose.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Transpose.h
src/Core/DiagonalMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DiagonalMatrix.h
src/Core/Diagonal.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Diagonal.h
src/Core/DiagonalProduct.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DiagonalProduct.h
src/Core/Redux.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Redux.h
src/Core/Visitor.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Visitor.h
src/Core/Fuzzy.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Fuzzy.h
src/Core/Swap.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Swap.h
src/Core/CommaInitializer.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CommaInitializer.h
src/Core/GeneralProduct.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/GeneralProduct.h
src/Core/Solve.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Solve.h
src/Core/Inverse.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Inverse.h
src/Core/SolverBase.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SolverBase.h
src/Core/PermutationMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/PermutationMatrix.h
src/Core/Transpositions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Transpositions.h
src/Core/TriangularMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/TriangularMatrix.h
src/Core/SelfAdjointView.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SelfAdjointView.h
src/Core/products/GeneralBlockPanelKernel.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralBlockPanelKernel.h
src/Core/products/Parallelizer.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/Parallelizer.h
src/Core/ProductEvaluators.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ProductEvaluators.h
src/Core/products/GeneralMatrixVector.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixVector.h
src/Core/products/GeneralMatrixMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrix.h
src/Core/SolveTriangular.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SolveTriangular.h
src/Core/products/GeneralMatrixMatrixTriangular.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrixTriangular.h
src/Core/products/SelfadjointMatrixVector.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixVector.h
src/Core/products/SelfadjointMatrixMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixMatrix.h
src/Core/products/SelfadjointProduct.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointProduct.h
src/Core/products/SelfadjointRank2Update.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointRank2Update.h
src/Core/products/TriangularMatrixVector.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixVector.h
src/Core/products/TriangularMatrixMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixMatrix.h
src/Core/products/TriangularSolverMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularSolverMatrix.h
src/Core/products/TriangularSolverVector.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularSolverVector.h
src/Core/BandMatrix.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/BandMatrix.h
src/Core/CoreIterators.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CoreIterators.h
src/Core/ConditionEstimator.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ConditionEstimator.h
src/Core/BooleanRedux.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/BooleanRedux.h
src/Core/Select.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Select.h
src/Core/VectorwiseOp.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/VectorwiseOp.h
src/Core/Random.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Random.h
src/Core/Replicate.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Replicate.h
src/Core/Reverse.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Reverse.h
src/Core/ArrayWrapper.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ArrayWrapper.h
src/Core/products/GeneralMatrixMatrix_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrix_BLAS.h
src/Core/products/GeneralMatrixVector_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixVector_BLAS.h
src/Core/products/GeneralMatrixMatrixTriangular_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrixTriangular_BLAS.h
src/Core/products/SelfadjointMatrixMatrix_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixMatrix_BLAS.h
src/Core/products/SelfadjointMatrixVector_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixVector_BLAS.h
src/Core/products/TriangularMatrixMatrix_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixMatrix_BLAS.h
src/Core/products/TriangularMatrixVector_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixVector_BLAS.h
src/Core/products/TriangularSolverMatrix_BLAS.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularSolverMatrix_BLAS.h
src/Core/Assign_MKL.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Assign_MKL.h
src/Core/GlobalFunctions.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/GlobalFunctions.h
src/Core/util/ReenableStupidWarnings.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ReenableStupidWarnings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Array.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ArrayBase.h
../plugins/CommonCwiseUnaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/CommonCwiseUnaryOps.h
../plugins/MatrixCwiseUnaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/MatrixCwiseUnaryOps.h
../plugins/ArrayCwiseUnaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/ArrayCwiseUnaryOps.h
../plugins/CommonCwiseBinaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/CommonCwiseBinaryOps.h
../plugins/MatrixCwiseBinaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/MatrixCwiseBinaryOps.h
../plugins/ArrayCwiseBinaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/ArrayCwiseBinaryOps.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ArrayWrapper.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Assign.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/AssignEvaluator.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Assign_MKL.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/BandMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Block.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/BooleanRedux.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CommaInitializer.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ConditionEstimator.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CoreEvaluators.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CoreIterators.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseBinaryOp.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseNullaryOp.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseTernaryOp.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseUnaryOp.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/CwiseUnaryView.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DenseBase.h
../plugins/BlockMethods.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/BlockMethods.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DenseCoeffsBase.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DenseStorage.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Diagonal.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DiagonalMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/DiagonalProduct.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Dot.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/EigenBase.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Fuzzy.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/GeneralProduct.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/GenericPacketMath.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/GlobalFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/IO.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Inverse.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Map.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MapBase.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MathFunctionsImpl.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Matrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/MatrixBase.h
../plugins/CommonCwiseUnaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/CommonCwiseUnaryOps.h
../plugins/CommonCwiseBinaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/CommonCwiseBinaryOps.h
../plugins/MatrixCwiseUnaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/MatrixCwiseUnaryOps.h
../plugins/MatrixCwiseBinaryOps.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/MatrixCwiseBinaryOps.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/NestByValue.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/NoAlias.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/NumTraits.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/PermutationMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/PlainObjectBase.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Product.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ProductEvaluators.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Random.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Redux.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Ref.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Replicate.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/ReturnByValue.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Reverse.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Select.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SelfAdjointView.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SelfCwiseBinaryOp.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Solve.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SolveTriangular.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/SolverBase.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/StableNorm.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Stride.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Swap.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Transpose.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Transpositions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/TriangularMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/VectorBlock.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/VectorwiseOp.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/Visitor.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/Complex.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/PacketMath.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX/TypeCasting.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX512/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AVX512/PacketMath.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AltiVec/Complex.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AltiVec/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/AltiVec/PacketMath.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/Complex.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/Half.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/PacketMath.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/PacketMathHalf.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/CUDA/TypeCasting.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/Default/ConjHelper.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/Default/Settings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/NEON/Complex.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/NEON/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/NEON/PacketMath.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/Complex.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/PacketMath.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/SSE/TypeCasting.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/ZVector/Complex.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/ZVector/MathFunctions.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/arch/ZVector/PacketMath.h
stdint.h
-

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/AssignmentFunctors.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/BinaryFunctors.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/NullaryFunctors.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/StlFunctors.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/TernaryFunctors.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/functors/UnaryFunctors.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralBlockPanelKernel.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrixTriangular.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrixTriangular_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixMatrix_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixVector.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/GeneralMatrixVector_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/Parallelizer.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixMatrix_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixVector.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointMatrixVector_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointProduct.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/SelfadjointRank2Update.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixMatrix_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixVector.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularMatrixVector_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularSolverMatrix.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularSolverMatrix_BLAS.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/products/TriangularSolverVector.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/BlasUtil.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Constants.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/DisableStupidWarnings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ForwardDeclarations.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/MKL_support.h
mkl.h
-
../../misc/blas.h
D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/blas.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Macros.h
cstdlib
-
iostream
-

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Memory.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/Meta.h
cfloat
-
math_constants.h
-
cstdint
-

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/ReenableStupidWarnings.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/StaticAssert.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/Core/util/XprHelper.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/misc/blas.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/ArrayCwiseBinaryOps.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/ArrayCwiseUnaryOps.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/BlockMethods.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/CommonCwiseBinaryOps.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/CommonCwiseUnaryOps.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/MatrixCwiseBinaryOps.h

D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3/Eigen/src/plugins/MatrixCwiseUnaryOps.h

cv_cpu_config.h

cvconfig.h

modules/photo/opencl_kernels_photo.hpp
opencv2/core/ocl.hpp
modules/photo/opencv2/core/ocl.hpp
opencv2/core/ocl_genbase.hpp
modules/photo/opencv2/core/ocl_genbase.hpp
opencv2/core/opencl/ocl_defs.hpp
modules/photo/opencv2/core/opencl/ocl_defs.hpp

opencv2/opencv_modules.hpp

