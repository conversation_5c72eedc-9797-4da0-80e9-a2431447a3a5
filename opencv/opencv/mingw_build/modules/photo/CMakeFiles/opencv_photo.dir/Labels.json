{"sources": [{"file": "D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo.hpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo/cuda.hpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo/legacy/constants_c.h", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo/photo.hpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/align.cpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/calibrate.cpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/contrast_preserve.cpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/denoise_tvl1.cpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/denoising.cpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/denoising.cuda.cpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/hdr_common.cpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/inpaint.cpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/merge.cpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/npr.cpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/seamless_cloning.cpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/seamless_cloning_impl.cpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/tonemap.cpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/opencl/nlmeans.cl", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/photo/opencl_kernels_photo.cpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/photo/opencl_kernels_photo.hpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/arrays.hpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/contrast_preserve.hpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_denoising_invoker.hpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_denoising_invoker_commons.hpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_denoising_opencl.hpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_multi_denoising_invoker.hpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/hdr_common.hpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/npr.hpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/photo/src/seamless_cloning.hpp", "labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/cvconfig.h"}, {"file": "D:/unet/opencv/opencv/mingw_build/opencv2/opencv_modules.hpp"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/photo/vs_version.rc"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/photo/opencv_photo_main.cpp"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/photo/opencl_kernels_photo.cpp.rule"}], "target": {"labels": ["Main", "opencv_photo", "<PERSON><PERSON><PERSON>"], "name": "opencv_photo"}}