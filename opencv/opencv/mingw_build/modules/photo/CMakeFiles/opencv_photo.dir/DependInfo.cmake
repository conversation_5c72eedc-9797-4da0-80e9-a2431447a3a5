# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  "RC"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "D:/unet/opencv/opencv/mingw_build/modules/photo/opencl_kernels_photo.cpp" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/opencl_kernels_photo.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/modules/photo/opencv_photo_main.cpp" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/opencv_photo_main.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/photo/src/align.cpp" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/src/align.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/photo/src/calibrate.cpp" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/src/calibrate.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/photo/src/contrast_preserve.cpp" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/src/contrast_preserve.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/photo/src/denoise_tvl1.cpp" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/src/denoise_tvl1.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/photo/src/denoising.cpp" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/photo/src/denoising.cuda.cpp" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cuda.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/photo/src/hdr_common.cpp" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/src/hdr_common.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/photo/src/inpaint.cpp" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/src/inpaint.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/photo/src/merge.cpp" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/src/merge.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/photo/src/npr.cpp" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/src/npr.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/photo/src/seamless_cloning.cpp" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/photo/src/seamless_cloning_impl.cpp" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning_impl.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/photo/src/tonemap.cpp" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/src/tonemap.cpp.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/photo/include"
  "modules/photo"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )
set(CMAKE_DEPENDS_CHECK_RC
  "D:/unet/opencv/opencv/mingw_build/modules/photo/vs_version.rc" "D:/unet/opencv/opencv/mingw_build/modules/photo/CMakeFiles/opencv_photo.dir/vs_version.rc.obj"
  )

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_RC
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_RC_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/photo/include"
  "modules/photo"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "D:/unet/opencv/opencv/mingw_build/modules/imgproc/CMakeFiles/opencv_imgproc.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/core/CMakeFiles/opencv_core.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
