# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

# Include any dependencies generated for this target.
include modules/photo/CMakeFiles/opencv_photo.dir/depend.make

# Include the progress variables for this target.
include modules/photo/CMakeFiles/opencv_photo.dir/progress.make

# Include the compile flags for this target's objects.
include modules/photo/CMakeFiles/opencv_photo.dir/flags.make

modules/photo/opencl_kernels_photo.cpp: D:/unet/opencv/opencv/sources/modules/photo/src/opencl/nlmeans.cl
modules/photo/opencl_kernels_photo.cpp: D:/unet/opencv/opencv/sources/cmake/cl2cpp.cmake
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Processing OpenCL kernels (photo)"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && "C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=photo -DCL_DIR=D:/unet/opencv/opencv/sources/modules/photo/src/opencl -DOUTPUT=D:/unet/opencv/opencv/mingw_build/modules/photo/opencl_kernels_photo.cpp -P D:/unet/opencv/opencv/sources/cmake/cl2cpp.cmake

modules/photo/CMakeFiles/opencv_photo.dir/src/align.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/src/align.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/includes_CXX.rsp
modules/photo/CMakeFiles/opencv_photo.dir/src/align.cpp.obj: D:/unet/opencv/opencv/sources/modules/photo/src/align.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object modules/photo/CMakeFiles/opencv_photo.dir/src/align.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_photo.dir\src\align.cpp.obj -c D:\unet\opencv\opencv\sources\modules\photo\src\align.cpp

modules/photo/CMakeFiles/opencv_photo.dir/src/align.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_photo.dir/src/align.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\photo\src\align.cpp > CMakeFiles\opencv_photo.dir\src\align.cpp.i

modules/photo/CMakeFiles/opencv_photo.dir/src/align.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_photo.dir/src/align.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\photo\src\align.cpp -o CMakeFiles\opencv_photo.dir\src\align.cpp.s

modules/photo/CMakeFiles/opencv_photo.dir/src/calibrate.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/src/calibrate.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/includes_CXX.rsp
modules/photo/CMakeFiles/opencv_photo.dir/src/calibrate.cpp.obj: D:/unet/opencv/opencv/sources/modules/photo/src/calibrate.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object modules/photo/CMakeFiles/opencv_photo.dir/src/calibrate.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_photo.dir\src\calibrate.cpp.obj -c D:\unet\opencv\opencv\sources\modules\photo\src\calibrate.cpp

modules/photo/CMakeFiles/opencv_photo.dir/src/calibrate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_photo.dir/src/calibrate.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\photo\src\calibrate.cpp > CMakeFiles\opencv_photo.dir\src\calibrate.cpp.i

modules/photo/CMakeFiles/opencv_photo.dir/src/calibrate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_photo.dir/src/calibrate.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\photo\src\calibrate.cpp -o CMakeFiles\opencv_photo.dir\src\calibrate.cpp.s

modules/photo/CMakeFiles/opencv_photo.dir/src/contrast_preserve.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/src/contrast_preserve.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/includes_CXX.rsp
modules/photo/CMakeFiles/opencv_photo.dir/src/contrast_preserve.cpp.obj: D:/unet/opencv/opencv/sources/modules/photo/src/contrast_preserve.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object modules/photo/CMakeFiles/opencv_photo.dir/src/contrast_preserve.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_photo.dir\src\contrast_preserve.cpp.obj -c D:\unet\opencv\opencv\sources\modules\photo\src\contrast_preserve.cpp

modules/photo/CMakeFiles/opencv_photo.dir/src/contrast_preserve.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_photo.dir/src/contrast_preserve.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\photo\src\contrast_preserve.cpp > CMakeFiles\opencv_photo.dir\src\contrast_preserve.cpp.i

modules/photo/CMakeFiles/opencv_photo.dir/src/contrast_preserve.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_photo.dir/src/contrast_preserve.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\photo\src\contrast_preserve.cpp -o CMakeFiles\opencv_photo.dir\src\contrast_preserve.cpp.s

modules/photo/CMakeFiles/opencv_photo.dir/src/denoise_tvl1.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/src/denoise_tvl1.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/includes_CXX.rsp
modules/photo/CMakeFiles/opencv_photo.dir/src/denoise_tvl1.cpp.obj: D:/unet/opencv/opencv/sources/modules/photo/src/denoise_tvl1.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object modules/photo/CMakeFiles/opencv_photo.dir/src/denoise_tvl1.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_photo.dir\src\denoise_tvl1.cpp.obj -c D:\unet\opencv\opencv\sources\modules\photo\src\denoise_tvl1.cpp

modules/photo/CMakeFiles/opencv_photo.dir/src/denoise_tvl1.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_photo.dir/src/denoise_tvl1.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\photo\src\denoise_tvl1.cpp > CMakeFiles\opencv_photo.dir\src\denoise_tvl1.cpp.i

modules/photo/CMakeFiles/opencv_photo.dir/src/denoise_tvl1.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_photo.dir/src/denoise_tvl1.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\photo\src\denoise_tvl1.cpp -o CMakeFiles\opencv_photo.dir\src\denoise_tvl1.cpp.s

modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/includes_CXX.rsp
modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cpp.obj: D:/unet/opencv/opencv/sources/modules/photo/src/denoising.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_photo.dir\src\denoising.cpp.obj -c D:\unet\opencv\opencv\sources\modules\photo\src\denoising.cpp

modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_photo.dir/src/denoising.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\photo\src\denoising.cpp > CMakeFiles\opencv_photo.dir\src\denoising.cpp.i

modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_photo.dir/src/denoising.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\photo\src\denoising.cpp -o CMakeFiles\opencv_photo.dir\src\denoising.cpp.s

modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cuda.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cuda.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/includes_CXX.rsp
modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cuda.cpp.obj: D:/unet/opencv/opencv/sources/modules/photo/src/denoising.cuda.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cuda.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_photo.dir\src\denoising.cuda.cpp.obj -c D:\unet\opencv\opencv\sources\modules\photo\src\denoising.cuda.cpp

modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cuda.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_photo.dir/src/denoising.cuda.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\photo\src\denoising.cuda.cpp > CMakeFiles\opencv_photo.dir\src\denoising.cuda.cpp.i

modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cuda.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_photo.dir/src/denoising.cuda.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\photo\src\denoising.cuda.cpp -o CMakeFiles\opencv_photo.dir\src\denoising.cuda.cpp.s

modules/photo/CMakeFiles/opencv_photo.dir/src/hdr_common.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/src/hdr_common.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/includes_CXX.rsp
modules/photo/CMakeFiles/opencv_photo.dir/src/hdr_common.cpp.obj: D:/unet/opencv/opencv/sources/modules/photo/src/hdr_common.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object modules/photo/CMakeFiles/opencv_photo.dir/src/hdr_common.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_photo.dir\src\hdr_common.cpp.obj -c D:\unet\opencv\opencv\sources\modules\photo\src\hdr_common.cpp

modules/photo/CMakeFiles/opencv_photo.dir/src/hdr_common.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_photo.dir/src/hdr_common.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\photo\src\hdr_common.cpp > CMakeFiles\opencv_photo.dir\src\hdr_common.cpp.i

modules/photo/CMakeFiles/opencv_photo.dir/src/hdr_common.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_photo.dir/src/hdr_common.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\photo\src\hdr_common.cpp -o CMakeFiles\opencv_photo.dir\src\hdr_common.cpp.s

modules/photo/CMakeFiles/opencv_photo.dir/src/inpaint.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/src/inpaint.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/includes_CXX.rsp
modules/photo/CMakeFiles/opencv_photo.dir/src/inpaint.cpp.obj: D:/unet/opencv/opencv/sources/modules/photo/src/inpaint.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object modules/photo/CMakeFiles/opencv_photo.dir/src/inpaint.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_photo.dir\src\inpaint.cpp.obj -c D:\unet\opencv\opencv\sources\modules\photo\src\inpaint.cpp

modules/photo/CMakeFiles/opencv_photo.dir/src/inpaint.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_photo.dir/src/inpaint.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\photo\src\inpaint.cpp > CMakeFiles\opencv_photo.dir\src\inpaint.cpp.i

modules/photo/CMakeFiles/opencv_photo.dir/src/inpaint.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_photo.dir/src/inpaint.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\photo\src\inpaint.cpp -o CMakeFiles\opencv_photo.dir\src\inpaint.cpp.s

modules/photo/CMakeFiles/opencv_photo.dir/src/merge.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/src/merge.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/includes_CXX.rsp
modules/photo/CMakeFiles/opencv_photo.dir/src/merge.cpp.obj: D:/unet/opencv/opencv/sources/modules/photo/src/merge.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object modules/photo/CMakeFiles/opencv_photo.dir/src/merge.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_photo.dir\src\merge.cpp.obj -c D:\unet\opencv\opencv\sources\modules\photo\src\merge.cpp

modules/photo/CMakeFiles/opencv_photo.dir/src/merge.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_photo.dir/src/merge.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\photo\src\merge.cpp > CMakeFiles\opencv_photo.dir\src\merge.cpp.i

modules/photo/CMakeFiles/opencv_photo.dir/src/merge.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_photo.dir/src/merge.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\photo\src\merge.cpp -o CMakeFiles\opencv_photo.dir\src\merge.cpp.s

modules/photo/CMakeFiles/opencv_photo.dir/src/npr.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/src/npr.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/includes_CXX.rsp
modules/photo/CMakeFiles/opencv_photo.dir/src/npr.cpp.obj: D:/unet/opencv/opencv/sources/modules/photo/src/npr.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object modules/photo/CMakeFiles/opencv_photo.dir/src/npr.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_photo.dir\src\npr.cpp.obj -c D:\unet\opencv\opencv\sources\modules\photo\src\npr.cpp

modules/photo/CMakeFiles/opencv_photo.dir/src/npr.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_photo.dir/src/npr.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\photo\src\npr.cpp > CMakeFiles\opencv_photo.dir\src\npr.cpp.i

modules/photo/CMakeFiles/opencv_photo.dir/src/npr.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_photo.dir/src/npr.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\photo\src\npr.cpp -o CMakeFiles\opencv_photo.dir\src\npr.cpp.s

modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/includes_CXX.rsp
modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning.cpp.obj: D:/unet/opencv/opencv/sources/modules/photo/src/seamless_cloning.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_photo.dir\src\seamless_cloning.cpp.obj -c D:\unet\opencv\opencv\sources\modules\photo\src\seamless_cloning.cpp

modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_photo.dir/src/seamless_cloning.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\photo\src\seamless_cloning.cpp > CMakeFiles\opencv_photo.dir\src\seamless_cloning.cpp.i

modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_photo.dir/src/seamless_cloning.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\photo\src\seamless_cloning.cpp -o CMakeFiles\opencv_photo.dir\src\seamless_cloning.cpp.s

modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning_impl.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning_impl.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/includes_CXX.rsp
modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning_impl.cpp.obj: D:/unet/opencv/opencv/sources/modules/photo/src/seamless_cloning_impl.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning_impl.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_photo.dir\src\seamless_cloning_impl.cpp.obj -c D:\unet\opencv\opencv\sources\modules\photo\src\seamless_cloning_impl.cpp

modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning_impl.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_photo.dir/src/seamless_cloning_impl.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\photo\src\seamless_cloning_impl.cpp > CMakeFiles\opencv_photo.dir\src\seamless_cloning_impl.cpp.i

modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning_impl.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_photo.dir/src/seamless_cloning_impl.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\photo\src\seamless_cloning_impl.cpp -o CMakeFiles\opencv_photo.dir\src\seamless_cloning_impl.cpp.s

modules/photo/CMakeFiles/opencv_photo.dir/src/tonemap.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/src/tonemap.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/includes_CXX.rsp
modules/photo/CMakeFiles/opencv_photo.dir/src/tonemap.cpp.obj: D:/unet/opencv/opencv/sources/modules/photo/src/tonemap.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object modules/photo/CMakeFiles/opencv_photo.dir/src/tonemap.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_photo.dir\src\tonemap.cpp.obj -c D:\unet\opencv\opencv\sources\modules\photo\src\tonemap.cpp

modules/photo/CMakeFiles/opencv_photo.dir/src/tonemap.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_photo.dir/src/tonemap.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\photo\src\tonemap.cpp > CMakeFiles\opencv_photo.dir\src\tonemap.cpp.i

modules/photo/CMakeFiles/opencv_photo.dir/src/tonemap.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_photo.dir/src/tonemap.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\photo\src\tonemap.cpp -o CMakeFiles\opencv_photo.dir\src\tonemap.cpp.s

modules/photo/CMakeFiles/opencv_photo.dir/opencl_kernels_photo.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/opencl_kernels_photo.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/includes_CXX.rsp
modules/photo/CMakeFiles/opencv_photo.dir/opencl_kernels_photo.cpp.obj: modules/photo/opencl_kernels_photo.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object modules/photo/CMakeFiles/opencv_photo.dir/opencl_kernels_photo.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_photo.dir\opencl_kernels_photo.cpp.obj -c D:\unet\opencv\opencv\mingw_build\modules\photo\opencl_kernels_photo.cpp

modules/photo/CMakeFiles/opencv_photo.dir/opencl_kernels_photo.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_photo.dir/opencl_kernels_photo.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\modules\photo\opencl_kernels_photo.cpp > CMakeFiles\opencv_photo.dir\opencl_kernels_photo.cpp.i

modules/photo/CMakeFiles/opencv_photo.dir/opencl_kernels_photo.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_photo.dir/opencl_kernels_photo.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\modules\photo\opencl_kernels_photo.cpp -o CMakeFiles\opencv_photo.dir\opencl_kernels_photo.cpp.s

modules/photo/CMakeFiles/opencv_photo.dir/vs_version.rc.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/vs_version.rc.obj: modules/photo/vs_version.rc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building RC object modules/photo/CMakeFiles/opencv_photo.dir/vs_version.rc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\windres.exe -O coff $(RC_DEFINES) $(RC_INCLUDES) $(RC_FLAGS) D:\unet\opencv\opencv\mingw_build\modules\photo\vs_version.rc CMakeFiles\opencv_photo.dir\vs_version.rc.obj

modules/photo/CMakeFiles/opencv_photo.dir/opencv_photo_main.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/flags.make
modules/photo/CMakeFiles/opencv_photo.dir/opencv_photo_main.cpp.obj: modules/photo/CMakeFiles/opencv_photo.dir/includes_CXX.rsp
modules/photo/CMakeFiles/opencv_photo.dir/opencv_photo_main.cpp.obj: modules/photo/opencv_photo_main.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object modules/photo/CMakeFiles/opencv_photo.dir/opencv_photo_main.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_photo.dir\opencv_photo_main.cpp.obj -c D:\unet\opencv\opencv\mingw_build\modules\photo\opencv_photo_main.cpp

modules/photo/CMakeFiles/opencv_photo.dir/opencv_photo_main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_photo.dir/opencv_photo_main.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\modules\photo\opencv_photo_main.cpp > CMakeFiles\opencv_photo.dir\opencv_photo_main.cpp.i

modules/photo/CMakeFiles/opencv_photo.dir/opencv_photo_main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_photo.dir/opencv_photo_main.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\modules\photo\opencv_photo_main.cpp -o CMakeFiles\opencv_photo.dir\opencv_photo_main.cpp.s

# Object files for target opencv_photo
opencv_photo_OBJECTS = \
"CMakeFiles/opencv_photo.dir/src/align.cpp.obj" \
"CMakeFiles/opencv_photo.dir/src/calibrate.cpp.obj" \
"CMakeFiles/opencv_photo.dir/src/contrast_preserve.cpp.obj" \
"CMakeFiles/opencv_photo.dir/src/denoise_tvl1.cpp.obj" \
"CMakeFiles/opencv_photo.dir/src/denoising.cpp.obj" \
"CMakeFiles/opencv_photo.dir/src/denoising.cuda.cpp.obj" \
"CMakeFiles/opencv_photo.dir/src/hdr_common.cpp.obj" \
"CMakeFiles/opencv_photo.dir/src/inpaint.cpp.obj" \
"CMakeFiles/opencv_photo.dir/src/merge.cpp.obj" \
"CMakeFiles/opencv_photo.dir/src/npr.cpp.obj" \
"CMakeFiles/opencv_photo.dir/src/seamless_cloning.cpp.obj" \
"CMakeFiles/opencv_photo.dir/src/seamless_cloning_impl.cpp.obj" \
"CMakeFiles/opencv_photo.dir/src/tonemap.cpp.obj" \
"CMakeFiles/opencv_photo.dir/opencl_kernels_photo.cpp.obj" \
"CMakeFiles/opencv_photo.dir/vs_version.rc.obj" \
"CMakeFiles/opencv_photo.dir/opencv_photo_main.cpp.obj"

# External object files for target opencv_photo
opencv_photo_EXTERNAL_OBJECTS =

bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/src/align.cpp.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/src/calibrate.cpp.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/src/contrast_preserve.cpp.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/src/denoise_tvl1.cpp.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cpp.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/src/denoising.cuda.cpp.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/src/hdr_common.cpp.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/src/inpaint.cpp.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/src/merge.cpp.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/src/npr.cpp.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning.cpp.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/src/seamless_cloning_impl.cpp.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/src/tonemap.cpp.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/opencl_kernels_photo.cpp.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/vs_version.rc.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/opencv_photo_main.cpp.obj
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/build.make
bin/libopencv_photo420.dll: lib/libopencv_imgproc420.dll.a
bin/libopencv_photo420.dll: lib/libopencv_core420.dll.a
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/linklibs.rsp
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/objects1.rsp
bin/libopencv_photo420.dll: modules/photo/CMakeFiles/opencv_photo.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Linking CXX shared library ..\..\bin\libopencv_photo420.dll"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\opencv_photo.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
modules/photo/CMakeFiles/opencv_photo.dir/build: bin/libopencv_photo420.dll

.PHONY : modules/photo/CMakeFiles/opencv_photo.dir/build

modules/photo/CMakeFiles/opencv_photo.dir/clean:
	cd /d D:\unet\opencv\opencv\mingw_build\modules\photo && $(CMAKE_COMMAND) -P CMakeFiles\opencv_photo.dir\cmake_clean.cmake
.PHONY : modules/photo/CMakeFiles/opencv_photo.dir/clean

modules/photo/CMakeFiles/opencv_photo.dir/depend: modules/photo/opencl_kernels_photo.cpp
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\unet\opencv\opencv\sources D:\unet\opencv\opencv\sources\modules\photo D:\unet\opencv\opencv\mingw_build D:\unet\opencv\opencv\mingw_build\modules\photo D:\unet\opencv\opencv\mingw_build\modules\photo\CMakeFiles\opencv_photo.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : modules/photo/CMakeFiles/opencv_photo.dir/depend

