# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

# Include any dependencies generated for this target.
include modules/video/CMakeFiles/opencv_video.dir/depend.make

# Include the progress variables for this target.
include modules/video/CMakeFiles/opencv_video.dir/progress.make

# Include the compile flags for this target's objects.
include modules/video/CMakeFiles/opencv_video.dir/flags.make

modules/video/opencl_kernels_video.cpp: D:/unet/opencv/opencv/sources/modules/video/src/opencl/bgfg_knn.cl
modules/video/opencl_kernels_video.cpp: D:/unet/opencv/opencv/sources/modules/video/src/opencl/bgfg_mog2.cl
modules/video/opencl_kernels_video.cpp: D:/unet/opencv/opencv/sources/modules/video/src/opencl/dis_flow.cl
modules/video/opencl_kernels_video.cpp: D:/unet/opencv/opencv/sources/modules/video/src/opencl/optical_flow_farneback.cl
modules/video/opencl_kernels_video.cpp: D:/unet/opencv/opencv/sources/modules/video/src/opencl/pyrlk.cl
modules/video/opencl_kernels_video.cpp: D:/unet/opencv/opencv/sources/cmake/cl2cpp.cmake
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Processing OpenCL kernels (video)"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && "C:\Program Files\CMake\bin\cmake.exe" -DMODULE_NAME=video -DCL_DIR=D:/unet/opencv/opencv/sources/modules/video/src/opencl -DOUTPUT=D:/unet/opencv/opencv/mingw_build/modules/video/opencl_kernels_video.cpp -P D:/unet/opencv/opencv/sources/cmake/cl2cpp.cmake

modules/video/CMakeFiles/opencv_video.dir/src/bgfg_KNN.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/flags.make
modules/video/CMakeFiles/opencv_video.dir/src/bgfg_KNN.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/includes_CXX.rsp
modules/video/CMakeFiles/opencv_video.dir/src/bgfg_KNN.cpp.obj: D:/unet/opencv/opencv/sources/modules/video/src/bgfg_KNN.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object modules/video/CMakeFiles/opencv_video.dir/src/bgfg_KNN.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_video.dir\src\bgfg_KNN.cpp.obj -c D:\unet\opencv\opencv\sources\modules\video\src\bgfg_KNN.cpp

modules/video/CMakeFiles/opencv_video.dir/src/bgfg_KNN.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_video.dir/src/bgfg_KNN.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\video\src\bgfg_KNN.cpp > CMakeFiles\opencv_video.dir\src\bgfg_KNN.cpp.i

modules/video/CMakeFiles/opencv_video.dir/src/bgfg_KNN.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_video.dir/src/bgfg_KNN.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\video\src\bgfg_KNN.cpp -o CMakeFiles\opencv_video.dir\src\bgfg_KNN.cpp.s

modules/video/CMakeFiles/opencv_video.dir/src/bgfg_gaussmix2.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/flags.make
modules/video/CMakeFiles/opencv_video.dir/src/bgfg_gaussmix2.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/includes_CXX.rsp
modules/video/CMakeFiles/opencv_video.dir/src/bgfg_gaussmix2.cpp.obj: D:/unet/opencv/opencv/sources/modules/video/src/bgfg_gaussmix2.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object modules/video/CMakeFiles/opencv_video.dir/src/bgfg_gaussmix2.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_video.dir\src\bgfg_gaussmix2.cpp.obj -c D:\unet\opencv\opencv\sources\modules\video\src\bgfg_gaussmix2.cpp

modules/video/CMakeFiles/opencv_video.dir/src/bgfg_gaussmix2.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_video.dir/src/bgfg_gaussmix2.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\video\src\bgfg_gaussmix2.cpp > CMakeFiles\opencv_video.dir\src\bgfg_gaussmix2.cpp.i

modules/video/CMakeFiles/opencv_video.dir/src/bgfg_gaussmix2.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_video.dir/src/bgfg_gaussmix2.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\video\src\bgfg_gaussmix2.cpp -o CMakeFiles\opencv_video.dir\src\bgfg_gaussmix2.cpp.s

modules/video/CMakeFiles/opencv_video.dir/src/camshift.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/flags.make
modules/video/CMakeFiles/opencv_video.dir/src/camshift.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/includes_CXX.rsp
modules/video/CMakeFiles/opencv_video.dir/src/camshift.cpp.obj: D:/unet/opencv/opencv/sources/modules/video/src/camshift.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object modules/video/CMakeFiles/opencv_video.dir/src/camshift.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_video.dir\src\camshift.cpp.obj -c D:\unet\opencv\opencv\sources\modules\video\src\camshift.cpp

modules/video/CMakeFiles/opencv_video.dir/src/camshift.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_video.dir/src/camshift.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\video\src\camshift.cpp > CMakeFiles\opencv_video.dir\src\camshift.cpp.i

modules/video/CMakeFiles/opencv_video.dir/src/camshift.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_video.dir/src/camshift.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\video\src\camshift.cpp -o CMakeFiles\opencv_video.dir\src\camshift.cpp.s

modules/video/CMakeFiles/opencv_video.dir/src/dis_flow.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/flags.make
modules/video/CMakeFiles/opencv_video.dir/src/dis_flow.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/includes_CXX.rsp
modules/video/CMakeFiles/opencv_video.dir/src/dis_flow.cpp.obj: D:/unet/opencv/opencv/sources/modules/video/src/dis_flow.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object modules/video/CMakeFiles/opencv_video.dir/src/dis_flow.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_video.dir\src\dis_flow.cpp.obj -c D:\unet\opencv\opencv\sources\modules\video\src\dis_flow.cpp

modules/video/CMakeFiles/opencv_video.dir/src/dis_flow.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_video.dir/src/dis_flow.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\video\src\dis_flow.cpp > CMakeFiles\opencv_video.dir\src\dis_flow.cpp.i

modules/video/CMakeFiles/opencv_video.dir/src/dis_flow.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_video.dir/src/dis_flow.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\video\src\dis_flow.cpp -o CMakeFiles\opencv_video.dir\src\dis_flow.cpp.s

modules/video/CMakeFiles/opencv_video.dir/src/ecc.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/flags.make
modules/video/CMakeFiles/opencv_video.dir/src/ecc.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/includes_CXX.rsp
modules/video/CMakeFiles/opencv_video.dir/src/ecc.cpp.obj: D:/unet/opencv/opencv/sources/modules/video/src/ecc.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object modules/video/CMakeFiles/opencv_video.dir/src/ecc.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_video.dir\src\ecc.cpp.obj -c D:\unet\opencv\opencv\sources\modules\video\src\ecc.cpp

modules/video/CMakeFiles/opencv_video.dir/src/ecc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_video.dir/src/ecc.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\video\src\ecc.cpp > CMakeFiles\opencv_video.dir\src\ecc.cpp.i

modules/video/CMakeFiles/opencv_video.dir/src/ecc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_video.dir/src/ecc.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\video\src\ecc.cpp -o CMakeFiles\opencv_video.dir\src\ecc.cpp.s

modules/video/CMakeFiles/opencv_video.dir/src/kalman.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/flags.make
modules/video/CMakeFiles/opencv_video.dir/src/kalman.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/includes_CXX.rsp
modules/video/CMakeFiles/opencv_video.dir/src/kalman.cpp.obj: D:/unet/opencv/opencv/sources/modules/video/src/kalman.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object modules/video/CMakeFiles/opencv_video.dir/src/kalman.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_video.dir\src\kalman.cpp.obj -c D:\unet\opencv\opencv\sources\modules\video\src\kalman.cpp

modules/video/CMakeFiles/opencv_video.dir/src/kalman.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_video.dir/src/kalman.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\video\src\kalman.cpp > CMakeFiles\opencv_video.dir\src\kalman.cpp.i

modules/video/CMakeFiles/opencv_video.dir/src/kalman.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_video.dir/src/kalman.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\video\src\kalman.cpp -o CMakeFiles\opencv_video.dir\src\kalman.cpp.s

modules/video/CMakeFiles/opencv_video.dir/src/lkpyramid.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/flags.make
modules/video/CMakeFiles/opencv_video.dir/src/lkpyramid.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/includes_CXX.rsp
modules/video/CMakeFiles/opencv_video.dir/src/lkpyramid.cpp.obj: D:/unet/opencv/opencv/sources/modules/video/src/lkpyramid.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object modules/video/CMakeFiles/opencv_video.dir/src/lkpyramid.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_video.dir\src\lkpyramid.cpp.obj -c D:\unet\opencv\opencv\sources\modules\video\src\lkpyramid.cpp

modules/video/CMakeFiles/opencv_video.dir/src/lkpyramid.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_video.dir/src/lkpyramid.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\video\src\lkpyramid.cpp > CMakeFiles\opencv_video.dir\src\lkpyramid.cpp.i

modules/video/CMakeFiles/opencv_video.dir/src/lkpyramid.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_video.dir/src/lkpyramid.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\video\src\lkpyramid.cpp -o CMakeFiles\opencv_video.dir\src\lkpyramid.cpp.s

modules/video/CMakeFiles/opencv_video.dir/src/optflowgf.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/flags.make
modules/video/CMakeFiles/opencv_video.dir/src/optflowgf.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/includes_CXX.rsp
modules/video/CMakeFiles/opencv_video.dir/src/optflowgf.cpp.obj: D:/unet/opencv/opencv/sources/modules/video/src/optflowgf.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object modules/video/CMakeFiles/opencv_video.dir/src/optflowgf.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_video.dir\src\optflowgf.cpp.obj -c D:\unet\opencv\opencv\sources\modules\video\src\optflowgf.cpp

modules/video/CMakeFiles/opencv_video.dir/src/optflowgf.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_video.dir/src/optflowgf.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\video\src\optflowgf.cpp > CMakeFiles\opencv_video.dir\src\optflowgf.cpp.i

modules/video/CMakeFiles/opencv_video.dir/src/optflowgf.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_video.dir/src/optflowgf.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\video\src\optflowgf.cpp -o CMakeFiles\opencv_video.dir\src\optflowgf.cpp.s

modules/video/CMakeFiles/opencv_video.dir/src/optical_flow_io.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/flags.make
modules/video/CMakeFiles/opencv_video.dir/src/optical_flow_io.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/includes_CXX.rsp
modules/video/CMakeFiles/opencv_video.dir/src/optical_flow_io.cpp.obj: D:/unet/opencv/opencv/sources/modules/video/src/optical_flow_io.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object modules/video/CMakeFiles/opencv_video.dir/src/optical_flow_io.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_video.dir\src\optical_flow_io.cpp.obj -c D:\unet\opencv\opencv\sources\modules\video\src\optical_flow_io.cpp

modules/video/CMakeFiles/opencv_video.dir/src/optical_flow_io.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_video.dir/src/optical_flow_io.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\video\src\optical_flow_io.cpp > CMakeFiles\opencv_video.dir\src\optical_flow_io.cpp.i

modules/video/CMakeFiles/opencv_video.dir/src/optical_flow_io.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_video.dir/src/optical_flow_io.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\video\src\optical_flow_io.cpp -o CMakeFiles\opencv_video.dir\src\optical_flow_io.cpp.s

modules/video/CMakeFiles/opencv_video.dir/src/variational_refinement.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/flags.make
modules/video/CMakeFiles/opencv_video.dir/src/variational_refinement.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/includes_CXX.rsp
modules/video/CMakeFiles/opencv_video.dir/src/variational_refinement.cpp.obj: D:/unet/opencv/opencv/sources/modules/video/src/variational_refinement.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object modules/video/CMakeFiles/opencv_video.dir/src/variational_refinement.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_video.dir\src\variational_refinement.cpp.obj -c D:\unet\opencv\opencv\sources\modules\video\src\variational_refinement.cpp

modules/video/CMakeFiles/opencv_video.dir/src/variational_refinement.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_video.dir/src/variational_refinement.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\video\src\variational_refinement.cpp > CMakeFiles\opencv_video.dir\src\variational_refinement.cpp.i

modules/video/CMakeFiles/opencv_video.dir/src/variational_refinement.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_video.dir/src/variational_refinement.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\video\src\variational_refinement.cpp -o CMakeFiles\opencv_video.dir\src\variational_refinement.cpp.s

modules/video/CMakeFiles/opencv_video.dir/opencl_kernels_video.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/flags.make
modules/video/CMakeFiles/opencv_video.dir/opencl_kernels_video.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/includes_CXX.rsp
modules/video/CMakeFiles/opencv_video.dir/opencl_kernels_video.cpp.obj: modules/video/opencl_kernels_video.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object modules/video/CMakeFiles/opencv_video.dir/opencl_kernels_video.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_video.dir\opencl_kernels_video.cpp.obj -c D:\unet\opencv\opencv\mingw_build\modules\video\opencl_kernels_video.cpp

modules/video/CMakeFiles/opencv_video.dir/opencl_kernels_video.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_video.dir/opencl_kernels_video.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\modules\video\opencl_kernels_video.cpp > CMakeFiles\opencv_video.dir\opencl_kernels_video.cpp.i

modules/video/CMakeFiles/opencv_video.dir/opencl_kernels_video.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_video.dir/opencl_kernels_video.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\modules\video\opencl_kernels_video.cpp -o CMakeFiles\opencv_video.dir\opencl_kernels_video.cpp.s

modules/video/CMakeFiles/opencv_video.dir/vs_version.rc.obj: modules/video/CMakeFiles/opencv_video.dir/flags.make
modules/video/CMakeFiles/opencv_video.dir/vs_version.rc.obj: modules/video/vs_version.rc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building RC object modules/video/CMakeFiles/opencv_video.dir/vs_version.rc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\windres.exe -O coff $(RC_DEFINES) $(RC_INCLUDES) $(RC_FLAGS) D:\unet\opencv\opencv\mingw_build\modules\video\vs_version.rc CMakeFiles\opencv_video.dir\vs_version.rc.obj

modules/video/CMakeFiles/opencv_video.dir/opencv_video_main.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/flags.make
modules/video/CMakeFiles/opencv_video.dir/opencv_video_main.cpp.obj: modules/video/CMakeFiles/opencv_video.dir/includes_CXX.rsp
modules/video/CMakeFiles/opencv_video.dir/opencv_video_main.cpp.obj: modules/video/opencv_video_main.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object modules/video/CMakeFiles/opencv_video.dir/opencv_video_main.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_video.dir\opencv_video_main.cpp.obj -c D:\unet\opencv\opencv\mingw_build\modules\video\opencv_video_main.cpp

modules/video/CMakeFiles/opencv_video.dir/opencv_video_main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_video.dir/opencv_video_main.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\modules\video\opencv_video_main.cpp > CMakeFiles\opencv_video.dir\opencv_video_main.cpp.i

modules/video/CMakeFiles/opencv_video.dir/opencv_video_main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_video.dir/opencv_video_main.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\modules\video\opencv_video_main.cpp -o CMakeFiles\opencv_video.dir\opencv_video_main.cpp.s

# Object files for target opencv_video
opencv_video_OBJECTS = \
"CMakeFiles/opencv_video.dir/src/bgfg_KNN.cpp.obj" \
"CMakeFiles/opencv_video.dir/src/bgfg_gaussmix2.cpp.obj" \
"CMakeFiles/opencv_video.dir/src/camshift.cpp.obj" \
"CMakeFiles/opencv_video.dir/src/dis_flow.cpp.obj" \
"CMakeFiles/opencv_video.dir/src/ecc.cpp.obj" \
"CMakeFiles/opencv_video.dir/src/kalman.cpp.obj" \
"CMakeFiles/opencv_video.dir/src/lkpyramid.cpp.obj" \
"CMakeFiles/opencv_video.dir/src/optflowgf.cpp.obj" \
"CMakeFiles/opencv_video.dir/src/optical_flow_io.cpp.obj" \
"CMakeFiles/opencv_video.dir/src/variational_refinement.cpp.obj" \
"CMakeFiles/opencv_video.dir/opencl_kernels_video.cpp.obj" \
"CMakeFiles/opencv_video.dir/vs_version.rc.obj" \
"CMakeFiles/opencv_video.dir/opencv_video_main.cpp.obj"

# External object files for target opencv_video
opencv_video_EXTERNAL_OBJECTS =

bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/src/bgfg_KNN.cpp.obj
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/src/bgfg_gaussmix2.cpp.obj
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/src/camshift.cpp.obj
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/src/dis_flow.cpp.obj
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/src/ecc.cpp.obj
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/src/kalman.cpp.obj
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/src/lkpyramid.cpp.obj
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/src/optflowgf.cpp.obj
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/src/optical_flow_io.cpp.obj
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/src/variational_refinement.cpp.obj
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/opencl_kernels_video.cpp.obj
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/vs_version.rc.obj
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/opencv_video_main.cpp.obj
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/build.make
bin/libopencv_video420.dll: lib/libopencv_calib3d420.dll.a
bin/libopencv_video420.dll: lib/libopencv_features2d420.dll.a
bin/libopencv_video420.dll: lib/libopencv_flann420.dll.a
bin/libopencv_video420.dll: lib/libopencv_imgproc420.dll.a
bin/libopencv_video420.dll: lib/libopencv_core420.dll.a
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/linklibs.rsp
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/objects1.rsp
bin/libopencv_video420.dll: modules/video/CMakeFiles/opencv_video.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Linking CXX shared library ..\..\bin\libopencv_video420.dll"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\opencv_video.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
modules/video/CMakeFiles/opencv_video.dir/build: bin/libopencv_video420.dll

.PHONY : modules/video/CMakeFiles/opencv_video.dir/build

modules/video/CMakeFiles/opencv_video.dir/clean:
	cd /d D:\unet\opencv\opencv\mingw_build\modules\video && $(CMAKE_COMMAND) -P CMakeFiles\opencv_video.dir\cmake_clean.cmake
.PHONY : modules/video/CMakeFiles/opencv_video.dir/clean

modules/video/CMakeFiles/opencv_video.dir/depend: modules/video/opencl_kernels_video.cpp
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\unet\opencv\opencv\sources D:\unet\opencv\opencv\sources\modules\video D:\unet\opencv\opencv\mingw_build D:\unet\opencv\opencv\mingw_build\modules\video D:\unet\opencv\opencv\mingw_build\modules\video\CMakeFiles\opencv_video.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : modules/video/CMakeFiles/opencv_video.dir/depend

