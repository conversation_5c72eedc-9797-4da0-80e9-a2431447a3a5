{"sources": [{"file": "D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video.hpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video/background_segm.hpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video/legacy/constants_c.h", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video/tracking.hpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video/video.hpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/bgfg_KNN.cpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/bgfg_gaussmix2.cpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/camshift.cpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/dis_flow.cpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/ecc.cpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/kalman.cpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/lkpyramid.cpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/optflowgf.cpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/optical_flow_io.cpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/variational_refinement.cpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/opencl/bgfg_knn.cl", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/opencl/bgfg_mog2.cl", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/opencl/dis_flow.cl", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/opencl/optical_flow_farneback.cl", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/opencl/pyrlk.cl", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/video/opencl_kernels_video.cpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/video/opencl_kernels_video.hpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/lkpyramid.hpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/video/src/precomp.hpp", "labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/cvconfig.h"}, {"file": "D:/unet/opencv/opencv/mingw_build/opencv2/opencv_modules.hpp"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/video/vs_version.rc"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/video/opencv_video_main.cpp"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/video/opencl_kernels_video.cpp.rule"}], "target": {"labels": ["Main", "opencv_video", "<PERSON><PERSON><PERSON>"], "name": "opencv_video"}}