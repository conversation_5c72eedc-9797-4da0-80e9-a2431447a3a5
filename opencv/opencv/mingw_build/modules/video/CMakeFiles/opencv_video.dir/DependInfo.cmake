# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  "RC"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "D:/unet/opencv/opencv/mingw_build/modules/video/opencl_kernels_video.cpp" "D:/unet/opencv/opencv/mingw_build/modules/video/CMakeFiles/opencv_video.dir/opencl_kernels_video.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/modules/video/opencv_video_main.cpp" "D:/unet/opencv/opencv/mingw_build/modules/video/CMakeFiles/opencv_video.dir/opencv_video_main.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/video/src/bgfg_KNN.cpp" "D:/unet/opencv/opencv/mingw_build/modules/video/CMakeFiles/opencv_video.dir/src/bgfg_KNN.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/video/src/bgfg_gaussmix2.cpp" "D:/unet/opencv/opencv/mingw_build/modules/video/CMakeFiles/opencv_video.dir/src/bgfg_gaussmix2.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/video/src/camshift.cpp" "D:/unet/opencv/opencv/mingw_build/modules/video/CMakeFiles/opencv_video.dir/src/camshift.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/video/src/dis_flow.cpp" "D:/unet/opencv/opencv/mingw_build/modules/video/CMakeFiles/opencv_video.dir/src/dis_flow.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/video/src/ecc.cpp" "D:/unet/opencv/opencv/mingw_build/modules/video/CMakeFiles/opencv_video.dir/src/ecc.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/video/src/kalman.cpp" "D:/unet/opencv/opencv/mingw_build/modules/video/CMakeFiles/opencv_video.dir/src/kalman.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/video/src/lkpyramid.cpp" "D:/unet/opencv/opencv/mingw_build/modules/video/CMakeFiles/opencv_video.dir/src/lkpyramid.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/video/src/optflowgf.cpp" "D:/unet/opencv/opencv/mingw_build/modules/video/CMakeFiles/opencv_video.dir/src/optflowgf.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/video/src/optical_flow_io.cpp" "D:/unet/opencv/opencv/mingw_build/modules/video/CMakeFiles/opencv_video.dir/src/optical_flow_io.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/video/src/variational_refinement.cpp" "D:/unet/opencv/opencv/mingw_build/modules/video/CMakeFiles/opencv_video.dir/src/variational_refinement.cpp.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/video/include"
  "modules/video"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/flann/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "D:/unet/opencv/opencv/sources/modules/features2d/include"
  "D:/unet/opencv/opencv/sources/modules/calib3d/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )
set(CMAKE_DEPENDS_CHECK_RC
  "D:/unet/opencv/opencv/mingw_build/modules/video/vs_version.rc" "D:/unet/opencv/opencv/mingw_build/modules/video/CMakeFiles/opencv_video.dir/vs_version.rc.obj"
  )

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_RC
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_RC_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/video/include"
  "modules/video"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/flann/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "D:/unet/opencv/opencv/sources/modules/features2d/include"
  "D:/unet/opencv/opencv/sources/modules/calib3d/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "D:/unet/opencv/opencv/mingw_build/modules/calib3d/CMakeFiles/opencv_calib3d.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/features2d/CMakeFiles/opencv_features2d.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/flann/CMakeFiles/opencv_flann.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/imgproc/CMakeFiles/opencv_imgproc.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/core/CMakeFiles/opencv_core.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
