# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\modules\video\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/video/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/video/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/video/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/video/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
modules/video/CMakeFiles/opencv_video.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/video/CMakeFiles/opencv_video.dir/rule
.PHONY : modules/video/CMakeFiles/opencv_video.dir/rule

# Convenience name for target.
opencv_video: modules/video/CMakeFiles/opencv_video.dir/rule

.PHONY : opencv_video

# fast build rule for target.
opencv_video/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/build
.PHONY : opencv_video/fast

opencl_kernels_video.obj: opencl_kernels_video.cpp.obj

.PHONY : opencl_kernels_video.obj

# target to build an object file
opencl_kernels_video.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/opencl_kernels_video.cpp.obj
.PHONY : opencl_kernels_video.cpp.obj

opencl_kernels_video.i: opencl_kernels_video.cpp.i

.PHONY : opencl_kernels_video.i

# target to preprocess a source file
opencl_kernels_video.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/opencl_kernels_video.cpp.i
.PHONY : opencl_kernels_video.cpp.i

opencl_kernels_video.s: opencl_kernels_video.cpp.s

.PHONY : opencl_kernels_video.s

# target to generate assembly for a file
opencl_kernels_video.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/opencl_kernels_video.cpp.s
.PHONY : opencl_kernels_video.cpp.s

opencv_video_main.obj: opencv_video_main.cpp.obj

.PHONY : opencv_video_main.obj

# target to build an object file
opencv_video_main.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/opencv_video_main.cpp.obj
.PHONY : opencv_video_main.cpp.obj

opencv_video_main.i: opencv_video_main.cpp.i

.PHONY : opencv_video_main.i

# target to preprocess a source file
opencv_video_main.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/opencv_video_main.cpp.i
.PHONY : opencv_video_main.cpp.i

opencv_video_main.s: opencv_video_main.cpp.s

.PHONY : opencv_video_main.s

# target to generate assembly for a file
opencv_video_main.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/opencv_video_main.cpp.s
.PHONY : opencv_video_main.cpp.s

src/bgfg_KNN.obj: src/bgfg_KNN.cpp.obj

.PHONY : src/bgfg_KNN.obj

# target to build an object file
src/bgfg_KNN.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/bgfg_KNN.cpp.obj
.PHONY : src/bgfg_KNN.cpp.obj

src/bgfg_KNN.i: src/bgfg_KNN.cpp.i

.PHONY : src/bgfg_KNN.i

# target to preprocess a source file
src/bgfg_KNN.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/bgfg_KNN.cpp.i
.PHONY : src/bgfg_KNN.cpp.i

src/bgfg_KNN.s: src/bgfg_KNN.cpp.s

.PHONY : src/bgfg_KNN.s

# target to generate assembly for a file
src/bgfg_KNN.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/bgfg_KNN.cpp.s
.PHONY : src/bgfg_KNN.cpp.s

src/bgfg_gaussmix2.obj: src/bgfg_gaussmix2.cpp.obj

.PHONY : src/bgfg_gaussmix2.obj

# target to build an object file
src/bgfg_gaussmix2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/bgfg_gaussmix2.cpp.obj
.PHONY : src/bgfg_gaussmix2.cpp.obj

src/bgfg_gaussmix2.i: src/bgfg_gaussmix2.cpp.i

.PHONY : src/bgfg_gaussmix2.i

# target to preprocess a source file
src/bgfg_gaussmix2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/bgfg_gaussmix2.cpp.i
.PHONY : src/bgfg_gaussmix2.cpp.i

src/bgfg_gaussmix2.s: src/bgfg_gaussmix2.cpp.s

.PHONY : src/bgfg_gaussmix2.s

# target to generate assembly for a file
src/bgfg_gaussmix2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/bgfg_gaussmix2.cpp.s
.PHONY : src/bgfg_gaussmix2.cpp.s

src/camshift.obj: src/camshift.cpp.obj

.PHONY : src/camshift.obj

# target to build an object file
src/camshift.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/camshift.cpp.obj
.PHONY : src/camshift.cpp.obj

src/camshift.i: src/camshift.cpp.i

.PHONY : src/camshift.i

# target to preprocess a source file
src/camshift.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/camshift.cpp.i
.PHONY : src/camshift.cpp.i

src/camshift.s: src/camshift.cpp.s

.PHONY : src/camshift.s

# target to generate assembly for a file
src/camshift.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/camshift.cpp.s
.PHONY : src/camshift.cpp.s

src/dis_flow.obj: src/dis_flow.cpp.obj

.PHONY : src/dis_flow.obj

# target to build an object file
src/dis_flow.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/dis_flow.cpp.obj
.PHONY : src/dis_flow.cpp.obj

src/dis_flow.i: src/dis_flow.cpp.i

.PHONY : src/dis_flow.i

# target to preprocess a source file
src/dis_flow.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/dis_flow.cpp.i
.PHONY : src/dis_flow.cpp.i

src/dis_flow.s: src/dis_flow.cpp.s

.PHONY : src/dis_flow.s

# target to generate assembly for a file
src/dis_flow.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/dis_flow.cpp.s
.PHONY : src/dis_flow.cpp.s

src/ecc.obj: src/ecc.cpp.obj

.PHONY : src/ecc.obj

# target to build an object file
src/ecc.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/ecc.cpp.obj
.PHONY : src/ecc.cpp.obj

src/ecc.i: src/ecc.cpp.i

.PHONY : src/ecc.i

# target to preprocess a source file
src/ecc.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/ecc.cpp.i
.PHONY : src/ecc.cpp.i

src/ecc.s: src/ecc.cpp.s

.PHONY : src/ecc.s

# target to generate assembly for a file
src/ecc.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/ecc.cpp.s
.PHONY : src/ecc.cpp.s

src/kalman.obj: src/kalman.cpp.obj

.PHONY : src/kalman.obj

# target to build an object file
src/kalman.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/kalman.cpp.obj
.PHONY : src/kalman.cpp.obj

src/kalman.i: src/kalman.cpp.i

.PHONY : src/kalman.i

# target to preprocess a source file
src/kalman.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/kalman.cpp.i
.PHONY : src/kalman.cpp.i

src/kalman.s: src/kalman.cpp.s

.PHONY : src/kalman.s

# target to generate assembly for a file
src/kalman.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/kalman.cpp.s
.PHONY : src/kalman.cpp.s

src/lkpyramid.obj: src/lkpyramid.cpp.obj

.PHONY : src/lkpyramid.obj

# target to build an object file
src/lkpyramid.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/lkpyramid.cpp.obj
.PHONY : src/lkpyramid.cpp.obj

src/lkpyramid.i: src/lkpyramid.cpp.i

.PHONY : src/lkpyramid.i

# target to preprocess a source file
src/lkpyramid.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/lkpyramid.cpp.i
.PHONY : src/lkpyramid.cpp.i

src/lkpyramid.s: src/lkpyramid.cpp.s

.PHONY : src/lkpyramid.s

# target to generate assembly for a file
src/lkpyramid.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/lkpyramid.cpp.s
.PHONY : src/lkpyramid.cpp.s

src/optflowgf.obj: src/optflowgf.cpp.obj

.PHONY : src/optflowgf.obj

# target to build an object file
src/optflowgf.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/optflowgf.cpp.obj
.PHONY : src/optflowgf.cpp.obj

src/optflowgf.i: src/optflowgf.cpp.i

.PHONY : src/optflowgf.i

# target to preprocess a source file
src/optflowgf.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/optflowgf.cpp.i
.PHONY : src/optflowgf.cpp.i

src/optflowgf.s: src/optflowgf.cpp.s

.PHONY : src/optflowgf.s

# target to generate assembly for a file
src/optflowgf.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/optflowgf.cpp.s
.PHONY : src/optflowgf.cpp.s

src/optical_flow_io.obj: src/optical_flow_io.cpp.obj

.PHONY : src/optical_flow_io.obj

# target to build an object file
src/optical_flow_io.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/optical_flow_io.cpp.obj
.PHONY : src/optical_flow_io.cpp.obj

src/optical_flow_io.i: src/optical_flow_io.cpp.i

.PHONY : src/optical_flow_io.i

# target to preprocess a source file
src/optical_flow_io.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/optical_flow_io.cpp.i
.PHONY : src/optical_flow_io.cpp.i

src/optical_flow_io.s: src/optical_flow_io.cpp.s

.PHONY : src/optical_flow_io.s

# target to generate assembly for a file
src/optical_flow_io.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/optical_flow_io.cpp.s
.PHONY : src/optical_flow_io.cpp.s

src/variational_refinement.obj: src/variational_refinement.cpp.obj

.PHONY : src/variational_refinement.obj

# target to build an object file
src/variational_refinement.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/variational_refinement.cpp.obj
.PHONY : src/variational_refinement.cpp.obj

src/variational_refinement.i: src/variational_refinement.cpp.i

.PHONY : src/variational_refinement.i

# target to preprocess a source file
src/variational_refinement.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/variational_refinement.cpp.i
.PHONY : src/variational_refinement.cpp.i

src/variational_refinement.s: src/variational_refinement.cpp.s

.PHONY : src/variational_refinement.s

# target to generate assembly for a file
src/variational_refinement.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/src/variational_refinement.cpp.s
.PHONY : src/variational_refinement.cpp.s

vs_version.obj: vs_version.rc.obj

.PHONY : vs_version.obj

# target to build an object file
vs_version.rc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/vs_version.rc.obj
.PHONY : vs_version.rc.obj

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... opencv_video
	@echo ... package
	@echo ... package_source
	@echo ... rebuild_cache
	@echo ... test
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... list_install_components
	@echo ... install/strip
	@echo ... opencl_kernels_video.obj
	@echo ... opencl_kernels_video.i
	@echo ... opencl_kernels_video.s
	@echo ... opencv_video_main.obj
	@echo ... opencv_video_main.i
	@echo ... opencv_video_main.s
	@echo ... src/bgfg_KNN.obj
	@echo ... src/bgfg_KNN.i
	@echo ... src/bgfg_KNN.s
	@echo ... src/bgfg_gaussmix2.obj
	@echo ... src/bgfg_gaussmix2.i
	@echo ... src/bgfg_gaussmix2.s
	@echo ... src/camshift.obj
	@echo ... src/camshift.i
	@echo ... src/camshift.s
	@echo ... src/dis_flow.obj
	@echo ... src/dis_flow.i
	@echo ... src/dis_flow.s
	@echo ... src/ecc.obj
	@echo ... src/ecc.i
	@echo ... src/ecc.s
	@echo ... src/kalman.obj
	@echo ... src/kalman.i
	@echo ... src/kalman.s
	@echo ... src/lkpyramid.obj
	@echo ... src/lkpyramid.i
	@echo ... src/lkpyramid.s
	@echo ... src/optflowgf.obj
	@echo ... src/optflowgf.i
	@echo ... src/optflowgf.s
	@echo ... src/optical_flow_io.obj
	@echo ... src/optical_flow_io.i
	@echo ... src/optical_flow_io.s
	@echo ... src/variational_refinement.obj
	@echo ... src/variational_refinement.i
	@echo ... src/variational_refinement.s
	@echo ... vs_version.obj
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

