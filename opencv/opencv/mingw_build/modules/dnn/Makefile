# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\modules\dnn\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/dnn/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/dnn/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/dnn/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/dnn/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
modules/dnn/CMakeFiles/opencv_dnn.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 modules/dnn/CMakeFiles/opencv_dnn.dir/rule
.PHONY : modules/dnn/CMakeFiles/opencv_dnn.dir/rule

# Convenience name for target.
opencv_dnn: modules/dnn/CMakeFiles/opencv_dnn.dir/rule

.PHONY : opencv_dnn

# fast build rule for target.
opencv_dnn/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/build
.PHONY : opencv_dnn/fast

layers/layers_common.avx.obj: layers/layers_common.avx.cpp.obj

.PHONY : layers/layers_common.avx.obj

# target to build an object file
layers/layers_common.avx.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/layers/layers_common.avx.cpp.obj
.PHONY : layers/layers_common.avx.cpp.obj

layers/layers_common.avx.i: layers/layers_common.avx.cpp.i

.PHONY : layers/layers_common.avx.i

# target to preprocess a source file
layers/layers_common.avx.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/layers/layers_common.avx.cpp.i
.PHONY : layers/layers_common.avx.cpp.i

layers/layers_common.avx.s: layers/layers_common.avx.cpp.s

.PHONY : layers/layers_common.avx.s

# target to generate assembly for a file
layers/layers_common.avx.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/layers/layers_common.avx.cpp.s
.PHONY : layers/layers_common.avx.cpp.s

layers/layers_common.avx2.obj: layers/layers_common.avx2.cpp.obj

.PHONY : layers/layers_common.avx2.obj

# target to build an object file
layers/layers_common.avx2.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/layers/layers_common.avx2.cpp.obj
.PHONY : layers/layers_common.avx2.cpp.obj

layers/layers_common.avx2.i: layers/layers_common.avx2.cpp.i

.PHONY : layers/layers_common.avx2.i

# target to preprocess a source file
layers/layers_common.avx2.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/layers/layers_common.avx2.cpp.i
.PHONY : layers/layers_common.avx2.cpp.i

layers/layers_common.avx2.s: layers/layers_common.avx2.cpp.s

.PHONY : layers/layers_common.avx2.s

# target to generate assembly for a file
layers/layers_common.avx2.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/layers/layers_common.avx2.cpp.s
.PHONY : layers/layers_common.avx2.cpp.s

misc/caffe/opencv-caffe.pb.obj: misc/caffe/opencv-caffe.pb.cc.obj

.PHONY : misc/caffe/opencv-caffe.pb.obj

# target to build an object file
misc/caffe/opencv-caffe.pb.cc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/caffe/opencv-caffe.pb.cc.obj
.PHONY : misc/caffe/opencv-caffe.pb.cc.obj

misc/caffe/opencv-caffe.pb.i: misc/caffe/opencv-caffe.pb.cc.i

.PHONY : misc/caffe/opencv-caffe.pb.i

# target to preprocess a source file
misc/caffe/opencv-caffe.pb.cc.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/caffe/opencv-caffe.pb.cc.i
.PHONY : misc/caffe/opencv-caffe.pb.cc.i

misc/caffe/opencv-caffe.pb.s: misc/caffe/opencv-caffe.pb.cc.s

.PHONY : misc/caffe/opencv-caffe.pb.s

# target to generate assembly for a file
misc/caffe/opencv-caffe.pb.cc.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/caffe/opencv-caffe.pb.cc.s
.PHONY : misc/caffe/opencv-caffe.pb.cc.s

misc/onnx/opencv-onnx.pb.obj: misc/onnx/opencv-onnx.pb.cc.obj

.PHONY : misc/onnx/opencv-onnx.pb.obj

# target to build an object file
misc/onnx/opencv-onnx.pb.cc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/onnx/opencv-onnx.pb.cc.obj
.PHONY : misc/onnx/opencv-onnx.pb.cc.obj

misc/onnx/opencv-onnx.pb.i: misc/onnx/opencv-onnx.pb.cc.i

.PHONY : misc/onnx/opencv-onnx.pb.i

# target to preprocess a source file
misc/onnx/opencv-onnx.pb.cc.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/onnx/opencv-onnx.pb.cc.i
.PHONY : misc/onnx/opencv-onnx.pb.cc.i

misc/onnx/opencv-onnx.pb.s: misc/onnx/opencv-onnx.pb.cc.s

.PHONY : misc/onnx/opencv-onnx.pb.s

# target to generate assembly for a file
misc/onnx/opencv-onnx.pb.cc.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/onnx/opencv-onnx.pb.cc.s
.PHONY : misc/onnx/opencv-onnx.pb.cc.s

misc/tensorflow/attr_value.pb.obj: misc/tensorflow/attr_value.pb.cc.obj

.PHONY : misc/tensorflow/attr_value.pb.obj

# target to build an object file
misc/tensorflow/attr_value.pb.cc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/attr_value.pb.cc.obj
.PHONY : misc/tensorflow/attr_value.pb.cc.obj

misc/tensorflow/attr_value.pb.i: misc/tensorflow/attr_value.pb.cc.i

.PHONY : misc/tensorflow/attr_value.pb.i

# target to preprocess a source file
misc/tensorflow/attr_value.pb.cc.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/attr_value.pb.cc.i
.PHONY : misc/tensorflow/attr_value.pb.cc.i

misc/tensorflow/attr_value.pb.s: misc/tensorflow/attr_value.pb.cc.s

.PHONY : misc/tensorflow/attr_value.pb.s

# target to generate assembly for a file
misc/tensorflow/attr_value.pb.cc.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/attr_value.pb.cc.s
.PHONY : misc/tensorflow/attr_value.pb.cc.s

misc/tensorflow/function.pb.obj: misc/tensorflow/function.pb.cc.obj

.PHONY : misc/tensorflow/function.pb.obj

# target to build an object file
misc/tensorflow/function.pb.cc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/function.pb.cc.obj
.PHONY : misc/tensorflow/function.pb.cc.obj

misc/tensorflow/function.pb.i: misc/tensorflow/function.pb.cc.i

.PHONY : misc/tensorflow/function.pb.i

# target to preprocess a source file
misc/tensorflow/function.pb.cc.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/function.pb.cc.i
.PHONY : misc/tensorflow/function.pb.cc.i

misc/tensorflow/function.pb.s: misc/tensorflow/function.pb.cc.s

.PHONY : misc/tensorflow/function.pb.s

# target to generate assembly for a file
misc/tensorflow/function.pb.cc.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/function.pb.cc.s
.PHONY : misc/tensorflow/function.pb.cc.s

misc/tensorflow/graph.pb.obj: misc/tensorflow/graph.pb.cc.obj

.PHONY : misc/tensorflow/graph.pb.obj

# target to build an object file
misc/tensorflow/graph.pb.cc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/graph.pb.cc.obj
.PHONY : misc/tensorflow/graph.pb.cc.obj

misc/tensorflow/graph.pb.i: misc/tensorflow/graph.pb.cc.i

.PHONY : misc/tensorflow/graph.pb.i

# target to preprocess a source file
misc/tensorflow/graph.pb.cc.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/graph.pb.cc.i
.PHONY : misc/tensorflow/graph.pb.cc.i

misc/tensorflow/graph.pb.s: misc/tensorflow/graph.pb.cc.s

.PHONY : misc/tensorflow/graph.pb.s

# target to generate assembly for a file
misc/tensorflow/graph.pb.cc.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/graph.pb.cc.s
.PHONY : misc/tensorflow/graph.pb.cc.s

misc/tensorflow/op_def.pb.obj: misc/tensorflow/op_def.pb.cc.obj

.PHONY : misc/tensorflow/op_def.pb.obj

# target to build an object file
misc/tensorflow/op_def.pb.cc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/op_def.pb.cc.obj
.PHONY : misc/tensorflow/op_def.pb.cc.obj

misc/tensorflow/op_def.pb.i: misc/tensorflow/op_def.pb.cc.i

.PHONY : misc/tensorflow/op_def.pb.i

# target to preprocess a source file
misc/tensorflow/op_def.pb.cc.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/op_def.pb.cc.i
.PHONY : misc/tensorflow/op_def.pb.cc.i

misc/tensorflow/op_def.pb.s: misc/tensorflow/op_def.pb.cc.s

.PHONY : misc/tensorflow/op_def.pb.s

# target to generate assembly for a file
misc/tensorflow/op_def.pb.cc.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/op_def.pb.cc.s
.PHONY : misc/tensorflow/op_def.pb.cc.s

misc/tensorflow/tensor.pb.obj: misc/tensorflow/tensor.pb.cc.obj

.PHONY : misc/tensorflow/tensor.pb.obj

# target to build an object file
misc/tensorflow/tensor.pb.cc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/tensor.pb.cc.obj
.PHONY : misc/tensorflow/tensor.pb.cc.obj

misc/tensorflow/tensor.pb.i: misc/tensorflow/tensor.pb.cc.i

.PHONY : misc/tensorflow/tensor.pb.i

# target to preprocess a source file
misc/tensorflow/tensor.pb.cc.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/tensor.pb.cc.i
.PHONY : misc/tensorflow/tensor.pb.cc.i

misc/tensorflow/tensor.pb.s: misc/tensorflow/tensor.pb.cc.s

.PHONY : misc/tensorflow/tensor.pb.s

# target to generate assembly for a file
misc/tensorflow/tensor.pb.cc.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/tensor.pb.cc.s
.PHONY : misc/tensorflow/tensor.pb.cc.s

misc/tensorflow/tensor_shape.pb.obj: misc/tensorflow/tensor_shape.pb.cc.obj

.PHONY : misc/tensorflow/tensor_shape.pb.obj

# target to build an object file
misc/tensorflow/tensor_shape.pb.cc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/tensor_shape.pb.cc.obj
.PHONY : misc/tensorflow/tensor_shape.pb.cc.obj

misc/tensorflow/tensor_shape.pb.i: misc/tensorflow/tensor_shape.pb.cc.i

.PHONY : misc/tensorflow/tensor_shape.pb.i

# target to preprocess a source file
misc/tensorflow/tensor_shape.pb.cc.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/tensor_shape.pb.cc.i
.PHONY : misc/tensorflow/tensor_shape.pb.cc.i

misc/tensorflow/tensor_shape.pb.s: misc/tensorflow/tensor_shape.pb.cc.s

.PHONY : misc/tensorflow/tensor_shape.pb.s

# target to generate assembly for a file
misc/tensorflow/tensor_shape.pb.cc.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/tensor_shape.pb.cc.s
.PHONY : misc/tensorflow/tensor_shape.pb.cc.s

misc/tensorflow/types.pb.obj: misc/tensorflow/types.pb.cc.obj

.PHONY : misc/tensorflow/types.pb.obj

# target to build an object file
misc/tensorflow/types.pb.cc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/types.pb.cc.obj
.PHONY : misc/tensorflow/types.pb.cc.obj

misc/tensorflow/types.pb.i: misc/tensorflow/types.pb.cc.i

.PHONY : misc/tensorflow/types.pb.i

# target to preprocess a source file
misc/tensorflow/types.pb.cc.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/types.pb.cc.i
.PHONY : misc/tensorflow/types.pb.cc.i

misc/tensorflow/types.pb.s: misc/tensorflow/types.pb.cc.s

.PHONY : misc/tensorflow/types.pb.s

# target to generate assembly for a file
misc/tensorflow/types.pb.cc.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/types.pb.cc.s
.PHONY : misc/tensorflow/types.pb.cc.s

misc/tensorflow/versions.pb.obj: misc/tensorflow/versions.pb.cc.obj

.PHONY : misc/tensorflow/versions.pb.obj

# target to build an object file
misc/tensorflow/versions.pb.cc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/versions.pb.cc.obj
.PHONY : misc/tensorflow/versions.pb.cc.obj

misc/tensorflow/versions.pb.i: misc/tensorflow/versions.pb.cc.i

.PHONY : misc/tensorflow/versions.pb.i

# target to preprocess a source file
misc/tensorflow/versions.pb.cc.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/versions.pb.cc.i
.PHONY : misc/tensorflow/versions.pb.cc.i

misc/tensorflow/versions.pb.s: misc/tensorflow/versions.pb.cc.s

.PHONY : misc/tensorflow/versions.pb.s

# target to generate assembly for a file
misc/tensorflow/versions.pb.cc.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/versions.pb.cc.s
.PHONY : misc/tensorflow/versions.pb.cc.s

opencl_kernels_dnn.obj: opencl_kernels_dnn.cpp.obj

.PHONY : opencl_kernels_dnn.obj

# target to build an object file
opencl_kernels_dnn.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/opencl_kernels_dnn.cpp.obj
.PHONY : opencl_kernels_dnn.cpp.obj

opencl_kernels_dnn.i: opencl_kernels_dnn.cpp.i

.PHONY : opencl_kernels_dnn.i

# target to preprocess a source file
opencl_kernels_dnn.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/opencl_kernels_dnn.cpp.i
.PHONY : opencl_kernels_dnn.cpp.i

opencl_kernels_dnn.s: opencl_kernels_dnn.cpp.s

.PHONY : opencl_kernels_dnn.s

# target to generate assembly for a file
opencl_kernels_dnn.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/opencl_kernels_dnn.cpp.s
.PHONY : opencl_kernels_dnn.cpp.s

opencv_dnn_main.obj: opencv_dnn_main.cpp.obj

.PHONY : opencv_dnn_main.obj

# target to build an object file
opencv_dnn_main.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/opencv_dnn_main.cpp.obj
.PHONY : opencv_dnn_main.cpp.obj

opencv_dnn_main.i: opencv_dnn_main.cpp.i

.PHONY : opencv_dnn_main.i

# target to preprocess a source file
opencv_dnn_main.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/opencv_dnn_main.cpp.i
.PHONY : opencv_dnn_main.cpp.i

opencv_dnn_main.s: opencv_dnn_main.cpp.s

.PHONY : opencv_dnn_main.s

# target to generate assembly for a file
opencv_dnn_main.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/opencv_dnn_main.cpp.s
.PHONY : opencv_dnn_main.cpp.s

src/caffe/caffe_importer.obj: src/caffe/caffe_importer.cpp.obj

.PHONY : src/caffe/caffe_importer.obj

# target to build an object file
src/caffe/caffe_importer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/caffe/caffe_importer.cpp.obj
.PHONY : src/caffe/caffe_importer.cpp.obj

src/caffe/caffe_importer.i: src/caffe/caffe_importer.cpp.i

.PHONY : src/caffe/caffe_importer.i

# target to preprocess a source file
src/caffe/caffe_importer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/caffe/caffe_importer.cpp.i
.PHONY : src/caffe/caffe_importer.cpp.i

src/caffe/caffe_importer.s: src/caffe/caffe_importer.cpp.s

.PHONY : src/caffe/caffe_importer.s

# target to generate assembly for a file
src/caffe/caffe_importer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/caffe/caffe_importer.cpp.s
.PHONY : src/caffe/caffe_importer.cpp.s

src/caffe/caffe_io.obj: src/caffe/caffe_io.cpp.obj

.PHONY : src/caffe/caffe_io.obj

# target to build an object file
src/caffe/caffe_io.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/caffe/caffe_io.cpp.obj
.PHONY : src/caffe/caffe_io.cpp.obj

src/caffe/caffe_io.i: src/caffe/caffe_io.cpp.i

.PHONY : src/caffe/caffe_io.i

# target to preprocess a source file
src/caffe/caffe_io.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/caffe/caffe_io.cpp.i
.PHONY : src/caffe/caffe_io.cpp.i

src/caffe/caffe_io.s: src/caffe/caffe_io.cpp.s

.PHONY : src/caffe/caffe_io.s

# target to generate assembly for a file
src/caffe/caffe_io.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/caffe/caffe_io.cpp.s
.PHONY : src/caffe/caffe_io.cpp.s

src/caffe/caffe_shrinker.obj: src/caffe/caffe_shrinker.cpp.obj

.PHONY : src/caffe/caffe_shrinker.obj

# target to build an object file
src/caffe/caffe_shrinker.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/caffe/caffe_shrinker.cpp.obj
.PHONY : src/caffe/caffe_shrinker.cpp.obj

src/caffe/caffe_shrinker.i: src/caffe/caffe_shrinker.cpp.i

.PHONY : src/caffe/caffe_shrinker.i

# target to preprocess a source file
src/caffe/caffe_shrinker.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/caffe/caffe_shrinker.cpp.i
.PHONY : src/caffe/caffe_shrinker.cpp.i

src/caffe/caffe_shrinker.s: src/caffe/caffe_shrinker.cpp.s

.PHONY : src/caffe/caffe_shrinker.s

# target to generate assembly for a file
src/caffe/caffe_shrinker.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/caffe/caffe_shrinker.cpp.s
.PHONY : src/caffe/caffe_shrinker.cpp.s

src/darknet/darknet_importer.obj: src/darknet/darknet_importer.cpp.obj

.PHONY : src/darknet/darknet_importer.obj

# target to build an object file
src/darknet/darknet_importer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/darknet/darknet_importer.cpp.obj
.PHONY : src/darknet/darknet_importer.cpp.obj

src/darknet/darknet_importer.i: src/darknet/darknet_importer.cpp.i

.PHONY : src/darknet/darknet_importer.i

# target to preprocess a source file
src/darknet/darknet_importer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/darknet/darknet_importer.cpp.i
.PHONY : src/darknet/darknet_importer.cpp.i

src/darknet/darknet_importer.s: src/darknet/darknet_importer.cpp.s

.PHONY : src/darknet/darknet_importer.s

# target to generate assembly for a file
src/darknet/darknet_importer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/darknet/darknet_importer.cpp.s
.PHONY : src/darknet/darknet_importer.cpp.s

src/darknet/darknet_io.obj: src/darknet/darknet_io.cpp.obj

.PHONY : src/darknet/darknet_io.obj

# target to build an object file
src/darknet/darknet_io.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/darknet/darknet_io.cpp.obj
.PHONY : src/darknet/darknet_io.cpp.obj

src/darknet/darknet_io.i: src/darknet/darknet_io.cpp.i

.PHONY : src/darknet/darknet_io.i

# target to preprocess a source file
src/darknet/darknet_io.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/darknet/darknet_io.cpp.i
.PHONY : src/darknet/darknet_io.cpp.i

src/darknet/darknet_io.s: src/darknet/darknet_io.cpp.s

.PHONY : src/darknet/darknet_io.s

# target to generate assembly for a file
src/darknet/darknet_io.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/darknet/darknet_io.cpp.s
.PHONY : src/darknet/darknet_io.cpp.s

src/dnn.obj: src/dnn.cpp.obj

.PHONY : src/dnn.obj

# target to build an object file
src/dnn.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/dnn.cpp.obj
.PHONY : src/dnn.cpp.obj

src/dnn.i: src/dnn.cpp.i

.PHONY : src/dnn.i

# target to preprocess a source file
src/dnn.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/dnn.cpp.i
.PHONY : src/dnn.cpp.i

src/dnn.s: src/dnn.cpp.s

.PHONY : src/dnn.s

# target to generate assembly for a file
src/dnn.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/dnn.cpp.s
.PHONY : src/dnn.cpp.s

src/halide_scheduler.obj: src/halide_scheduler.cpp.obj

.PHONY : src/halide_scheduler.obj

# target to build an object file
src/halide_scheduler.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/halide_scheduler.cpp.obj
.PHONY : src/halide_scheduler.cpp.obj

src/halide_scheduler.i: src/halide_scheduler.cpp.i

.PHONY : src/halide_scheduler.i

# target to preprocess a source file
src/halide_scheduler.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/halide_scheduler.cpp.i
.PHONY : src/halide_scheduler.cpp.i

src/halide_scheduler.s: src/halide_scheduler.cpp.s

.PHONY : src/halide_scheduler.s

# target to generate assembly for a file
src/halide_scheduler.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/halide_scheduler.cpp.s
.PHONY : src/halide_scheduler.cpp.s

src/ie_ngraph.obj: src/ie_ngraph.cpp.obj

.PHONY : src/ie_ngraph.obj

# target to build an object file
src/ie_ngraph.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ie_ngraph.cpp.obj
.PHONY : src/ie_ngraph.cpp.obj

src/ie_ngraph.i: src/ie_ngraph.cpp.i

.PHONY : src/ie_ngraph.i

# target to preprocess a source file
src/ie_ngraph.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ie_ngraph.cpp.i
.PHONY : src/ie_ngraph.cpp.i

src/ie_ngraph.s: src/ie_ngraph.cpp.s

.PHONY : src/ie_ngraph.s

# target to generate assembly for a file
src/ie_ngraph.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ie_ngraph.cpp.s
.PHONY : src/ie_ngraph.cpp.s

src/init.obj: src/init.cpp.obj

.PHONY : src/init.obj

# target to build an object file
src/init.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/init.cpp.obj
.PHONY : src/init.cpp.obj

src/init.i: src/init.cpp.i

.PHONY : src/init.i

# target to preprocess a source file
src/init.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/init.cpp.i
.PHONY : src/init.cpp.i

src/init.s: src/init.cpp.s

.PHONY : src/init.s

# target to generate assembly for a file
src/init.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/init.cpp.s
.PHONY : src/init.cpp.s

src/layers/batch_norm_layer.obj: src/layers/batch_norm_layer.cpp.obj

.PHONY : src/layers/batch_norm_layer.obj

# target to build an object file
src/layers/batch_norm_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/batch_norm_layer.cpp.obj
.PHONY : src/layers/batch_norm_layer.cpp.obj

src/layers/batch_norm_layer.i: src/layers/batch_norm_layer.cpp.i

.PHONY : src/layers/batch_norm_layer.i

# target to preprocess a source file
src/layers/batch_norm_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/batch_norm_layer.cpp.i
.PHONY : src/layers/batch_norm_layer.cpp.i

src/layers/batch_norm_layer.s: src/layers/batch_norm_layer.cpp.s

.PHONY : src/layers/batch_norm_layer.s

# target to generate assembly for a file
src/layers/batch_norm_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/batch_norm_layer.cpp.s
.PHONY : src/layers/batch_norm_layer.cpp.s

src/layers/blank_layer.obj: src/layers/blank_layer.cpp.obj

.PHONY : src/layers/blank_layer.obj

# target to build an object file
src/layers/blank_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/blank_layer.cpp.obj
.PHONY : src/layers/blank_layer.cpp.obj

src/layers/blank_layer.i: src/layers/blank_layer.cpp.i

.PHONY : src/layers/blank_layer.i

# target to preprocess a source file
src/layers/blank_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/blank_layer.cpp.i
.PHONY : src/layers/blank_layer.cpp.i

src/layers/blank_layer.s: src/layers/blank_layer.cpp.s

.PHONY : src/layers/blank_layer.s

# target to generate assembly for a file
src/layers/blank_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/blank_layer.cpp.s
.PHONY : src/layers/blank_layer.cpp.s

src/layers/concat_layer.obj: src/layers/concat_layer.cpp.obj

.PHONY : src/layers/concat_layer.obj

# target to build an object file
src/layers/concat_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/concat_layer.cpp.obj
.PHONY : src/layers/concat_layer.cpp.obj

src/layers/concat_layer.i: src/layers/concat_layer.cpp.i

.PHONY : src/layers/concat_layer.i

# target to preprocess a source file
src/layers/concat_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/concat_layer.cpp.i
.PHONY : src/layers/concat_layer.cpp.i

src/layers/concat_layer.s: src/layers/concat_layer.cpp.s

.PHONY : src/layers/concat_layer.s

# target to generate assembly for a file
src/layers/concat_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/concat_layer.cpp.s
.PHONY : src/layers/concat_layer.cpp.s

src/layers/const_layer.obj: src/layers/const_layer.cpp.obj

.PHONY : src/layers/const_layer.obj

# target to build an object file
src/layers/const_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/const_layer.cpp.obj
.PHONY : src/layers/const_layer.cpp.obj

src/layers/const_layer.i: src/layers/const_layer.cpp.i

.PHONY : src/layers/const_layer.i

# target to preprocess a source file
src/layers/const_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/const_layer.cpp.i
.PHONY : src/layers/const_layer.cpp.i

src/layers/const_layer.s: src/layers/const_layer.cpp.s

.PHONY : src/layers/const_layer.s

# target to generate assembly for a file
src/layers/const_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/const_layer.cpp.s
.PHONY : src/layers/const_layer.cpp.s

src/layers/convolution_layer.obj: src/layers/convolution_layer.cpp.obj

.PHONY : src/layers/convolution_layer.obj

# target to build an object file
src/layers/convolution_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/convolution_layer.cpp.obj
.PHONY : src/layers/convolution_layer.cpp.obj

src/layers/convolution_layer.i: src/layers/convolution_layer.cpp.i

.PHONY : src/layers/convolution_layer.i

# target to preprocess a source file
src/layers/convolution_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/convolution_layer.cpp.i
.PHONY : src/layers/convolution_layer.cpp.i

src/layers/convolution_layer.s: src/layers/convolution_layer.cpp.s

.PHONY : src/layers/convolution_layer.s

# target to generate assembly for a file
src/layers/convolution_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/convolution_layer.cpp.s
.PHONY : src/layers/convolution_layer.cpp.s

src/layers/crop_and_resize_layer.obj: src/layers/crop_and_resize_layer.cpp.obj

.PHONY : src/layers/crop_and_resize_layer.obj

# target to build an object file
src/layers/crop_and_resize_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/crop_and_resize_layer.cpp.obj
.PHONY : src/layers/crop_and_resize_layer.cpp.obj

src/layers/crop_and_resize_layer.i: src/layers/crop_and_resize_layer.cpp.i

.PHONY : src/layers/crop_and_resize_layer.i

# target to preprocess a source file
src/layers/crop_and_resize_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/crop_and_resize_layer.cpp.i
.PHONY : src/layers/crop_and_resize_layer.cpp.i

src/layers/crop_and_resize_layer.s: src/layers/crop_and_resize_layer.cpp.s

.PHONY : src/layers/crop_and_resize_layer.s

# target to generate assembly for a file
src/layers/crop_and_resize_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/crop_and_resize_layer.cpp.s
.PHONY : src/layers/crop_and_resize_layer.cpp.s

src/layers/detection_output_layer.obj: src/layers/detection_output_layer.cpp.obj

.PHONY : src/layers/detection_output_layer.obj

# target to build an object file
src/layers/detection_output_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/detection_output_layer.cpp.obj
.PHONY : src/layers/detection_output_layer.cpp.obj

src/layers/detection_output_layer.i: src/layers/detection_output_layer.cpp.i

.PHONY : src/layers/detection_output_layer.i

# target to preprocess a source file
src/layers/detection_output_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/detection_output_layer.cpp.i
.PHONY : src/layers/detection_output_layer.cpp.i

src/layers/detection_output_layer.s: src/layers/detection_output_layer.cpp.s

.PHONY : src/layers/detection_output_layer.s

# target to generate assembly for a file
src/layers/detection_output_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/detection_output_layer.cpp.s
.PHONY : src/layers/detection_output_layer.cpp.s

src/layers/elementwise_layers.obj: src/layers/elementwise_layers.cpp.obj

.PHONY : src/layers/elementwise_layers.obj

# target to build an object file
src/layers/elementwise_layers.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/elementwise_layers.cpp.obj
.PHONY : src/layers/elementwise_layers.cpp.obj

src/layers/elementwise_layers.i: src/layers/elementwise_layers.cpp.i

.PHONY : src/layers/elementwise_layers.i

# target to preprocess a source file
src/layers/elementwise_layers.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/elementwise_layers.cpp.i
.PHONY : src/layers/elementwise_layers.cpp.i

src/layers/elementwise_layers.s: src/layers/elementwise_layers.cpp.s

.PHONY : src/layers/elementwise_layers.s

# target to generate assembly for a file
src/layers/elementwise_layers.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/elementwise_layers.cpp.s
.PHONY : src/layers/elementwise_layers.cpp.s

src/layers/eltwise_layer.obj: src/layers/eltwise_layer.cpp.obj

.PHONY : src/layers/eltwise_layer.obj

# target to build an object file
src/layers/eltwise_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/eltwise_layer.cpp.obj
.PHONY : src/layers/eltwise_layer.cpp.obj

src/layers/eltwise_layer.i: src/layers/eltwise_layer.cpp.i

.PHONY : src/layers/eltwise_layer.i

# target to preprocess a source file
src/layers/eltwise_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/eltwise_layer.cpp.i
.PHONY : src/layers/eltwise_layer.cpp.i

src/layers/eltwise_layer.s: src/layers/eltwise_layer.cpp.s

.PHONY : src/layers/eltwise_layer.s

# target to generate assembly for a file
src/layers/eltwise_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/eltwise_layer.cpp.s
.PHONY : src/layers/eltwise_layer.cpp.s

src/layers/flatten_layer.obj: src/layers/flatten_layer.cpp.obj

.PHONY : src/layers/flatten_layer.obj

# target to build an object file
src/layers/flatten_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/flatten_layer.cpp.obj
.PHONY : src/layers/flatten_layer.cpp.obj

src/layers/flatten_layer.i: src/layers/flatten_layer.cpp.i

.PHONY : src/layers/flatten_layer.i

# target to preprocess a source file
src/layers/flatten_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/flatten_layer.cpp.i
.PHONY : src/layers/flatten_layer.cpp.i

src/layers/flatten_layer.s: src/layers/flatten_layer.cpp.s

.PHONY : src/layers/flatten_layer.s

# target to generate assembly for a file
src/layers/flatten_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/flatten_layer.cpp.s
.PHONY : src/layers/flatten_layer.cpp.s

src/layers/fully_connected_layer.obj: src/layers/fully_connected_layer.cpp.obj

.PHONY : src/layers/fully_connected_layer.obj

# target to build an object file
src/layers/fully_connected_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/fully_connected_layer.cpp.obj
.PHONY : src/layers/fully_connected_layer.cpp.obj

src/layers/fully_connected_layer.i: src/layers/fully_connected_layer.cpp.i

.PHONY : src/layers/fully_connected_layer.i

# target to preprocess a source file
src/layers/fully_connected_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/fully_connected_layer.cpp.i
.PHONY : src/layers/fully_connected_layer.cpp.i

src/layers/fully_connected_layer.s: src/layers/fully_connected_layer.cpp.s

.PHONY : src/layers/fully_connected_layer.s

# target to generate assembly for a file
src/layers/fully_connected_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/fully_connected_layer.cpp.s
.PHONY : src/layers/fully_connected_layer.cpp.s

src/layers/layers_common.obj: src/layers/layers_common.cpp.obj

.PHONY : src/layers/layers_common.obj

# target to build an object file
src/layers/layers_common.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/layers_common.cpp.obj
.PHONY : src/layers/layers_common.cpp.obj

src/layers/layers_common.i: src/layers/layers_common.cpp.i

.PHONY : src/layers/layers_common.i

# target to preprocess a source file
src/layers/layers_common.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/layers_common.cpp.i
.PHONY : src/layers/layers_common.cpp.i

src/layers/layers_common.s: src/layers/layers_common.cpp.s

.PHONY : src/layers/layers_common.s

# target to generate assembly for a file
src/layers/layers_common.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/layers_common.cpp.s
.PHONY : src/layers/layers_common.cpp.s

src/layers/lrn_layer.obj: src/layers/lrn_layer.cpp.obj

.PHONY : src/layers/lrn_layer.obj

# target to build an object file
src/layers/lrn_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/lrn_layer.cpp.obj
.PHONY : src/layers/lrn_layer.cpp.obj

src/layers/lrn_layer.i: src/layers/lrn_layer.cpp.i

.PHONY : src/layers/lrn_layer.i

# target to preprocess a source file
src/layers/lrn_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/lrn_layer.cpp.i
.PHONY : src/layers/lrn_layer.cpp.i

src/layers/lrn_layer.s: src/layers/lrn_layer.cpp.s

.PHONY : src/layers/lrn_layer.s

# target to generate assembly for a file
src/layers/lrn_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/lrn_layer.cpp.s
.PHONY : src/layers/lrn_layer.cpp.s

src/layers/max_unpooling_layer.obj: src/layers/max_unpooling_layer.cpp.obj

.PHONY : src/layers/max_unpooling_layer.obj

# target to build an object file
src/layers/max_unpooling_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/max_unpooling_layer.cpp.obj
.PHONY : src/layers/max_unpooling_layer.cpp.obj

src/layers/max_unpooling_layer.i: src/layers/max_unpooling_layer.cpp.i

.PHONY : src/layers/max_unpooling_layer.i

# target to preprocess a source file
src/layers/max_unpooling_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/max_unpooling_layer.cpp.i
.PHONY : src/layers/max_unpooling_layer.cpp.i

src/layers/max_unpooling_layer.s: src/layers/max_unpooling_layer.cpp.s

.PHONY : src/layers/max_unpooling_layer.s

# target to generate assembly for a file
src/layers/max_unpooling_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/max_unpooling_layer.cpp.s
.PHONY : src/layers/max_unpooling_layer.cpp.s

src/layers/mvn_layer.obj: src/layers/mvn_layer.cpp.obj

.PHONY : src/layers/mvn_layer.obj

# target to build an object file
src/layers/mvn_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/mvn_layer.cpp.obj
.PHONY : src/layers/mvn_layer.cpp.obj

src/layers/mvn_layer.i: src/layers/mvn_layer.cpp.i

.PHONY : src/layers/mvn_layer.i

# target to preprocess a source file
src/layers/mvn_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/mvn_layer.cpp.i
.PHONY : src/layers/mvn_layer.cpp.i

src/layers/mvn_layer.s: src/layers/mvn_layer.cpp.s

.PHONY : src/layers/mvn_layer.s

# target to generate assembly for a file
src/layers/mvn_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/mvn_layer.cpp.s
.PHONY : src/layers/mvn_layer.cpp.s

src/layers/normalize_bbox_layer.obj: src/layers/normalize_bbox_layer.cpp.obj

.PHONY : src/layers/normalize_bbox_layer.obj

# target to build an object file
src/layers/normalize_bbox_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/normalize_bbox_layer.cpp.obj
.PHONY : src/layers/normalize_bbox_layer.cpp.obj

src/layers/normalize_bbox_layer.i: src/layers/normalize_bbox_layer.cpp.i

.PHONY : src/layers/normalize_bbox_layer.i

# target to preprocess a source file
src/layers/normalize_bbox_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/normalize_bbox_layer.cpp.i
.PHONY : src/layers/normalize_bbox_layer.cpp.i

src/layers/normalize_bbox_layer.s: src/layers/normalize_bbox_layer.cpp.s

.PHONY : src/layers/normalize_bbox_layer.s

# target to generate assembly for a file
src/layers/normalize_bbox_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/normalize_bbox_layer.cpp.s
.PHONY : src/layers/normalize_bbox_layer.cpp.s

src/layers/padding_layer.obj: src/layers/padding_layer.cpp.obj

.PHONY : src/layers/padding_layer.obj

# target to build an object file
src/layers/padding_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/padding_layer.cpp.obj
.PHONY : src/layers/padding_layer.cpp.obj

src/layers/padding_layer.i: src/layers/padding_layer.cpp.i

.PHONY : src/layers/padding_layer.i

# target to preprocess a source file
src/layers/padding_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/padding_layer.cpp.i
.PHONY : src/layers/padding_layer.cpp.i

src/layers/padding_layer.s: src/layers/padding_layer.cpp.s

.PHONY : src/layers/padding_layer.s

# target to generate assembly for a file
src/layers/padding_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/padding_layer.cpp.s
.PHONY : src/layers/padding_layer.cpp.s

src/layers/permute_layer.obj: src/layers/permute_layer.cpp.obj

.PHONY : src/layers/permute_layer.obj

# target to build an object file
src/layers/permute_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/permute_layer.cpp.obj
.PHONY : src/layers/permute_layer.cpp.obj

src/layers/permute_layer.i: src/layers/permute_layer.cpp.i

.PHONY : src/layers/permute_layer.i

# target to preprocess a source file
src/layers/permute_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/permute_layer.cpp.i
.PHONY : src/layers/permute_layer.cpp.i

src/layers/permute_layer.s: src/layers/permute_layer.cpp.s

.PHONY : src/layers/permute_layer.s

# target to generate assembly for a file
src/layers/permute_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/permute_layer.cpp.s
.PHONY : src/layers/permute_layer.cpp.s

src/layers/pooling_layer.obj: src/layers/pooling_layer.cpp.obj

.PHONY : src/layers/pooling_layer.obj

# target to build an object file
src/layers/pooling_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/pooling_layer.cpp.obj
.PHONY : src/layers/pooling_layer.cpp.obj

src/layers/pooling_layer.i: src/layers/pooling_layer.cpp.i

.PHONY : src/layers/pooling_layer.i

# target to preprocess a source file
src/layers/pooling_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/pooling_layer.cpp.i
.PHONY : src/layers/pooling_layer.cpp.i

src/layers/pooling_layer.s: src/layers/pooling_layer.cpp.s

.PHONY : src/layers/pooling_layer.s

# target to generate assembly for a file
src/layers/pooling_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/pooling_layer.cpp.s
.PHONY : src/layers/pooling_layer.cpp.s

src/layers/prior_box_layer.obj: src/layers/prior_box_layer.cpp.obj

.PHONY : src/layers/prior_box_layer.obj

# target to build an object file
src/layers/prior_box_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/prior_box_layer.cpp.obj
.PHONY : src/layers/prior_box_layer.cpp.obj

src/layers/prior_box_layer.i: src/layers/prior_box_layer.cpp.i

.PHONY : src/layers/prior_box_layer.i

# target to preprocess a source file
src/layers/prior_box_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/prior_box_layer.cpp.i
.PHONY : src/layers/prior_box_layer.cpp.i

src/layers/prior_box_layer.s: src/layers/prior_box_layer.cpp.s

.PHONY : src/layers/prior_box_layer.s

# target to generate assembly for a file
src/layers/prior_box_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/prior_box_layer.cpp.s
.PHONY : src/layers/prior_box_layer.cpp.s

src/layers/proposal_layer.obj: src/layers/proposal_layer.cpp.obj

.PHONY : src/layers/proposal_layer.obj

# target to build an object file
src/layers/proposal_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/proposal_layer.cpp.obj
.PHONY : src/layers/proposal_layer.cpp.obj

src/layers/proposal_layer.i: src/layers/proposal_layer.cpp.i

.PHONY : src/layers/proposal_layer.i

# target to preprocess a source file
src/layers/proposal_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/proposal_layer.cpp.i
.PHONY : src/layers/proposal_layer.cpp.i

src/layers/proposal_layer.s: src/layers/proposal_layer.cpp.s

.PHONY : src/layers/proposal_layer.s

# target to generate assembly for a file
src/layers/proposal_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/proposal_layer.cpp.s
.PHONY : src/layers/proposal_layer.cpp.s

src/layers/recurrent_layers.obj: src/layers/recurrent_layers.cpp.obj

.PHONY : src/layers/recurrent_layers.obj

# target to build an object file
src/layers/recurrent_layers.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/recurrent_layers.cpp.obj
.PHONY : src/layers/recurrent_layers.cpp.obj

src/layers/recurrent_layers.i: src/layers/recurrent_layers.cpp.i

.PHONY : src/layers/recurrent_layers.i

# target to preprocess a source file
src/layers/recurrent_layers.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/recurrent_layers.cpp.i
.PHONY : src/layers/recurrent_layers.cpp.i

src/layers/recurrent_layers.s: src/layers/recurrent_layers.cpp.s

.PHONY : src/layers/recurrent_layers.s

# target to generate assembly for a file
src/layers/recurrent_layers.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/recurrent_layers.cpp.s
.PHONY : src/layers/recurrent_layers.cpp.s

src/layers/region_layer.obj: src/layers/region_layer.cpp.obj

.PHONY : src/layers/region_layer.obj

# target to build an object file
src/layers/region_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/region_layer.cpp.obj
.PHONY : src/layers/region_layer.cpp.obj

src/layers/region_layer.i: src/layers/region_layer.cpp.i

.PHONY : src/layers/region_layer.i

# target to preprocess a source file
src/layers/region_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/region_layer.cpp.i
.PHONY : src/layers/region_layer.cpp.i

src/layers/region_layer.s: src/layers/region_layer.cpp.s

.PHONY : src/layers/region_layer.s

# target to generate assembly for a file
src/layers/region_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/region_layer.cpp.s
.PHONY : src/layers/region_layer.cpp.s

src/layers/reorg_layer.obj: src/layers/reorg_layer.cpp.obj

.PHONY : src/layers/reorg_layer.obj

# target to build an object file
src/layers/reorg_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/reorg_layer.cpp.obj
.PHONY : src/layers/reorg_layer.cpp.obj

src/layers/reorg_layer.i: src/layers/reorg_layer.cpp.i

.PHONY : src/layers/reorg_layer.i

# target to preprocess a source file
src/layers/reorg_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/reorg_layer.cpp.i
.PHONY : src/layers/reorg_layer.cpp.i

src/layers/reorg_layer.s: src/layers/reorg_layer.cpp.s

.PHONY : src/layers/reorg_layer.s

# target to generate assembly for a file
src/layers/reorg_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/reorg_layer.cpp.s
.PHONY : src/layers/reorg_layer.cpp.s

src/layers/reshape_layer.obj: src/layers/reshape_layer.cpp.obj

.PHONY : src/layers/reshape_layer.obj

# target to build an object file
src/layers/reshape_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/reshape_layer.cpp.obj
.PHONY : src/layers/reshape_layer.cpp.obj

src/layers/reshape_layer.i: src/layers/reshape_layer.cpp.i

.PHONY : src/layers/reshape_layer.i

# target to preprocess a source file
src/layers/reshape_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/reshape_layer.cpp.i
.PHONY : src/layers/reshape_layer.cpp.i

src/layers/reshape_layer.s: src/layers/reshape_layer.cpp.s

.PHONY : src/layers/reshape_layer.s

# target to generate assembly for a file
src/layers/reshape_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/reshape_layer.cpp.s
.PHONY : src/layers/reshape_layer.cpp.s

src/layers/resize_layer.obj: src/layers/resize_layer.cpp.obj

.PHONY : src/layers/resize_layer.obj

# target to build an object file
src/layers/resize_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/resize_layer.cpp.obj
.PHONY : src/layers/resize_layer.cpp.obj

src/layers/resize_layer.i: src/layers/resize_layer.cpp.i

.PHONY : src/layers/resize_layer.i

# target to preprocess a source file
src/layers/resize_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/resize_layer.cpp.i
.PHONY : src/layers/resize_layer.cpp.i

src/layers/resize_layer.s: src/layers/resize_layer.cpp.s

.PHONY : src/layers/resize_layer.s

# target to generate assembly for a file
src/layers/resize_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/resize_layer.cpp.s
.PHONY : src/layers/resize_layer.cpp.s

src/layers/scale_layer.obj: src/layers/scale_layer.cpp.obj

.PHONY : src/layers/scale_layer.obj

# target to build an object file
src/layers/scale_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/scale_layer.cpp.obj
.PHONY : src/layers/scale_layer.cpp.obj

src/layers/scale_layer.i: src/layers/scale_layer.cpp.i

.PHONY : src/layers/scale_layer.i

# target to preprocess a source file
src/layers/scale_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/scale_layer.cpp.i
.PHONY : src/layers/scale_layer.cpp.i

src/layers/scale_layer.s: src/layers/scale_layer.cpp.s

.PHONY : src/layers/scale_layer.s

# target to generate assembly for a file
src/layers/scale_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/scale_layer.cpp.s
.PHONY : src/layers/scale_layer.cpp.s

src/layers/shuffle_channel_layer.obj: src/layers/shuffle_channel_layer.cpp.obj

.PHONY : src/layers/shuffle_channel_layer.obj

# target to build an object file
src/layers/shuffle_channel_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/shuffle_channel_layer.cpp.obj
.PHONY : src/layers/shuffle_channel_layer.cpp.obj

src/layers/shuffle_channel_layer.i: src/layers/shuffle_channel_layer.cpp.i

.PHONY : src/layers/shuffle_channel_layer.i

# target to preprocess a source file
src/layers/shuffle_channel_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/shuffle_channel_layer.cpp.i
.PHONY : src/layers/shuffle_channel_layer.cpp.i

src/layers/shuffle_channel_layer.s: src/layers/shuffle_channel_layer.cpp.s

.PHONY : src/layers/shuffle_channel_layer.s

# target to generate assembly for a file
src/layers/shuffle_channel_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/shuffle_channel_layer.cpp.s
.PHONY : src/layers/shuffle_channel_layer.cpp.s

src/layers/slice_layer.obj: src/layers/slice_layer.cpp.obj

.PHONY : src/layers/slice_layer.obj

# target to build an object file
src/layers/slice_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/slice_layer.cpp.obj
.PHONY : src/layers/slice_layer.cpp.obj

src/layers/slice_layer.i: src/layers/slice_layer.cpp.i

.PHONY : src/layers/slice_layer.i

# target to preprocess a source file
src/layers/slice_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/slice_layer.cpp.i
.PHONY : src/layers/slice_layer.cpp.i

src/layers/slice_layer.s: src/layers/slice_layer.cpp.s

.PHONY : src/layers/slice_layer.s

# target to generate assembly for a file
src/layers/slice_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/slice_layer.cpp.s
.PHONY : src/layers/slice_layer.cpp.s

src/layers/softmax_layer.obj: src/layers/softmax_layer.cpp.obj

.PHONY : src/layers/softmax_layer.obj

# target to build an object file
src/layers/softmax_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/softmax_layer.cpp.obj
.PHONY : src/layers/softmax_layer.cpp.obj

src/layers/softmax_layer.i: src/layers/softmax_layer.cpp.i

.PHONY : src/layers/softmax_layer.i

# target to preprocess a source file
src/layers/softmax_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/softmax_layer.cpp.i
.PHONY : src/layers/softmax_layer.cpp.i

src/layers/softmax_layer.s: src/layers/softmax_layer.cpp.s

.PHONY : src/layers/softmax_layer.s

# target to generate assembly for a file
src/layers/softmax_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/softmax_layer.cpp.s
.PHONY : src/layers/softmax_layer.cpp.s

src/layers/split_layer.obj: src/layers/split_layer.cpp.obj

.PHONY : src/layers/split_layer.obj

# target to build an object file
src/layers/split_layer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/split_layer.cpp.obj
.PHONY : src/layers/split_layer.cpp.obj

src/layers/split_layer.i: src/layers/split_layer.cpp.i

.PHONY : src/layers/split_layer.i

# target to preprocess a source file
src/layers/split_layer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/split_layer.cpp.i
.PHONY : src/layers/split_layer.cpp.i

src/layers/split_layer.s: src/layers/split_layer.cpp.s

.PHONY : src/layers/split_layer.s

# target to generate assembly for a file
src/layers/split_layer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/split_layer.cpp.s
.PHONY : src/layers/split_layer.cpp.s

src/model.obj: src/model.cpp.obj

.PHONY : src/model.obj

# target to build an object file
src/model.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/model.cpp.obj
.PHONY : src/model.cpp.obj

src/model.i: src/model.cpp.i

.PHONY : src/model.i

# target to preprocess a source file
src/model.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/model.cpp.i
.PHONY : src/model.cpp.i

src/model.s: src/model.cpp.s

.PHONY : src/model.s

# target to generate assembly for a file
src/model.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/model.cpp.s
.PHONY : src/model.cpp.s

src/nms.obj: src/nms.cpp.obj

.PHONY : src/nms.obj

# target to build an object file
src/nms.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/nms.cpp.obj
.PHONY : src/nms.cpp.obj

src/nms.i: src/nms.cpp.i

.PHONY : src/nms.i

# target to preprocess a source file
src/nms.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/nms.cpp.i
.PHONY : src/nms.cpp.i

src/nms.s: src/nms.cpp.s

.PHONY : src/nms.s

# target to generate assembly for a file
src/nms.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/nms.cpp.s
.PHONY : src/nms.cpp.s

src/ocl4dnn/src/common.obj: src/ocl4dnn/src/common.cpp.obj

.PHONY : src/ocl4dnn/src/common.obj

# target to build an object file
src/ocl4dnn/src/common.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/common.cpp.obj
.PHONY : src/ocl4dnn/src/common.cpp.obj

src/ocl4dnn/src/common.i: src/ocl4dnn/src/common.cpp.i

.PHONY : src/ocl4dnn/src/common.i

# target to preprocess a source file
src/ocl4dnn/src/common.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/common.cpp.i
.PHONY : src/ocl4dnn/src/common.cpp.i

src/ocl4dnn/src/common.s: src/ocl4dnn/src/common.cpp.s

.PHONY : src/ocl4dnn/src/common.s

# target to generate assembly for a file
src/ocl4dnn/src/common.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/common.cpp.s
.PHONY : src/ocl4dnn/src/common.cpp.s

src/ocl4dnn/src/math_functions.obj: src/ocl4dnn/src/math_functions.cpp.obj

.PHONY : src/ocl4dnn/src/math_functions.obj

# target to build an object file
src/ocl4dnn/src/math_functions.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/math_functions.cpp.obj
.PHONY : src/ocl4dnn/src/math_functions.cpp.obj

src/ocl4dnn/src/math_functions.i: src/ocl4dnn/src/math_functions.cpp.i

.PHONY : src/ocl4dnn/src/math_functions.i

# target to preprocess a source file
src/ocl4dnn/src/math_functions.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/math_functions.cpp.i
.PHONY : src/ocl4dnn/src/math_functions.cpp.i

src/ocl4dnn/src/math_functions.s: src/ocl4dnn/src/math_functions.cpp.s

.PHONY : src/ocl4dnn/src/math_functions.s

# target to generate assembly for a file
src/ocl4dnn/src/math_functions.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/math_functions.cpp.s
.PHONY : src/ocl4dnn/src/math_functions.cpp.s

src/ocl4dnn/src/ocl4dnn_conv_spatial.obj: src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp.obj

.PHONY : src/ocl4dnn/src/ocl4dnn_conv_spatial.obj

# target to build an object file
src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp.obj
.PHONY : src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp.obj

src/ocl4dnn/src/ocl4dnn_conv_spatial.i: src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp.i

.PHONY : src/ocl4dnn/src/ocl4dnn_conv_spatial.i

# target to preprocess a source file
src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp.i
.PHONY : src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp.i

src/ocl4dnn/src/ocl4dnn_conv_spatial.s: src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp.s

.PHONY : src/ocl4dnn/src/ocl4dnn_conv_spatial.s

# target to generate assembly for a file
src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp.s
.PHONY : src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp.s

src/ocl4dnn/src/ocl4dnn_inner_product.obj: src/ocl4dnn/src/ocl4dnn_inner_product.cpp.obj

.PHONY : src/ocl4dnn/src/ocl4dnn_inner_product.obj

# target to build an object file
src/ocl4dnn/src/ocl4dnn_inner_product.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_inner_product.cpp.obj
.PHONY : src/ocl4dnn/src/ocl4dnn_inner_product.cpp.obj

src/ocl4dnn/src/ocl4dnn_inner_product.i: src/ocl4dnn/src/ocl4dnn_inner_product.cpp.i

.PHONY : src/ocl4dnn/src/ocl4dnn_inner_product.i

# target to preprocess a source file
src/ocl4dnn/src/ocl4dnn_inner_product.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_inner_product.cpp.i
.PHONY : src/ocl4dnn/src/ocl4dnn_inner_product.cpp.i

src/ocl4dnn/src/ocl4dnn_inner_product.s: src/ocl4dnn/src/ocl4dnn_inner_product.cpp.s

.PHONY : src/ocl4dnn/src/ocl4dnn_inner_product.s

# target to generate assembly for a file
src/ocl4dnn/src/ocl4dnn_inner_product.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_inner_product.cpp.s
.PHONY : src/ocl4dnn/src/ocl4dnn_inner_product.cpp.s

src/ocl4dnn/src/ocl4dnn_lrn.obj: src/ocl4dnn/src/ocl4dnn_lrn.cpp.obj

.PHONY : src/ocl4dnn/src/ocl4dnn_lrn.obj

# target to build an object file
src/ocl4dnn/src/ocl4dnn_lrn.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_lrn.cpp.obj
.PHONY : src/ocl4dnn/src/ocl4dnn_lrn.cpp.obj

src/ocl4dnn/src/ocl4dnn_lrn.i: src/ocl4dnn/src/ocl4dnn_lrn.cpp.i

.PHONY : src/ocl4dnn/src/ocl4dnn_lrn.i

# target to preprocess a source file
src/ocl4dnn/src/ocl4dnn_lrn.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_lrn.cpp.i
.PHONY : src/ocl4dnn/src/ocl4dnn_lrn.cpp.i

src/ocl4dnn/src/ocl4dnn_lrn.s: src/ocl4dnn/src/ocl4dnn_lrn.cpp.s

.PHONY : src/ocl4dnn/src/ocl4dnn_lrn.s

# target to generate assembly for a file
src/ocl4dnn/src/ocl4dnn_lrn.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_lrn.cpp.s
.PHONY : src/ocl4dnn/src/ocl4dnn_lrn.cpp.s

src/ocl4dnn/src/ocl4dnn_pool.obj: src/ocl4dnn/src/ocl4dnn_pool.cpp.obj

.PHONY : src/ocl4dnn/src/ocl4dnn_pool.obj

# target to build an object file
src/ocl4dnn/src/ocl4dnn_pool.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_pool.cpp.obj
.PHONY : src/ocl4dnn/src/ocl4dnn_pool.cpp.obj

src/ocl4dnn/src/ocl4dnn_pool.i: src/ocl4dnn/src/ocl4dnn_pool.cpp.i

.PHONY : src/ocl4dnn/src/ocl4dnn_pool.i

# target to preprocess a source file
src/ocl4dnn/src/ocl4dnn_pool.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_pool.cpp.i
.PHONY : src/ocl4dnn/src/ocl4dnn_pool.cpp.i

src/ocl4dnn/src/ocl4dnn_pool.s: src/ocl4dnn/src/ocl4dnn_pool.cpp.s

.PHONY : src/ocl4dnn/src/ocl4dnn_pool.s

# target to generate assembly for a file
src/ocl4dnn/src/ocl4dnn_pool.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_pool.cpp.s
.PHONY : src/ocl4dnn/src/ocl4dnn_pool.cpp.s

src/ocl4dnn/src/ocl4dnn_softmax.obj: src/ocl4dnn/src/ocl4dnn_softmax.cpp.obj

.PHONY : src/ocl4dnn/src/ocl4dnn_softmax.obj

# target to build an object file
src/ocl4dnn/src/ocl4dnn_softmax.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_softmax.cpp.obj
.PHONY : src/ocl4dnn/src/ocl4dnn_softmax.cpp.obj

src/ocl4dnn/src/ocl4dnn_softmax.i: src/ocl4dnn/src/ocl4dnn_softmax.cpp.i

.PHONY : src/ocl4dnn/src/ocl4dnn_softmax.i

# target to preprocess a source file
src/ocl4dnn/src/ocl4dnn_softmax.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_softmax.cpp.i
.PHONY : src/ocl4dnn/src/ocl4dnn_softmax.cpp.i

src/ocl4dnn/src/ocl4dnn_softmax.s: src/ocl4dnn/src/ocl4dnn_softmax.cpp.s

.PHONY : src/ocl4dnn/src/ocl4dnn_softmax.s

# target to generate assembly for a file
src/ocl4dnn/src/ocl4dnn_softmax.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_softmax.cpp.s
.PHONY : src/ocl4dnn/src/ocl4dnn_softmax.cpp.s

src/onnx/onnx_importer.obj: src/onnx/onnx_importer.cpp.obj

.PHONY : src/onnx/onnx_importer.obj

# target to build an object file
src/onnx/onnx_importer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/onnx/onnx_importer.cpp.obj
.PHONY : src/onnx/onnx_importer.cpp.obj

src/onnx/onnx_importer.i: src/onnx/onnx_importer.cpp.i

.PHONY : src/onnx/onnx_importer.i

# target to preprocess a source file
src/onnx/onnx_importer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/onnx/onnx_importer.cpp.i
.PHONY : src/onnx/onnx_importer.cpp.i

src/onnx/onnx_importer.s: src/onnx/onnx_importer.cpp.s

.PHONY : src/onnx/onnx_importer.s

# target to generate assembly for a file
src/onnx/onnx_importer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/onnx/onnx_importer.cpp.s
.PHONY : src/onnx/onnx_importer.cpp.s

src/op_halide.obj: src/op_halide.cpp.obj

.PHONY : src/op_halide.obj

# target to build an object file
src/op_halide.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/op_halide.cpp.obj
.PHONY : src/op_halide.cpp.obj

src/op_halide.i: src/op_halide.cpp.i

.PHONY : src/op_halide.i

# target to preprocess a source file
src/op_halide.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/op_halide.cpp.i
.PHONY : src/op_halide.cpp.i

src/op_halide.s: src/op_halide.cpp.s

.PHONY : src/op_halide.s

# target to generate assembly for a file
src/op_halide.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/op_halide.cpp.s
.PHONY : src/op_halide.cpp.s

src/op_inf_engine.obj: src/op_inf_engine.cpp.obj

.PHONY : src/op_inf_engine.obj

# target to build an object file
src/op_inf_engine.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/op_inf_engine.cpp.obj
.PHONY : src/op_inf_engine.cpp.obj

src/op_inf_engine.i: src/op_inf_engine.cpp.i

.PHONY : src/op_inf_engine.i

# target to preprocess a source file
src/op_inf_engine.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/op_inf_engine.cpp.i
.PHONY : src/op_inf_engine.cpp.i

src/op_inf_engine.s: src/op_inf_engine.cpp.s

.PHONY : src/op_inf_engine.s

# target to generate assembly for a file
src/op_inf_engine.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/op_inf_engine.cpp.s
.PHONY : src/op_inf_engine.cpp.s

src/op_vkcom.obj: src/op_vkcom.cpp.obj

.PHONY : src/op_vkcom.obj

# target to build an object file
src/op_vkcom.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/op_vkcom.cpp.obj
.PHONY : src/op_vkcom.cpp.obj

src/op_vkcom.i: src/op_vkcom.cpp.i

.PHONY : src/op_vkcom.i

# target to preprocess a source file
src/op_vkcom.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/op_vkcom.cpp.i
.PHONY : src/op_vkcom.cpp.i

src/op_vkcom.s: src/op_vkcom.cpp.s

.PHONY : src/op_vkcom.s

# target to generate assembly for a file
src/op_vkcom.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/op_vkcom.cpp.s
.PHONY : src/op_vkcom.cpp.s

src/tensorflow/tf_graph_simplifier.obj: src/tensorflow/tf_graph_simplifier.cpp.obj

.PHONY : src/tensorflow/tf_graph_simplifier.obj

# target to build an object file
src/tensorflow/tf_graph_simplifier.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/tensorflow/tf_graph_simplifier.cpp.obj
.PHONY : src/tensorflow/tf_graph_simplifier.cpp.obj

src/tensorflow/tf_graph_simplifier.i: src/tensorflow/tf_graph_simplifier.cpp.i

.PHONY : src/tensorflow/tf_graph_simplifier.i

# target to preprocess a source file
src/tensorflow/tf_graph_simplifier.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/tensorflow/tf_graph_simplifier.cpp.i
.PHONY : src/tensorflow/tf_graph_simplifier.cpp.i

src/tensorflow/tf_graph_simplifier.s: src/tensorflow/tf_graph_simplifier.cpp.s

.PHONY : src/tensorflow/tf_graph_simplifier.s

# target to generate assembly for a file
src/tensorflow/tf_graph_simplifier.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/tensorflow/tf_graph_simplifier.cpp.s
.PHONY : src/tensorflow/tf_graph_simplifier.cpp.s

src/tensorflow/tf_importer.obj: src/tensorflow/tf_importer.cpp.obj

.PHONY : src/tensorflow/tf_importer.obj

# target to build an object file
src/tensorflow/tf_importer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/tensorflow/tf_importer.cpp.obj
.PHONY : src/tensorflow/tf_importer.cpp.obj

src/tensorflow/tf_importer.i: src/tensorflow/tf_importer.cpp.i

.PHONY : src/tensorflow/tf_importer.i

# target to preprocess a source file
src/tensorflow/tf_importer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/tensorflow/tf_importer.cpp.i
.PHONY : src/tensorflow/tf_importer.cpp.i

src/tensorflow/tf_importer.s: src/tensorflow/tf_importer.cpp.s

.PHONY : src/tensorflow/tf_importer.s

# target to generate assembly for a file
src/tensorflow/tf_importer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/tensorflow/tf_importer.cpp.s
.PHONY : src/tensorflow/tf_importer.cpp.s

src/tensorflow/tf_io.obj: src/tensorflow/tf_io.cpp.obj

.PHONY : src/tensorflow/tf_io.obj

# target to build an object file
src/tensorflow/tf_io.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/tensorflow/tf_io.cpp.obj
.PHONY : src/tensorflow/tf_io.cpp.obj

src/tensorflow/tf_io.i: src/tensorflow/tf_io.cpp.i

.PHONY : src/tensorflow/tf_io.i

# target to preprocess a source file
src/tensorflow/tf_io.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/tensorflow/tf_io.cpp.i
.PHONY : src/tensorflow/tf_io.cpp.i

src/tensorflow/tf_io.s: src/tensorflow/tf_io.cpp.s

.PHONY : src/tensorflow/tf_io.s

# target to generate assembly for a file
src/tensorflow/tf_io.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/tensorflow/tf_io.cpp.s
.PHONY : src/tensorflow/tf_io.cpp.s

src/torch/THDiskFile.obj: src/torch/THDiskFile.cpp.obj

.PHONY : src/torch/THDiskFile.obj

# target to build an object file
src/torch/THDiskFile.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/THDiskFile.cpp.obj
.PHONY : src/torch/THDiskFile.cpp.obj

src/torch/THDiskFile.i: src/torch/THDiskFile.cpp.i

.PHONY : src/torch/THDiskFile.i

# target to preprocess a source file
src/torch/THDiskFile.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/THDiskFile.cpp.i
.PHONY : src/torch/THDiskFile.cpp.i

src/torch/THDiskFile.s: src/torch/THDiskFile.cpp.s

.PHONY : src/torch/THDiskFile.s

# target to generate assembly for a file
src/torch/THDiskFile.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/THDiskFile.cpp.s
.PHONY : src/torch/THDiskFile.cpp.s

src/torch/THFile.obj: src/torch/THFile.cpp.obj

.PHONY : src/torch/THFile.obj

# target to build an object file
src/torch/THFile.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/THFile.cpp.obj
.PHONY : src/torch/THFile.cpp.obj

src/torch/THFile.i: src/torch/THFile.cpp.i

.PHONY : src/torch/THFile.i

# target to preprocess a source file
src/torch/THFile.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/THFile.cpp.i
.PHONY : src/torch/THFile.cpp.i

src/torch/THFile.s: src/torch/THFile.cpp.s

.PHONY : src/torch/THFile.s

# target to generate assembly for a file
src/torch/THFile.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/THFile.cpp.s
.PHONY : src/torch/THFile.cpp.s

src/torch/THGeneral.obj: src/torch/THGeneral.cpp.obj

.PHONY : src/torch/THGeneral.obj

# target to build an object file
src/torch/THGeneral.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/THGeneral.cpp.obj
.PHONY : src/torch/THGeneral.cpp.obj

src/torch/THGeneral.i: src/torch/THGeneral.cpp.i

.PHONY : src/torch/THGeneral.i

# target to preprocess a source file
src/torch/THGeneral.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/THGeneral.cpp.i
.PHONY : src/torch/THGeneral.cpp.i

src/torch/THGeneral.s: src/torch/THGeneral.cpp.s

.PHONY : src/torch/THGeneral.s

# target to generate assembly for a file
src/torch/THGeneral.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/THGeneral.cpp.s
.PHONY : src/torch/THGeneral.cpp.s

src/torch/torch_importer.obj: src/torch/torch_importer.cpp.obj

.PHONY : src/torch/torch_importer.obj

# target to build an object file
src/torch/torch_importer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/torch_importer.cpp.obj
.PHONY : src/torch/torch_importer.cpp.obj

src/torch/torch_importer.i: src/torch/torch_importer.cpp.i

.PHONY : src/torch/torch_importer.i

# target to preprocess a source file
src/torch/torch_importer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/torch_importer.cpp.i
.PHONY : src/torch/torch_importer.cpp.i

src/torch/torch_importer.s: src/torch/torch_importer.cpp.s

.PHONY : src/torch/torch_importer.s

# target to generate assembly for a file
src/torch/torch_importer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/torch_importer.cpp.s
.PHONY : src/torch/torch_importer.cpp.s

src/vkcom/shader/avg_pool_spv.obj: src/vkcom/shader/avg_pool_spv.cpp.obj

.PHONY : src/vkcom/shader/avg_pool_spv.obj

# target to build an object file
src/vkcom/shader/avg_pool_spv.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/avg_pool_spv.cpp.obj
.PHONY : src/vkcom/shader/avg_pool_spv.cpp.obj

src/vkcom/shader/avg_pool_spv.i: src/vkcom/shader/avg_pool_spv.cpp.i

.PHONY : src/vkcom/shader/avg_pool_spv.i

# target to preprocess a source file
src/vkcom/shader/avg_pool_spv.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/avg_pool_spv.cpp.i
.PHONY : src/vkcom/shader/avg_pool_spv.cpp.i

src/vkcom/shader/avg_pool_spv.s: src/vkcom/shader/avg_pool_spv.cpp.s

.PHONY : src/vkcom/shader/avg_pool_spv.s

# target to generate assembly for a file
src/vkcom/shader/avg_pool_spv.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/avg_pool_spv.cpp.s
.PHONY : src/vkcom/shader/avg_pool_spv.cpp.s

src/vkcom/shader/concat_spv.obj: src/vkcom/shader/concat_spv.cpp.obj

.PHONY : src/vkcom/shader/concat_spv.obj

# target to build an object file
src/vkcom/shader/concat_spv.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/concat_spv.cpp.obj
.PHONY : src/vkcom/shader/concat_spv.cpp.obj

src/vkcom/shader/concat_spv.i: src/vkcom/shader/concat_spv.cpp.i

.PHONY : src/vkcom/shader/concat_spv.i

# target to preprocess a source file
src/vkcom/shader/concat_spv.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/concat_spv.cpp.i
.PHONY : src/vkcom/shader/concat_spv.cpp.i

src/vkcom/shader/concat_spv.s: src/vkcom/shader/concat_spv.cpp.s

.PHONY : src/vkcom/shader/concat_spv.s

# target to generate assembly for a file
src/vkcom/shader/concat_spv.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/concat_spv.cpp.s
.PHONY : src/vkcom/shader/concat_spv.cpp.s

src/vkcom/shader/conv48_spv.obj: src/vkcom/shader/conv48_spv.cpp.obj

.PHONY : src/vkcom/shader/conv48_spv.obj

# target to build an object file
src/vkcom/shader/conv48_spv.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/conv48_spv.cpp.obj
.PHONY : src/vkcom/shader/conv48_spv.cpp.obj

src/vkcom/shader/conv48_spv.i: src/vkcom/shader/conv48_spv.cpp.i

.PHONY : src/vkcom/shader/conv48_spv.i

# target to preprocess a source file
src/vkcom/shader/conv48_spv.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/conv48_spv.cpp.i
.PHONY : src/vkcom/shader/conv48_spv.cpp.i

src/vkcom/shader/conv48_spv.s: src/vkcom/shader/conv48_spv.cpp.s

.PHONY : src/vkcom/shader/conv48_spv.s

# target to generate assembly for a file
src/vkcom/shader/conv48_spv.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/conv48_spv.cpp.s
.PHONY : src/vkcom/shader/conv48_spv.cpp.s

src/vkcom/shader/conv_spv.obj: src/vkcom/shader/conv_spv.cpp.obj

.PHONY : src/vkcom/shader/conv_spv.obj

# target to build an object file
src/vkcom/shader/conv_spv.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/conv_spv.cpp.obj
.PHONY : src/vkcom/shader/conv_spv.cpp.obj

src/vkcom/shader/conv_spv.i: src/vkcom/shader/conv_spv.cpp.i

.PHONY : src/vkcom/shader/conv_spv.i

# target to preprocess a source file
src/vkcom/shader/conv_spv.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/conv_spv.cpp.i
.PHONY : src/vkcom/shader/conv_spv.cpp.i

src/vkcom/shader/conv_spv.s: src/vkcom/shader/conv_spv.cpp.s

.PHONY : src/vkcom/shader/conv_spv.s

# target to generate assembly for a file
src/vkcom/shader/conv_spv.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/conv_spv.cpp.s
.PHONY : src/vkcom/shader/conv_spv.cpp.s

src/vkcom/shader/dw_conv_spv.obj: src/vkcom/shader/dw_conv_spv.cpp.obj

.PHONY : src/vkcom/shader/dw_conv_spv.obj

# target to build an object file
src/vkcom/shader/dw_conv_spv.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/dw_conv_spv.cpp.obj
.PHONY : src/vkcom/shader/dw_conv_spv.cpp.obj

src/vkcom/shader/dw_conv_spv.i: src/vkcom/shader/dw_conv_spv.cpp.i

.PHONY : src/vkcom/shader/dw_conv_spv.i

# target to preprocess a source file
src/vkcom/shader/dw_conv_spv.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/dw_conv_spv.cpp.i
.PHONY : src/vkcom/shader/dw_conv_spv.cpp.i

src/vkcom/shader/dw_conv_spv.s: src/vkcom/shader/dw_conv_spv.cpp.s

.PHONY : src/vkcom/shader/dw_conv_spv.s

# target to generate assembly for a file
src/vkcom/shader/dw_conv_spv.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/dw_conv_spv.cpp.s
.PHONY : src/vkcom/shader/dw_conv_spv.cpp.s

src/vkcom/shader/lrn_spv.obj: src/vkcom/shader/lrn_spv.cpp.obj

.PHONY : src/vkcom/shader/lrn_spv.obj

# target to build an object file
src/vkcom/shader/lrn_spv.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/lrn_spv.cpp.obj
.PHONY : src/vkcom/shader/lrn_spv.cpp.obj

src/vkcom/shader/lrn_spv.i: src/vkcom/shader/lrn_spv.cpp.i

.PHONY : src/vkcom/shader/lrn_spv.i

# target to preprocess a source file
src/vkcom/shader/lrn_spv.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/lrn_spv.cpp.i
.PHONY : src/vkcom/shader/lrn_spv.cpp.i

src/vkcom/shader/lrn_spv.s: src/vkcom/shader/lrn_spv.cpp.s

.PHONY : src/vkcom/shader/lrn_spv.s

# target to generate assembly for a file
src/vkcom/shader/lrn_spv.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/lrn_spv.cpp.s
.PHONY : src/vkcom/shader/lrn_spv.cpp.s

src/vkcom/shader/max_pool_spv.obj: src/vkcom/shader/max_pool_spv.cpp.obj

.PHONY : src/vkcom/shader/max_pool_spv.obj

# target to build an object file
src/vkcom/shader/max_pool_spv.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/max_pool_spv.cpp.obj
.PHONY : src/vkcom/shader/max_pool_spv.cpp.obj

src/vkcom/shader/max_pool_spv.i: src/vkcom/shader/max_pool_spv.cpp.i

.PHONY : src/vkcom/shader/max_pool_spv.i

# target to preprocess a source file
src/vkcom/shader/max_pool_spv.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/max_pool_spv.cpp.i
.PHONY : src/vkcom/shader/max_pool_spv.cpp.i

src/vkcom/shader/max_pool_spv.s: src/vkcom/shader/max_pool_spv.cpp.s

.PHONY : src/vkcom/shader/max_pool_spv.s

# target to generate assembly for a file
src/vkcom/shader/max_pool_spv.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/max_pool_spv.cpp.s
.PHONY : src/vkcom/shader/max_pool_spv.cpp.s

src/vkcom/shader/permute_spv.obj: src/vkcom/shader/permute_spv.cpp.obj

.PHONY : src/vkcom/shader/permute_spv.obj

# target to build an object file
src/vkcom/shader/permute_spv.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/permute_spv.cpp.obj
.PHONY : src/vkcom/shader/permute_spv.cpp.obj

src/vkcom/shader/permute_spv.i: src/vkcom/shader/permute_spv.cpp.i

.PHONY : src/vkcom/shader/permute_spv.i

# target to preprocess a source file
src/vkcom/shader/permute_spv.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/permute_spv.cpp.i
.PHONY : src/vkcom/shader/permute_spv.cpp.i

src/vkcom/shader/permute_spv.s: src/vkcom/shader/permute_spv.cpp.s

.PHONY : src/vkcom/shader/permute_spv.s

# target to generate assembly for a file
src/vkcom/shader/permute_spv.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/permute_spv.cpp.s
.PHONY : src/vkcom/shader/permute_spv.cpp.s

src/vkcom/shader/prior_box_spv.obj: src/vkcom/shader/prior_box_spv.cpp.obj

.PHONY : src/vkcom/shader/prior_box_spv.obj

# target to build an object file
src/vkcom/shader/prior_box_spv.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/prior_box_spv.cpp.obj
.PHONY : src/vkcom/shader/prior_box_spv.cpp.obj

src/vkcom/shader/prior_box_spv.i: src/vkcom/shader/prior_box_spv.cpp.i

.PHONY : src/vkcom/shader/prior_box_spv.i

# target to preprocess a source file
src/vkcom/shader/prior_box_spv.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/prior_box_spv.cpp.i
.PHONY : src/vkcom/shader/prior_box_spv.cpp.i

src/vkcom/shader/prior_box_spv.s: src/vkcom/shader/prior_box_spv.cpp.s

.PHONY : src/vkcom/shader/prior_box_spv.s

# target to generate assembly for a file
src/vkcom/shader/prior_box_spv.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/prior_box_spv.cpp.s
.PHONY : src/vkcom/shader/prior_box_spv.cpp.s

src/vkcom/shader/relu_spv.obj: src/vkcom/shader/relu_spv.cpp.obj

.PHONY : src/vkcom/shader/relu_spv.obj

# target to build an object file
src/vkcom/shader/relu_spv.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/relu_spv.cpp.obj
.PHONY : src/vkcom/shader/relu_spv.cpp.obj

src/vkcom/shader/relu_spv.i: src/vkcom/shader/relu_spv.cpp.i

.PHONY : src/vkcom/shader/relu_spv.i

# target to preprocess a source file
src/vkcom/shader/relu_spv.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/relu_spv.cpp.i
.PHONY : src/vkcom/shader/relu_spv.cpp.i

src/vkcom/shader/relu_spv.s: src/vkcom/shader/relu_spv.cpp.s

.PHONY : src/vkcom/shader/relu_spv.s

# target to generate assembly for a file
src/vkcom/shader/relu_spv.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/relu_spv.cpp.s
.PHONY : src/vkcom/shader/relu_spv.cpp.s

src/vkcom/shader/softmax_spv.obj: src/vkcom/shader/softmax_spv.cpp.obj

.PHONY : src/vkcom/shader/softmax_spv.obj

# target to build an object file
src/vkcom/shader/softmax_spv.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/softmax_spv.cpp.obj
.PHONY : src/vkcom/shader/softmax_spv.cpp.obj

src/vkcom/shader/softmax_spv.i: src/vkcom/shader/softmax_spv.cpp.i

.PHONY : src/vkcom/shader/softmax_spv.i

# target to preprocess a source file
src/vkcom/shader/softmax_spv.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/softmax_spv.cpp.i
.PHONY : src/vkcom/shader/softmax_spv.cpp.i

src/vkcom/shader/softmax_spv.s: src/vkcom/shader/softmax_spv.cpp.s

.PHONY : src/vkcom/shader/softmax_spv.s

# target to generate assembly for a file
src/vkcom/shader/softmax_spv.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/softmax_spv.cpp.s
.PHONY : src/vkcom/shader/softmax_spv.cpp.s

src/vkcom/src/buffer.obj: src/vkcom/src/buffer.cpp.obj

.PHONY : src/vkcom/src/buffer.obj

# target to build an object file
src/vkcom/src/buffer.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/buffer.cpp.obj
.PHONY : src/vkcom/src/buffer.cpp.obj

src/vkcom/src/buffer.i: src/vkcom/src/buffer.cpp.i

.PHONY : src/vkcom/src/buffer.i

# target to preprocess a source file
src/vkcom/src/buffer.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/buffer.cpp.i
.PHONY : src/vkcom/src/buffer.cpp.i

src/vkcom/src/buffer.s: src/vkcom/src/buffer.cpp.s

.PHONY : src/vkcom/src/buffer.s

# target to generate assembly for a file
src/vkcom/src/buffer.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/buffer.cpp.s
.PHONY : src/vkcom/src/buffer.cpp.s

src/vkcom/src/context.obj: src/vkcom/src/context.cpp.obj

.PHONY : src/vkcom/src/context.obj

# target to build an object file
src/vkcom/src/context.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/context.cpp.obj
.PHONY : src/vkcom/src/context.cpp.obj

src/vkcom/src/context.i: src/vkcom/src/context.cpp.i

.PHONY : src/vkcom/src/context.i

# target to preprocess a source file
src/vkcom/src/context.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/context.cpp.i
.PHONY : src/vkcom/src/context.cpp.i

src/vkcom/src/context.s: src/vkcom/src/context.cpp.s

.PHONY : src/vkcom/src/context.s

# target to generate assembly for a file
src/vkcom/src/context.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/context.cpp.s
.PHONY : src/vkcom/src/context.cpp.s

src/vkcom/src/internal.obj: src/vkcom/src/internal.cpp.obj

.PHONY : src/vkcom/src/internal.obj

# target to build an object file
src/vkcom/src/internal.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/internal.cpp.obj
.PHONY : src/vkcom/src/internal.cpp.obj

src/vkcom/src/internal.i: src/vkcom/src/internal.cpp.i

.PHONY : src/vkcom/src/internal.i

# target to preprocess a source file
src/vkcom/src/internal.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/internal.cpp.i
.PHONY : src/vkcom/src/internal.cpp.i

src/vkcom/src/internal.s: src/vkcom/src/internal.cpp.s

.PHONY : src/vkcom/src/internal.s

# target to generate assembly for a file
src/vkcom/src/internal.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/internal.cpp.s
.PHONY : src/vkcom/src/internal.cpp.s

src/vkcom/src/op_base.obj: src/vkcom/src/op_base.cpp.obj

.PHONY : src/vkcom/src/op_base.obj

# target to build an object file
src/vkcom/src/op_base.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_base.cpp.obj
.PHONY : src/vkcom/src/op_base.cpp.obj

src/vkcom/src/op_base.i: src/vkcom/src/op_base.cpp.i

.PHONY : src/vkcom/src/op_base.i

# target to preprocess a source file
src/vkcom/src/op_base.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_base.cpp.i
.PHONY : src/vkcom/src/op_base.cpp.i

src/vkcom/src/op_base.s: src/vkcom/src/op_base.cpp.s

.PHONY : src/vkcom/src/op_base.s

# target to generate assembly for a file
src/vkcom/src/op_base.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_base.cpp.s
.PHONY : src/vkcom/src/op_base.cpp.s

src/vkcom/src/op_concat.obj: src/vkcom/src/op_concat.cpp.obj

.PHONY : src/vkcom/src/op_concat.obj

# target to build an object file
src/vkcom/src/op_concat.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_concat.cpp.obj
.PHONY : src/vkcom/src/op_concat.cpp.obj

src/vkcom/src/op_concat.i: src/vkcom/src/op_concat.cpp.i

.PHONY : src/vkcom/src/op_concat.i

# target to preprocess a source file
src/vkcom/src/op_concat.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_concat.cpp.i
.PHONY : src/vkcom/src/op_concat.cpp.i

src/vkcom/src/op_concat.s: src/vkcom/src/op_concat.cpp.s

.PHONY : src/vkcom/src/op_concat.s

# target to generate assembly for a file
src/vkcom/src/op_concat.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_concat.cpp.s
.PHONY : src/vkcom/src/op_concat.cpp.s

src/vkcom/src/op_conv.obj: src/vkcom/src/op_conv.cpp.obj

.PHONY : src/vkcom/src/op_conv.obj

# target to build an object file
src/vkcom/src/op_conv.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_conv.cpp.obj
.PHONY : src/vkcom/src/op_conv.cpp.obj

src/vkcom/src/op_conv.i: src/vkcom/src/op_conv.cpp.i

.PHONY : src/vkcom/src/op_conv.i

# target to preprocess a source file
src/vkcom/src/op_conv.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_conv.cpp.i
.PHONY : src/vkcom/src/op_conv.cpp.i

src/vkcom/src/op_conv.s: src/vkcom/src/op_conv.cpp.s

.PHONY : src/vkcom/src/op_conv.s

# target to generate assembly for a file
src/vkcom/src/op_conv.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_conv.cpp.s
.PHONY : src/vkcom/src/op_conv.cpp.s

src/vkcom/src/op_lrn.obj: src/vkcom/src/op_lrn.cpp.obj

.PHONY : src/vkcom/src/op_lrn.obj

# target to build an object file
src/vkcom/src/op_lrn.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_lrn.cpp.obj
.PHONY : src/vkcom/src/op_lrn.cpp.obj

src/vkcom/src/op_lrn.i: src/vkcom/src/op_lrn.cpp.i

.PHONY : src/vkcom/src/op_lrn.i

# target to preprocess a source file
src/vkcom/src/op_lrn.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_lrn.cpp.i
.PHONY : src/vkcom/src/op_lrn.cpp.i

src/vkcom/src/op_lrn.s: src/vkcom/src/op_lrn.cpp.s

.PHONY : src/vkcom/src/op_lrn.s

# target to generate assembly for a file
src/vkcom/src/op_lrn.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_lrn.cpp.s
.PHONY : src/vkcom/src/op_lrn.cpp.s

src/vkcom/src/op_permute.obj: src/vkcom/src/op_permute.cpp.obj

.PHONY : src/vkcom/src/op_permute.obj

# target to build an object file
src/vkcom/src/op_permute.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_permute.cpp.obj
.PHONY : src/vkcom/src/op_permute.cpp.obj

src/vkcom/src/op_permute.i: src/vkcom/src/op_permute.cpp.i

.PHONY : src/vkcom/src/op_permute.i

# target to preprocess a source file
src/vkcom/src/op_permute.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_permute.cpp.i
.PHONY : src/vkcom/src/op_permute.cpp.i

src/vkcom/src/op_permute.s: src/vkcom/src/op_permute.cpp.s

.PHONY : src/vkcom/src/op_permute.s

# target to generate assembly for a file
src/vkcom/src/op_permute.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_permute.cpp.s
.PHONY : src/vkcom/src/op_permute.cpp.s

src/vkcom/src/op_pool.obj: src/vkcom/src/op_pool.cpp.obj

.PHONY : src/vkcom/src/op_pool.obj

# target to build an object file
src/vkcom/src/op_pool.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_pool.cpp.obj
.PHONY : src/vkcom/src/op_pool.cpp.obj

src/vkcom/src/op_pool.i: src/vkcom/src/op_pool.cpp.i

.PHONY : src/vkcom/src/op_pool.i

# target to preprocess a source file
src/vkcom/src/op_pool.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_pool.cpp.i
.PHONY : src/vkcom/src/op_pool.cpp.i

src/vkcom/src/op_pool.s: src/vkcom/src/op_pool.cpp.s

.PHONY : src/vkcom/src/op_pool.s

# target to generate assembly for a file
src/vkcom/src/op_pool.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_pool.cpp.s
.PHONY : src/vkcom/src/op_pool.cpp.s

src/vkcom/src/op_prior_box.obj: src/vkcom/src/op_prior_box.cpp.obj

.PHONY : src/vkcom/src/op_prior_box.obj

# target to build an object file
src/vkcom/src/op_prior_box.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_prior_box.cpp.obj
.PHONY : src/vkcom/src/op_prior_box.cpp.obj

src/vkcom/src/op_prior_box.i: src/vkcom/src/op_prior_box.cpp.i

.PHONY : src/vkcom/src/op_prior_box.i

# target to preprocess a source file
src/vkcom/src/op_prior_box.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_prior_box.cpp.i
.PHONY : src/vkcom/src/op_prior_box.cpp.i

src/vkcom/src/op_prior_box.s: src/vkcom/src/op_prior_box.cpp.s

.PHONY : src/vkcom/src/op_prior_box.s

# target to generate assembly for a file
src/vkcom/src/op_prior_box.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_prior_box.cpp.s
.PHONY : src/vkcom/src/op_prior_box.cpp.s

src/vkcom/src/op_relu.obj: src/vkcom/src/op_relu.cpp.obj

.PHONY : src/vkcom/src/op_relu.obj

# target to build an object file
src/vkcom/src/op_relu.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_relu.cpp.obj
.PHONY : src/vkcom/src/op_relu.cpp.obj

src/vkcom/src/op_relu.i: src/vkcom/src/op_relu.cpp.i

.PHONY : src/vkcom/src/op_relu.i

# target to preprocess a source file
src/vkcom/src/op_relu.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_relu.cpp.i
.PHONY : src/vkcom/src/op_relu.cpp.i

src/vkcom/src/op_relu.s: src/vkcom/src/op_relu.cpp.s

.PHONY : src/vkcom/src/op_relu.s

# target to generate assembly for a file
src/vkcom/src/op_relu.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_relu.cpp.s
.PHONY : src/vkcom/src/op_relu.cpp.s

src/vkcom/src/op_softmax.obj: src/vkcom/src/op_softmax.cpp.obj

.PHONY : src/vkcom/src/op_softmax.obj

# target to build an object file
src/vkcom/src/op_softmax.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_softmax.cpp.obj
.PHONY : src/vkcom/src/op_softmax.cpp.obj

src/vkcom/src/op_softmax.i: src/vkcom/src/op_softmax.cpp.i

.PHONY : src/vkcom/src/op_softmax.i

# target to preprocess a source file
src/vkcom/src/op_softmax.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_softmax.cpp.i
.PHONY : src/vkcom/src/op_softmax.cpp.i

src/vkcom/src/op_softmax.s: src/vkcom/src/op_softmax.cpp.s

.PHONY : src/vkcom/src/op_softmax.s

# target to generate assembly for a file
src/vkcom/src/op_softmax.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_softmax.cpp.s
.PHONY : src/vkcom/src/op_softmax.cpp.s

src/vkcom/src/tensor.obj: src/vkcom/src/tensor.cpp.obj

.PHONY : src/vkcom/src/tensor.obj

# target to build an object file
src/vkcom/src/tensor.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/tensor.cpp.obj
.PHONY : src/vkcom/src/tensor.cpp.obj

src/vkcom/src/tensor.i: src/vkcom/src/tensor.cpp.i

.PHONY : src/vkcom/src/tensor.i

# target to preprocess a source file
src/vkcom/src/tensor.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/tensor.cpp.i
.PHONY : src/vkcom/src/tensor.cpp.i

src/vkcom/src/tensor.s: src/vkcom/src/tensor.cpp.s

.PHONY : src/vkcom/src/tensor.s

# target to generate assembly for a file
src/vkcom/src/tensor.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/tensor.cpp.s
.PHONY : src/vkcom/src/tensor.cpp.s

src/vkcom/vulkan/vk_functions.obj: src/vkcom/vulkan/vk_functions.cpp.obj

.PHONY : src/vkcom/vulkan/vk_functions.obj

# target to build an object file
src/vkcom/vulkan/vk_functions.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/vulkan/vk_functions.cpp.obj
.PHONY : src/vkcom/vulkan/vk_functions.cpp.obj

src/vkcom/vulkan/vk_functions.i: src/vkcom/vulkan/vk_functions.cpp.i

.PHONY : src/vkcom/vulkan/vk_functions.i

# target to preprocess a source file
src/vkcom/vulkan/vk_functions.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/vulkan/vk_functions.cpp.i
.PHONY : src/vkcom/vulkan/vk_functions.cpp.i

src/vkcom/vulkan/vk_functions.s: src/vkcom/vulkan/vk_functions.cpp.s

.PHONY : src/vkcom/vulkan/vk_functions.s

# target to generate assembly for a file
src/vkcom/vulkan/vk_functions.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/vulkan/vk_functions.cpp.s
.PHONY : src/vkcom/vulkan/vk_functions.cpp.s

src/vkcom/vulkan/vk_loader.obj: src/vkcom/vulkan/vk_loader.cpp.obj

.PHONY : src/vkcom/vulkan/vk_loader.obj

# target to build an object file
src/vkcom/vulkan/vk_loader.cpp.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/vulkan/vk_loader.cpp.obj
.PHONY : src/vkcom/vulkan/vk_loader.cpp.obj

src/vkcom/vulkan/vk_loader.i: src/vkcom/vulkan/vk_loader.cpp.i

.PHONY : src/vkcom/vulkan/vk_loader.i

# target to preprocess a source file
src/vkcom/vulkan/vk_loader.cpp.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/vulkan/vk_loader.cpp.i
.PHONY : src/vkcom/vulkan/vk_loader.cpp.i

src/vkcom/vulkan/vk_loader.s: src/vkcom/vulkan/vk_loader.cpp.s

.PHONY : src/vkcom/vulkan/vk_loader.s

# target to generate assembly for a file
src/vkcom/vulkan/vk_loader.cpp.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/vulkan/vk_loader.cpp.s
.PHONY : src/vkcom/vulkan/vk_loader.cpp.s

vs_version.obj: vs_version.rc.obj

.PHONY : vs_version.obj

# target to build an object file
vs_version.rc.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/vs_version.rc.obj
.PHONY : vs_version.rc.obj

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... package
	@echo ... opencv_dnn
	@echo ... package_source
	@echo ... rebuild_cache
	@echo ... test
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... list_install_components
	@echo ... install/strip
	@echo ... layers/layers_common.avx.obj
	@echo ... layers/layers_common.avx.i
	@echo ... layers/layers_common.avx.s
	@echo ... layers/layers_common.avx2.obj
	@echo ... layers/layers_common.avx2.i
	@echo ... layers/layers_common.avx2.s
	@echo ... misc/caffe/opencv-caffe.pb.obj
	@echo ... misc/caffe/opencv-caffe.pb.i
	@echo ... misc/caffe/opencv-caffe.pb.s
	@echo ... misc/onnx/opencv-onnx.pb.obj
	@echo ... misc/onnx/opencv-onnx.pb.i
	@echo ... misc/onnx/opencv-onnx.pb.s
	@echo ... misc/tensorflow/attr_value.pb.obj
	@echo ... misc/tensorflow/attr_value.pb.i
	@echo ... misc/tensorflow/attr_value.pb.s
	@echo ... misc/tensorflow/function.pb.obj
	@echo ... misc/tensorflow/function.pb.i
	@echo ... misc/tensorflow/function.pb.s
	@echo ... misc/tensorflow/graph.pb.obj
	@echo ... misc/tensorflow/graph.pb.i
	@echo ... misc/tensorflow/graph.pb.s
	@echo ... misc/tensorflow/op_def.pb.obj
	@echo ... misc/tensorflow/op_def.pb.i
	@echo ... misc/tensorflow/op_def.pb.s
	@echo ... misc/tensorflow/tensor.pb.obj
	@echo ... misc/tensorflow/tensor.pb.i
	@echo ... misc/tensorflow/tensor.pb.s
	@echo ... misc/tensorflow/tensor_shape.pb.obj
	@echo ... misc/tensorflow/tensor_shape.pb.i
	@echo ... misc/tensorflow/tensor_shape.pb.s
	@echo ... misc/tensorflow/types.pb.obj
	@echo ... misc/tensorflow/types.pb.i
	@echo ... misc/tensorflow/types.pb.s
	@echo ... misc/tensorflow/versions.pb.obj
	@echo ... misc/tensorflow/versions.pb.i
	@echo ... misc/tensorflow/versions.pb.s
	@echo ... opencl_kernels_dnn.obj
	@echo ... opencl_kernels_dnn.i
	@echo ... opencl_kernels_dnn.s
	@echo ... opencv_dnn_main.obj
	@echo ... opencv_dnn_main.i
	@echo ... opencv_dnn_main.s
	@echo ... src/caffe/caffe_importer.obj
	@echo ... src/caffe/caffe_importer.i
	@echo ... src/caffe/caffe_importer.s
	@echo ... src/caffe/caffe_io.obj
	@echo ... src/caffe/caffe_io.i
	@echo ... src/caffe/caffe_io.s
	@echo ... src/caffe/caffe_shrinker.obj
	@echo ... src/caffe/caffe_shrinker.i
	@echo ... src/caffe/caffe_shrinker.s
	@echo ... src/darknet/darknet_importer.obj
	@echo ... src/darknet/darknet_importer.i
	@echo ... src/darknet/darknet_importer.s
	@echo ... src/darknet/darknet_io.obj
	@echo ... src/darknet/darknet_io.i
	@echo ... src/darknet/darknet_io.s
	@echo ... src/dnn.obj
	@echo ... src/dnn.i
	@echo ... src/dnn.s
	@echo ... src/halide_scheduler.obj
	@echo ... src/halide_scheduler.i
	@echo ... src/halide_scheduler.s
	@echo ... src/ie_ngraph.obj
	@echo ... src/ie_ngraph.i
	@echo ... src/ie_ngraph.s
	@echo ... src/init.obj
	@echo ... src/init.i
	@echo ... src/init.s
	@echo ... src/layers/batch_norm_layer.obj
	@echo ... src/layers/batch_norm_layer.i
	@echo ... src/layers/batch_norm_layer.s
	@echo ... src/layers/blank_layer.obj
	@echo ... src/layers/blank_layer.i
	@echo ... src/layers/blank_layer.s
	@echo ... src/layers/concat_layer.obj
	@echo ... src/layers/concat_layer.i
	@echo ... src/layers/concat_layer.s
	@echo ... src/layers/const_layer.obj
	@echo ... src/layers/const_layer.i
	@echo ... src/layers/const_layer.s
	@echo ... src/layers/convolution_layer.obj
	@echo ... src/layers/convolution_layer.i
	@echo ... src/layers/convolution_layer.s
	@echo ... src/layers/crop_and_resize_layer.obj
	@echo ... src/layers/crop_and_resize_layer.i
	@echo ... src/layers/crop_and_resize_layer.s
	@echo ... src/layers/detection_output_layer.obj
	@echo ... src/layers/detection_output_layer.i
	@echo ... src/layers/detection_output_layer.s
	@echo ... src/layers/elementwise_layers.obj
	@echo ... src/layers/elementwise_layers.i
	@echo ... src/layers/elementwise_layers.s
	@echo ... src/layers/eltwise_layer.obj
	@echo ... src/layers/eltwise_layer.i
	@echo ... src/layers/eltwise_layer.s
	@echo ... src/layers/flatten_layer.obj
	@echo ... src/layers/flatten_layer.i
	@echo ... src/layers/flatten_layer.s
	@echo ... src/layers/fully_connected_layer.obj
	@echo ... src/layers/fully_connected_layer.i
	@echo ... src/layers/fully_connected_layer.s
	@echo ... src/layers/layers_common.obj
	@echo ... src/layers/layers_common.i
	@echo ... src/layers/layers_common.s
	@echo ... src/layers/lrn_layer.obj
	@echo ... src/layers/lrn_layer.i
	@echo ... src/layers/lrn_layer.s
	@echo ... src/layers/max_unpooling_layer.obj
	@echo ... src/layers/max_unpooling_layer.i
	@echo ... src/layers/max_unpooling_layer.s
	@echo ... src/layers/mvn_layer.obj
	@echo ... src/layers/mvn_layer.i
	@echo ... src/layers/mvn_layer.s
	@echo ... src/layers/normalize_bbox_layer.obj
	@echo ... src/layers/normalize_bbox_layer.i
	@echo ... src/layers/normalize_bbox_layer.s
	@echo ... src/layers/padding_layer.obj
	@echo ... src/layers/padding_layer.i
	@echo ... src/layers/padding_layer.s
	@echo ... src/layers/permute_layer.obj
	@echo ... src/layers/permute_layer.i
	@echo ... src/layers/permute_layer.s
	@echo ... src/layers/pooling_layer.obj
	@echo ... src/layers/pooling_layer.i
	@echo ... src/layers/pooling_layer.s
	@echo ... src/layers/prior_box_layer.obj
	@echo ... src/layers/prior_box_layer.i
	@echo ... src/layers/prior_box_layer.s
	@echo ... src/layers/proposal_layer.obj
	@echo ... src/layers/proposal_layer.i
	@echo ... src/layers/proposal_layer.s
	@echo ... src/layers/recurrent_layers.obj
	@echo ... src/layers/recurrent_layers.i
	@echo ... src/layers/recurrent_layers.s
	@echo ... src/layers/region_layer.obj
	@echo ... src/layers/region_layer.i
	@echo ... src/layers/region_layer.s
	@echo ... src/layers/reorg_layer.obj
	@echo ... src/layers/reorg_layer.i
	@echo ... src/layers/reorg_layer.s
	@echo ... src/layers/reshape_layer.obj
	@echo ... src/layers/reshape_layer.i
	@echo ... src/layers/reshape_layer.s
	@echo ... src/layers/resize_layer.obj
	@echo ... src/layers/resize_layer.i
	@echo ... src/layers/resize_layer.s
	@echo ... src/layers/scale_layer.obj
	@echo ... src/layers/scale_layer.i
	@echo ... src/layers/scale_layer.s
	@echo ... src/layers/shuffle_channel_layer.obj
	@echo ... src/layers/shuffle_channel_layer.i
	@echo ... src/layers/shuffle_channel_layer.s
	@echo ... src/layers/slice_layer.obj
	@echo ... src/layers/slice_layer.i
	@echo ... src/layers/slice_layer.s
	@echo ... src/layers/softmax_layer.obj
	@echo ... src/layers/softmax_layer.i
	@echo ... src/layers/softmax_layer.s
	@echo ... src/layers/split_layer.obj
	@echo ... src/layers/split_layer.i
	@echo ... src/layers/split_layer.s
	@echo ... src/model.obj
	@echo ... src/model.i
	@echo ... src/model.s
	@echo ... src/nms.obj
	@echo ... src/nms.i
	@echo ... src/nms.s
	@echo ... src/ocl4dnn/src/common.obj
	@echo ... src/ocl4dnn/src/common.i
	@echo ... src/ocl4dnn/src/common.s
	@echo ... src/ocl4dnn/src/math_functions.obj
	@echo ... src/ocl4dnn/src/math_functions.i
	@echo ... src/ocl4dnn/src/math_functions.s
	@echo ... src/ocl4dnn/src/ocl4dnn_conv_spatial.obj
	@echo ... src/ocl4dnn/src/ocl4dnn_conv_spatial.i
	@echo ... src/ocl4dnn/src/ocl4dnn_conv_spatial.s
	@echo ... src/ocl4dnn/src/ocl4dnn_inner_product.obj
	@echo ... src/ocl4dnn/src/ocl4dnn_inner_product.i
	@echo ... src/ocl4dnn/src/ocl4dnn_inner_product.s
	@echo ... src/ocl4dnn/src/ocl4dnn_lrn.obj
	@echo ... src/ocl4dnn/src/ocl4dnn_lrn.i
	@echo ... src/ocl4dnn/src/ocl4dnn_lrn.s
	@echo ... src/ocl4dnn/src/ocl4dnn_pool.obj
	@echo ... src/ocl4dnn/src/ocl4dnn_pool.i
	@echo ... src/ocl4dnn/src/ocl4dnn_pool.s
	@echo ... src/ocl4dnn/src/ocl4dnn_softmax.obj
	@echo ... src/ocl4dnn/src/ocl4dnn_softmax.i
	@echo ... src/ocl4dnn/src/ocl4dnn_softmax.s
	@echo ... src/onnx/onnx_importer.obj
	@echo ... src/onnx/onnx_importer.i
	@echo ... src/onnx/onnx_importer.s
	@echo ... src/op_halide.obj
	@echo ... src/op_halide.i
	@echo ... src/op_halide.s
	@echo ... src/op_inf_engine.obj
	@echo ... src/op_inf_engine.i
	@echo ... src/op_inf_engine.s
	@echo ... src/op_vkcom.obj
	@echo ... src/op_vkcom.i
	@echo ... src/op_vkcom.s
	@echo ... src/tensorflow/tf_graph_simplifier.obj
	@echo ... src/tensorflow/tf_graph_simplifier.i
	@echo ... src/tensorflow/tf_graph_simplifier.s
	@echo ... src/tensorflow/tf_importer.obj
	@echo ... src/tensorflow/tf_importer.i
	@echo ... src/tensorflow/tf_importer.s
	@echo ... src/tensorflow/tf_io.obj
	@echo ... src/tensorflow/tf_io.i
	@echo ... src/tensorflow/tf_io.s
	@echo ... src/torch/THDiskFile.obj
	@echo ... src/torch/THDiskFile.i
	@echo ... src/torch/THDiskFile.s
	@echo ... src/torch/THFile.obj
	@echo ... src/torch/THFile.i
	@echo ... src/torch/THFile.s
	@echo ... src/torch/THGeneral.obj
	@echo ... src/torch/THGeneral.i
	@echo ... src/torch/THGeneral.s
	@echo ... src/torch/torch_importer.obj
	@echo ... src/torch/torch_importer.i
	@echo ... src/torch/torch_importer.s
	@echo ... src/vkcom/shader/avg_pool_spv.obj
	@echo ... src/vkcom/shader/avg_pool_spv.i
	@echo ... src/vkcom/shader/avg_pool_spv.s
	@echo ... src/vkcom/shader/concat_spv.obj
	@echo ... src/vkcom/shader/concat_spv.i
	@echo ... src/vkcom/shader/concat_spv.s
	@echo ... src/vkcom/shader/conv48_spv.obj
	@echo ... src/vkcom/shader/conv48_spv.i
	@echo ... src/vkcom/shader/conv48_spv.s
	@echo ... src/vkcom/shader/conv_spv.obj
	@echo ... src/vkcom/shader/conv_spv.i
	@echo ... src/vkcom/shader/conv_spv.s
	@echo ... src/vkcom/shader/dw_conv_spv.obj
	@echo ... src/vkcom/shader/dw_conv_spv.i
	@echo ... src/vkcom/shader/dw_conv_spv.s
	@echo ... src/vkcom/shader/lrn_spv.obj
	@echo ... src/vkcom/shader/lrn_spv.i
	@echo ... src/vkcom/shader/lrn_spv.s
	@echo ... src/vkcom/shader/max_pool_spv.obj
	@echo ... src/vkcom/shader/max_pool_spv.i
	@echo ... src/vkcom/shader/max_pool_spv.s
	@echo ... src/vkcom/shader/permute_spv.obj
	@echo ... src/vkcom/shader/permute_spv.i
	@echo ... src/vkcom/shader/permute_spv.s
	@echo ... src/vkcom/shader/prior_box_spv.obj
	@echo ... src/vkcom/shader/prior_box_spv.i
	@echo ... src/vkcom/shader/prior_box_spv.s
	@echo ... src/vkcom/shader/relu_spv.obj
	@echo ... src/vkcom/shader/relu_spv.i
	@echo ... src/vkcom/shader/relu_spv.s
	@echo ... src/vkcom/shader/softmax_spv.obj
	@echo ... src/vkcom/shader/softmax_spv.i
	@echo ... src/vkcom/shader/softmax_spv.s
	@echo ... src/vkcom/src/buffer.obj
	@echo ... src/vkcom/src/buffer.i
	@echo ... src/vkcom/src/buffer.s
	@echo ... src/vkcom/src/context.obj
	@echo ... src/vkcom/src/context.i
	@echo ... src/vkcom/src/context.s
	@echo ... src/vkcom/src/internal.obj
	@echo ... src/vkcom/src/internal.i
	@echo ... src/vkcom/src/internal.s
	@echo ... src/vkcom/src/op_base.obj
	@echo ... src/vkcom/src/op_base.i
	@echo ... src/vkcom/src/op_base.s
	@echo ... src/vkcom/src/op_concat.obj
	@echo ... src/vkcom/src/op_concat.i
	@echo ... src/vkcom/src/op_concat.s
	@echo ... src/vkcom/src/op_conv.obj
	@echo ... src/vkcom/src/op_conv.i
	@echo ... src/vkcom/src/op_conv.s
	@echo ... src/vkcom/src/op_lrn.obj
	@echo ... src/vkcom/src/op_lrn.i
	@echo ... src/vkcom/src/op_lrn.s
	@echo ... src/vkcom/src/op_permute.obj
	@echo ... src/vkcom/src/op_permute.i
	@echo ... src/vkcom/src/op_permute.s
	@echo ... src/vkcom/src/op_pool.obj
	@echo ... src/vkcom/src/op_pool.i
	@echo ... src/vkcom/src/op_pool.s
	@echo ... src/vkcom/src/op_prior_box.obj
	@echo ... src/vkcom/src/op_prior_box.i
	@echo ... src/vkcom/src/op_prior_box.s
	@echo ... src/vkcom/src/op_relu.obj
	@echo ... src/vkcom/src/op_relu.i
	@echo ... src/vkcom/src/op_relu.s
	@echo ... src/vkcom/src/op_softmax.obj
	@echo ... src/vkcom/src/op_softmax.i
	@echo ... src/vkcom/src/op_softmax.s
	@echo ... src/vkcom/src/tensor.obj
	@echo ... src/vkcom/src/tensor.i
	@echo ... src/vkcom/src/tensor.s
	@echo ... src/vkcom/vulkan/vk_functions.obj
	@echo ... src/vkcom/vulkan/vk_functions.i
	@echo ... src/vkcom/vulkan/vk_functions.s
	@echo ... src/vkcom/vulkan/vk_loader.obj
	@echo ... src/vkcom/vulkan/vk_loader.i
	@echo ... src/vkcom/vulkan/vk_loader.s
	@echo ... vs_version.obj
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

