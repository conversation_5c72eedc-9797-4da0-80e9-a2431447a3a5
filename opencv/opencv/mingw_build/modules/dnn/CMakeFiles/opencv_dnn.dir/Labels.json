{"sources": [{"file": "D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/all_layers.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/dict.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/dnn.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/dnn.inl.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/layer.details.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/layer.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/shape_utils.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/utils/inference_engine.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/version.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/misc/caffe/opencv-caffe.pb.cc", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/misc/onnx/opencv-onnx.pb.cc", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/attr_value.pb.cc", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/function.pb.cc", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/graph.pb.cc", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/op_def.pb.cc", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/tensor.pb.cc", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/tensor_shape.pb.cc", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/types.pb.cc", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/versions.pb.cc", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/caffe/caffe_importer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/caffe/caffe_io.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/caffe/caffe_shrinker.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/darknet/darknet_importer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/darknet/darknet_io.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/dnn.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/halide_scheduler.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/ie_ngraph.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/init.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/batch_norm_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/blank_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/concat_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/const_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/convolution_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/crop_and_resize_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/detection_output_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/elementwise_layers.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/eltwise_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/flatten_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/fully_connected_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/layers_common.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/lrn_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/max_unpooling_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/mvn_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/normalize_bbox_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/padding_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/permute_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/pooling_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/prior_box_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/proposal_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/recurrent_layers.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/region_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/reorg_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/reshape_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/resize_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/scale_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/shuffle_channel_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/slice_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/softmax_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/split_layer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/model.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/nms.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/common.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/math_functions.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/ocl4dnn_inner_product.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/ocl4dnn_lrn.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/ocl4dnn_pool.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/ocl4dnn_softmax.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/onnx/onnx_importer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/op_halide.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/op_inf_engine.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/op_vkcom.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/tensorflow/tf_graph_simplifier.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/tensorflow/tf_importer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/tensorflow/tf_io.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THDiskFile.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THFile.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THGeneral.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/torch/torch_importer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/avg_pool_spv.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/concat_spv.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/conv48_spv.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/conv_spv.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/dw_conv_spv.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/lrn_spv.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/max_pool_spv.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/permute_spv.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/prior_box_spv.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/relu_spv.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/softmax_spv.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/buffer.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/context.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/internal.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_base.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_concat.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_conv.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_lrn.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_permute.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_pool.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_prior_box.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_relu.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_softmax.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/tensor.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/vulkan/vk_functions.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/vulkan/vk_loader.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/activations.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/batchnorm.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/col2im.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/concat.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/conv_layer_spatial.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/conv_spatial_helper.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/detection_output.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/dummy.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/eltwise.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/gemm_buffer.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/gemm_image.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/im2col.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/lrn.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/math.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/matvec_mul.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/mvn.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/ocl4dnn_lrn.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/ocl4dnn_pooling.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/permute.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/pooling.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/prior_box.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/region.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/slice.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/softmax.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/softmax_loss.cl", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/dnn/opencl_kernels_dnn.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/dnn/opencl_kernels_dnn.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/caffe/caffe_io.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/caffe/glog_emulator.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/array.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/atomics.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/execution.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/grid_stride_range.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/kernel_dispatcher.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/limits.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/math.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/types.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/vector_traits.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/darknet/darknet_io.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/halide_scheduler.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/ie_ngraph.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/layers_common.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/layers_common.simd.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/nms.inl.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/include/common.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/include/default_kernel_config.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/include/math_functions.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/include/ocl4dnn.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/op_cuda.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/op_halide.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/op_inf_engine.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/op_vkcom.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/precomp.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/tensorflow/tf_graph_simplifier.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/tensorflow/tf_io.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THDiskFile.h", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THFile.h", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THFilePrivate.h", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THGeneral.h", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/buffer.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_base.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_concat.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_conv.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_lrn.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_permute.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_pool.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_prior_box.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_relu.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_softmax.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/tensor.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/vkcom.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/spv_shader.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/common.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/context.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/internal.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/vulkan/function_list.inl.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/vulkan/vk_functions.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/vulkan/vk_loader.hpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/dnn/layers/layers_common.avx.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/dnn/layers/layers_common.avx2.cpp", "labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"]}, {"file": "D:/unet/opencv/opencv/mingw_build/cvconfig.h"}, {"file": "D:/unet/opencv/opencv/mingw_build/opencv2/opencv_modules.hpp"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/dnn/vs_version.rc"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/dnn/opencv_dnn_main.cpp"}, {"file": "D:/unet/opencv/opencv/mingw_build/modules/dnn/opencl_kernels_dnn.cpp.rule"}], "target": {"labels": ["Main", "opencv_dnn", "<PERSON><PERSON><PERSON>"], "name": "opencv_dnn"}}