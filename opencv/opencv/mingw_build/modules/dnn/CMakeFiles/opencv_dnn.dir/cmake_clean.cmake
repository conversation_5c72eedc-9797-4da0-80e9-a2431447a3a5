file(REMOVE_RECURSE
  "opencl_kernels_dnn.cpp"
  "CMakeFiles/opencv_dnn.dir/misc/caffe/opencv-caffe.pb.cc.obj"
  "CMakeFiles/opencv_dnn.dir/misc/onnx/opencv-onnx.pb.cc.obj"
  "CMakeFiles/opencv_dnn.dir/misc/tensorflow/attr_value.pb.cc.obj"
  "CMakeFiles/opencv_dnn.dir/misc/tensorflow/function.pb.cc.obj"
  "CMakeFiles/opencv_dnn.dir/misc/tensorflow/graph.pb.cc.obj"
  "CMakeFiles/opencv_dnn.dir/misc/tensorflow/op_def.pb.cc.obj"
  "CMakeFiles/opencv_dnn.dir/misc/tensorflow/tensor.pb.cc.obj"
  "CMakeFiles/opencv_dnn.dir/misc/tensorflow/tensor_shape.pb.cc.obj"
  "CMakeFiles/opencv_dnn.dir/misc/tensorflow/types.pb.cc.obj"
  "CMakeFiles/opencv_dnn.dir/misc/tensorflow/versions.pb.cc.obj"
  "CMakeFiles/opencv_dnn.dir/src/caffe/caffe_importer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/caffe/caffe_io.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/caffe/caffe_shrinker.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/darknet/darknet_importer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/darknet/darknet_io.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/dnn.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/halide_scheduler.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/ie_ngraph.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/init.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/batch_norm_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/blank_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/concat_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/const_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/convolution_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/crop_and_resize_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/detection_output_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/elementwise_layers.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/eltwise_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/flatten_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/fully_connected_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/layers_common.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/lrn_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/max_unpooling_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/mvn_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/normalize_bbox_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/padding_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/permute_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/pooling_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/prior_box_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/proposal_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/recurrent_layers.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/region_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/reorg_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/reshape_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/resize_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/scale_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/shuffle_channel_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/slice_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/softmax_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/layers/split_layer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/model.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/nms.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/common.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/math_functions.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_inner_product.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_lrn.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_pool.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_softmax.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/onnx/onnx_importer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/op_halide.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/op_inf_engine.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/op_vkcom.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/tensorflow/tf_graph_simplifier.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/tensorflow/tf_importer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/tensorflow/tf_io.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/torch/THDiskFile.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/torch/THFile.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/torch/THGeneral.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/torch/torch_importer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/shader/avg_pool_spv.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/shader/concat_spv.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/shader/conv48_spv.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/shader/conv_spv.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/shader/dw_conv_spv.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/shader/lrn_spv.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/shader/max_pool_spv.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/shader/permute_spv.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/shader/prior_box_spv.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/shader/relu_spv.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/shader/softmax_spv.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/src/buffer.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/src/context.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/src/internal.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_base.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_concat.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_conv.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_lrn.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_permute.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_pool.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_prior_box.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_relu.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_softmax.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/src/tensor.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/vulkan/vk_functions.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/src/vkcom/vulkan/vk_loader.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/opencl_kernels_dnn.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/layers/layers_common.avx.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/layers/layers_common.avx2.cpp.obj"
  "CMakeFiles/opencv_dnn.dir/vs_version.rc.obj"
  "CMakeFiles/opencv_dnn.dir/opencv_dnn_main.cpp.obj"
  "../../bin/libopencv_dnn420.pdb"
  "../../bin/libopencv_dnn420.dll"
  "../../lib/libopencv_dnn420.dll.a"
  "../../bin/libopencv_dnn420.dll.manifest"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX RC)
  include(CMakeFiles/opencv_dnn.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
