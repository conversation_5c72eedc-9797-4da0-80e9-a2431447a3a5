# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  "RC"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "D:/unet/opencv/opencv/mingw_build/modules/dnn/layers/layers_common.avx.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/layers/layers_common.avx.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/modules/dnn/layers/layers_common.avx2.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/layers/layers_common.avx2.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/caffe/opencv-caffe.pb.cc" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/misc/caffe/opencv-caffe.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/onnx/opencv-onnx.pb.cc" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/misc/onnx/opencv-onnx.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/attr_value.pb.cc" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/attr_value.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/function.pb.cc" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/function.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/graph.pb.cc" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/graph.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/op_def.pb.cc" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/op_def.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/tensor.pb.cc" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/tensor.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/tensor_shape.pb.cc" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/tensor_shape.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/types.pb.cc" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/types.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/versions.pb.cc" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/misc/tensorflow/versions.pb.cc.obj"
  "D:/unet/opencv/opencv/mingw_build/modules/dnn/opencl_kernels_dnn.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/opencl_kernels_dnn.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/modules/dnn/opencv_dnn_main.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/opencv_dnn_main.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/caffe/caffe_importer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/caffe/caffe_importer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/caffe/caffe_io.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/caffe/caffe_io.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/caffe/caffe_shrinker.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/caffe/caffe_shrinker.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/darknet/darknet_importer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/darknet/darknet_importer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/darknet/darknet_io.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/darknet/darknet_io.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/dnn.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/dnn.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/halide_scheduler.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/halide_scheduler.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/ie_ngraph.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/ie_ngraph.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/init.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/init.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/batch_norm_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/batch_norm_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/blank_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/blank_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/concat_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/concat_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/const_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/const_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/convolution_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/convolution_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/crop_and_resize_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/crop_and_resize_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/detection_output_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/detection_output_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/elementwise_layers.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/elementwise_layers.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/eltwise_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/eltwise_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/flatten_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/flatten_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/fully_connected_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/fully_connected_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/layers_common.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/layers_common.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/lrn_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/lrn_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/max_unpooling_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/max_unpooling_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/mvn_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/mvn_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/normalize_bbox_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/normalize_bbox_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/padding_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/padding_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/permute_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/permute_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/pooling_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/pooling_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/prior_box_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/prior_box_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/proposal_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/proposal_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/recurrent_layers.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/recurrent_layers.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/region_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/region_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/reorg_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/reorg_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/reshape_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/reshape_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/resize_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/resize_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/scale_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/scale_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/shuffle_channel_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/shuffle_channel_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/slice_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/slice_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/softmax_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/softmax_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/layers/split_layer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/layers/split_layer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/model.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/model.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/nms.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/nms.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/common.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/common.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/math_functions.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/math_functions.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/ocl4dnn_inner_product.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_inner_product.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/ocl4dnn_lrn.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_lrn.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/ocl4dnn_pool.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_pool.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/ocl4dnn_softmax.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/ocl4dnn/src/ocl4dnn_softmax.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/onnx/onnx_importer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/onnx/onnx_importer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/op_halide.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/op_halide.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/op_inf_engine.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/op_inf_engine.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/op_vkcom.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/op_vkcom.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/tensorflow/tf_graph_simplifier.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/tensorflow/tf_graph_simplifier.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/tensorflow/tf_importer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/tensorflow/tf_importer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/tensorflow/tf_io.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/tensorflow/tf_io.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THDiskFile.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/THDiskFile.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THFile.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/THFile.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THGeneral.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/THGeneral.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/torch/torch_importer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/torch/torch_importer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/avg_pool_spv.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/avg_pool_spv.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/concat_spv.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/concat_spv.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/conv48_spv.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/conv48_spv.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/conv_spv.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/conv_spv.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/dw_conv_spv.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/dw_conv_spv.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/lrn_spv.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/lrn_spv.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/max_pool_spv.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/max_pool_spv.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/permute_spv.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/permute_spv.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/prior_box_spv.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/prior_box_spv.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/relu_spv.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/relu_spv.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/softmax_spv.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/shader/softmax_spv.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/buffer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/buffer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/context.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/context.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/internal.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/internal.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_base.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_base.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_concat.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_concat.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_conv.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_conv.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_lrn.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_lrn.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_permute.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_permute.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_pool.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_pool.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_prior_box.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_prior_box.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_relu.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_relu.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_softmax.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/op_softmax.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/tensor.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/src/tensor.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/vulkan/vk_functions.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/vulkan/vk_functions.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/vulkan/vk_loader.cpp" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/src/vkcom/vulkan/vk_loader.cpp.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "CV_OCL4DNN=1"
  "HAVE_PROTOBUF=1"
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/dnn/include"
  "modules/dnn"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/caffe"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/onnx"
  "D:/unet/opencv/opencv/sources/3rdparty/include/opencl/1.2"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src"
  )
set(CMAKE_DEPENDS_CHECK_RC
  "D:/unet/opencv/opencv/mingw_build/modules/dnn/vs_version.rc" "D:/unet/opencv/opencv/mingw_build/modules/dnn/CMakeFiles/opencv_dnn.dir/vs_version.rc.obj"
  )

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_RC
  "CV_OCL4DNN=1"
  "HAVE_PROTOBUF=1"
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_RC_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/dnn/include"
  "modules/dnn"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/caffe"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow"
  "D:/unet/opencv/opencv/sources/modules/dnn/misc/onnx"
  "D:/unet/opencv/opencv/sources/3rdparty/include/opencl/1.2"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "D:/unet/opencv/opencv/mingw_build/modules/imgproc/CMakeFiles/opencv_imgproc.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/core/CMakeFiles/opencv_core.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
