// This file is auto-generated. Do not edit!

#include "opencv2/core/ocl.hpp"
#include "opencv2/core/ocl_genbase.hpp"
#include "opencv2/core/opencl/ocl_defs.hpp"

#ifdef HAVE_OPENCL

namespace cv
{
namespace ocl
{
namespace dnn
{

extern struct cv::ocl::internal::ProgramEntry activations_oclsrc;
extern struct cv::ocl::internal::ProgramEntry batchnorm_oclsrc;
extern struct cv::ocl::internal::ProgramEntry col2im_oclsrc;
extern struct cv::ocl::internal::ProgramEntry concat_oclsrc;
extern struct cv::ocl::internal::ProgramEntry conv_layer_spatial_oclsrc;
extern struct cv::ocl::internal::ProgramEntry conv_spatial_helper_oclsrc;
extern struct cv::ocl::internal::ProgramEntry detection_output_oclsrc;
extern struct cv::ocl::internal::ProgramEntry dummy_oclsrc;
extern struct cv::ocl::internal::ProgramEntry eltwise_oclsrc;
extern struct cv::ocl::internal::ProgramEntry gemm_buffer_oclsrc;
extern struct cv::ocl::internal::ProgramEntry gemm_image_oclsrc;
extern struct cv::ocl::internal::ProgramEntry im2col_oclsrc;
extern struct cv::ocl::internal::ProgramEntry lrn_oclsrc;
extern struct cv::ocl::internal::ProgramEntry math_oclsrc;
extern struct cv::ocl::internal::ProgramEntry matvec_mul_oclsrc;
extern struct cv::ocl::internal::ProgramEntry mvn_oclsrc;
extern struct cv::ocl::internal::ProgramEntry ocl4dnn_lrn_oclsrc;
extern struct cv::ocl::internal::ProgramEntry ocl4dnn_pooling_oclsrc;
extern struct cv::ocl::internal::ProgramEntry permute_oclsrc;
extern struct cv::ocl::internal::ProgramEntry pooling_oclsrc;
extern struct cv::ocl::internal::ProgramEntry prior_box_oclsrc;
extern struct cv::ocl::internal::ProgramEntry region_oclsrc;
extern struct cv::ocl::internal::ProgramEntry slice_oclsrc;
extern struct cv::ocl::internal::ProgramEntry softmax_oclsrc;
extern struct cv::ocl::internal::ProgramEntry softmax_loss_oclsrc;

}}}
#endif
