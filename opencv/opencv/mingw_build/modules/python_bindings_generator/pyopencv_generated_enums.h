typedef cv::AKAZE::DescriptorType AKAZE_DescriptorType;
CV_PY_FROM_ENUM(AKAZE_DescriptorType);
CV_PY_TO_ENUM(AKAZE_DescriptorType);

CV_PY_FROM_ENUM(AccessFlag);
CV_PY_TO_ENUM(AccessFlag);

CV_PY_FROM_ENUM(AdaptiveThresholdTypes);
CV_PY_TO_ENUM(AdaptiveThresholdTypes);

typedef cv::AgastFeatureDetector::DetectorType AgastFeatureDetector_DetectorType;
CV_PY_FROM_ENUM(AgastFeatureDetector_DetectorType);
CV_PY_TO_ENUM(AgastFeatureDetector_DetectorType);

CV_PY_FROM_ENUM(BorderTypes);
CV_PY_TO_ENUM(BorderTypes);

typedef cv::CirclesGridFinderParameters::GridType CirclesGridFinderParameters_GridType;
CV_PY_FROM_ENUM(CirclesGridFinderParameters_GridType);
CV_PY_TO_ENUM(CirclesGridFinderParameters_GridType);

CV_PY_FROM_ENUM(CmpTypes);
CV_PY_TO_ENUM(CmpTypes);

CV_PY_FROM_ENUM(ColorConversionCodes);
CV_PY_TO_ENUM(ColorConversionCodes);

CV_PY_FROM_ENUM(ColormapTypes);
CV_PY_TO_ENUM(ColormapTypes);

CV_PY_FROM_ENUM(ConnectedComponentsAlgorithmsTypes);
CV_PY_TO_ENUM(ConnectedComponentsAlgorithmsTypes);

CV_PY_FROM_ENUM(ConnectedComponentsTypes);
CV_PY_TO_ENUM(ConnectedComponentsTypes);

CV_PY_FROM_ENUM(ContourApproximationModes);
CV_PY_TO_ENUM(ContourApproximationModes);

CV_PY_FROM_ENUM(CovarFlags);
CV_PY_TO_ENUM(CovarFlags);

CV_PY_FROM_ENUM(DecompTypes);
CV_PY_TO_ENUM(DecompTypes);

typedef cv::DescriptorMatcher::MatcherType DescriptorMatcher_MatcherType;
CV_PY_FROM_ENUM(DescriptorMatcher_MatcherType);
CV_PY_TO_ENUM(DescriptorMatcher_MatcherType);

CV_PY_FROM_ENUM(DftFlags);
CV_PY_TO_ENUM(DftFlags);

CV_PY_FROM_ENUM(DistanceTransformLabelTypes);
CV_PY_TO_ENUM(DistanceTransformLabelTypes);

CV_PY_FROM_ENUM(DistanceTransformMasks);
CV_PY_TO_ENUM(DistanceTransformMasks);

CV_PY_FROM_ENUM(DistanceTypes);
CV_PY_TO_ENUM(DistanceTypes);

CV_PY_FROM_ENUM(DrawMatchesFlags);
CV_PY_TO_ENUM(DrawMatchesFlags);

typedef cv::Error::Code Error_Code;
CV_PY_FROM_ENUM(Error_Code);
CV_PY_TO_ENUM(Error_Code);

typedef cv::FastFeatureDetector::DetectorType FastFeatureDetector_DetectorType;
CV_PY_FROM_ENUM(FastFeatureDetector_DetectorType);
CV_PY_TO_ENUM(FastFeatureDetector_DetectorType);

typedef cv::FileStorage::Mode FileStorage_Mode;
CV_PY_FROM_ENUM(FileStorage_Mode);
CV_PY_TO_ENUM(FileStorage_Mode);

typedef cv::FileStorage::State FileStorage_State;
CV_PY_FROM_ENUM(FileStorage_State);
CV_PY_TO_ENUM(FileStorage_State);

CV_PY_FROM_ENUM(FloodFillFlags);
CV_PY_TO_ENUM(FloodFillFlags);

typedef cv::Formatter::FormatType Formatter_FormatType;
CV_PY_FROM_ENUM(Formatter_FormatType);
CV_PY_TO_ENUM(Formatter_FormatType);

CV_PY_FROM_ENUM(GemmFlags);
CV_PY_TO_ENUM(GemmFlags);

CV_PY_FROM_ENUM(GrabCutClasses);
CV_PY_TO_ENUM(GrabCutClasses);

CV_PY_FROM_ENUM(GrabCutModes);
CV_PY_TO_ENUM(GrabCutModes);

typedef cv::HOGDescriptor::DescriptorStorageFormat HOGDescriptor_DescriptorStorageFormat;
CV_PY_FROM_ENUM(HOGDescriptor_DescriptorStorageFormat);
CV_PY_TO_ENUM(HOGDescriptor_DescriptorStorageFormat);

typedef cv::HOGDescriptor::HistogramNormType HOGDescriptor_HistogramNormType;
CV_PY_FROM_ENUM(HOGDescriptor_HistogramNormType);
CV_PY_TO_ENUM(HOGDescriptor_HistogramNormType);

CV_PY_FROM_ENUM(HandEyeCalibrationMethod);
CV_PY_TO_ENUM(HandEyeCalibrationMethod);

CV_PY_FROM_ENUM(HersheyFonts);
CV_PY_TO_ENUM(HersheyFonts);

CV_PY_FROM_ENUM(HistCompMethods);
CV_PY_TO_ENUM(HistCompMethods);

CV_PY_FROM_ENUM(HoughModes);
CV_PY_TO_ENUM(HoughModes);

CV_PY_FROM_ENUM(ImreadModes);
CV_PY_TO_ENUM(ImreadModes);

CV_PY_FROM_ENUM(ImwriteEXRTypeFlags);
CV_PY_TO_ENUM(ImwriteEXRTypeFlags);

CV_PY_FROM_ENUM(ImwriteFlags);
CV_PY_TO_ENUM(ImwriteFlags);

CV_PY_FROM_ENUM(ImwritePAMFlags);
CV_PY_TO_ENUM(ImwritePAMFlags);

CV_PY_FROM_ENUM(ImwritePNGFlags);
CV_PY_TO_ENUM(ImwritePNGFlags);

CV_PY_FROM_ENUM(InterpolationFlags);
CV_PY_TO_ENUM(InterpolationFlags);

CV_PY_FROM_ENUM(InterpolationMasks);
CV_PY_TO_ENUM(InterpolationMasks);

typedef cv::KAZE::DiffusivityType KAZE_DiffusivityType;
CV_PY_FROM_ENUM(KAZE_DiffusivityType);
CV_PY_TO_ENUM(KAZE_DiffusivityType);

CV_PY_FROM_ENUM(KmeansFlags);
CV_PY_TO_ENUM(KmeansFlags);

CV_PY_FROM_ENUM(LineSegmentDetectorModes);
CV_PY_TO_ENUM(LineSegmentDetectorModes);

CV_PY_FROM_ENUM(LineTypes);
CV_PY_TO_ENUM(LineTypes);

CV_PY_FROM_ENUM(MarkerTypes);
CV_PY_TO_ENUM(MarkerTypes);

CV_PY_FROM_ENUM(MorphShapes);
CV_PY_TO_ENUM(MorphShapes);

CV_PY_FROM_ENUM(MorphTypes);
CV_PY_TO_ENUM(MorphTypes);

CV_PY_FROM_ENUM(MouseEventFlags);
CV_PY_TO_ENUM(MouseEventFlags);

CV_PY_FROM_ENUM(MouseEventTypes);
CV_PY_TO_ENUM(MouseEventTypes);

CV_PY_FROM_ENUM(NormTypes);
CV_PY_TO_ENUM(NormTypes);

typedef cv::ORB::ScoreType ORB_ScoreType;
CV_PY_FROM_ENUM(ORB_ScoreType);
CV_PY_TO_ENUM(ORB_ScoreType);

typedef cv::PCA::Flags PCA_Flags;
CV_PY_FROM_ENUM(PCA_Flags);
CV_PY_TO_ENUM(PCA_Flags);

CV_PY_FROM_ENUM(Param);
CV_PY_TO_ENUM(Param);

CV_PY_FROM_ENUM(QtButtonTypes);
CV_PY_TO_ENUM(QtButtonTypes);

CV_PY_FROM_ENUM(QtFontStyles);
CV_PY_TO_ENUM(QtFontStyles);

CV_PY_FROM_ENUM(QtFontWeights);
CV_PY_TO_ENUM(QtFontWeights);

CV_PY_FROM_ENUM(RectanglesIntersectTypes);
CV_PY_TO_ENUM(RectanglesIntersectTypes);

CV_PY_FROM_ENUM(ReduceTypes);
CV_PY_TO_ENUM(ReduceTypes);

CV_PY_FROM_ENUM(RetrievalModes);
CV_PY_TO_ENUM(RetrievalModes);

CV_PY_FROM_ENUM(RotateFlags);
CV_PY_TO_ENUM(RotateFlags);

typedef cv::SVD::Flags SVD_Flags;
CV_PY_FROM_ENUM(SVD_Flags);
CV_PY_TO_ENUM(SVD_Flags);

CV_PY_FROM_ENUM(ShapeMatchModes);
CV_PY_TO_ENUM(ShapeMatchModes);

CV_PY_FROM_ENUM(SolveLPResult);
CV_PY_TO_ENUM(SolveLPResult);

CV_PY_FROM_ENUM(SolvePnPMethod);
CV_PY_TO_ENUM(SolvePnPMethod);

CV_PY_FROM_ENUM(SortFlags);
CV_PY_TO_ENUM(SortFlags);

CV_PY_FROM_ENUM(SpecialFilter);
CV_PY_TO_ENUM(SpecialFilter);

typedef cv::Stitcher::Mode Stitcher_Mode;
CV_PY_FROM_ENUM(Stitcher_Mode);
CV_PY_TO_ENUM(Stitcher_Mode);

typedef cv::Stitcher::Status Stitcher_Status;
CV_PY_FROM_ENUM(Stitcher_Status);
CV_PY_TO_ENUM(Stitcher_Status);

CV_PY_FROM_ENUM(TemplateMatchModes);
CV_PY_TO_ENUM(TemplateMatchModes);

typedef cv::TermCriteria::Type TermCriteria_Type;
CV_PY_FROM_ENUM(TermCriteria_Type);
CV_PY_TO_ENUM(TermCriteria_Type);

CV_PY_FROM_ENUM(ThresholdTypes);
CV_PY_TO_ENUM(ThresholdTypes);

typedef cv::UMatData::MemoryFlag UMatData_MemoryFlag;
CV_PY_FROM_ENUM(UMatData_MemoryFlag);
CV_PY_TO_ENUM(UMatData_MemoryFlag);

CV_PY_FROM_ENUM(UMatUsageFlags);
CV_PY_TO_ENUM(UMatUsageFlags);

CV_PY_FROM_ENUM(UndistortTypes);
CV_PY_TO_ENUM(UndistortTypes);

CV_PY_FROM_ENUM(VideoCaptureAPIs);
CV_PY_TO_ENUM(VideoCaptureAPIs);

CV_PY_FROM_ENUM(VideoCaptureProperties);
CV_PY_TO_ENUM(VideoCaptureProperties);

CV_PY_FROM_ENUM(VideoWriterProperties);
CV_PY_TO_ENUM(VideoWriterProperties);

CV_PY_FROM_ENUM(WarpPolarMode);
CV_PY_TO_ENUM(WarpPolarMode);

CV_PY_FROM_ENUM(WindowFlags);
CV_PY_TO_ENUM(WindowFlags);

CV_PY_FROM_ENUM(WindowPropertyFlags);
CV_PY_TO_ENUM(WindowPropertyFlags);

typedef cv::_InputArray::KindFlag _InputArray_KindFlag;
CV_PY_FROM_ENUM(_InputArray_KindFlag);
CV_PY_TO_ENUM(_InputArray_KindFlag);

typedef cv::_OutputArray::DepthMask _OutputArray_DepthMask;
CV_PY_FROM_ENUM(_OutputArray_DepthMask);
CV_PY_TO_ENUM(_OutputArray_DepthMask);

typedef cv::cuda::DeviceInfo::ComputeMode cuda_DeviceInfo_ComputeMode;
CV_PY_FROM_ENUM(cuda_DeviceInfo_ComputeMode);
CV_PY_TO_ENUM(cuda_DeviceInfo_ComputeMode);

typedef cv::cuda::Event::CreateFlags cuda_Event_CreateFlags;
CV_PY_FROM_ENUM(cuda_Event_CreateFlags);
CV_PY_TO_ENUM(cuda_Event_CreateFlags);

typedef cv::cuda::FeatureSet cuda_FeatureSet;
CV_PY_FROM_ENUM(cuda_FeatureSet);
CV_PY_TO_ENUM(cuda_FeatureSet);

typedef cv::cuda::HostMem::AllocType cuda_HostMem_AllocType;
CV_PY_FROM_ENUM(cuda_HostMem_AllocType);
CV_PY_TO_ENUM(cuda_HostMem_AllocType);

typedef cv::detail::DpSeamFinder::CostFunction detail_DpSeamFinder_CostFunction;
CV_PY_FROM_ENUM(detail_DpSeamFinder_CostFunction);
CV_PY_TO_ENUM(detail_DpSeamFinder_CostFunction);

typedef cv::detail::GraphCutSeamFinderBase::CostType detail_GraphCutSeamFinderBase_CostType;
CV_PY_FROM_ENUM(detail_GraphCutSeamFinderBase_CostType);
CV_PY_TO_ENUM(detail_GraphCutSeamFinderBase_CostType);

typedef cv::detail::TestOp detail_TestOp;
CV_PY_FROM_ENUM(detail_TestOp);
CV_PY_TO_ENUM(detail_TestOp);

typedef cv::detail::WaveCorrectKind detail_WaveCorrectKind;
CV_PY_FROM_ENUM(detail_WaveCorrectKind);
CV_PY_TO_ENUM(detail_WaveCorrectKind);

typedef cv::dnn::Backend dnn_Backend;
CV_PY_FROM_ENUM(dnn_Backend);
CV_PY_TO_ENUM(dnn_Backend);

typedef cv::dnn::Target dnn_Target;
CV_PY_FROM_ENUM(dnn_Target);
CV_PY_TO_ENUM(dnn_Target);

typedef cv::flann::FlannIndexType flann_FlannIndexType;
CV_PY_FROM_ENUM(flann_FlannIndexType);
CV_PY_TO_ENUM(flann_FlannIndexType);

typedef cv::ml::ANN_MLP::ActivationFunctions ml_ANN_MLP_ActivationFunctions;
CV_PY_FROM_ENUM(ml_ANN_MLP_ActivationFunctions);
CV_PY_TO_ENUM(ml_ANN_MLP_ActivationFunctions);

typedef cv::ml::ANN_MLP::TrainFlags ml_ANN_MLP_TrainFlags;
CV_PY_FROM_ENUM(ml_ANN_MLP_TrainFlags);
CV_PY_TO_ENUM(ml_ANN_MLP_TrainFlags);

typedef cv::ml::ANN_MLP::TrainingMethods ml_ANN_MLP_TrainingMethods;
CV_PY_FROM_ENUM(ml_ANN_MLP_TrainingMethods);
CV_PY_TO_ENUM(ml_ANN_MLP_TrainingMethods);

typedef cv::ml::Boost::Types ml_Boost_Types;
CV_PY_FROM_ENUM(ml_Boost_Types);
CV_PY_TO_ENUM(ml_Boost_Types);

typedef cv::ml::DTrees::Flags ml_DTrees_Flags;
CV_PY_FROM_ENUM(ml_DTrees_Flags);
CV_PY_TO_ENUM(ml_DTrees_Flags);

typedef cv::ml::EM::Types ml_EM_Types;
CV_PY_FROM_ENUM(ml_EM_Types);
CV_PY_TO_ENUM(ml_EM_Types);

typedef cv::ml::ErrorTypes ml_ErrorTypes;
CV_PY_FROM_ENUM(ml_ErrorTypes);
CV_PY_TO_ENUM(ml_ErrorTypes);

typedef cv::ml::KNearest::Types ml_KNearest_Types;
CV_PY_FROM_ENUM(ml_KNearest_Types);
CV_PY_TO_ENUM(ml_KNearest_Types);

typedef cv::ml::LogisticRegression::Methods ml_LogisticRegression_Methods;
CV_PY_FROM_ENUM(ml_LogisticRegression_Methods);
CV_PY_TO_ENUM(ml_LogisticRegression_Methods);

typedef cv::ml::LogisticRegression::RegKinds ml_LogisticRegression_RegKinds;
CV_PY_FROM_ENUM(ml_LogisticRegression_RegKinds);
CV_PY_TO_ENUM(ml_LogisticRegression_RegKinds);

typedef cv::ml::SVM::KernelTypes ml_SVM_KernelTypes;
CV_PY_FROM_ENUM(ml_SVM_KernelTypes);
CV_PY_TO_ENUM(ml_SVM_KernelTypes);

typedef cv::ml::SVM::ParamTypes ml_SVM_ParamTypes;
CV_PY_FROM_ENUM(ml_SVM_ParamTypes);
CV_PY_TO_ENUM(ml_SVM_ParamTypes);

typedef cv::ml::SVM::Types ml_SVM_Types;
CV_PY_FROM_ENUM(ml_SVM_Types);
CV_PY_TO_ENUM(ml_SVM_Types);

typedef cv::ml::SVMSGD::MarginType ml_SVMSGD_MarginType;
CV_PY_FROM_ENUM(ml_SVMSGD_MarginType);
CV_PY_TO_ENUM(ml_SVMSGD_MarginType);

typedef cv::ml::SVMSGD::SvmsgdType ml_SVMSGD_SvmsgdType;
CV_PY_FROM_ENUM(ml_SVMSGD_SvmsgdType);
CV_PY_TO_ENUM(ml_SVMSGD_SvmsgdType);

typedef cv::ml::SampleTypes ml_SampleTypes;
CV_PY_FROM_ENUM(ml_SampleTypes);
CV_PY_TO_ENUM(ml_SampleTypes);

typedef cv::ml::StatModel::Flags ml_StatModel_Flags;
CV_PY_FROM_ENUM(ml_StatModel_Flags);
CV_PY_TO_ENUM(ml_StatModel_Flags);

typedef cv::ml::VariableTypes ml_VariableTypes;
CV_PY_FROM_ENUM(ml_VariableTypes);
CV_PY_TO_ENUM(ml_VariableTypes);

typedef cv::ocl::OclVectorStrategy ocl_OclVectorStrategy;
CV_PY_FROM_ENUM(ocl_OclVectorStrategy);
CV_PY_TO_ENUM(ocl_OclVectorStrategy);

typedef cv::ogl::Buffer::Access ogl_Buffer_Access;
CV_PY_FROM_ENUM(ogl_Buffer_Access);
CV_PY_TO_ENUM(ogl_Buffer_Access);

typedef cv::ogl::Buffer::Target ogl_Buffer_Target;
CV_PY_FROM_ENUM(ogl_Buffer_Target);
CV_PY_TO_ENUM(ogl_Buffer_Target);

typedef cv::ogl::RenderModes ogl_RenderModes;
CV_PY_FROM_ENUM(ogl_RenderModes);
CV_PY_TO_ENUM(ogl_RenderModes);

typedef cv::ogl::Texture2D::Format ogl_Texture2D_Format;
CV_PY_FROM_ENUM(ogl_Texture2D_Format);
CV_PY_TO_ENUM(ogl_Texture2D_Format);

