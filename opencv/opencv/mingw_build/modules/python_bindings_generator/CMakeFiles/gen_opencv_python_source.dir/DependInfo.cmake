# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )
# The set of files for implicit dependencies of each language:

# Pairs of files generated by the same build rule.
set(CMAKE_MULTIPLE_OUTPUT_PAIRS
  "D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/pyopencv_generated_funcs.h" "D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/pyopencv_generated_enums.h"
  "D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/pyopencv_generated_include.h" "D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/pyopencv_generated_enums.h"
  "D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/pyopencv_generated_modules.h" "D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/pyopencv_generated_enums.h"
  "D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/pyopencv_generated_modules_content.h" "D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/pyopencv_generated_enums.h"
  "D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/pyopencv_generated_types.h" "D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/pyopencv_generated_enums.h"
  "D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/pyopencv_generated_types_content.h" "D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/pyopencv_generated_enums.h"
  "D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/pyopencv_signatures.json" "D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/pyopencv_generated_enums.h"
  )


# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
