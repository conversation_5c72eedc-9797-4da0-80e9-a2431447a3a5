# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

# Utility rule file for gen_opencv_python_source.

# Include the progress variables for this target.
include modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/progress.make

modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source: modules/python_bindings_generator/pyopencv_generated_enums.h
modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source: modules/python_bindings_generator/pyopencv_generated_funcs.h
modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source: modules/python_bindings_generator/pyopencv_generated_include.h
modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source: modules/python_bindings_generator/pyopencv_generated_modules.h
modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source: modules/python_bindings_generator/pyopencv_generated_modules_content.h
modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source: modules/python_bindings_generator/pyopencv_generated_types.h
modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source: modules/python_bindings_generator/pyopencv_generated_types_content.h
modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source: modules/python_bindings_generator/pyopencv_signatures.json


modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/python/src2/gen2.py
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/python/src2/hdr_parser.py
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/affine.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/async.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/base.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/bindings_utils.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/bufferpool.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/check.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/core.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda_stream_accessor.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda_types.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd_wrapper.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/directx.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/eigen.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/fast_math.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/matx.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/neon_utils.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl_genbase.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opengl.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/operations.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/optim.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ovx.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/persistence.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/saturate.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/simd_intrinsics.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/softfloat.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/sse_utils.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/traits.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/types.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utility.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/va_intel.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/version.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/vsx_utils.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/detail/async_promise.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/detail/exception_ptr.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/core/misc/python/shadow_umat.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/flann.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/flann_base.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/miniflann.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/hal/hal.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/imgproc.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/detail/gcgraph.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/ml/include/opencv2/ml.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/ml/include/opencv2/ml/ml.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo/cuda.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo/photo.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/all_layers.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/dict.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/dnn.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/layer.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/shape_utils.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/version.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/features2d/include/opencv2/features2d.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/features2d/include/opencv2/features2d/features2d.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/imgcodecs/include/opencv2/imgcodecs.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/imgcodecs/include/opencv2/imgcodecs/imgcodecs.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio/registry.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio/videoio.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/calib3d/include/opencv2/calib3d.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/calib3d/include/opencv2/calib3d/calib3d.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/highgui/include/opencv2/highgui.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/highgui/include/opencv2/highgui/highgui.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/objdetect/include/opencv2/objdetect.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/objdetect/include/opencv2/objdetect/objdetect.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/warpers.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/autocalib.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/blenders.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/camera.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/exposure_compensate.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/matchers.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/motion_estimators.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/seam_finders.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/timelapsers.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/util.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/warpers.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video/background_segm.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video/tracking.hpp
modules/python_bindings_generator/pyopencv_generated_enums.h: D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video/video.hpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --blue --bold --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generate files for Python bindings and documentation"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\python_bindings_generator && D:\anaconda\python.exe D:/unet/opencv/opencv/sources/modules/python/bindings/..//src2/gen2.py D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/headers.txt

modules/python_bindings_generator/pyopencv_generated_funcs.h: modules/python_bindings_generator/pyopencv_generated_enums.h
	@$(CMAKE_COMMAND) -E touch_nocreate modules\python_bindings_generator\pyopencv_generated_funcs.h

modules/python_bindings_generator/pyopencv_generated_include.h: modules/python_bindings_generator/pyopencv_generated_enums.h
	@$(CMAKE_COMMAND) -E touch_nocreate modules\python_bindings_generator\pyopencv_generated_include.h

modules/python_bindings_generator/pyopencv_generated_modules.h: modules/python_bindings_generator/pyopencv_generated_enums.h
	@$(CMAKE_COMMAND) -E touch_nocreate modules\python_bindings_generator\pyopencv_generated_modules.h

modules/python_bindings_generator/pyopencv_generated_modules_content.h: modules/python_bindings_generator/pyopencv_generated_enums.h
	@$(CMAKE_COMMAND) -E touch_nocreate modules\python_bindings_generator\pyopencv_generated_modules_content.h

modules/python_bindings_generator/pyopencv_generated_types.h: modules/python_bindings_generator/pyopencv_generated_enums.h
	@$(CMAKE_COMMAND) -E touch_nocreate modules\python_bindings_generator\pyopencv_generated_types.h

modules/python_bindings_generator/pyopencv_generated_types_content.h: modules/python_bindings_generator/pyopencv_generated_enums.h
	@$(CMAKE_COMMAND) -E touch_nocreate modules\python_bindings_generator\pyopencv_generated_types_content.h

modules/python_bindings_generator/pyopencv_signatures.json: modules/python_bindings_generator/pyopencv_generated_enums.h
	@$(CMAKE_COMMAND) -E touch_nocreate modules\python_bindings_generator\pyopencv_signatures.json

gen_opencv_python_source: modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source
gen_opencv_python_source: modules/python_bindings_generator/pyopencv_generated_enums.h
gen_opencv_python_source: modules/python_bindings_generator/pyopencv_generated_funcs.h
gen_opencv_python_source: modules/python_bindings_generator/pyopencv_generated_include.h
gen_opencv_python_source: modules/python_bindings_generator/pyopencv_generated_modules.h
gen_opencv_python_source: modules/python_bindings_generator/pyopencv_generated_modules_content.h
gen_opencv_python_source: modules/python_bindings_generator/pyopencv_generated_types.h
gen_opencv_python_source: modules/python_bindings_generator/pyopencv_generated_types_content.h
gen_opencv_python_source: modules/python_bindings_generator/pyopencv_signatures.json
gen_opencv_python_source: modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/build.make

.PHONY : gen_opencv_python_source

# Rule to build all files generated by this target.
modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/build: gen_opencv_python_source

.PHONY : modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/build

modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/clean:
	cd /d D:\unet\opencv\opencv\mingw_build\modules\python_bindings_generator && $(CMAKE_COMMAND) -P CMakeFiles\gen_opencv_python_source.dir\cmake_clean.cmake
.PHONY : modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/clean

modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\unet\opencv\opencv\sources D:\unet\opencv\opencv\sources\modules\python\bindings D:\unet\opencv\opencv\mingw_build D:\unet\opencv\opencv\mingw_build\modules\python_bindings_generator D:\unet\opencv\opencv\mingw_build\modules\python_bindings_generator\CMakeFiles\gen_opencv_python_source.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/depend

