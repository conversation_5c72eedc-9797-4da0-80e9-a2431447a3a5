//user-defined headers
#include "D:/unet/opencv/opencv/sources/modules/core/misc/python/pyopencv_async.hpp"
#include "D:/unet/opencv/opencv/sources/modules/core/misc/python/pyopencv_cuda.hpp"
#include "D:/unet/opencv/opencv/sources/modules/core/misc/python/pyopencv_umat.hpp"
#include "D:/unet/opencv/opencv/sources/modules/flann/misc/python/pyopencv_flann.hpp"
#include "D:/unet/opencv/opencv/sources/modules/ml/misc/python/pyopencv_ml.hpp"
#include "D:/unet/opencv/opencv/sources/modules/dnn/misc/python/pyopencv_dnn.hpp"
#include "D:/unet/opencv/opencv/sources/modules/features2d/misc/python/pyopencv_features2d.hpp"
#include "D:/unet/opencv/opencv/sources/modules/videoio/misc/python/pyopencv_videoio.hpp"
#include "D:/unet/opencv/opencv/sources/modules/objdetect/misc/python/pyopencv_objdetect.hpp"
#include "D:/unet/opencv/opencv/sources/modules/stitching/misc/python/pyopencv_stitching.hpp"
