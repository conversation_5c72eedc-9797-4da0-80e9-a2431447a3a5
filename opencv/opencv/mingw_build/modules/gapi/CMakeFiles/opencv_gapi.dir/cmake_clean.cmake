file(REMOVE_RECURSE
  "CMakeFiles/opencv_gapi.dir/src/api/gorigin.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/gmat.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/garray.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/gscalar.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/gkernel.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/gbackend.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/gproto.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/gnode.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/gcall.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/gcomputation.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/operators.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/kernels_core.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/kernels_imgproc.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/render.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/render_ocv.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/ginfer.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/api/ft_render.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/gmodel.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/gmodelbuilder.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/gislandmodel.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/gcompiler.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/gcompiled.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/gstreaming.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/passes/helpers.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/passes/dump_dot.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/passes/islands.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/passes/meta.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/passes/kernels.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/passes/exec.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/passes/transformations.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/passes/pattern_matching.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/passes/perform_substitution.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/compiler/passes/streaming.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/executor/gexecutor.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/executor/gstreamingexecutor.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/executor/gasync.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpubackend.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpukernel.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpuimgproc.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpucore.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbuffer.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbackend.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc_func.dispatch.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidcore.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclbackend.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclkernel.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclimgproc.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclcore.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/ie/giebackend.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocvbackend.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocv.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlcore.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlbackend.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundbackend.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundkernel.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.sse4_1.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.avx2.cpp.obj"
  "CMakeFiles/opencv_gapi.dir/vs_version.rc.obj"
  "CMakeFiles/opencv_gapi.dir/opencv_gapi_main.cpp.obj"
  "../../bin/libopencv_gapi420.pdb"
  "../../bin/libopencv_gapi420.dll"
  "../../lib/libopencv_gapi420.dll.a"
  "../../bin/libopencv_gapi420.dll.manifest"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX RC)
  include(CMakeFiles/opencv_gapi.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
