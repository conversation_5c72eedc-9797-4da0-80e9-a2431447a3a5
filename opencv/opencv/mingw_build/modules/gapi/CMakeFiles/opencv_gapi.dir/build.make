# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

# Include any dependencies generated for this target.
include modules/gapi/CMakeFiles/opencv_gapi.dir/depend.make

# Include the progress variables for this target.
include modules/gapi/CMakeFiles/opencv_gapi.dir/progress.make

# Include the compile flags for this target's objects.
include modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gorigin.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gorigin.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gorigin.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/gorigin.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gorigin.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\gorigin.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\gorigin.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gorigin.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/gorigin.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\gorigin.cpp > CMakeFiles\opencv_gapi.dir\src\api\gorigin.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gorigin.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/gorigin.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\gorigin.cpp -o CMakeFiles\opencv_gapi.dir\src\api\gorigin.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gmat.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gmat.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gmat.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/gmat.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gmat.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\gmat.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\gmat.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gmat.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/gmat.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\gmat.cpp > CMakeFiles\opencv_gapi.dir\src\api\gmat.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gmat.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/gmat.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\gmat.cpp -o CMakeFiles\opencv_gapi.dir\src\api\gmat.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/garray.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/garray.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/garray.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/garray.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/garray.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\garray.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\garray.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/garray.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/garray.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\garray.cpp > CMakeFiles\opencv_gapi.dir\src\api\garray.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/garray.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/garray.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\garray.cpp -o CMakeFiles\opencv_gapi.dir\src\api\garray.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gscalar.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gscalar.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gscalar.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/gscalar.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gscalar.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\gscalar.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\gscalar.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gscalar.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/gscalar.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\gscalar.cpp > CMakeFiles\opencv_gapi.dir\src\api\gscalar.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gscalar.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/gscalar.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\gscalar.cpp -o CMakeFiles\opencv_gapi.dir\src\api\gscalar.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gkernel.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gkernel.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gkernel.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/gkernel.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gkernel.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\gkernel.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\gkernel.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gkernel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/gkernel.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\gkernel.cpp > CMakeFiles\opencv_gapi.dir\src\api\gkernel.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gkernel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/gkernel.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\gkernel.cpp -o CMakeFiles\opencv_gapi.dir\src\api\gkernel.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gbackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gbackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gbackend.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/gbackend.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gbackend.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\gbackend.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\gbackend.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gbackend.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/gbackend.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\gbackend.cpp > CMakeFiles\opencv_gapi.dir\src\api\gbackend.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gbackend.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/gbackend.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\gbackend.cpp -o CMakeFiles\opencv_gapi.dir\src\api\gbackend.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gproto.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gproto.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gproto.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/gproto.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gproto.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\gproto.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\gproto.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gproto.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/gproto.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\gproto.cpp > CMakeFiles\opencv_gapi.dir\src\api\gproto.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gproto.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/gproto.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\gproto.cpp -o CMakeFiles\opencv_gapi.dir\src\api\gproto.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gnode.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gnode.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gnode.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/gnode.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gnode.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\gnode.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\gnode.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gnode.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/gnode.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\gnode.cpp > CMakeFiles\opencv_gapi.dir\src\api\gnode.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gnode.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/gnode.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\gnode.cpp -o CMakeFiles\opencv_gapi.dir\src\api\gnode.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcall.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcall.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcall.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/gcall.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcall.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\gcall.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\gcall.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcall.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/gcall.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\gcall.cpp > CMakeFiles\opencv_gapi.dir\src\api\gcall.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcall.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/gcall.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\gcall.cpp -o CMakeFiles\opencv_gapi.dir\src\api\gcall.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcomputation.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcomputation.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcomputation.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/gcomputation.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcomputation.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\gcomputation.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\gcomputation.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcomputation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/gcomputation.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\gcomputation.cpp > CMakeFiles\opencv_gapi.dir\src\api\gcomputation.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcomputation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/gcomputation.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\gcomputation.cpp -o CMakeFiles\opencv_gapi.dir\src\api\gcomputation.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/operators.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/operators.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/operators.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/operators.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/operators.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\operators.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\operators.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/operators.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/operators.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\operators.cpp > CMakeFiles\opencv_gapi.dir\src\api\operators.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/operators.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/operators.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\operators.cpp -o CMakeFiles\opencv_gapi.dir\src\api\operators.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_core.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_core.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_core.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/kernels_core.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_core.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\kernels_core.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\kernels_core.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_core.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/kernels_core.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\kernels_core.cpp > CMakeFiles\opencv_gapi.dir\src\api\kernels_core.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_core.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/kernels_core.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\kernels_core.cpp -o CMakeFiles\opencv_gapi.dir\src\api\kernels_core.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_imgproc.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_imgproc.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_imgproc.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/kernels_imgproc.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_imgproc.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\kernels_imgproc.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\kernels_imgproc.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_imgproc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/kernels_imgproc.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\kernels_imgproc.cpp > CMakeFiles\opencv_gapi.dir\src\api\kernels_imgproc.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_imgproc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/kernels_imgproc.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\kernels_imgproc.cpp -o CMakeFiles\opencv_gapi.dir\src\api\kernels_imgproc.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/render.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\render.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\render.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/render.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\render.cpp > CMakeFiles\opencv_gapi.dir\src\api\render.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/render.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\render.cpp -o CMakeFiles\opencv_gapi.dir\src\api\render.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render_ocv.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render_ocv.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render_ocv.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/render_ocv.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render_ocv.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\render_ocv.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\render_ocv.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render_ocv.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/render_ocv.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\render_ocv.cpp > CMakeFiles\opencv_gapi.dir\src\api\render_ocv.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render_ocv.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/render_ocv.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\render_ocv.cpp -o CMakeFiles\opencv_gapi.dir\src\api\render_ocv.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ginfer.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ginfer.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ginfer.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/ginfer.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ginfer.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\ginfer.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\ginfer.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ginfer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/ginfer.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\ginfer.cpp > CMakeFiles\opencv_gapi.dir\src\api\ginfer.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ginfer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/ginfer.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\ginfer.cpp -o CMakeFiles\opencv_gapi.dir\src\api\ginfer.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ft_render.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ft_render.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ft_render.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/api/ft_render.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ft_render.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\api\ft_render.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\api\ft_render.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ft_render.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/api/ft_render.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\api\ft_render.cpp > CMakeFiles\opencv_gapi.dir\src\api\ft_render.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ft_render.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/api/ft_render.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\api\ft_render.cpp -o CMakeFiles\opencv_gapi.dir\src\api\ft_render.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodel.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodel.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodel.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gmodel.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodel.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\gmodel.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gmodel.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/gmodel.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gmodel.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\gmodel.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/gmodel.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gmodel.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\gmodel.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodelbuilder.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodelbuilder.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodelbuilder.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gmodelbuilder.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodelbuilder.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\gmodelbuilder.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gmodelbuilder.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodelbuilder.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/gmodelbuilder.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gmodelbuilder.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\gmodelbuilder.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodelbuilder.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/gmodelbuilder.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gmodelbuilder.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\gmodelbuilder.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gislandmodel.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gislandmodel.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gislandmodel.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gislandmodel.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gislandmodel.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\gislandmodel.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gislandmodel.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gislandmodel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/gislandmodel.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gislandmodel.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\gislandmodel.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gislandmodel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/gislandmodel.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gislandmodel.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\gislandmodel.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiler.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiler.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiler.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gcompiler.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiler.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\gcompiler.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gcompiler.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/gcompiler.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gcompiler.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\gcompiler.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/gcompiler.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gcompiler.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\gcompiler.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiled.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiled.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiled.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gcompiled.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiled.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\gcompiled.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gcompiled.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiled.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/gcompiled.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gcompiled.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\gcompiled.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiled.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/gcompiled.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gcompiled.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\gcompiled.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gstreaming.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gstreaming.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gstreaming.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gstreaming.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gstreaming.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\gstreaming.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gstreaming.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gstreaming.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/gstreaming.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gstreaming.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\gstreaming.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gstreaming.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/gstreaming.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\gstreaming.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\gstreaming.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/helpers.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/helpers.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/helpers.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/helpers.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/helpers.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\helpers.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\helpers.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/helpers.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/passes/helpers.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\helpers.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\passes\helpers.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/helpers.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/passes/helpers.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\helpers.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\helpers.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/dump_dot.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/dump_dot.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/dump_dot.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/dump_dot.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/dump_dot.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\dump_dot.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\dump_dot.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/dump_dot.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/passes/dump_dot.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\dump_dot.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\passes\dump_dot.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/dump_dot.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/passes/dump_dot.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\dump_dot.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\dump_dot.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/islands.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/islands.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/islands.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/islands.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/islands.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\islands.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\islands.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/islands.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/passes/islands.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\islands.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\passes\islands.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/islands.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/passes/islands.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\islands.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\islands.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/meta.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/meta.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/meta.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/meta.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/meta.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\meta.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\meta.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/meta.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/passes/meta.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\meta.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\passes\meta.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/meta.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/passes/meta.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\meta.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\meta.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/kernels.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/kernels.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/kernels.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/kernels.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/kernels.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\kernels.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\kernels.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/kernels.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/passes/kernels.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\kernels.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\passes\kernels.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/kernels.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/passes/kernels.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\kernels.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\kernels.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/exec.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/exec.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/exec.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/exec.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/exec.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\exec.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\exec.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/exec.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/passes/exec.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\exec.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\passes\exec.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/exec.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/passes/exec.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\exec.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\exec.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/transformations.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/transformations.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/transformations.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/transformations.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/transformations.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\transformations.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\transformations.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/transformations.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/passes/transformations.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\transformations.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\passes\transformations.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/transformations.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/passes/transformations.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\transformations.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\transformations.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/pattern_matching.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/pattern_matching.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/pattern_matching.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/pattern_matching.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/pattern_matching.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\pattern_matching.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\pattern_matching.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/pattern_matching.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/passes/pattern_matching.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\pattern_matching.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\passes\pattern_matching.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/pattern_matching.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/passes/pattern_matching.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\pattern_matching.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\pattern_matching.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/perform_substitution.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/perform_substitution.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/perform_substitution.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/perform_substitution.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/perform_substitution.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\perform_substitution.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\perform_substitution.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/perform_substitution.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/passes/perform_substitution.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\perform_substitution.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\passes\perform_substitution.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/perform_substitution.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/passes/perform_substitution.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\perform_substitution.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\perform_substitution.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/streaming.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/streaming.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/streaming.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/streaming.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/streaming.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\streaming.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\streaming.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/streaming.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/compiler/passes/streaming.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\streaming.cpp > CMakeFiles\opencv_gapi.dir\src\compiler\passes\streaming.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/streaming.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/compiler/passes/streaming.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\compiler\passes\streaming.cpp -o CMakeFiles\opencv_gapi.dir\src\compiler\passes\streaming.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gexecutor.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gexecutor.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gexecutor.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/executor/gexecutor.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gexecutor.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\executor\gexecutor.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\executor\gexecutor.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gexecutor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/executor/gexecutor.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\executor\gexecutor.cpp > CMakeFiles\opencv_gapi.dir\src\executor\gexecutor.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gexecutor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/executor/gexecutor.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\executor\gexecutor.cpp -o CMakeFiles\opencv_gapi.dir\src\executor\gexecutor.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gstreamingexecutor.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gstreamingexecutor.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gstreamingexecutor.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/executor/gstreamingexecutor.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gstreamingexecutor.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\executor\gstreamingexecutor.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\executor\gstreamingexecutor.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gstreamingexecutor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/executor/gstreamingexecutor.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\executor\gstreamingexecutor.cpp > CMakeFiles\opencv_gapi.dir\src\executor\gstreamingexecutor.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gstreamingexecutor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/executor/gstreamingexecutor.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\executor\gstreamingexecutor.cpp -o CMakeFiles\opencv_gapi.dir\src\executor\gstreamingexecutor.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gasync.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gasync.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gasync.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/executor/gasync.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gasync.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\executor\gasync.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\executor\gasync.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gasync.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/executor/gasync.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\executor\gasync.cpp > CMakeFiles\opencv_gapi.dir\src\executor\gasync.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gasync.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/executor/gasync.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\executor\gasync.cpp -o CMakeFiles\opencv_gapi.dir\src\executor\gasync.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpubackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpubackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpubackend.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/cpu/gcpubackend.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpubackend.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\cpu\gcpubackend.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\cpu\gcpubackend.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpubackend.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpubackend.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\cpu\gcpubackend.cpp > CMakeFiles\opencv_gapi.dir\src\backends\cpu\gcpubackend.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpubackend.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpubackend.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\cpu\gcpubackend.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\cpu\gcpubackend.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpukernel.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpukernel.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpukernel.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/cpu/gcpukernel.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpukernel.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\cpu\gcpukernel.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\cpu\gcpukernel.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpukernel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpukernel.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\cpu\gcpukernel.cpp > CMakeFiles\opencv_gapi.dir\src\backends\cpu\gcpukernel.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpukernel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpukernel.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\cpu\gcpukernel.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\cpu\gcpukernel.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpuimgproc.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpuimgproc.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpuimgproc.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/cpu/gcpuimgproc.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpuimgproc.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\cpu\gcpuimgproc.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\cpu\gcpuimgproc.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpuimgproc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpuimgproc.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\cpu\gcpuimgproc.cpp > CMakeFiles\opencv_gapi.dir\src\backends\cpu\gcpuimgproc.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpuimgproc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpuimgproc.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\cpu\gcpuimgproc.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\cpu\gcpuimgproc.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpucore.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpucore.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpucore.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/cpu/gcpucore.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpucore.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\cpu\gcpucore.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\cpu\gcpucore.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpucore.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpucore.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\cpu\gcpucore.cpp > CMakeFiles\opencv_gapi.dir\src\backends\cpu\gcpucore.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpucore.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpucore.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\cpu\gcpucore.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\cpu\gcpucore.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbuffer.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbuffer.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbuffer.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/fluid/gfluidbuffer.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbuffer.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\fluid\gfluidbuffer.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\fluid\gfluidbuffer.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbuffer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbuffer.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\fluid\gfluidbuffer.cpp > CMakeFiles\opencv_gapi.dir\src\backends\fluid\gfluidbuffer.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbuffer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbuffer.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\fluid\gfluidbuffer.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\fluid\gfluidbuffer.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbackend.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/fluid/gfluidbackend.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbackend.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\fluid\gfluidbackend.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\fluid\gfluidbackend.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbackend.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbackend.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\fluid\gfluidbackend.cpp > CMakeFiles\opencv_gapi.dir\src\backends\fluid\gfluidbackend.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbackend.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbackend.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\fluid\gfluidbackend.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\fluid\gfluidbackend.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/fluid/gfluidimgproc.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\fluid\gfluidimgproc.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\fluid\gfluidimgproc.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\fluid\gfluidimgproc.cpp > CMakeFiles\opencv_gapi.dir\src\backends\fluid\gfluidimgproc.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\fluid\gfluidimgproc.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\fluid\gfluidimgproc.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc_func.dispatch.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc_func.dispatch.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc_func.dispatch.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/fluid/gfluidimgproc_func.dispatch.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_44) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc_func.dispatch.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\fluid\gfluidimgproc_func.dispatch.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\fluid\gfluidimgproc_func.dispatch.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc_func.dispatch.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc_func.dispatch.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\fluid\gfluidimgproc_func.dispatch.cpp > CMakeFiles\opencv_gapi.dir\src\backends\fluid\gfluidimgproc_func.dispatch.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc_func.dispatch.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc_func.dispatch.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\fluid\gfluidimgproc_func.dispatch.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\fluid\gfluidimgproc_func.dispatch.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidcore.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidcore.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidcore.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/fluid/gfluidcore.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_45) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidcore.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\fluid\gfluidcore.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\fluid\gfluidcore.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidcore.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidcore.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\fluid\gfluidcore.cpp > CMakeFiles\opencv_gapi.dir\src\backends\fluid\gfluidcore.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidcore.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidcore.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\fluid\gfluidcore.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\fluid\gfluidcore.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclbackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclbackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclbackend.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/ocl/goclbackend.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_46) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclbackend.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\ocl\goclbackend.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\ocl\goclbackend.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclbackend.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclbackend.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\ocl\goclbackend.cpp > CMakeFiles\opencv_gapi.dir\src\backends\ocl\goclbackend.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclbackend.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclbackend.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\ocl\goclbackend.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\ocl\goclbackend.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclkernel.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclkernel.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclkernel.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/ocl/goclkernel.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_47) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclkernel.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\ocl\goclkernel.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\ocl\goclkernel.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclkernel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclkernel.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\ocl\goclkernel.cpp > CMakeFiles\opencv_gapi.dir\src\backends\ocl\goclkernel.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclkernel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclkernel.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\ocl\goclkernel.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\ocl\goclkernel.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclimgproc.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclimgproc.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclimgproc.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/ocl/goclimgproc.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_48) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclimgproc.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\ocl\goclimgproc.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\ocl\goclimgproc.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclimgproc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclimgproc.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\ocl\goclimgproc.cpp > CMakeFiles\opencv_gapi.dir\src\backends\ocl\goclimgproc.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclimgproc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclimgproc.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\ocl\goclimgproc.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\ocl\goclimgproc.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclcore.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclcore.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclcore.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/ocl/goclcore.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_49) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclcore.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\ocl\goclcore.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\ocl\goclcore.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclcore.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclcore.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\ocl\goclcore.cpp > CMakeFiles\opencv_gapi.dir\src\backends\ocl\goclcore.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclcore.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclcore.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\ocl\goclcore.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\ocl\goclcore.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ie/giebackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ie/giebackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ie/giebackend.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/ie/giebackend.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_50) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ie/giebackend.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\ie\giebackend.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\ie\giebackend.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ie/giebackend.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/ie/giebackend.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\ie\giebackend.cpp > CMakeFiles\opencv_gapi.dir\src\backends\ie\giebackend.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ie/giebackend.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/ie/giebackend.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\ie\giebackend.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\ie\giebackend.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocvbackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocvbackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocvbackend.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/render/grenderocvbackend.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_51) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocvbackend.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\render\grenderocvbackend.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\render\grenderocvbackend.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocvbackend.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocvbackend.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\render\grenderocvbackend.cpp > CMakeFiles\opencv_gapi.dir\src\backends\render\grenderocvbackend.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocvbackend.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocvbackend.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\render\grenderocvbackend.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\render\grenderocvbackend.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocv.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocv.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocv.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/render/grenderocv.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_52) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocv.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\render\grenderocv.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\render\grenderocv.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocv.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocv.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\render\grenderocv.cpp > CMakeFiles\opencv_gapi.dir\src\backends\render\grenderocv.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocv.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocv.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\render\grenderocv.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\render\grenderocv.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlcore.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlcore.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlcore.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/plaidml/gplaidmlcore.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_53) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlcore.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\plaidml\gplaidmlcore.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\plaidml\gplaidmlcore.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlcore.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlcore.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\plaidml\gplaidmlcore.cpp > CMakeFiles\opencv_gapi.dir\src\backends\plaidml\gplaidmlcore.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlcore.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlcore.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\plaidml\gplaidmlcore.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\plaidml\gplaidmlcore.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlbackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlbackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlbackend.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/plaidml/gplaidmlbackend.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_54) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlbackend.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\plaidml\gplaidmlbackend.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\plaidml\gplaidmlbackend.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlbackend.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlbackend.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\plaidml\gplaidmlbackend.cpp > CMakeFiles\opencv_gapi.dir\src\backends\plaidml\gplaidmlbackend.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlbackend.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlbackend.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\plaidml\gplaidmlbackend.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\plaidml\gplaidmlbackend.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundbackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundbackend.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundbackend.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/common/gcompoundbackend.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_55) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundbackend.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\common\gcompoundbackend.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\common\gcompoundbackend.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundbackend.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundbackend.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\common\gcompoundbackend.cpp > CMakeFiles\opencv_gapi.dir\src\backends\common\gcompoundbackend.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundbackend.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundbackend.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\common\gcompoundbackend.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\common\gcompoundbackend.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundkernel.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundkernel.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundkernel.cpp.obj: D:/unet/opencv/opencv/sources/modules/gapi/src/backends/common/gcompoundkernel.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_56) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundkernel.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\src\backends\common\gcompoundkernel.cpp.obj -c D:\unet\opencv\opencv\sources\modules\gapi\src\backends\common\gcompoundkernel.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundkernel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundkernel.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\modules\gapi\src\backends\common\gcompoundkernel.cpp > CMakeFiles\opencv_gapi.dir\src\backends\common\gcompoundkernel.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundkernel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundkernel.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\modules\gapi\src\backends\common\gcompoundkernel.cpp -o CMakeFiles\opencv_gapi.dir\src\backends\common\gcompoundkernel.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.sse4_1.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.sse4_1.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.sse4_1.cpp.obj: modules/gapi/backends/fluid/gfluidimgproc_func.sse4_1.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_57) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.sse4_1.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) -DCV_CPU_COMPILE_SSE4_1=1 -DCV_CPU_COMPILE_SSSE3=1 -DCV_CPU_DISPATCH_MODE=SSE4_1 $(CXX_INCLUDES) $(CXX_FLAGS)  -mssse3 -msse4.1 -o CMakeFiles\opencv_gapi.dir\backends\fluid\gfluidimgproc_func.sse4_1.cpp.obj -c D:\unet\opencv\opencv\mingw_build\modules\gapi\backends\fluid\gfluidimgproc_func.sse4_1.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.sse4_1.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.sse4_1.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) -DCV_CPU_COMPILE_SSE4_1=1 -DCV_CPU_COMPILE_SSSE3=1 -DCV_CPU_DISPATCH_MODE=SSE4_1 $(CXX_INCLUDES) $(CXX_FLAGS)  -mssse3 -msse4.1 -E D:\unet\opencv\opencv\mingw_build\modules\gapi\backends\fluid\gfluidimgproc_func.sse4_1.cpp > CMakeFiles\opencv_gapi.dir\backends\fluid\gfluidimgproc_func.sse4_1.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.sse4_1.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.sse4_1.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) -DCV_CPU_COMPILE_SSE4_1=1 -DCV_CPU_COMPILE_SSSE3=1 -DCV_CPU_DISPATCH_MODE=SSE4_1 $(CXX_INCLUDES) $(CXX_FLAGS)  -mssse3 -msse4.1 -S D:\unet\opencv\opencv\mingw_build\modules\gapi\backends\fluid\gfluidimgproc_func.sse4_1.cpp -o CMakeFiles\opencv_gapi.dir\backends\fluid\gfluidimgproc_func.sse4_1.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.avx2.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.avx2.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.avx2.cpp.obj: modules/gapi/backends/fluid/gfluidimgproc_func.avx2.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_58) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.avx2.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) -DCV_CPU_COMPILE_AVX2=1 -DCV_CPU_COMPILE_AVX=1 -DCV_CPU_COMPILE_FMA3=1 -DCV_CPU_COMPILE_FP16=1 -DCV_CPU_COMPILE_POPCNT=1 -DCV_CPU_COMPILE_SSE4_1=1 -DCV_CPU_COMPILE_SSE4_2=1 -DCV_CPU_COMPILE_SSSE3=1 -DCV_CPU_DISPATCH_MODE=AVX2 $(CXX_INCLUDES) $(CXX_FLAGS)  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2 -o CMakeFiles\opencv_gapi.dir\backends\fluid\gfluidimgproc_func.avx2.cpp.obj -c D:\unet\opencv\opencv\mingw_build\modules\gapi\backends\fluid\gfluidimgproc_func.avx2.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.avx2.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.avx2.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) -DCV_CPU_COMPILE_AVX2=1 -DCV_CPU_COMPILE_AVX=1 -DCV_CPU_COMPILE_FMA3=1 -DCV_CPU_COMPILE_FP16=1 -DCV_CPU_COMPILE_POPCNT=1 -DCV_CPU_COMPILE_SSE4_1=1 -DCV_CPU_COMPILE_SSE4_2=1 -DCV_CPU_COMPILE_SSSE3=1 -DCV_CPU_DISPATCH_MODE=AVX2 $(CXX_INCLUDES) $(CXX_FLAGS)  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2 -E D:\unet\opencv\opencv\mingw_build\modules\gapi\backends\fluid\gfluidimgproc_func.avx2.cpp > CMakeFiles\opencv_gapi.dir\backends\fluid\gfluidimgproc_func.avx2.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.avx2.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.avx2.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) -DCV_CPU_COMPILE_AVX2=1 -DCV_CPU_COMPILE_AVX=1 -DCV_CPU_COMPILE_FMA3=1 -DCV_CPU_COMPILE_FP16=1 -DCV_CPU_COMPILE_POPCNT=1 -DCV_CPU_COMPILE_SSE4_1=1 -DCV_CPU_COMPILE_SSE4_2=1 -DCV_CPU_COMPILE_SSSE3=1 -DCV_CPU_DISPATCH_MODE=AVX2 $(CXX_INCLUDES) $(CXX_FLAGS)  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2 -S D:\unet\opencv\opencv\mingw_build\modules\gapi\backends\fluid\gfluidimgproc_func.avx2.cpp -o CMakeFiles\opencv_gapi.dir\backends\fluid\gfluidimgproc_func.avx2.cpp.s

modules/gapi/CMakeFiles/opencv_gapi.dir/vs_version.rc.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/vs_version.rc.obj: modules/gapi/vs_version.rc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_59) "Building RC object modules/gapi/CMakeFiles/opencv_gapi.dir/vs_version.rc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\windres.exe -O coff $(RC_DEFINES) $(RC_INCLUDES) $(RC_FLAGS) D:\unet\opencv\opencv\mingw_build\modules\gapi\vs_version.rc CMakeFiles\opencv_gapi.dir\vs_version.rc.obj

modules/gapi/CMakeFiles/opencv_gapi.dir/opencv_gapi_main.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/flags.make
modules/gapi/CMakeFiles/opencv_gapi.dir/opencv_gapi_main.cpp.obj: modules/gapi/CMakeFiles/opencv_gapi.dir/includes_CXX.rsp
modules/gapi/CMakeFiles/opencv_gapi.dir/opencv_gapi_main.cpp.obj: modules/gapi/opencv_gapi_main.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_60) "Building CXX object modules/gapi/CMakeFiles/opencv_gapi.dir/opencv_gapi_main.cpp.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\opencv_gapi.dir\opencv_gapi_main.cpp.obj -c D:\unet\opencv\opencv\mingw_build\modules\gapi\opencv_gapi_main.cpp

modules/gapi/CMakeFiles/opencv_gapi.dir/opencv_gapi_main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/opencv_gapi.dir/opencv_gapi_main.cpp.i"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\mingw_build\modules\gapi\opencv_gapi_main.cpp > CMakeFiles\opencv_gapi.dir\opencv_gapi_main.cpp.i

modules/gapi/CMakeFiles/opencv_gapi.dir/opencv_gapi_main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/opencv_gapi.dir/opencv_gapi_main.cpp.s"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\mingw_build\modules\gapi\opencv_gapi_main.cpp -o CMakeFiles\opencv_gapi.dir\opencv_gapi_main.cpp.s

# Object files for target opencv_gapi
opencv_gapi_OBJECTS = \
"CMakeFiles/opencv_gapi.dir/src/api/gorigin.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/gmat.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/garray.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/gscalar.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/gkernel.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/gbackend.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/gproto.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/gnode.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/gcall.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/gcomputation.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/operators.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/kernels_core.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/kernels_imgproc.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/render.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/render_ocv.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/ginfer.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/api/ft_render.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/gmodel.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/gmodelbuilder.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/gislandmodel.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/gcompiler.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/gcompiled.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/gstreaming.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/passes/helpers.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/passes/dump_dot.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/passes/islands.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/passes/meta.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/passes/kernels.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/passes/exec.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/passes/transformations.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/passes/pattern_matching.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/passes/perform_substitution.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/compiler/passes/streaming.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/executor/gexecutor.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/executor/gstreamingexecutor.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/executor/gasync.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpubackend.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpukernel.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpuimgproc.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpucore.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbuffer.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbackend.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc_func.dispatch.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidcore.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclbackend.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclkernel.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclimgproc.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclcore.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/ie/giebackend.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocvbackend.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocv.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlcore.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlbackend.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundbackend.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundkernel.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.sse4_1.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.avx2.cpp.obj" \
"CMakeFiles/opencv_gapi.dir/vs_version.rc.obj" \
"CMakeFiles/opencv_gapi.dir/opencv_gapi_main.cpp.obj"

# External object files for target opencv_gapi
opencv_gapi_EXTERNAL_OBJECTS =

bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gorigin.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gmat.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/garray.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gscalar.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gkernel.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gbackend.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gproto.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gnode.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcall.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcomputation.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/operators.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_core.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_imgproc.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render_ocv.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ginfer.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ft_render.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodel.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodelbuilder.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gislandmodel.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiler.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiled.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gstreaming.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/helpers.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/dump_dot.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/islands.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/meta.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/kernels.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/exec.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/transformations.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/pattern_matching.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/perform_substitution.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/streaming.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gexecutor.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gstreamingexecutor.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gasync.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpubackend.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpukernel.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpuimgproc.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpucore.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbuffer.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbackend.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc_func.dispatch.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidcore.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclbackend.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclkernel.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclimgproc.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclcore.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ie/giebackend.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocvbackend.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocv.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlcore.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlbackend.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundbackend.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundkernel.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.sse4_1.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.avx2.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/vs_version.rc.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/opencv_gapi_main.cpp.obj
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/build.make
bin/libopencv_gapi420.dll: lib/libopencv_imgproc420.dll.a
bin/libopencv_gapi420.dll: lib/libade.a
bin/libopencv_gapi420.dll: lib/libopencv_core420.dll.a
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/linklibs.rsp
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/objects1.rsp
bin/libopencv_gapi420.dll: modules/gapi/CMakeFiles/opencv_gapi.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_61) "Linking CXX shared library ..\..\bin\libopencv_gapi420.dll"
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\opencv_gapi.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
modules/gapi/CMakeFiles/opencv_gapi.dir/build: bin/libopencv_gapi420.dll

.PHONY : modules/gapi/CMakeFiles/opencv_gapi.dir/build

modules/gapi/CMakeFiles/opencv_gapi.dir/clean:
	cd /d D:\unet\opencv\opencv\mingw_build\modules\gapi && $(CMAKE_COMMAND) -P CMakeFiles\opencv_gapi.dir\cmake_clean.cmake
.PHONY : modules/gapi/CMakeFiles/opencv_gapi.dir/clean

modules/gapi/CMakeFiles/opencv_gapi.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\unet\opencv\opencv\sources D:\unet\opencv\opencv\sources\modules\gapi D:\unet\opencv\opencv\mingw_build D:\unet\opencv\opencv\mingw_build\modules\gapi D:\unet\opencv\opencv\mingw_build\modules\gapi\CMakeFiles\opencv_gapi.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : modules/gapi/CMakeFiles/opencv_gapi.dir/depend

