# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  "RC"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "D:/unet/opencv/opencv/mingw_build/modules/gapi/backends/fluid/gfluidimgproc_func.avx2.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.avx2.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/modules/gapi/backends/fluid/gfluidimgproc_func.sse4_1.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/backends/fluid/gfluidimgproc_func.sse4_1.cpp.obj"
  "D:/unet/opencv/opencv/mingw_build/modules/gapi/opencv_gapi_main.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/opencv_gapi_main.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/ft_render.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ft_render.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/garray.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/garray.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/gbackend.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gbackend.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/gcall.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcall.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/gcomputation.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gcomputation.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/ginfer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/ginfer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/gkernel.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gkernel.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/gmat.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gmat.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/gnode.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gnode.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/gorigin.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gorigin.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/gproto.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gproto.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/gscalar.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/gscalar.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/kernels_core.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_core.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/kernels_imgproc.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/kernels_imgproc.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/operators.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/operators.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/render.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/api/render_ocv.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/api/render_ocv.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/common/gcompoundbackend.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundbackend.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/common/gcompoundkernel.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/common/gcompoundkernel.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/cpu/gcpubackend.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpubackend.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/cpu/gcpucore.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpucore.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/cpu/gcpuimgproc.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpuimgproc.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/cpu/gcpukernel.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/cpu/gcpukernel.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/fluid/gfluidbackend.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbackend.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/fluid/gfluidbuffer.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidbuffer.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/fluid/gfluidcore.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidcore.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/fluid/gfluidimgproc.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/fluid/gfluidimgproc_func.dispatch.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/fluid/gfluidimgproc_func.dispatch.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/ie/giebackend.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ie/giebackend.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/ocl/goclbackend.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclbackend.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/ocl/goclcore.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclcore.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/ocl/goclimgproc.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclimgproc.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/ocl/goclkernel.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/ocl/goclkernel.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/plaidml/gplaidmlbackend.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlbackend.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/plaidml/gplaidmlcore.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/plaidml/gplaidmlcore.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/render/grenderocv.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocv.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/backends/render/grenderocvbackend.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/backends/render/grenderocvbackend.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gcompiled.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiled.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gcompiler.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gcompiler.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gislandmodel.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gislandmodel.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gmodel.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodel.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gmodelbuilder.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gmodelbuilder.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gstreaming.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/gstreaming.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/dump_dot.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/dump_dot.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/exec.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/exec.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/helpers.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/helpers.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/islands.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/islands.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/kernels.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/kernels.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/meta.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/meta.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/pattern_matching.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/pattern_matching.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/perform_substitution.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/perform_substitution.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/streaming.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/streaming.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/transformations.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/compiler/passes/transformations.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/executor/gasync.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gasync.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/executor/gexecutor.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gexecutor.cpp.obj"
  "D:/unet/opencv/opencv/sources/modules/gapi/src/executor/gstreamingexecutor.cpp" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/src/executor/gstreamingexecutor.cpp.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/gapi/include"
  "modules/gapi"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "D:/unet/opencv/opencv/sources/modules/gapi/src"
  "3rdparty/ade/ade-0.1.1f/sources/ade/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )
set(CMAKE_DEPENDS_CHECK_RC
  "D:/unet/opencv/opencv/mingw_build/modules/gapi/vs_version.rc" "D:/unet/opencv/opencv/mingw_build/modules/gapi/CMakeFiles/opencv_gapi.dir/vs_version.rc.obj"
  )

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_RC
  "_USE_MATH_DEFINES"
  "_WIN32_WINNT=0x0601"
  "__OPENCV_BUILD=1"
  "__STDC_CONSTANT_MACROS"
  "__STDC_FORMAT_MACROS"
  "__STDC_LIMIT_MACROS"
  )

# The include file search paths:
set(CMAKE_RC_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/modules/gapi/include"
  "modules/gapi"
  "D:/unet/opencv/opencv/sources/modules/core/include"
  "D:/unet/opencv/opencv/sources/modules/imgproc/include"
  "D:/unet/opencv/opencv/sources/modules/gapi/src"
  "3rdparty/ade/ade-0.1.1f/sources/ade/include"
  "."
  "D:/visp-ws/eigen-3.3.7/build-vc14/install/include/eigen3"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "D:/unet/opencv/opencv/mingw_build/modules/imgproc/CMakeFiles/opencv_imgproc.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/CMakeFiles/ade.dir/DependInfo.cmake"
  "D:/unet/opencv/opencv/mingw_build/modules/core/CMakeFiles/opencv_core.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
