"\n"
"General configuration for OpenCV 4.2.0 =====================================\n"
"  Version control:               unknown\n"
"\n"
"  Platform:\n"
"    Timestamp:                   2021-08-17T07:55:06Z\n"
"    Host:                        Windows 10.0.19042 AMD64\n"
"    CMake:                       3.13.1\n"
"    CMake generator:             MinGW Makefiles\n"
"    CMake build tool:            E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe\n"
"    Configuration:               Release\n"
"\n"
"  CPU/HW features:\n"
"    Baseline:                    SSE SSE2 SSE3\n"
"      requested:                 SSE3\n"
"    Dispatched code generation:  SSE4_1 SSE4_2 FP16 AVX AVX2\n"
"      requested:                 SSE4_1 SSE4_2 AVX FP16 AVX2 AVX512_SKX\n"
"      SSE4_1 (14 files):         + SSSE3 SSE4_1\n"
"      SSE4_2 (1 files):          + SSSE3 SSE4_1 POPCNT SSE4_2\n"
"      FP16 (0 files):            + SSSE3 SSE4_1 POPCNT SSE4_2 FP16 AVX\n"
"      AVX (4 files):             + SSSE3 SSE4_1 POPCNT SSE4_2 AVX\n"
"      AVX2 (27 files):           + SSSE3 SSE4_1 POPCNT SSE4_2 FP16 FMA3 AVX AVX2\n"
"\n"
"  C/C++:\n"
"    Built as dynamic libs?:      YES\n"
"    C++ Compiler:                E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/g++.exe  (ver 8.1.0)\n"
"    C++ flags (Release):         -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wundef -Winit-self -Wpointer-arith -Wshadow -Wsign-promo -Wuninitialized -Winit-self -Wsuggest-override -Wno-delete-non-virtual-dtor -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -fvisibility-inlines-hidden -O3 -DNDEBUG  -DNDEBUG\n"
"    C++ flags (Debug):           -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wundef -Winit-self -Wpointer-arith -Wshadow -Wsign-promo -Wuninitialized -Winit-self -Wsuggest-override -Wno-delete-non-virtual-dtor -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -fvisibility-inlines-hidden -g  -O0 -DDEBUG -D_DEBUG\n"
"    C Compiler:                  E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/gcc.exe\n"
"    C flags (Release):           -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -O3 -DNDEBUG  -DNDEBUG\n"
"    C flags (Debug):             -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -g  -O0 -DDEBUG -D_DEBUG\n"
"    Linker flags (Release):      -Wl,--gc-sections  \n"
"    Linker flags (Debug):        -Wl,--gc-sections  \n"
"    ccache:                      NO\n"
"    Precompiled headers:         NO\n"
"    Extra dependencies:\n"
"    3rdparty dependencies:\n"
"\n"
"  OpenCV modules:\n"
"    To be built:                 calib3d core dnn features2d flann gapi highgui imgcodecs imgproc ml objdetect photo python3 stitching video videoio\n"
"    Disabled:                    ts world\n"
"    Disabled by dependency:      -\n"
"    Unavailable:                 java js python2\n"
"    Applications:                apps\n"
"    Documentation:               NO\n"
"    Non-free algorithms:         NO\n"
"\n"
"  Windows RT support:            NO\n"
"\n"
"  GUI: \n"
"    Win32 UI:                    YES\n"
"    VTK support:                 NO\n"
"\n"
"  Media I/O: \n"
"    ZLib:                        build (ver 1.2.11)\n"
"    JPEG:                        build-libjpeg-turbo (ver 2.0.2-62)\n"
"    WEBP:                        build (ver encoder: 0x020e)\n"
"    PNG:                         build (ver 1.6.37)\n"
"    TIFF:                        build (ver 42 - 4.0.10)\n"
"    JPEG 2000:                   build (ver 1.900.1)\n"
"    OpenEXR:                     build (ver 2.3.0)\n"
"    HDR:                         YES\n"
"    SUNRASTER:                   YES\n"
"    PXM:                         YES\n"
"    PFM:                         YES\n"
"\n"
"  Video I/O:\n"
"    DC1394:                      NO\n"
"    FFMPEG:                      NO\n"
"      avcodec:                   NO\n"
"      avformat:                  NO\n"
"      avutil:                    NO\n"
"      swscale:                   NO\n"
"      avresample:                NO\n"
"    GStreamer:                   NO\n"
"    DirectShow:                  YES\n"
"\n"
"  Parallel framework:            none\n"
"\n"
"  Trace:                         YES (built-in)\n"
"\n"
"  Other third-party libraries:\n"
"    Lapack:                      NO\n"
"    Eigen:                       YES (ver 3.3.7)\n"
"    Custom HAL:                  NO\n"
"    Protobuf:                    build (3.5.1)\n"
"\n"
"  OpenCL:                        YES (no extra features)\n"
"    Include path:                D:/unet/opencv/opencv/sources/3rdparty/include/opencl/1.2\n"
"    Link libraries:              Dynamic load\n"
"\n"
"  Python 3:\n"
"    Interpreter:                 D:/anaconda/python.exe (ver 3.8.8)\n"
"    Libraries:                   D:/anaconda/python38.dll (ver 3.8.8)\n"
"    numpy:                       D:/anaconda/lib/site-packages/numpy/core/include (ver 1.19.5)\n"
"    install path:                D:/anaconda/Lib/site-packages/cv2/python-3.8\n"
"\n"
"  Python (for build):            D:/anaconda/python.exe\n"
"\n"
"  Java:                          \n"
"    ant:                         NO\n"
"    JNI:                         NO\n"
"    Java wrappers:               NO\n"
"    Java tests:                  NO\n"
"\n"
"  Install to:                    D:/unet/opencv/opencv/mingw_build/install\n"
"-----------------------------------------------------------------\n"
"\n"
