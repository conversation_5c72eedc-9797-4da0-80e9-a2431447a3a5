# CMake generated Testfile for 
# Source directory: D:/unet/opencv/opencv/sources
# Build directory: D:/unet/opencv/opencv/mingw_build
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
subdirs("3rdparty/zlib")
subdirs("3rdparty/libjpeg-turbo")
subdirs("3rdparty/libtiff")
subdirs("3rdparty/libwebp")
subdirs("3rdparty/libjasper")
subdirs("3rdparty/libpng")
subdirs("3rdparty/openexr")
subdirs("3rdparty/protobuf")
subdirs("3rdparty/quirc")
subdirs("include")
subdirs("modules")
subdirs("doc")
subdirs("data")
subdirs("apps")
