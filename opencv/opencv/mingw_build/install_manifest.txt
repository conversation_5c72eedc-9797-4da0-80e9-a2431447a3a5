D:/unet/opencv/opencv/mingw_build/install/etc/licenses/opencl-headers-LICENSE.txt
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/cvconfig.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/opencv_modules.hpp
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/OpenCVModules.cmake
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/OpenCVModules-release.cmake
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/OpenCVConfig-version.cmake
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/OpenCVConfig.cmake
D:/unet/opencv/opencv/mingw_build/install/./OpenCVConfig-version.cmake
D:/unet/opencv/opencv/mingw_build/install/./OpenCVConfig.cmake
D:/unet/opencv/opencv/mingw_build/install/./LICENSE
D:/unet/opencv/opencv/mingw_build/install/./setup_vars_opencv4.cmd
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/zlib-README
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/libjpeg-turbo-README.md
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/libjpeg-turbo-LICENSE.md
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/libjpeg-turbo-README.ijg
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/libtiff-COPYRIGHT
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/jasper-LICENSE
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/jasper-README
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/jasper-copyright
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/libpng-LICENSE
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/libpng-README
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/openexr-LICENSE
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/openexr-AUTHORS.ilmbase
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/openexr-AUTHORS.openexr
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/protobuf-LICENSE
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/protobuf-README.md
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/quirc-LICENSE
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/opencv.hpp
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/ade-LICENSE
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/ffmpeg-license.txt
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/ffmpeg-readme.txt
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/libopencv_core420.dll.a
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_core420.dll
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/ocl_defs.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/opencl_info.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/opencl_svm.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_clamdblas.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_clamdfft.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_core.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_core_wrappers.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_gl.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/runtime/autogenerated/opencl_gl_wrappers.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/runtime/opencl_clamdblas.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/runtime/opencl_clamdfft.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/runtime/opencl_core.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/runtime/opencl_core_wrappers.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/runtime/opencl_gl.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/runtime/opencl_gl_wrappers.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/runtime/opencl_svm_20.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/runtime/opencl_svm_definitions.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opencl/runtime/opencl_svm_hsa_extension.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/block.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/border_interpolate.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/color.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/common.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/datamov_utils.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/dynamic_smem.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/emulation.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/filters.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/funcattrib.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/functional.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/limits.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/reduce.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/saturate_cast.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/scan.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/simd_functions.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/transform.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/type_traits.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/utility.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/vec_distance.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/vec_math.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/vec_traits.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/warp.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/warp_reduce.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/warp_shuffle.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/detail/color_detail.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/detail/reduce.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/detail/reduce_key_val.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/detail/transform_detail.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/detail/type_traits_detail.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda/detail/vec_distance_detail.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/affine.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/async.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/base.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/bindings_utils.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/bufferpool.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/check.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/core.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/core_c.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda.inl.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda_stream_accessor.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda_types.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cv_cpu_dispatch.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cv_cpu_helper.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cvdef.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cvstd.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cvstd.inl.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cvstd_wrapper.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/directx.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/eigen.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/fast_math.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/hal/hal.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/hal/interface.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/hal/intrin.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/hal/intrin_avx.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/hal/intrin_avx512.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/hal/intrin_cpp.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/hal/intrin_forward.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/hal/intrin_msa.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/hal/intrin_neon.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/hal/intrin_sse.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/hal/intrin_sse_em.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/hal/intrin_vsx.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/hal/intrin_wasm.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/hal/msa_macros.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/hal/simd_utils.impl.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/mat.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/mat.inl.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/matx.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/neon_utils.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/ocl.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/ocl_genbase.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/opengl.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/operations.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/optim.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/ovx.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/persistence.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/saturate.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/simd_intrinsics.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/softfloat.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/sse_utils.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/traits.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/types.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/types_c.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/utility.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/utils/allocator_stats.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/utils/allocator_stats.impl.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/utils/filesystem.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/utils/instrumentation.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/utils/logger.defines.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/utils/logger.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/utils/logtag.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/utils/tls.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/utils/trace.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/va_intel.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/version.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/vsx_utils.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/detail/async_promise.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/detail/exception_ptr.hpp
D:/unet/opencv/opencv/mingw_build/install/etc/licenses/SoftFloat-COPYING.txt
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/libopencv_flann420.dll.a
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_flann420.dll
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/all_indices.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/allocator.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/any.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/autotuned_index.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/composite_index.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/config.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/defines.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/dist.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/dummy.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/dynamic_bitset.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/flann.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/flann_base.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/general.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/ground_truth.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/hdf5.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/heap.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/hierarchical_clustering_index.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/index_testing.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/kdtree_index.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/kdtree_single_index.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/kmeans_index.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/linear_index.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/logger.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/lsh_index.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/lsh_table.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/matrix.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/miniflann.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/nn_index.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/object_factory.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/params.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/random.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/result_set.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/sampling.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/saving.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/simplex_downhill.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/flann/timer.h
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/libopencv_imgproc420.dll.a
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_imgproc420.dll
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/imgproc.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/imgproc/hal/hal.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/imgproc/hal/interface.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/imgproc/imgproc.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/imgproc/imgproc_c.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/imgproc/types_c.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/imgproc/detail/gcgraph.hpp
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/libopencv_ml420.dll.a
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_ml420.dll
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/ml.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/ml/ml.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/ml/ml.inl.hpp
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/libopencv_photo420.dll.a
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_photo420.dll
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/photo.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/photo/cuda.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/photo/legacy/constants_c.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/photo/photo.hpp
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/libopencv_dnn420.dll.a
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_dnn420.dll
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/dnn.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/dnn/all_layers.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/dnn/dict.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/dnn/dnn.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/dnn/dnn.inl.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/dnn/layer.details.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/dnn/layer.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/dnn/shape_utils.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/dnn/utils/inference_engine.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/dnn/version.hpp
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/libopencv_features2d420.dll.a
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_features2d420.dll
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/features2d.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/features2d/features2d.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/features2d/hal/interface.h
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/libopencv_gapi420.dll.a
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_gapi420.dll
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/core.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/cpu/core.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/cpu/gcpukernel.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/cpu/imgproc.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/fluid/core.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/fluid/gfluidbuffer.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/fluid/gfluidkernel.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/fluid/imgproc.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/garg.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/garray.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gasync_context.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gcall.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gcommon.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gcompiled.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gcompiled_async.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gcompoundkernel.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gcomputation.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gcomputation_async.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gkernel.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gmat.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gmetaarg.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gproto.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gpu/core.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gpu/ggpukernel.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gpu/imgproc.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gscalar.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gstreaming.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gtransform.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gtype_traits.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gtyped.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/imgproc.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/infer.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/infer/ie.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/ocl/core.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/ocl/goclkernel.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/ocl/imgproc.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/opencv_includes.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/operators.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/own/assert.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/own/convert.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/own/cvdefs.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/own/exports.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/own/mat.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/own/saturate.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/own/scalar.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/own/types.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/plaidml/core.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/plaidml/gplaidmlkernel.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/plaidml/plaidml.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/render.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/render/render.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/streaming/cap.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/streaming/source.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/util/any.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/util/compiler_hints.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/util/optional.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/util/throw.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/util/util.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/util/variant.hpp
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/libopencv_imgcodecs420.dll.a
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_imgcodecs420.dll
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/imgcodecs.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/imgcodecs/imgcodecs.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/imgcodecs/imgcodecs_c.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/imgcodecs/ios.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/imgcodecs/legacy/constants_c.h
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/libopencv_videoio420.dll.a
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_videoio420.dll
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/videoio.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/videoio/cap_ios.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/videoio/legacy/constants_c.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/videoio/registry.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/videoio/videoio.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/videoio/videoio_c.h
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/libopencv_calib3d420.dll.a
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_calib3d420.dll
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/calib3d.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/calib3d/calib3d.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/calib3d/calib3d_c.h
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/libopencv_highgui420.dll.a
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_highgui420.dll
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/highgui.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/highgui/highgui.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/highgui/highgui_c.h
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/libopencv_objdetect420.dll.a
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_objdetect420.dll
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/objdetect.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/objdetect/detection_based_tracker.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/objdetect/objdetect.hpp
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/libopencv_stitching420.dll.a
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_stitching420.dll
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/stitching.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/stitching/warpers.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/stitching/detail/autocalib.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/stitching/detail/blenders.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/stitching/detail/camera.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/stitching/detail/exposure_compensate.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/stitching/detail/matchers.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/stitching/detail/motion_estimators.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/stitching/detail/seam_finders.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/stitching/detail/timelapsers.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/stitching/detail/util.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/stitching/detail/util_inl.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/stitching/detail/warpers.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/stitching/detail/warpers_inl.hpp
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib/libopencv_video420.dll.a
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_video420.dll
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/video.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/video/background_segm.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/video/legacy/constants_c.h
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/video/tracking.hpp
D:/unet/opencv/opencv/mingw_build/install/include/opencv2/video/video.hpp
D:/anaconda/Lib/site-packages/cv2/__init__.py
D:/anaconda/Lib/site-packages/cv2/load_config_py2.py
D:/anaconda/Lib/site-packages/cv2/load_config_py3.py
D:/anaconda/Lib/site-packages/cv2/config.py
D:/anaconda/Lib/site-packages/cv2/python-3.8/cv2.cp38-win_amd64.pyd
D:/anaconda/Lib/site-packages/cv2/config-3.8.py
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_eye.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_eye_tree_eyeglasses.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_frontalcatface.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_frontalcatface_extended.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_frontalface_alt.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_frontalface_alt2.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_frontalface_alt_tree.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_frontalface_default.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_fullbody.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_lefteye_2splits.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_licence_plate_rus_16stages.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_lowerbody.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_profileface.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_righteye_2splits.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_russian_plate_number.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_smile.xml
D:/unet/opencv/opencv/mingw_build/install/etc/haarcascades/haarcascade_upperbody.xml
D:/unet/opencv/opencv/mingw_build/install/etc/lbpcascades/lbpcascade_frontalcatface.xml
D:/unet/opencv/opencv/mingw_build/install/etc/lbpcascades/lbpcascade_frontalface.xml
D:/unet/opencv/opencv/mingw_build/install/etc/lbpcascades/lbpcascade_frontalface_improved.xml
D:/unet/opencv/opencv/mingw_build/install/etc/lbpcascades/lbpcascade_profileface.xml
D:/unet/opencv/opencv/mingw_build/install/etc/lbpcascades/lbpcascade_silverware.xml
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/opencv_annotation.exe
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/opencv_visualisation.exe
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/opencv_interactive-calibration.exe
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/opencv_version.exe
D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/opencv_version_win32.exe