# This is the CMakeCache file.
# For build in directory: d:/unet/opencv/opencv/mingw_build
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
ANT_EXECUTABLE:FILEPATH=ANT_EXECUTABLE-NOTFOUND

//Path to a library.
BLAS_Accelerate_LIBRARY:FILEPATH=BLAS_Accelerate_LIBRARY-NOTFOUND

//Path to a library.
BLAS_acml_LIBRARY:FILEPATH=BLAS_acml_LIBRARY-NOTFOUND

//Path to a library.
BLAS_acml_mp_LIBRARY:FILEPATH=BLAS_acml_mp_LIBRARY-NOTFOUND

//Path to a library.
BLAS_blas_LIBRARY:FILEPATH=BLAS_blas_LIBRARY-NOTFOUND

//Path to a library.
BLAS_blis_LIBRARY:FILEPATH=BLAS_blis_LIBRARY-NOTFOUND

//Path to a library.
BLAS_complib.sgimath_LIBRARY:FILEPATH=BLAS_complib.sgimath_LIBRARY-NOTFOUND

//Path to a library.
BLAS_cxml_LIBRARY:FILEPATH=BLAS_cxml_LIBRARY-NOTFOUND

//Path to a library.
BLAS_dxml_LIBRARY:FILEPATH=BLAS_dxml_LIBRARY-NOTFOUND

//Path to a library.
BLAS_essl_LIBRARY:FILEPATH=BLAS_essl_LIBRARY-NOTFOUND

//Path to a library.
BLAS_f77blas_LIBRARY:FILEPATH=BLAS_f77blas_LIBRARY-NOTFOUND

//Path to a library.
BLAS_goto2_LIBRARY:FILEPATH=BLAS_goto2_LIBRARY-NOTFOUND

//Path to a library.
BLAS_mkl_intel_c_LIBRARY:FILEPATH=BLAS_mkl_intel_c_LIBRARY-NOTFOUND

//Path to a library.
BLAS_mkl_intel_lp64_LIBRARY:FILEPATH=BLAS_mkl_intel_lp64_LIBRARY-NOTFOUND

//Path to a library.
BLAS_openblas_LIBRARY:FILEPATH=BLAS_openblas_LIBRARY-NOTFOUND

//Path to a library.
BLAS_scsl_LIBRARY:FILEPATH=BLAS_scsl_LIBRARY-NOTFOUND

//Path to a library.
BLAS_sgemm_LIBRARY:FILEPATH=BLAS_sgemm_LIBRARY-NOTFOUND

//Path to a library.
BLAS_sunperf_LIBRARY:FILEPATH=BLAS_sunperf_LIBRARY-NOTFOUND

//Path to a library.
BLAS_vecLib_LIBRARY:FILEPATH=BLAS_vecLib_LIBRARY-NOTFOUND

//Build CUDA modules stubs when no CUDA SDK
BUILD_CUDA_STUBS:BOOL=OFF

//Create build rules for OpenCV Documentation
BUILD_DOCS:BOOL=OFF

//Build all examples
BUILD_EXAMPLES:BOOL=OFF

//Build IPP IW from source
BUILD_IPP_IW:BOOL=OFF

//Build Intel ITT from source
BUILD_ITT:BOOL=OFF

//Build libjasper from source
BUILD_JASPER:BOOL=ON

//Enable Java support
BUILD_JAVA:BOOL=ON

//Build libjpeg from source
BUILD_JPEG:BOOL=ON

//Build only listed modules (comma-separated, e.g. 'videoio,dnn,ts')
BUILD_LIST:STRING=

//Build openexr from source
BUILD_OPENEXR:BOOL=ON

//Enables 'make package_source' command
BUILD_PACKAGE:BOOL=ON

//Build performance tests
BUILD_PERF_TESTS:BOOL=ON

//Build libpng from source
BUILD_PNG:BOOL=ON

//Force to build libprotobuf from sources
BUILD_PROTOBUF:BOOL=ON

//Build shared libraries (.dll/.so) instead of static ones (.lib/.a)
BUILD_SHARED_LIBS:BOOL=ON

//Download and build TBB from source
BUILD_TBB:BOOL=OFF

//Build accuracy & regression tests
BUILD_TESTS:BOOL=ON

//Build libtiff from source
BUILD_TIFF:BOOL=ON

//Use symlinks instead of files copying during build (and !!INSTALL!!)
BUILD_USE_SYMLINKS:BOOL=OFF

//Build WebP from source
BUILD_WEBP:BOOL=ON

//Include debug info into release binaries ('OFF' means default
// settings)
BUILD_WITH_DEBUG_INFO:BOOL=OFF

//Enables dynamic linking of IPP (only for standalone IPP)
BUILD_WITH_DYNAMIC_IPP:BOOL=OFF

//Build zlib from source
BUILD_ZLIB:BOOL=ON

//Build utility applications (used for example to train classifiers)
BUILD_opencv_apps:BOOL=ON

//Include opencv_calib3d module into the OpenCV build
BUILD_opencv_calib3d:BOOL=ON

//Include opencv_core module into the OpenCV build
BUILD_opencv_core:BOOL=ON

//Include opencv_dnn module into the OpenCV build
BUILD_opencv_dnn:BOOL=ON

//Include opencv_features2d module into the OpenCV build
BUILD_opencv_features2d:BOOL=ON

//Include opencv_flann module into the OpenCV build
BUILD_opencv_flann:BOOL=ON

//Include opencv_gapi module into the OpenCV build
BUILD_opencv_gapi:BOOL=ON

//Include opencv_highgui module into the OpenCV build
BUILD_opencv_highgui:BOOL=ON

//Include opencv_imgcodecs module into the OpenCV build
BUILD_opencv_imgcodecs:BOOL=ON

//Include opencv_imgproc module into the OpenCV build
BUILD_opencv_imgproc:BOOL=ON

//Include opencv_java_bindings_generator module into the OpenCV
// build
BUILD_opencv_java_bindings_generator:BOOL=ON

//Build JavaScript bindings by Emscripten
BUILD_opencv_js:BOOL=OFF

//Include opencv_ml module into the OpenCV build
BUILD_opencv_ml:BOOL=ON

//Include opencv_objdetect module into the OpenCV build
BUILD_opencv_objdetect:BOOL=ON

//Include opencv_photo module into the OpenCV build
BUILD_opencv_photo:BOOL=ON

//Include opencv_python3 module into the OpenCV build
BUILD_opencv_python3:BOOL=ON

//Include opencv_python_bindings_generator module into the OpenCV
// build
BUILD_opencv_python_bindings_generator:BOOL=ON

//Include opencv_python_tests module into the OpenCV build
BUILD_opencv_python_tests:BOOL=ON

//Include opencv_stitching module into the OpenCV build
BUILD_opencv_stitching:BOOL=ON

//Include opencv_ts module into the OpenCV build
BUILD_opencv_ts:BOOL=OFF

//Include opencv_video module into the OpenCV build
BUILD_opencv_video:BOOL=ON

//Include opencv_videoio module into the OpenCV build
BUILD_opencv_videoio:BOOL=ON

//Include opencv_world module into the OpenCV build
BUILD_opencv_world:BOOL=OFF

//clAmdFft include directory
CLAMDBLAS_INCLUDE_DIR:PATH=CLAMDBLAS_INCLUDE_DIR-NOTFOUND

//AMD FFT root directory
CLAMDBLAS_ROOT_DIR:PATH=CLAMDBLAS_ROOT_DIR-NOTFOUND

//clAmdFft include directory
CLAMDFFT_INCLUDE_DIR:PATH=CLAMDFFT_INCLUDE_DIR-NOTFOUND

//AMD FFT root directory
CLAMDFFT_ROOT_DIR:PATH=CLAMDFFT_ROOT_DIR-NOTFOUND

//Path to a program.
CMAKE_AR:FILEPATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/ar.exe

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//Configs
CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release

//CXX compiler.
CMAKE_CXX_COMPILER:FILEPATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/g++.exe

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/gcc-ar.exe

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/gcc-ranlib.exe

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32

//C compiler.
CMAKE_C_COMPILER:FILEPATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/gcc.exe

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/gcc-ar.exe

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/gcc-ranlib.exe

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Convert GNU import libraries to MS format (requires Visual Studio)
CMAKE_GNUtoMS:BOOL=OFF

//Installation Directory
CMAKE_INSTALL_PREFIX:PATH=D:/unet/opencv/opencv/mingw_build/install

//Path to a program.
CMAKE_LINKER:FILEPATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/ld.exe

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/nm.exe

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/objcopy.exe

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/objdump.exe

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=OpenCV

//Path to a program.
CMAKE_RANLIB:FILEPATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/ranlib.exe

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/windres.exe

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_SH:FILEPATH=CMAKE_SH-NOTFOUND

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=OFF

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=OFF

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/strip.exe

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=OFF

//Enable to build 7-Zip packages
CPACK_BINARY_7Z:BOOL=OFF

//Enable to build IFW packages
CPACK_BINARY_IFW:BOOL=OFF

//Enable to build NSIS packages
CPACK_BINARY_NSIS:BOOL=ON

//Enable to build NuGet packages
CPACK_BINARY_NUGET:BOOL=OFF

//Enable to build WiX packages
CPACK_BINARY_WIX:BOOL=OFF

//Enable to build ZIP packages
CPACK_BINARY_ZIP:BOOL=OFF

//Enable to build 7-Zip source packages
CPACK_SOURCE_7Z:BOOL=ON

//Enable to build ZIP source packages
CPACK_SOURCE_ZIP:BOOL=ON

//Specify list of enabled baseline CPU optimizations
CPU_BASELINE:STRING=SSE3

//Specify list of dispatched CPU optimizations
CPU_DISPATCH:STRING=SSE4_1;SSE4_2;AVX;FP16;AVX2;AVX512_SKX

//Disable explicit optimized code (dispatched code/intrinsics/loop
// unrolling/etc)
CV_DISABLE_OPTIMIZATION:BOOL=OFF

//Use intrinsic-based optimized code
CV_ENABLE_INTRINSICS:BOOL=ON

//Enable OpenCV code trace
CV_TRACE:BOOL=ON

//Path to a file.
DC1394_INCLUDE:PATH=DC1394_INCLUDE-NOTFOUND

//Path to a library.
DC1394_LIBRARY:FILEPATH=DC1394_LIBRARY-NOTFOUND

//Enable hardening of the resulting binaries (against security
// attacks, detects memory corruption, etc)
ENABLE_BUILD_HARDENING:BOOL=OFF

//Use ccache
ENABLE_CCACHE:BOOL=OFF

//Fail build if actual configuration doesn't match requested (WITH_XXX
// != HAVE_XXX)
ENABLE_CONFIG_VERIFICATION:BOOL=OFF

//Enable coverage collection with  GCov
ENABLE_COVERAGE:BOOL=OFF

//Enable compiler options for fast math optimizations on FP computations
// (not recommended)
ENABLE_FAST_MATH:BOOL=OFF

//Add target with Python flake8 checker
ENABLE_FLAKE8:BOOL=OFF

//Enable GNU STL Debug mode (defines _GLIBCXX_DEBUG)
ENABLE_GNU_STL_DEBUG:BOOL=OFF

//Collect implementation data on function call
ENABLE_IMPL_COLLECTION:BOOL=OFF

//Instrument functions to collect calls trace and performance
ENABLE_INSTRUMENTATION:BOOL=OFF

//Enable Link Time Optimization
ENABLE_LTO:BOOL=OFF

//Show all warnings even if they are too noisy
ENABLE_NOISY_WARNINGS:BOOL=OFF

//Enable -fomit-frame-pointer for GCC
ENABLE_OMIT_FRAME_POINTER:BOOL=ON

//Generate position independent code (necessary for shared libraries)
ENABLE_PIC:BOOL=ON

//Use precompiled headers
ENABLE_PRECOMPILED_HEADERS:BOOL=OFF

//Enable profiling in the GCC compiler (Add flags: -g -pg)
ENABLE_PROFILING:BOOL=OFF

//Add target with Pylint checks
ENABLE_PYLINT:BOOL=OFF

//Solution folder in Visual Studio or in other IDEs
ENABLE_SOLUTION_FOLDERS:BOOL=OFF

//Output directory for applications
EXECUTABLE_OUTPUT_PATH:PATH=D:/unet/opencv/opencv/mingw_build/bin

//The directory containing a CMake configuration file for Eigen3.
Eigen3_DIR:PATH=D:/visp-ws/eigen-3.3.7/build-vc14/install/share/eigen3/cmake

//Git command line client
GIT_EXECUTABLE:FILEPATH=GIT_EXECUTABLE-NOTFOUND

//Path to a library.
GSTREAMER_app_LIBRARY:FILEPATH=GSTREAMER_app_LIBRARY-NOTFOUND

//Path to a library.
GSTREAMER_base_LIBRARY:FILEPATH=GSTREAMER_base_LIBRARY-NOTFOUND

//Path to a file.
GSTREAMER_glib_INCLUDE_DIR:PATH=GSTREAMER_glib_INCLUDE_DIR-NOTFOUND

//Path to a library.
GSTREAMER_glib_LIBRARY:FILEPATH=GSTREAMER_glib_LIBRARY-NOTFOUND

//Path to a file.
GSTREAMER_glibconfig_INCLUDE_DIR:PATH=GSTREAMER_glibconfig_INCLUDE_DIR-NOTFOUND

//Path to a library.
GSTREAMER_gobject_LIBRARY:FILEPATH=GSTREAMER_gobject_LIBRARY-NOTFOUND

//Path to a file.
GSTREAMER_gst_INCLUDE_DIR:PATH=GSTREAMER_gst_INCLUDE_DIR-NOTFOUND

//Path to a library.
GSTREAMER_gstreamer_LIBRARY:FILEPATH=GSTREAMER_gstreamer_LIBRARY-NOTFOUND

//Path to a library.
GSTREAMER_pbutils_LIBRARY:FILEPATH=GSTREAMER_pbutils_LIBRARY-NOTFOUND

//Path to a library.
GSTREAMER_riff_LIBRARY:FILEPATH=GSTREAMER_riff_LIBRARY-NOTFOUND

//IEEE floating point is available
HAVE_IEEEFP:STRING=1

//Native CPU bit order
HOST_BIG_ENDIAN:STRING=OFF

//Native CPU bit order
HOST_FILLORDER:STRING=FILLORDER_MSB2LSB

//Change install rules to build the distribution package
INSTALL_CREATE_DISTRIB:BOOL=OFF

//Install C examples
INSTALL_C_EXAMPLES:BOOL=OFF

//Install Python examples
INSTALL_PYTHON_EXAMPLES:BOOL=OFF

//Install accuracy and performance test binaries and test data
INSTALL_TESTS:BOOL=OFF

//Dependencies for the target
IlmImf_LIB_DEPENDS:STATIC=general;zlib;

//Path to a file.
JAVA_AWT_INCLUDE_PATH:PATH=JAVA_AWT_INCLUDE_PATH-NOTFOUND

//Path to a library.
JAVA_AWT_LIBRARY:FILEPATH=JAVA_AWT_LIBRARY-NOTFOUND

//Path to a file.
JAVA_INCLUDE_PATH:PATH=JAVA_INCLUDE_PATH-NOTFOUND

//Path to a file.
JAVA_INCLUDE_PATH2:PATH=JAVA_INCLUDE_PATH2-NOTFOUND

//Path to a library.
JAVA_JVM_LIBRARY:FILEPATH=JAVA_JVM_LIBRARY-NOTFOUND

//Alternative name of cblas.h
LAPACK_CBLAS_H:STRING=

//Lapack implementation id
LAPACK_IMPL:STRING=Unknown

//Path to BLAS include dir
LAPACK_INCLUDE_DIR:PATH=

//Alternative name of lapacke.h
LAPACK_LAPACKE_H:STRING=

//Names of BLAS & LAPACK binaries (.so, .dll, .a, .lib)
LAPACK_LIBRARIES:STRING=

//Path to MKL include directory
MKL_INCLUDE_DIRS:PATH=MKL_ROOT_DIR-NOTFOUND/include

//Path to a file.
MKL_ROOT_DIR:PATH=MKL_ROOT_DIR-NOTFOUND

//Use MKL with OpenMP multithreading
MKL_WITH_OPENMP:BOOL=OFF

//Use MKL with TBB multithreading
MKL_WITH_TBB:BOOL=OFF

//Path to a library.
M_LIBRARY:FILEPATH=M_LIBRARY-NOTFOUND

//OpenCL library is found
OPENCL_FOUND:BOOL=ON

//OpenCL include directory
OPENCL_INCLUDE_DIR:PATH=D:/unet/opencv/opencv/sources/3rdparty/include/opencl/1.2

//OpenCL library
OPENCL_LIBRARY:STRING=

//Value of _WIN32_WINNT macro
OPENCV_CMAKE_MACRO_WIN32_WINNT:STRING=0x0601

//Where to create the platform-dependant cvconfig.h
OPENCV_CONFIG_FILE_INCLUDE_DIR:PATH=D:/unet/opencv/opencv/mingw_build

//Build with CUDA support
OPENCV_DNN_CUDA:BOOL=OFF

//Build with OpenCL support
OPENCV_DNN_OPENCL:BOOL=ON

//Download parameters to be passed to file(DOWNLAOD ...)
OPENCV_DOWNLOAD_PARAMS:STRING=INACTIVITY_TIMEOUT;60;TIMEOUT;600

//Cache directory for downloaded files
OPENCV_DOWNLOAD_PATH:PATH=D:/unet/opencv/opencv/sources/.cache

//List of download tries
OPENCV_DOWNLOAD_TRIES_LIST:STRING=1

//Dump called OpenCV hooks
OPENCV_DUMP_HOOKS_FLOW:BOOL=OFF

//Enable posix_memalign or memalign usage
OPENCV_ENABLE_MEMALIGN:BOOL=ON

//Better support for memory/address sanitizers
OPENCV_ENABLE_MEMORY_SANITIZER:BOOL=OFF

//Enable non-free algorithms
OPENCV_ENABLE_NONFREE:BOOL=OFF

//Where to look for additional OpenCV modules (can be ;-separated
// list of paths)
OPENCV_EXTRA_MODULES_PATH:PATH=

//Force using 3rdparty code from source
OPENCV_FORCE_3RDPARTY_BUILD:BOOL=OFF

OPENCV_FORCE_PYTHON_LIBS:BOOL=OFF

//Generate .pc file for pkg-config build tool (deprecated)
OPENCV_GENERATE_PKGCONFIG:BOOL=OFF

//Generate setup_vars* scripts
OPENCV_GENERATE_SETUPVARS:BOOL=ON

//Java source version (javac Ant target)
OPENCV_JAVA_SOURCE_VERSION:STRING=

//Java target version (javac Ant target)
OPENCV_JAVA_TARGET_VERSION:STRING=

//URI to a MathJax installation
OPENCV_MATHJAX_RELPATH:STRING=https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0

//Python3 version
OPENCV_PYTHON3_VERSION:BOOL=OFF

//Timestamp of OpenCV build configuration
OPENCV_TIMESTAMP:STRING=2021-08-17T07:55:06Z

//Treat warnings as errors
OPENCV_WARNINGS_ARE_ERRORS:BOOL=OFF

//Path to a file.
OpenBLAS_INCLUDE_DIR:PATH=OpenBLAS_INCLUDE_DIR-NOTFOUND

//Path to a library.
OpenBLAS_LIB:FILEPATH=OpenBLAS_LIB-NOTFOUND

//Value Computed by CMake
OpenCV_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build

//The directory containing a CMake configuration file for OpenCV_HAL.
OpenCV_HAL_DIR:PATH=OpenCV_HAL_DIR-NOTFOUND

//Value Computed by CMake
OpenCV_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources

//Force rebuilding .proto files (protoc should be available)
PROTOBUF_UPDATE_FILES:BOOL=OFF

//Path to Python interpreter
PYTHON2_EXECUTABLE:FILEPATH=

//Python include dir
PYTHON2_INCLUDE_DIR:PATH=

//Python include dir 2
PYTHON2_INCLUDE_DIR2:PATH=

//Path to Python library
PYTHON2_LIBRARY:FILEPATH=

//Path to Python debug
PYTHON2_LIBRARY_DEBUG:FILEPATH=

//Path to numpy headers
PYTHON2_NUMPY_INCLUDE_DIRS:PATH=

//Where to install the python packages.
PYTHON2_PACKAGES_PATH:PATH=

//Path to Python interpreter
PYTHON3_EXECUTABLE:FILEPATH=D:/anaconda/python.exe

//Python include dir
PYTHON3_INCLUDE_DIR:PATH=D:/anaconda/include

//Python include dir 2
PYTHON3_INCLUDE_DIR2:PATH=

//Path to Python library
PYTHON3_LIBRARY:FILEPATH=D:/anaconda/python38.dll

//Path to Python debug
PYTHON3_LIBRARY_DEBUG:FILEPATH=PYTHON_DEBUG_LIBRARY-NOTFOUND

//Path to numpy headers
PYTHON3_NUMPY_INCLUDE_DIRS:PATH=D:/anaconda/lib/site-packages/numpy/core/include

//Where to install the python packages.
PYTHON3_PACKAGES_PATH:PATH=D:/anaconda/Lib/site-packages

//Use win32 IO system (Microsoft Windows only)
USE_WIN32_FILEIO:BOOL=ON

//Allow building videoio plugin support
VIDEOIO_ENABLE_PLUGINS:BOOL=ON

//Make sure OpenCV version is the same in plugin and host code
VIDEOIO_ENABLE_STRICT_PLUGIN_CHECK:BOOL=ON

//List of videoio backends to be compiled as plugins (ffmpeg, gstreamer)
VIDEOIO_PLUGIN_LIST:STRING=

//The directory containing a CMake configuration file for VTK.
VTK_DIR:PATH=VTK_DIR-NOTFOUND

//Include IEEE1394 support
WITH_1394:BOOL=ON

//Enable ADE framework (required for Graph API module)
WITH_ADE:BOOL=ON

//Include arithmetic decoding support when emulating the libjpeg
// v6b API/ABI
WITH_ARITH_DEC:BOOL=ON

//Include arithmetic encoding support when emulating the libjpeg
// v6b API/ABI
WITH_ARITH_ENC:BOOL=ON

//Include Clp support (EPL)
WITH_CLP:BOOL=OFF

//Include NVidia Cuda Runtime support
WITH_CUDA:BOOL=OFF

//Include DirectX support
WITH_DIRECTX:BOOL=ON

//Build VideoIO with DirectShow support
WITH_DSHOW:BOOL=ON

//Include Eigen2/Eigen3 support
WITH_EIGEN:BOOL=ON

//Include FFMPEG support
WITH_FFMPEG:BOOL=ON

//Enable FreeType framework
WITH_FREETYPE:BOOL=OFF

//Include GDAL Support
WITH_GDAL:BOOL=OFF

//Include DICOM support
WITH_GDCM:BOOL=OFF

//Include Gstreamer support
WITH_GSTREAMER:BOOL=ON

//Include Halide support
WITH_HALIDE:BOOL=OFF

//Include Ste||ar Group HPX support
WITH_HPX:BOOL=OFF

//Include HDR support
WITH_IMGCODEC_HDR:BOOL=ON

//Include PFM formats support
WITH_IMGCODEC_PFM:BOOL=ON

//Include PNM (PBM,PGM,PPM) and PAM formats support
WITH_IMGCODEC_PXM:BOOL=ON

//Include SUNRASTER support
WITH_IMGCODEC_SUNRASTER:BOOL=ON

//Include Intel Inference Engine support
WITH_INF_ENGINE:BOOL=OFF

//Include Intel IPP support
WITH_IPP:BOOL=OFF

//Include Intel ITT support
WITH_ITT:BOOL=ON

//Include JPEG2K support
WITH_JASPER:BOOL=ON

//Include JPEG support
WITH_JPEG:BOOL=ON

//Include Lapack library support
WITH_LAPACK:BOOL=ON

//Include Intel librealsense support
WITH_LIBREALSENSE:BOOL=OFF

//Build VideoIO with Media Foundation support
WITH_MSMF:BOOL=OFF

//Enable hardware acceleration in Media Foundation backend
WITH_MSMF_DXVA:BOOL=OFF

//Include nGraph support
WITH_NGRAPH:BOOL=OFF

//Include OpenCL Runtime support
WITH_OPENCL:BOOL=ON

//Include AMD OpenCL BLAS library support
WITH_OPENCLAMDBLAS:BOOL=ON

//Include AMD OpenCL FFT library support
WITH_OPENCLAMDFFT:BOOL=ON

//Include NVIDIA OpenCL D3D11 support
WITH_OPENCL_D3D11_NV:BOOL=ON

//Include OpenCL Shared Virtual Memory support
WITH_OPENCL_SVM:BOOL=OFF

//Include ILM support via OpenEXR
WITH_OPENEXR:BOOL=ON

//Include OpenGL support
WITH_OPENGL:BOOL=OFF

//Include OpenMP support
WITH_OPENMP:BOOL=OFF

//Include OpenNI support
WITH_OPENNI:BOOL=OFF

//Include OpenNI2 support
WITH_OPENNI2:BOOL=OFF

//Include OpenVX support
WITH_OPENVX:BOOL=OFF

//Include PlaidML2 support
WITH_PLAIDML:BOOL=OFF

//Include PNG support
WITH_PNG:BOOL=ON

//Enable libprotobuf
WITH_PROTOBUF:BOOL=ON

//Use pthreads-based parallel_for
WITH_PTHREADS_PF:BOOL=ON

//Include Prosilica GigE support
WITH_PVAPI:BOOL=OFF

//Build with Qt Backend support
WITH_QT:BOOL=OFF

//Include library QR-code decoding
WITH_QUIRC:BOOL=ON

//Include Intel TBB support
WITH_TBB:BOOL=OFF

//Include TIFF support
WITH_TIFF:BOOL=ON

//Include VTK library support (and build opencv_viz module eiher)
WITH_VTK:BOOL=ON

//Include Vulkan support
WITH_VULKAN:BOOL=OFF

//Include WebP support
WITH_WEBP:BOOL=ON

//Build with Win32 UI Backend support
WITH_WIN32UI:BOOL=ON

//Include XIMEA cameras support
WITH_XIMEA:BOOL=OFF

//support for CCITT Group 3 & 4 algorithms
ccitt:BOOL=ON

//Value Computed by CMake
libjasper_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper

//Value Computed by CMake
libjasper_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/3rdparty/libjasper

//Value Computed by CMake
libjpeg-turbo_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo

//Value Computed by CMake
libjpeg-turbo_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo

//Value Computed by CMake
libpng_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/3rdparty/libpng

//Dependencies for the target
libpng_LIB_DEPENDS:STATIC=general;zlib;

//Value Computed by CMake
libpng_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/3rdparty/libpng

//Value Computed by CMake
libprotobuf_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf

//Value Computed by CMake
libprotobuf_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/3rdparty/protobuf

//Value Computed by CMake
libtiff_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff

//Dependencies for the target
libtiff_LIB_DEPENDS:STATIC=general;zlib;

//Value Computed by CMake
libtiff_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/3rdparty/libtiff

//Value Computed by CMake
libwebp_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp

//Value Computed by CMake
libwebp_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/3rdparty/libwebp

//support for LogLuv high dynamic range algorithm
logluv:BOOL=ON

//support for LZW algorithm
lzw:BOOL=ON

//support for Microsoft Document Imaging
mdi:BOOL=ON

//support for NeXT 2-bit RLE algorithm
next:BOOL=ON

//support for Old JPEG compression (read-only)
old-jpeg:BOOL=OFF

//Value Computed by CMake
opencv_annotation_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/apps/annotation

//Value Computed by CMake
opencv_annotation_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/apps/annotation

//Value Computed by CMake
opencv_calib3d_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/calib3d

//Dependencies for the target
opencv_calib3d_LIB_DEPENDS:STATIC=general;opencv_core;general;opencv_flann;general;opencv_imgproc;general;opencv_features2d;

//Value Computed by CMake
opencv_calib3d_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/calib3d

//Value Computed by CMake
opencv_core_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/core

//Dependencies for the target
opencv_core_LIB_DEPENDS:STATIC=general;zlib;

//Value Computed by CMake
opencv_core_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/core

//Value Computed by CMake
opencv_dnn_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/dnn

//Dependencies for the target
opencv_dnn_LIB_DEPENDS:STATIC=general;opencv_core;general;opencv_imgproc;general;libprotobuf;

//Add performance tests of Caffe framework
opencv_dnn_PERF_CAFFE:BOOL=OFF

//Add performance tests of clCaffe framework
opencv_dnn_PERF_CLCAFFE:BOOL=OFF

//Value Computed by CMake
opencv_dnn_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/dnn

//Value Computed by CMake
opencv_features2d_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/features2d

//Dependencies for the target
opencv_features2d_LIB_DEPENDS:STATIC=general;opencv_core;general;opencv_flann;general;opencv_imgproc;

//Value Computed by CMake
opencv_features2d_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/features2d

//Value Computed by CMake
opencv_flann_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/flann

//Dependencies for the target
opencv_flann_LIB_DEPENDS:STATIC=general;opencv_core;

//Value Computed by CMake
opencv_flann_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/flann

//Value Computed by CMake
opencv_gapi_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/gapi

//Dependencies for the target
opencv_gapi_LIB_DEPENDS:STATIC=general;opencv_core;general;opencv_imgproc;general;ade;

//Value Computed by CMake
opencv_gapi_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/gapi

//Value Computed by CMake
opencv_highgui_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/highgui

//Dependencies for the target
opencv_highgui_LIB_DEPENDS:STATIC=general;opencv_core;general;opencv_imgproc;general;opencv_imgcodecs;general;opencv_videoio;general;comctl32;general;gdi32;general;ole32;general;setupapi;general;ws2_32;

//Value Computed by CMake
opencv_highgui_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/highgui

//Value Computed by CMake
opencv_imgcodecs_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/imgcodecs

//Dependencies for the target
opencv_imgcodecs_LIB_DEPENDS:STATIC=general;opencv_core;general;opencv_imgproc;general;zlib;general;libjpeg-turbo;general;libwebp;general;libpng;general;libtiff;general;libjasper;general;IlmImf;

//Value Computed by CMake
opencv_imgcodecs_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/imgcodecs

//Value Computed by CMake
opencv_imgproc_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/imgproc

//Dependencies for the target
opencv_imgproc_LIB_DEPENDS:STATIC=general;opencv_core;

//Value Computed by CMake
opencv_imgproc_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/imgproc

//Value Computed by CMake
opencv_interactive-calibration_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/apps/interactive-calibration

//Value Computed by CMake
opencv_interactive-calibration_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/apps/interactive-calibration

//Value Computed by CMake
opencv_java_bindings_generator_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/java_bindings_generator

//Value Computed by CMake
opencv_java_bindings_generator_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/java/generator

//Value Computed by CMake
opencv_ml_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/ml

//Dependencies for the target
opencv_ml_LIB_DEPENDS:STATIC=general;opencv_core;

//Value Computed by CMake
opencv_ml_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/ml

//Value Computed by CMake
opencv_objdetect_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/objdetect

//Dependencies for the target
opencv_objdetect_LIB_DEPENDS:STATIC=general;opencv_core;general;opencv_flann;general;opencv_imgproc;general;opencv_features2d;general;opencv_calib3d;general;quirc;

//Value Computed by CMake
opencv_objdetect_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/objdetect

//Value Computed by CMake
opencv_photo_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/photo

//Dependencies for the target
opencv_photo_LIB_DEPENDS:STATIC=general;opencv_core;general;opencv_imgproc;

//Value Computed by CMake
opencv_photo_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/photo

//Value Computed by CMake
opencv_python3_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/python3

//Dependencies for the target
opencv_python3_LIB_DEPENDS:STATIC=general;D:/anaconda/python38.dll;general;opencv_core;general;opencv_flann;general;opencv_imgproc;general;opencv_ml;general;opencv_photo;general;opencv_dnn;general;opencv_features2d;general;opencv_imgcodecs;general;opencv_videoio;general;opencv_calib3d;general;opencv_highgui;general;opencv_objdetect;general;opencv_stitching;general;opencv_video;

//Value Computed by CMake
opencv_python3_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/python/python3

//Value Computed by CMake
opencv_python_bindings_generator_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator

//Value Computed by CMake
opencv_python_bindings_generator_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/python/bindings

//Value Computed by CMake
opencv_python_tests_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/python_tests

//Value Computed by CMake
opencv_python_tests_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/python/test

//Value Computed by CMake
opencv_stitching_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/stitching

//Dependencies for the target
opencv_stitching_LIB_DEPENDS:STATIC=general;opencv_core;general;opencv_flann;general;opencv_imgproc;general;opencv_features2d;general;opencv_calib3d;

//Value Computed by CMake
opencv_stitching_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/stitching

//Value Computed by CMake
opencv_ts_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/ts

//Dependencies for the target
opencv_ts_LIB_DEPENDS:STATIC=general;opencv_core;general;opencv_imgproc;general;opencv_imgcodecs;general;opencv_videoio;general;opencv_highgui;

//Value Computed by CMake
opencv_ts_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/ts

//Value Computed by CMake
opencv_version_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/apps/version

//Value Computed by CMake
opencv_version_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/apps/version

//Value Computed by CMake
opencv_version_win32_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/apps/version

//Value Computed by CMake
opencv_version_win32_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/apps/version

//Value Computed by CMake
opencv_video_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/video

//Dependencies for the target
opencv_video_LIB_DEPENDS:STATIC=general;opencv_core;general;opencv_flann;general;opencv_imgproc;general;opencv_features2d;general;opencv_calib3d;

//Value Computed by CMake
opencv_video_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/video

//Value Computed by CMake
opencv_videoio_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/modules/videoio

//Dependencies for the target
opencv_videoio_LIB_DEPENDS:STATIC=general;opencv_core;general;opencv_imgproc;general;opencv_imgcodecs;

//Value Computed by CMake
opencv_videoio_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/modules/videoio

//Value Computed by CMake
opencv_visualisation_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/apps/visualisation

//Value Computed by CMake
opencv_visualisation_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/apps/visualisation

//Value Computed by CMake
openexr_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/3rdparty/openexr

//Value Computed by CMake
openexr_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/3rdparty/openexr

//support for Macintosh PackBits algorithm
packbits:BOOL=ON

//Value Computed by CMake
quirc_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/3rdparty/quirc

//Value Computed by CMake
quirc_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/3rdparty/quirc

//support for ThunderScan 4-bit RLE algorithm
thunder:BOOL=ON

//Value Computed by CMake
zlib_BINARY_DIR:STATIC=D:/unet/opencv/opencv/mingw_build/3rdparty/zlib

//Value Computed by CMake
zlib_SOURCE_DIR:STATIC=D:/unet/opencv/opencv/sources/3rdparty/zlib


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: BLAS_Accelerate_LIBRARY
BLAS_Accelerate_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_WORKS
BLAS_WORKS-ADVANCED:INTERNAL=1
//Have function sgemm_
BLAS_WORKS:INTERNAL=
//ADVANCED property for variable: BLAS_acml_LIBRARY
BLAS_acml_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_acml_mp_LIBRARY
BLAS_acml_mp_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_blas_LIBRARY
BLAS_blas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_blis_LIBRARY
BLAS_blis_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_complib.sgimath_LIBRARY
BLAS_complib.sgimath_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_cxml_LIBRARY
BLAS_cxml_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_dxml_LIBRARY
BLAS_dxml_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_essl_LIBRARY
BLAS_essl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_f77blas_LIBRARY
BLAS_f77blas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_goto2_LIBRARY
BLAS_goto2_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_mkl_intel_c_LIBRARY
BLAS_mkl_intel_c_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_mkl_intel_lp64_LIBRARY
BLAS_mkl_intel_lp64_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_openblas_LIBRARY
BLAS_openblas_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_scsl_LIBRARY
BLAS_scsl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_sgemm_LIBRARY
BLAS_sgemm_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_sunperf_LIBRARY
BLAS_sunperf_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: BLAS_vecLib_LIBRARY
BLAS_vecLib_LIBRARY-ADVANCED:INTERNAL=1
CACHED_CPU_BASELINE_FLAGS:INTERNAL= -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wundef -Winit-self -Wpointer-arith -Wshadow -Wsign-promo -Wuninitialized -Winit-self -Wsuggest-override -Wno-delete-non-virtual-dtor -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3
CACHED_CPU_DISPATCH_AVX2_FLAGS:INTERNAL= -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wundef -Winit-self -Wpointer-arith -Wshadow -Wsign-promo -Wuninitialized -Winit-self -Wsuggest-override -Wno-delete-non-virtual-dtor -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mfma -mavx -mavx2
CACHED_CPU_DISPATCH_AVX_FLAGS:INTERNAL= -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wundef -Winit-self -Wpointer-arith -Wshadow -Wsign-promo -Wuninitialized -Winit-self -Wsuggest-override -Wno-delete-non-virtual-dtor -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mavx
CACHED_CPU_DISPATCH_FP16_FLAGS:INTERNAL= -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wundef -Winit-self -Wpointer-arith -Wshadow -Wsign-promo -Wuninitialized -Winit-self -Wsuggest-override -Wno-delete-non-virtual-dtor -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3  -mssse3 -msse4.1 -mpopcnt -msse4.2 -mf16c -mavx
CACHED_CPU_DISPATCH_SSE4_1_FLAGS:INTERNAL= -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wundef -Winit-self -Wpointer-arith -Wshadow -Wsign-promo -Wuninitialized -Winit-self -Wsuggest-override -Wno-delete-non-virtual-dtor -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3  -mssse3 -msse4.1
CACHED_CPU_DISPATCH_SSE4_2_FLAGS:INTERNAL= -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wundef -Winit-self -Wpointer-arith -Wshadow -Wsign-promo -Wuninitialized -Winit-self -Wsuggest-override -Wno-delete-non-virtual-dtor -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3  -mssse3 -msse4.1 -mpopcnt -msse4.2
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//STRINGS property for variable: CMAKE_BUILD_TYPE
CMAKE_BUILD_TYPE-STRINGS:INTERNAL=Debug;Release
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=d:/unet/opencv/opencv/mingw_build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=13
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=1
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake-gui.exe
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=MinGW Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Have symbol pthread_create
CMAKE_HAVE_LIBC_CREATE:INTERNAL=1
//Have include pthread.h
CMAKE_HAVE_PTHREAD_H:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=D:/unet/opencv/opencv/sources
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=63
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-3.13
//ADVANCED property for variable: CMAKE_SH
CMAKE_SH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//CHECK_TYPE_SIZE: sizeof(unsigned short)
CMAKE_SIZEOF_UNSIGNED_SHORT:INTERNAL=2
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_7Z
CPACK_BINARY_7Z-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_IFW
CPACK_BINARY_IFW-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_NSIS
CPACK_BINARY_NSIS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_NUGET
CPACK_BINARY_NUGET-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_WIX
CPACK_BINARY_WIX-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_BINARY_ZIP
CPACK_BINARY_ZIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_7Z
CPACK_SOURCE_7Z-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CPACK_SOURCE_ZIP
CPACK_SOURCE_ZIP-ADVANCED:INTERNAL=1
CPU_AVX2_USAGE_COUNT:INTERNAL=27
CPU_AVX512_CLX_USAGE_COUNT:INTERNAL=0
CPU_AVX512_CNL_USAGE_COUNT:INTERNAL=0
CPU_AVX512_COMMON_USAGE_COUNT:INTERNAL=0
CPU_AVX512_ICL_USAGE_COUNT:INTERNAL=0
CPU_AVX512_KNL_USAGE_COUNT:INTERNAL=0
CPU_AVX512_KNM_USAGE_COUNT:INTERNAL=0
CPU_AVX512_SKX_USAGE_COUNT:INTERNAL=0
CPU_AVX_512F_USAGE_COUNT:INTERNAL=0
CPU_AVX_USAGE_COUNT:INTERNAL=4
//STRINGS property for variable: CPU_BASELINE
CPU_BASELINE-STRINGS:INTERNAL=;SSE;SSE2;SSE3;SSSE3;SSE4_1;POPCNT;SSE4_2;FP16;FMA3;AVX;AVX2;AVX_512F;AVX512_COMMON;AVX512_KNL;AVX512_KNM;AVX512_SKX;AVX512_CNL;AVX512_CLX;AVX512_ICL
//STRINGS property for variable: CPU_DISPATCH
CPU_DISPATCH-STRINGS:INTERNAL=;SSE;SSE2;SSE3;SSSE3;SSE4_1;POPCNT;SSE4_2;FP16;FMA3;AVX;AVX2;AVX_512F;AVX512_COMMON;AVX512_KNL;AVX512_KNM;AVX512_SKX;AVX512_CNL;AVX512_CLX;AVX512_ICL
CPU_FMA3_USAGE_COUNT:INTERNAL=0
CPU_FP16_USAGE_COUNT:INTERNAL=0
CPU_POPCNT_USAGE_COUNT:INTERNAL=0
CPU_SSE2_USAGE_COUNT:INTERNAL=0
CPU_SSE3_USAGE_COUNT:INTERNAL=0
CPU_SSE4_1_USAGE_COUNT:INTERNAL=14
CPU_SSE4_2_USAGE_COUNT:INTERNAL=1
CPU_SSE_USAGE_COUNT:INTERNAL=0
CPU_SSSE3_USAGE_COUNT:INTERNAL=0
//Test C_HAS_inline
C_HAS_inline:INTERNAL=1
//Details about finding PythonInterp
FIND_PACKAGE_MESSAGE_DETAILS_PythonInterp:INTERNAL=[D:/anaconda/python.exe][v3.8.8(2.7)]
//Details about finding PythonLibs
FIND_PACKAGE_MESSAGE_DETAILS_PythonLibs:INTERNAL=[D:/anaconda/python38.dll][D:/anaconda/include][v3.8.8(3.8.8)]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//Have include assert.h
HAVE_ASSERT_H:INTERNAL=1
//Result of TRY_COMPILE
HAVE_CMAKE_SIZEOF_UNSIGNED_SHORT:INTERNAL=TRUE
//Test HAVE_CPU_BASELINE_FLAGS
HAVE_CPU_BASELINE_FLAGS:INTERNAL=1
//Test HAVE_CPU_DISPATCH_FLAGS_AVX
HAVE_CPU_DISPATCH_FLAGS_AVX:INTERNAL=1
//Test HAVE_CPU_DISPATCH_FLAGS_AVX2
HAVE_CPU_DISPATCH_FLAGS_AVX2:INTERNAL=1
//Test HAVE_CPU_DISPATCH_FLAGS_FP16
HAVE_CPU_DISPATCH_FLAGS_FP16:INTERNAL=1
//Test HAVE_CPU_DISPATCH_FLAGS_SSE4_1
HAVE_CPU_DISPATCH_FLAGS_SSE4_1:INTERNAL=1
//Test HAVE_CPU_DISPATCH_FLAGS_SSE4_2
HAVE_CPU_DISPATCH_FLAGS_SSE4_2:INTERNAL=1
//Test HAVE_CXX_FDATA_SECTIONS
HAVE_CXX_FDATA_SECTIONS:INTERNAL=1
//Test HAVE_CXX_FDIAGNOSTICS_SHOW_OPTION
HAVE_CXX_FDIAGNOSTICS_SHOW_OPTION:INTERNAL=1
//Test HAVE_CXX_FFUNCTION_SECTIONS
HAVE_CXX_FFUNCTION_SECTIONS:INTERNAL=1
//Test HAVE_CXX_FOMIT_FRAME_POINTER
HAVE_CXX_FOMIT_FRAME_POINTER:INTERNAL=1
//Test HAVE_CXX_FSIGNED_CHAR
HAVE_CXX_FSIGNED_CHAR:INTERNAL=1
//Test HAVE_CXX_FVISIBILITY_HIDDEN
HAVE_CXX_FVISIBILITY_HIDDEN:INTERNAL=1
//Test HAVE_CXX_FVISIBILITY_INLINES_HIDDEN
HAVE_CXX_FVISIBILITY_INLINES_HIDDEN:INTERNAL=1
//Test HAVE_CXX_MAVX
HAVE_CXX_MAVX:INTERNAL=1
//Test HAVE_CXX_MAVX2
HAVE_CXX_MAVX2:INTERNAL=1
//Test HAVE_CXX_MAVX512F
HAVE_CXX_MAVX512F:INTERNAL=
//Test HAVE_CXX_MAVX512F_MAVX512CD
HAVE_CXX_MAVX512F_MAVX512CD:INTERNAL=
//Test HAVE_CXX_MAVX512F_MAVX512CD_MAVX512VL_MAVX512BW_MAVX512DQ
HAVE_CXX_MAVX512F_MAVX512CD_MAVX512VL_MAVX512BW_MAVX512DQ:INTERNAL=
//Test HAVE_CXX_MF16C
HAVE_CXX_MF16C:INTERNAL=1
//Test HAVE_CXX_MFMA
HAVE_CXX_MFMA:INTERNAL=1
//Test HAVE_CXX_MPOPCNT
HAVE_CXX_MPOPCNT:INTERNAL=1
//Test HAVE_CXX_MSSE
HAVE_CXX_MSSE:INTERNAL=1
//Test HAVE_CXX_MSSE2
HAVE_CXX_MSSE2:INTERNAL=1
//Test HAVE_CXX_MSSE3
HAVE_CXX_MSSE3:INTERNAL=1
//Test HAVE_CXX_MSSE4_1
HAVE_CXX_MSSE4_1:INTERNAL=1
//Test HAVE_CXX_MSSE4_2
HAVE_CXX_MSSE4_2:INTERNAL=1
//Test HAVE_CXX_MSSSE3
HAVE_CXX_MSSSE3:INTERNAL=1
//Test HAVE_CXX_W
HAVE_CXX_W:INTERNAL=1
//Test HAVE_CXX_WALL
HAVE_CXX_WALL:INTERNAL=1
//Test HAVE_CXX_WERROR_ADDRESS
HAVE_CXX_WERROR_ADDRESS:INTERNAL=1
//Test HAVE_CXX_WERROR_FORMAT_SECURITY
HAVE_CXX_WERROR_FORMAT_SECURITY:INTERNAL=1
//Test HAVE_CXX_WERROR_NON_VIRTUAL_DTOR
HAVE_CXX_WERROR_NON_VIRTUAL_DTOR:INTERNAL=1
//Test HAVE_CXX_WERROR_RETURN_TYPE
HAVE_CXX_WERROR_RETURN_TYPE:INTERNAL=1
//Test HAVE_CXX_WERROR_SEQUENCE_POINT
HAVE_CXX_WERROR_SEQUENCE_POINT:INTERNAL=1
//Test HAVE_CXX_WFORMAT
HAVE_CXX_WFORMAT:INTERNAL=1
//Test HAVE_CXX_WIMPLICIT_FALLTHROUGH_3
HAVE_CXX_WIMPLICIT_FALLTHROUGH_3:INTERNAL=1
//Test HAVE_CXX_WINIT_SELF
HAVE_CXX_WINIT_SELF:INTERNAL=1
//Test HAVE_CXX_WMISSING_DECLARATIONS
HAVE_CXX_WMISSING_DECLARATIONS:INTERNAL=1
//Test HAVE_CXX_WMISSING_PROTOTYPES
HAVE_CXX_WMISSING_PROTOTYPES:INTERNAL=
//Test HAVE_CXX_WNO_ARRAY_BOUNDS
HAVE_CXX_WNO_ARRAY_BOUNDS:INTERNAL=1
//Test HAVE_CXX_WNO_CLASS_MEMACCESS
HAVE_CXX_WNO_CLASS_MEMACCESS:INTERNAL=1
//Test HAVE_CXX_WNO_COMMENT
HAVE_CXX_WNO_COMMENT:INTERNAL=1
//Test HAVE_CXX_WNO_DELETE_NON_VIRTUAL_DTOR
HAVE_CXX_WNO_DELETE_NON_VIRTUAL_DTOR:INTERNAL=1
//Test HAVE_CXX_WNO_DEPRECATED
HAVE_CXX_WNO_DEPRECATED:INTERNAL=1
//Test HAVE_CXX_WNO_DEPRECATED_DECLARATIONS
HAVE_CXX_WNO_DEPRECATED_DECLARATIONS:INTERNAL=1
//Test HAVE_CXX_WNO_ENUM_COMPARE_SWITCH
HAVE_CXX_WNO_ENUM_COMPARE_SWITCH:INTERNAL=
//Test HAVE_CXX_WNO_EXTRA
HAVE_CXX_WNO_EXTRA:INTERNAL=1
//Test HAVE_CXX_WNO_IGNORED_QUALIFIERS
HAVE_CXX_WNO_IGNORED_QUALIFIERS:INTERNAL=1
//Test HAVE_CXX_WNO_IMPLICIT_FALLTHROUGH
HAVE_CXX_WNO_IMPLICIT_FALLTHROUGH:INTERNAL=1
//Test HAVE_CXX_WNO_INCONSISTENT_MISSING_OVERRIDE
HAVE_CXX_WNO_INCONSISTENT_MISSING_OVERRIDE:INTERNAL=
//Test HAVE_CXX_WNO_INVALID_OFFSETOF
HAVE_CXX_WNO_INVALID_OFFSETOF:INTERNAL=1
//Test HAVE_CXX_WNO_LONG_LONG
HAVE_CXX_WNO_LONG_LONG:INTERNAL=1
//Test HAVE_CXX_WNO_MISLEADING_INDENTATION
HAVE_CXX_WNO_MISLEADING_INDENTATION:INTERNAL=1
//Test HAVE_CXX_WNO_MISSING_DECLARATIONS
HAVE_CXX_WNO_MISSING_DECLARATIONS:INTERNAL=1
//Test HAVE_CXX_WNO_MISSING_PROTOTYPES
HAVE_CXX_WNO_MISSING_PROTOTYPES:INTERNAL=
//Test HAVE_CXX_WNO_OVERLOADED_VIRTUAL
HAVE_CXX_WNO_OVERLOADED_VIRTUAL:INTERNAL=1
//Test HAVE_CXX_WNO_PARENTHESES
HAVE_CXX_WNO_PARENTHESES:INTERNAL=1
//Test HAVE_CXX_WNO_REORDER
HAVE_CXX_WNO_REORDER:INTERNAL=1
//Test HAVE_CXX_WNO_SHADOW
HAVE_CXX_WNO_SHADOW:INTERNAL=1
//Test HAVE_CXX_WNO_SHORTEN_64_TO_32
HAVE_CXX_WNO_SHORTEN_64_TO_32:INTERNAL=
//Test HAVE_CXX_WNO_SIGN_COMPARE
HAVE_CXX_WNO_SIGN_COMPARE:INTERNAL=1
//Test HAVE_CXX_WNO_SIGN_PROMO
HAVE_CXX_WNO_SIGN_PROMO:INTERNAL=1
//Test HAVE_CXX_WNO_STRICT_OVERFLOW
HAVE_CXX_WNO_STRICT_OVERFLOW:INTERNAL=1
//Test HAVE_CXX_WNO_SUGGEST_OVERRIDE
HAVE_CXX_WNO_SUGGEST_OVERRIDE:INTERNAL=1
//Test HAVE_CXX_WNO_SWITCH
HAVE_CXX_WNO_SWITCH:INTERNAL=1
//Test HAVE_CXX_WNO_TAUTOLOGICAL_COMPARE
HAVE_CXX_WNO_TAUTOLOGICAL_COMPARE:INTERNAL=1
//Test HAVE_CXX_WNO_TAUTOLOGICAL_UNDEFINED_COMPARE
HAVE_CXX_WNO_TAUTOLOGICAL_UNDEFINED_COMPARE:INTERNAL=
//Test HAVE_CXX_WNO_UNDEF
HAVE_CXX_WNO_UNDEF:INTERNAL=1
//Test HAVE_CXX_WNO_UNINITIALIZED
HAVE_CXX_WNO_UNINITIALIZED:INTERNAL=1
//Test HAVE_CXX_WNO_UNNAMED_TYPE_TEMPLATE_ARGS
HAVE_CXX_WNO_UNNAMED_TYPE_TEMPLATE_ARGS:INTERNAL=
//Test HAVE_CXX_WNO_UNUSED
HAVE_CXX_WNO_UNUSED:INTERNAL=1
//Test HAVE_CXX_WNO_UNUSED_CONST_VARIABLE
HAVE_CXX_WNO_UNUSED_CONST_VARIABLE:INTERNAL=1
//Test HAVE_CXX_WNO_UNUSED_FUNCTION
HAVE_CXX_WNO_UNUSED_FUNCTION:INTERNAL=1
//Test HAVE_CXX_WNO_UNUSED_LOCAL_TYPEDEFS
HAVE_CXX_WNO_UNUSED_LOCAL_TYPEDEFS:INTERNAL=1
//Test HAVE_CXX_WNO_UNUSED_PARAMETER
HAVE_CXX_WNO_UNUSED_PARAMETER:INTERNAL=1
//Test HAVE_CXX_WNO_UNUSED_PRIVATE_FIELD
HAVE_CXX_WNO_UNUSED_PRIVATE_FIELD:INTERNAL=
//Test HAVE_CXX_WNO_UNUSED_RESULT
HAVE_CXX_WNO_UNUSED_RESULT:INTERNAL=1
//Test HAVE_CXX_WPOINTER_ARITH
HAVE_CXX_WPOINTER_ARITH:INTERNAL=1
//Test HAVE_CXX_WSHADOW
HAVE_CXX_WSHADOW:INTERNAL=1
//Test HAVE_CXX_WSIGN_PROMO
HAVE_CXX_WSIGN_PROMO:INTERNAL=1
//Test HAVE_CXX_WSTRICT_PROTOTYPES
HAVE_CXX_WSTRICT_PROTOTYPES:INTERNAL=
//Test HAVE_CXX_WSUGGEST_OVERRIDE
HAVE_CXX_WSUGGEST_OVERRIDE:INTERNAL=1
//Test HAVE_CXX_WUNDEF
HAVE_CXX_WUNDEF:INTERNAL=1
//Test HAVE_CXX_WUNINITIALIZED
HAVE_CXX_WUNINITIALIZED:INTERNAL=1
//Test HAVE_C_FDATA_SECTIONS
HAVE_C_FDATA_SECTIONS:INTERNAL=1
//Test HAVE_C_FDIAGNOSTICS_SHOW_OPTION
HAVE_C_FDIAGNOSTICS_SHOW_OPTION:INTERNAL=1
//Test HAVE_C_FFUNCTION_SECTIONS
HAVE_C_FFUNCTION_SECTIONS:INTERNAL=1
//Test HAVE_C_FOMIT_FRAME_POINTER
HAVE_C_FOMIT_FRAME_POINTER:INTERNAL=1
//Test HAVE_C_FSIGNED_CHAR
HAVE_C_FSIGNED_CHAR:INTERNAL=1
//Test HAVE_C_FVISIBILITY_HIDDEN
HAVE_C_FVISIBILITY_HIDDEN:INTERNAL=1
//Test HAVE_C_FVISIBILITY_INLINES_HIDDEN
HAVE_C_FVISIBILITY_INLINES_HIDDEN:INTERNAL=
//Test HAVE_C_STD_C99
HAVE_C_STD_C99:INTERNAL=1
//Test HAVE_C_W
HAVE_C_W:INTERNAL=1
//Test HAVE_C_WALL
HAVE_C_WALL:INTERNAL=1
//Test HAVE_C_WERROR_ADDRESS
HAVE_C_WERROR_ADDRESS:INTERNAL=1
//Test HAVE_C_WERROR_FORMAT_SECURITY
HAVE_C_WERROR_FORMAT_SECURITY:INTERNAL=1
//Test HAVE_C_WERROR_NON_VIRTUAL_DTOR
HAVE_C_WERROR_NON_VIRTUAL_DTOR:INTERNAL=1
//Test HAVE_C_WERROR_RETURN_TYPE
HAVE_C_WERROR_RETURN_TYPE:INTERNAL=1
//Test HAVE_C_WERROR_SEQUENCE_POINT
HAVE_C_WERROR_SEQUENCE_POINT:INTERNAL=1
//Test HAVE_C_WFORMAT
HAVE_C_WFORMAT:INTERNAL=1
//Test HAVE_C_WIMPLICIT_FALLTHROUGH_3
HAVE_C_WIMPLICIT_FALLTHROUGH_3:INTERNAL=1
//Test HAVE_C_WINIT_SELF
HAVE_C_WINIT_SELF:INTERNAL=1
//Test HAVE_C_WMISSING_DECLARATIONS
HAVE_C_WMISSING_DECLARATIONS:INTERNAL=1
//Test HAVE_C_WMISSING_PROTOTYPES
HAVE_C_WMISSING_PROTOTYPES:INTERNAL=1
//Test HAVE_C_WNO_ABSOLUTE_VALUE
HAVE_C_WNO_ABSOLUTE_VALUE:INTERNAL=
//Test HAVE_C_WNO_ATTRIBUTES
HAVE_C_WNO_ATTRIBUTES:INTERNAL=1
//Test HAVE_C_WNO_CAST_ALIGN
HAVE_C_WNO_CAST_ALIGN:INTERNAL=1
//Test HAVE_C_WNO_COMMENT
HAVE_C_WNO_COMMENT:INTERNAL=1
//Test HAVE_C_WNO_DELETE_NON_VIRTUAL_DTOR
HAVE_C_WNO_DELETE_NON_VIRTUAL_DTOR:INTERNAL=
//Test HAVE_C_WNO_IMPLICIT_FALLTHROUGH
HAVE_C_WNO_IMPLICIT_FALLTHROUGH:INTERNAL=1
//Test HAVE_C_WNO_IMPLICIT_FUNCTION_DECLARATION
HAVE_C_WNO_IMPLICIT_FUNCTION_DECLARATION:INTERNAL=1
//Test HAVE_C_WNO_INT_TO_POINTER_CAST
HAVE_C_WNO_INT_TO_POINTER_CAST:INTERNAL=1
//Test HAVE_C_WNO_LONG_LONG
HAVE_C_WNO_LONG_LONG:INTERNAL=1
//Test HAVE_C_WNO_MAYBE_UNINITIALIZED
HAVE_C_WNO_MAYBE_UNINITIALIZED:INTERNAL=1
//Test HAVE_C_WNO_MISLEADING_INDENTATION
HAVE_C_WNO_MISLEADING_INDENTATION:INTERNAL=1
//Test HAVE_C_WNO_MISSING_DECLARATIONS
HAVE_C_WNO_MISSING_DECLARATIONS:INTERNAL=1
//Test HAVE_C_WNO_MISSING_PROTOTYPES
HAVE_C_WNO_MISSING_PROTOTYPES:INTERNAL=1
//Test HAVE_C_WNO_POINTER_COMPARE
HAVE_C_WNO_POINTER_COMPARE:INTERNAL=1
//Test HAVE_C_WNO_POINTER_TO_INT_CAST
HAVE_C_WNO_POINTER_TO_INT_CAST:INTERNAL=1
//Test HAVE_C_WNO_SHADOW
HAVE_C_WNO_SHADOW:INTERNAL=1
//Test HAVE_C_WNO_SHIFT_NEGATIVE_VALUE
HAVE_C_WNO_SHIFT_NEGATIVE_VALUE:INTERNAL=1
//Test HAVE_C_WNO_SHORTEN_64_TO_32
HAVE_C_WNO_SHORTEN_64_TO_32:INTERNAL=
//Test HAVE_C_WNO_SIGN_COMPARE
HAVE_C_WNO_SIGN_COMPARE:INTERNAL=1
//Test HAVE_C_WNO_STRICT_OVERFLOW
HAVE_C_WNO_STRICT_OVERFLOW:INTERNAL=1
//Test HAVE_C_WNO_STRICT_PROTOTYPES
HAVE_C_WNO_STRICT_PROTOTYPES:INTERNAL=1
//Test HAVE_C_WNO_UNDEF
HAVE_C_WNO_UNDEF:INTERNAL=1
//Test HAVE_C_WNO_UNINITIALIZED
HAVE_C_WNO_UNINITIALIZED:INTERNAL=1
//Test HAVE_C_WNO_UNNAMED_TYPE_TEMPLATE_ARGS
HAVE_C_WNO_UNNAMED_TYPE_TEMPLATE_ARGS:INTERNAL=
//Test HAVE_C_WNO_UNUSED
HAVE_C_WNO_UNUSED:INTERNAL=1
//Test HAVE_C_WNO_UNUSED_BUT_SET_PARAMETER
HAVE_C_WNO_UNUSED_BUT_SET_PARAMETER:INTERNAL=1
//Test HAVE_C_WNO_UNUSED_BUT_SET_VARIABLE
HAVE_C_WNO_UNUSED_BUT_SET_VARIABLE:INTERNAL=1
//Test HAVE_C_WNO_UNUSED_FUNCTION
HAVE_C_WNO_UNUSED_FUNCTION:INTERNAL=1
//Test HAVE_C_WNO_UNUSED_PARAMETER
HAVE_C_WNO_UNUSED_PARAMETER:INTERNAL=1
//Test HAVE_C_WNO_UNUSED_VARIABLE
HAVE_C_WNO_UNUSED_VARIABLE:INTERNAL=1
//Test HAVE_C_WPOINTER_ARITH
HAVE_C_WPOINTER_ARITH:INTERNAL=1
//Test HAVE_C_WSHADOW
HAVE_C_WSHADOW:INTERNAL=1
//Test HAVE_C_WSIGN_PROMO
HAVE_C_WSIGN_PROMO:INTERNAL=
//Test HAVE_C_WSTRICT_PROTOTYPES
HAVE_C_WSTRICT_PROTOTYPES:INTERNAL=1
//Test HAVE_C_WSUGGEST_OVERRIDE
HAVE_C_WSUGGEST_OVERRIDE:INTERNAL=
//Test HAVE_C_WUNDEF
HAVE_C_WUNDEF:INTERNAL=1
//Test HAVE_C_WUNINITIALIZED
HAVE_C_WUNINITIALIZED:INTERNAL=1
//Have include dlfcn.h
HAVE_DLFCN_H:INTERNAL=
//Have include dshow.h
HAVE_DSHOW:INTERNAL=1
//Have include fcntl.h
HAVE_FCNTL_H:INTERNAL=1
//Have function floor
HAVE_FLOOR:INTERNAL=1
//Have function fseeko
HAVE_FSEEKO:INTERNAL=1
//Have function getopt
HAVE_GETOPT:INTERNAL=1
//ADVANCED property for variable: HAVE_IEEEFP
HAVE_IEEEFP-ADVANCED:INTERNAL=1
//Have include inttypes.h
HAVE_INTTYPES_H:INTERNAL=1
//Have include io.h
HAVE_IO_H:INTERNAL=1
//Have function isascii
HAVE_ISASCII:INTERNAL=1
//Have function lfind
HAVE_LFIND:INTERNAL=1
//Have include limits.h
HAVE_LIMITS_H:INTERNAL=1
//Have include malloc.h
HAVE_MALLOC_H:INTERNAL=1
//Have function memmove
HAVE_MEMMOVE:INTERNAL=1
//Have include memory.h
HAVE_MEMORY_H:INTERNAL=1
//Have function memset
HAVE_MEMSET:INTERNAL=1
//Have function mmap
HAVE_MMAP:INTERNAL=
//Result of TRY_COMPILE
HAVE_OFF64_T:INTERNAL=TRUE
//Have function pow
HAVE_POW:INTERNAL=1
//Have include search.h
HAVE_SEARCH_H:INTERNAL=1
//Have function setmode
HAVE_SETMODE:INTERNAL=1
//Result of TRY_COMPILE
HAVE_SIZEOF_PTRDIFF_T:INTERNAL=TRUE
//Result of TRY_COMPILE
HAVE_SIZEOF_SIGNED_INT:INTERNAL=TRUE
//Result of TRY_COMPILE
HAVE_SIZEOF_SIGNED_LONG:INTERNAL=TRUE
//Result of TRY_COMPILE
HAVE_SIZEOF_SIGNED_LONG_LONG:INTERNAL=TRUE
//Result of TRY_COMPILE
HAVE_SIZEOF_SIGNED_SHORT:INTERNAL=TRUE
//Result of TRY_COMPILE
HAVE_SIZEOF_SIZE_T:INTERNAL=TRUE
//Result of TRY_COMPILE
HAVE_SIZEOF_UNSIGNED_CHAR_P:INTERNAL=TRUE
//Result of TRY_COMPILE
HAVE_SIZEOF_UNSIGNED_INT:INTERNAL=TRUE
//Result of TRY_COMPILE
HAVE_SIZEOF_UNSIGNED_LONG:INTERNAL=TRUE
//Result of TRY_COMPILE
HAVE_SIZEOF_UNSIGNED_LONG_LONG:INTERNAL=TRUE
//Result of TRY_COMPILE
HAVE_SIZEOF_UNSIGNED_SHORT:INTERNAL=TRUE
//Test HAVE_SNPRINTF
HAVE_SNPRINTF:INTERNAL=1
//Have function sqrt
HAVE_SQRT:INTERNAL=1
//Have include stddef.h
HAVE_STDDEF_H:INTERNAL=1
//Have include stdint.h
HAVE_STDINT_H:INTERNAL=1
//Have function strcasecmp
HAVE_STRCASECMP:INTERNAL=1
//Have function strchr
HAVE_STRCHR:INTERNAL=1
//Have include strings.h
HAVE_STRINGS_H:INTERNAL=1
//Have include string.h
HAVE_STRING_H:INTERNAL=1
//Have function strrchr
HAVE_STRRCHR:INTERNAL=1
//Have function strstr
HAVE_STRSTR:INTERNAL=1
//Have function strtol
HAVE_STRTOL:INTERNAL=1
//Have function strtol
HAVE_STRTOUL:INTERNAL=1
//Have function strtoull
HAVE_STRTOULL:INTERNAL=1
//Have include sys/time.h
HAVE_SYS_TIME_H:INTERNAL=1
//Have include sys/types.h
HAVE_SYS_TYPES_H:INTERNAL=1
//Have include unistd.h
HAVE_UNISTD_H:INTERNAL=1
//Result of TRY_COMPILE
HAVE_WIN32UI:INTERNAL=TRUE
//Result of TRY_COMPILE
HAVE_WORDS_BIGENDIAN:INTERNAL=TRUE
//Result of TRY_COMPILE
HAVE_bigendian:INTERNAL=TRUE
//Result of TRY_COMPILE
HAVE_int16:INTERNAL=FALSE
//Result of TRY_COMPILE
HAVE_int32:INTERNAL=FALSE
//Result of TRY_COMPILE
HAVE_int8:INTERNAL=FALSE
//Module opencv_calib3d will be built in current configuration
HAVE_opencv_calib3d:INTERNAL=ON
//Module opencv_core will be built in current configuration
HAVE_opencv_core:INTERNAL=ON
//Module opencv_dnn will be built in current configuration
HAVE_opencv_dnn:INTERNAL=ON
//Module opencv_features2d will be built in current configuration
HAVE_opencv_features2d:INTERNAL=ON
//Module opencv_flann will be built in current configuration
HAVE_opencv_flann:INTERNAL=ON
//Module opencv_gapi will be built in current configuration
HAVE_opencv_gapi:INTERNAL=ON
//Module opencv_highgui will be built in current configuration
HAVE_opencv_highgui:INTERNAL=ON
//Module opencv_imgcodecs will be built in current configuration
HAVE_opencv_imgcodecs:INTERNAL=ON
//Module opencv_imgproc will be built in current configuration
HAVE_opencv_imgproc:INTERNAL=ON
//Module opencv_java can not be built in current configuration
HAVE_opencv_java:INTERNAL=OFF
//Module opencv_java_bindings_generator will be built in current
// configuration
HAVE_opencv_java_bindings_generator:INTERNAL=ON
//Module opencv_js can not be built in current configuration
HAVE_opencv_js:INTERNAL=OFF
//Module opencv_ml will be built in current configuration
HAVE_opencv_ml:INTERNAL=ON
//Module opencv_objdetect will be built in current configuration
HAVE_opencv_objdetect:INTERNAL=ON
//Module opencv_photo will be built in current configuration
HAVE_opencv_photo:INTERNAL=ON
//Module opencv_python2 can not be built in current configuration
HAVE_opencv_python2:INTERNAL=OFF
//Module opencv_python3 will be built in current configuration
HAVE_opencv_python3:INTERNAL=ON
//Module opencv_python_bindings_generator will be built in current
// configuration
HAVE_opencv_python_bindings_generator:INTERNAL=ON
//Module opencv_python_tests will be built in current configuration
HAVE_opencv_python_tests:INTERNAL=ON
//Module opencv_stitching will be built in current configuration
HAVE_opencv_stitching:INTERNAL=ON
//Module opencv_ts will not be built in current configuration
HAVE_opencv_ts:INTERNAL=OFF
//Module opencv_video will be built in current configuration
HAVE_opencv_video:INTERNAL=ON
//Module opencv_videoio will be built in current configuration
HAVE_opencv_videoio:INTERNAL=ON
//Module opencv_world will not be built in current configuration
HAVE_opencv_world:INTERNAL=OFF
//ADVANCED property for variable: HOST_BIG_ENDIAN
HOST_BIG_ENDIAN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: HOST_FILLORDER
HOST_FILLORDER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: JAVA_AWT_INCLUDE_PATH
JAVA_AWT_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: JAVA_AWT_LIBRARY
JAVA_AWT_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: JAVA_INCLUDE_PATH
JAVA_INCLUDE_PATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: JAVA_INCLUDE_PATH2
JAVA_INCLUDE_PATH2-ADVANCED:INTERNAL=1
//ADVANCED property for variable: JAVA_JVM_LIBRARY
JAVA_JVM_LIBRARY-ADVANCED:INTERNAL=1
OCV_DOWNLOAD_ADE_HASH_3rdparty_ade_v0_1_1f_zip:INTERNAL=b624b995ec9c439cbc2e9e6ee940d3a2
//CHECK_TYPE_SIZE: sizeof(off64_t)
OFF64_T:INTERNAL=8
//ADVANCED property for variable: OPENCL_INCLUDE_DIR
OPENCL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENCL_LIBRARY
OPENCL_LIBRARY-ADVANCED:INTERNAL=1
OPENCV_DEPHELPER:INTERNAL=D:/unet/opencv/opencv/mingw_build/CMakeFiles/dephelper
//ADVANCED property for variable: OPENCV_DOWNLOAD_PARAMS
OPENCV_DOWNLOAD_PARAMS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENCV_DOWNLOAD_TRIES_LIST
OPENCV_DOWNLOAD_TRIES_LIST-ADVANCED:INTERNAL=1
OPENCV_JAVA_BINDINGS_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/java_bindings_generator
OPENCV_JAVA_SIGNATURES_FILE:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/java_bindings_generator/opencv_java_signatures.json
//List of OpenCV modules included into the build
OPENCV_MODULES_BUILD:INTERNAL=opencv_core;opencv_flann;opencv_imgproc;opencv_java_bindings_generator;opencv_ml;opencv_photo;opencv_python_tests;opencv_dnn;opencv_features2d;opencv_gapi;opencv_imgcodecs;opencv_videoio;opencv_calib3d;opencv_highgui;opencv_objdetect;opencv_stitching;opencv_video;opencv_python_bindings_generator;opencv_python3
//List of OpenCV modules implicitly disabled due to dependencies
OPENCV_MODULES_DISABLED_AUTO:INTERNAL=
//List of OpenCV modules which can not be build in current configuration
OPENCV_MODULES_DISABLED_FORCE:INTERNAL=opencv_java;opencv_js;opencv_python2
//List of OpenCV modules explicitly disabled by user
OPENCV_MODULES_DISABLED_USER:INTERNAL=opencv_ts;opencv_world
//List of extra modules
OPENCV_MODULES_EXTRA:INTERNAL=java_bindings_generator;python2;python3;python_bindings_generator;python_tests
//List of main modules
OPENCV_MODULES_MAIN:INTERNAL=core;imgproc;imgcodecs;videoio;highgui;video;calib3d;features2d;objdetect;dnn;ml;flann;photo;stitching;gapi;java;js;ts;world
//List of OpenCV modules marked for export
OPENCV_MODULES_PUBLIC:INTERNAL=opencv_calib3d;opencv_core;opencv_dnn;opencv_features2d;opencv_flann;opencv_gapi;opencv_highgui;opencv_imgcodecs;opencv_imgproc;opencv_ml;opencv_objdetect;opencv_photo;opencv_stitching;opencv_video;opencv_videoio
OPENCV_MODULE_opencv_calib3d_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/calib3d
//The category of the module
OPENCV_MODULE_opencv_calib3d_CLASS:INTERNAL=PUBLIC
//Flattened dependencies of opencv_calib3d module
OPENCV_MODULE_opencv_calib3d_DEPS:INTERNAL=opencv_core;opencv_flann;opencv_imgproc;opencv_features2d
//Extra dependencies of opencv_calib3d module
OPENCV_MODULE_opencv_calib3d_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_calib3d module (for linker)
OPENCV_MODULE_opencv_calib3d_DEPS_TO_LINK:INTERNAL=opencv_core;opencv_flann;opencv_imgproc;opencv_features2d
//Brief description of opencv_calib3d module
OPENCV_MODULE_opencv_calib3d_DESCRIPTION:INTERNAL=Camera Calibration and 3D Reconstruction
//List of header files for opencv_calib3d
OPENCV_MODULE_opencv_calib3d_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/calib3d/include/opencv2/calib3d.hpp;D:/unet/opencv/opencv/sources/modules/calib3d/include/opencv2/calib3d/calib3d.hpp;D:/unet/opencv/opencv/sources/modules/calib3d/include/opencv2/calib3d/calib3d_c.h
OPENCV_MODULE_opencv_calib3d_IS_PART_OF_WORLD:INTERNAL=ON
OPENCV_MODULE_opencv_calib3d_LABEL:INTERNAL=Main;opencv_calib3d
OPENCV_MODULE_opencv_calib3d_LINK_DEPS:INTERNAL=
//Location of opencv_calib3d module sources
OPENCV_MODULE_opencv_calib3d_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/calib3d
//Optional dependencies of opencv_calib3d module
OPENCV_MODULE_opencv_calib3d_OPT_DEPS:INTERNAL=
//Optional private dependencies of opencv_calib3d module
OPENCV_MODULE_opencv_calib3d_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_calib3d module
OPENCV_MODULE_opencv_calib3d_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_calib3d module
OPENCV_MODULE_opencv_calib3d_REQ_DEPS:INTERNAL=opencv_imgproc;opencv_features2d;opencv_flann
//List of source files for opencv_calib3d
OPENCV_MODULE_opencv_calib3d_SOURCES:INTERNAL=D:/unet/opencv/opencv/sources/modules/calib3d/src/ap3p.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/calibinit.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/calibration.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/calibration_handeye.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/checkchessboard.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/chessboard.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/circlesgrid.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/compat_ptsetreg.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/dls.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/epnp.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/fisheye.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/five-point.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/fundam.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/homography_decomp.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/ippe.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/levmarq.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/main.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/p3p.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/polynom_solver.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/posit.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/ptsetreg.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/quadsubpix.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/rho.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/solvepnp.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/stereobm.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/stereosgbm.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/triangulate.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/undistort.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/upnp.cpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/opencl/stereobm.cl;D:/unet/opencv/opencv/mingw_build/modules/calib3d/opencl_kernels_calib3d.cpp;D:/unet/opencv/opencv/mingw_build/modules/calib3d/opencl_kernels_calib3d.hpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/ap3p.h;D:/unet/opencv/opencv/sources/modules/calib3d/src/calib3d_c_api.h;D:/unet/opencv/opencv/sources/modules/calib3d/src/chessboard.hpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/circlesgrid.hpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/distortion_model.hpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/dls.h;D:/unet/opencv/opencv/sources/modules/calib3d/src/epnp.h;D:/unet/opencv/opencv/sources/modules/calib3d/src/fisheye.hpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/ippe.hpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/p3p.h;D:/unet/opencv/opencv/sources/modules/calib3d/src/polynom_solver.h;D:/unet/opencv/opencv/sources/modules/calib3d/src/precomp.hpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/rho.h;D:/unet/opencv/opencv/sources/modules/calib3d/src/undistort.simd.hpp;D:/unet/opencv/opencv/sources/modules/calib3d/src/upnp.h;D:/unet/opencv/opencv/mingw_build/modules/calib3d/undistort.avx2.cpp
//List of wrappers supporting module opencv_calib3d
OPENCV_MODULE_opencv_calib3d_WRAPPERS:INTERNAL=java;js;python_bindings_generator;python2;python3
OPENCV_MODULE_opencv_core_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/core
//The category of the module
OPENCV_MODULE_opencv_core_CLASS:INTERNAL=PUBLIC
//Flattened dependencies of opencv_core module
OPENCV_MODULE_opencv_core_DEPS:INTERNAL=
//Extra dependencies of opencv_core module
OPENCV_MODULE_opencv_core_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_core module (for linker)
OPENCV_MODULE_opencv_core_DEPS_TO_LINK:INTERNAL=
//Brief description of opencv_core module
OPENCV_MODULE_opencv_core_DESCRIPTION:INTERNAL=The Core Functionality
//List of header files for opencv_core
OPENCV_MODULE_opencv_core_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/ocl_defs.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/opencl_info.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/opencl_svm.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_clamdblas.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_clamdfft.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_core.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_core_wrappers.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_gl.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/autogenerated/opencl_gl_wrappers.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_clamdblas.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_clamdfft.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_core.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_core_wrappers.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_gl.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_gl_wrappers.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_svm_20.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_svm_definitions.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/runtime/opencl_svm_hsa_extension.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/block.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/border_interpolate.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/color.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/common.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/datamov_utils.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/dynamic_smem.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/emulation.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/filters.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/funcattrib.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/functional.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/limits.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/reduce.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/saturate_cast.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/scan.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/simd_functions.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/transform.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/type_traits.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/utility.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/vec_distance.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/vec_math.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/vec_traits.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/warp.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/warp_reduce.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/warp_shuffle.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/detail/color_detail.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/detail/reduce.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/detail/reduce_key_val.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/detail/transform_detail.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/detail/type_traits_detail.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda/detail/vec_distance_detail.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/affine.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/async.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/base.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/bindings_utils.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/bufferpool.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/check.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/core.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/core_c.h;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda.inl.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda_stream_accessor.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cuda_types.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_dispatch.h;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_helper.h;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvdef.h;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.inl.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd_wrapper.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/directx.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/eigen.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/fast_math.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/hal.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/interface.h;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_avx.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_avx512.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_cpp.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_forward.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_msa.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_neon.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_sse.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_sse_em.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_vsx.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/intrin_wasm.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/msa_macros.h;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/simd_utils.impl.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.inl.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/matx.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/neon_utils.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl_genbase.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opengl.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/operations.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/optim.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ovx.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/persistence.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/private.cuda.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/private.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/saturate.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/simd_intrinsics.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/softfloat.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/sse_utils.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/traits.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/types.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/types_c.h;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utility.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/allocator_stats.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/allocator_stats.impl.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/configuration.private.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/filesystem.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/filesystem.private.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/instrumentation.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/lock.private.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/logger.defines.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/logger.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/logtag.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/tls.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/trace.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/trace.private.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/va_intel.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/version.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/vsx_utils.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/detail/async_promise.hpp;D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/detail/exception_ptr.hpp
OPENCV_MODULE_opencv_core_IS_PART_OF_WORLD:INTERNAL=ON
OPENCV_MODULE_opencv_core_LABEL:INTERNAL=Main;opencv_core
OPENCV_MODULE_opencv_core_LINK_DEPS:INTERNAL=
//Location of opencv_core module sources
OPENCV_MODULE_opencv_core_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/core
//Optional dependencies of opencv_core module
OPENCV_MODULE_opencv_core_OPT_DEPS:INTERNAL=opencv_cudev
//Optional private dependencies of opencv_core module
OPENCV_MODULE_opencv_core_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_core module
OPENCV_MODULE_opencv_core_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_core module
OPENCV_MODULE_opencv_core_REQ_DEPS:INTERNAL=
//List of source files for opencv_core
OPENCV_MODULE_opencv_core_SOURCES:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/core/version_string.inc;D:/unet/opencv/opencv/sources/modules/core/src/algorithm.cpp;D:/unet/opencv/opencv/sources/modules/core/src/alloc.cpp;D:/unet/opencv/opencv/sources/modules/core/src/arithm.cpp;D:/unet/opencv/opencv/sources/modules/core/src/arithm.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/core/src/array.cpp;D:/unet/opencv/opencv/sources/modules/core/src/async.cpp;D:/unet/opencv/opencv/sources/modules/core/src/batch_distance.cpp;D:/unet/opencv/opencv/sources/modules/core/src/bindings_utils.cpp;D:/unet/opencv/opencv/sources/modules/core/src/channels.cpp;D:/unet/opencv/opencv/sources/modules/core/src/check.cpp;D:/unet/opencv/opencv/sources/modules/core/src/command_line_parser.cpp;D:/unet/opencv/opencv/sources/modules/core/src/conjugate_gradient.cpp;D:/unet/opencv/opencv/sources/modules/core/src/convert.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/core/src/convert_c.cpp;D:/unet/opencv/opencv/sources/modules/core/src/convert_scale.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/core/src/copy.cpp;D:/unet/opencv/opencv/sources/modules/core/src/count_non_zero.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/core/src/cuda_gpu_mat.cpp;D:/unet/opencv/opencv/sources/modules/core/src/cuda_host_mem.cpp;D:/unet/opencv/opencv/sources/modules/core/src/cuda_info.cpp;D:/unet/opencv/opencv/sources/modules/core/src/cuda_stream.cpp;D:/unet/opencv/opencv/sources/modules/core/src/datastructs.cpp;D:/unet/opencv/opencv/sources/modules/core/src/directx.cpp;D:/unet/opencv/opencv/sources/modules/core/src/downhill_simplex.cpp;D:/unet/opencv/opencv/sources/modules/core/src/dxt.cpp;D:/unet/opencv/opencv/sources/modules/core/src/gl_core_3_1.cpp;D:/unet/opencv/opencv/sources/modules/core/src/glob.cpp;D:/unet/opencv/opencv/sources/modules/core/src/hal_internal.cpp;D:/unet/opencv/opencv/sources/modules/core/src/kmeans.cpp;D:/unet/opencv/opencv/sources/modules/core/src/lapack.cpp;D:/unet/opencv/opencv/sources/modules/core/src/lda.cpp;D:/unet/opencv/opencv/sources/modules/core/src/logger.cpp;D:/unet/opencv/opencv/sources/modules/core/src/lpsolver.cpp;D:/unet/opencv/opencv/sources/modules/core/src/lut.cpp;D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs.cpp;D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs_core.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/core/src/matmul.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/core/src/matrix.cpp;D:/unet/opencv/opencv/sources/modules/core/src/matrix_c.cpp;D:/unet/opencv/opencv/sources/modules/core/src/matrix_decomp.cpp;D:/unet/opencv/opencv/sources/modules/core/src/matrix_expressions.cpp;D:/unet/opencv/opencv/sources/modules/core/src/matrix_iterator.cpp;D:/unet/opencv/opencv/sources/modules/core/src/matrix_operations.cpp;D:/unet/opencv/opencv/sources/modules/core/src/matrix_sparse.cpp;D:/unet/opencv/opencv/sources/modules/core/src/matrix_wrap.cpp;D:/unet/opencv/opencv/sources/modules/core/src/mean.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/core/src/merge.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/core/src/minmax.cpp;D:/unet/opencv/opencv/sources/modules/core/src/norm.cpp;D:/unet/opencv/opencv/sources/modules/core/src/ocl.cpp;D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencl_clamdblas.cpp;D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencl_clamdfft.cpp;D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/opencl_core.cpp;D:/unet/opencv/opencv/sources/modules/core/src/opengl.cpp;D:/unet/opencv/opencv/sources/modules/core/src/out.cpp;D:/unet/opencv/opencv/sources/modules/core/src/ovx.cpp;D:/unet/opencv/opencv/sources/modules/core/src/parallel.cpp;D:/unet/opencv/opencv/sources/modules/core/src/parallel_impl.cpp;D:/unet/opencv/opencv/sources/modules/core/src/pca.cpp;D:/unet/opencv/opencv/sources/modules/core/src/persistence.cpp;D:/unet/opencv/opencv/sources/modules/core/src/persistence_json.cpp;D:/unet/opencv/opencv/sources/modules/core/src/persistence_types.cpp;D:/unet/opencv/opencv/sources/modules/core/src/persistence_xml.cpp;D:/unet/opencv/opencv/sources/modules/core/src/persistence_yml.cpp;D:/unet/opencv/opencv/sources/modules/core/src/rand.cpp;D:/unet/opencv/opencv/sources/modules/core/src/softfloat.cpp;D:/unet/opencv/opencv/sources/modules/core/src/split.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/core/src/stat.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/core/src/stat_c.cpp;D:/unet/opencv/opencv/sources/modules/core/src/stl.cpp;D:/unet/opencv/opencv/sources/modules/core/src/sum.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/core/src/system.cpp;D:/unet/opencv/opencv/sources/modules/core/src/tables.cpp;D:/unet/opencv/opencv/sources/modules/core/src/trace.cpp;D:/unet/opencv/opencv/sources/modules/core/src/types.cpp;D:/unet/opencv/opencv/sources/modules/core/src/umatrix.cpp;D:/unet/opencv/opencv/sources/modules/core/src/utils/datafile.cpp;D:/unet/opencv/opencv/sources/modules/core/src/utils/filesystem.cpp;D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagconfigparser.cpp;D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagmanager.cpp;D:/unet/opencv/opencv/sources/modules/core/src/utils/samples.cpp;D:/unet/opencv/opencv/sources/modules/core/src/va_intel.cpp;D:/unet/opencv/opencv/sources/modules/core/src/opencl/arithm.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/convert.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/copymakeborder.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/copyset.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/cvtclr_dx.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/fft.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/flip.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/gemm.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/halfconvert.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/inrange.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/intel_gemm.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/lut.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/meanstddev.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/minmaxloc.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/mixchannels.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/mulspectrums.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/normalize.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/reduce.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/reduce2.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/repeat.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/set_identity.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/split_merge.cl;D:/unet/opencv/opencv/sources/modules/core/src/opencl/transpose.cl;D:/unet/opencv/opencv/mingw_build/modules/core/opencl_kernels_core.cpp;D:/unet/opencv/opencv/mingw_build/modules/core/opencl_kernels_core.hpp;D:/unet/opencv/opencv/sources/modules/core/src/arithm.simd.hpp;D:/unet/opencv/opencv/sources/modules/core/src/arithm_ipp.hpp;D:/unet/opencv/opencv/sources/modules/core/src/bufferpool.impl.hpp;D:/unet/opencv/opencv/sources/modules/core/src/convert.hpp;D:/unet/opencv/opencv/sources/modules/core/src/convert.simd.hpp;D:/unet/opencv/opencv/sources/modules/core/src/convert_scale.simd.hpp;D:/unet/opencv/opencv/sources/modules/core/src/count_non_zero.simd.hpp;D:/unet/opencv/opencv/sources/modules/core/src/directx.inc.hpp;D:/unet/opencv/opencv/sources/modules/core/src/gl_core_3_1.hpp;D:/unet/opencv/opencv/sources/modules/core/src/hal_internal.hpp;D:/unet/opencv/opencv/sources/modules/core/src/hal_replacement.hpp;D:/unet/opencv/opencv/sources/modules/core/src/intel_gpu_gemm.inl.hpp;D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs.hpp;D:/unet/opencv/opencv/sources/modules/core/src/mathfuncs_core.simd.hpp;D:/unet/opencv/opencv/sources/modules/core/src/matmul.simd.hpp;D:/unet/opencv/opencv/sources/modules/core/src/mean.simd.hpp;D:/unet/opencv/opencv/sources/modules/core/src/merge.simd.hpp;D:/unet/opencv/opencv/sources/modules/core/src/ocl_deprecated.hpp;D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_clamdblas_impl.hpp;D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_clamdfft_impl.hpp;D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_core_impl.hpp;D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_core_static_impl.hpp;D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_gl_impl.hpp;D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/autogenerated/opencl_gl_static_impl.hpp;D:/unet/opencv/opencv/sources/modules/core/src/opencl/runtime/runtime_common.hpp;D:/unet/opencv/opencv/sources/modules/core/src/parallel_impl.hpp;D:/unet/opencv/opencv/sources/modules/core/src/persistence.hpp;D:/unet/opencv/opencv/sources/modules/core/src/precomp.hpp;D:/unet/opencv/opencv/sources/modules/core/src/split.simd.hpp;D:/unet/opencv/opencv/sources/modules/core/src/stat.hpp;D:/unet/opencv/opencv/sources/modules/core/src/stat.simd.hpp;D:/unet/opencv/opencv/sources/modules/core/src/sum.simd.hpp;D:/unet/opencv/opencv/sources/modules/core/src/umatrix.hpp;D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagconfig.hpp;D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagconfigparser.hpp;D:/unet/opencv/opencv/sources/modules/core/src/utils/logtagmanager.hpp;D:/unet/opencv/opencv/mingw_build/modules/core/arithm.sse4_1.cpp;D:/unet/opencv/opencv/mingw_build/modules/core/matmul.sse4_1.cpp;D:/unet/opencv/opencv/mingw_build/modules/core/stat.sse4_2.cpp;D:/unet/opencv/opencv/mingw_build/modules/core/mathfuncs_core.avx.cpp;D:/unet/opencv/opencv/mingw_build/modules/core/mathfuncs_core.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/core/stat.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/core/arithm.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/core/convert.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/core/convert_scale.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/core/count_non_zero.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/core/matmul.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/core/mean.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/core/merge.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/core/split.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/core/sum.avx2.cpp
//List of wrappers supporting module opencv_core
OPENCV_MODULE_opencv_core_WRAPPERS:INTERNAL=java;js;python_bindings_generator;python2;python3
OPENCV_MODULE_opencv_dnn_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/dnn
//The category of the module
OPENCV_MODULE_opencv_dnn_CLASS:INTERNAL=PUBLIC
//Flattened dependencies of opencv_dnn module
OPENCV_MODULE_opencv_dnn_DEPS:INTERNAL=opencv_core;opencv_imgproc
//Extra dependencies of opencv_dnn module
OPENCV_MODULE_opencv_dnn_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_dnn module (for linker)
OPENCV_MODULE_opencv_dnn_DEPS_TO_LINK:INTERNAL=opencv_core;opencv_imgproc
//Brief description of opencv_dnn module
OPENCV_MODULE_opencv_dnn_DESCRIPTION:INTERNAL=Deep neural network module. It allows to load models from different frameworks and to make forward pass
//List of header files for opencv_dnn
OPENCV_MODULE_opencv_dnn_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn.hpp;D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/all_layers.hpp;D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/dict.hpp;D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/dnn.hpp;D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/dnn.inl.hpp;D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/layer.details.hpp;D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/layer.hpp;D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/shape_utils.hpp;D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/utils/inference_engine.hpp;D:/unet/opencv/opencv/sources/modules/dnn/include/opencv2/dnn/version.hpp
OPENCV_MODULE_opencv_dnn_IS_PART_OF_WORLD:INTERNAL=ON
OPENCV_MODULE_opencv_dnn_LABEL:INTERNAL=Main;opencv_dnn
OPENCV_MODULE_opencv_dnn_LINK_DEPS:INTERNAL=;libprotobuf
//Location of opencv_dnn module sources
OPENCV_MODULE_opencv_dnn_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/dnn
//Optional dependencies of opencv_dnn module
OPENCV_MODULE_opencv_dnn_OPT_DEPS:INTERNAL=
//Optional private dependencies of opencv_dnn module
OPENCV_MODULE_opencv_dnn_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_dnn module
OPENCV_MODULE_opencv_dnn_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_dnn module
OPENCV_MODULE_opencv_dnn_REQ_DEPS:INTERNAL=opencv_core;opencv_imgproc
//List of source files for opencv_dnn
OPENCV_MODULE_opencv_dnn_SOURCES:INTERNAL=D:/unet/opencv/opencv/sources/modules/dnn/misc/caffe/opencv-caffe.pb.cc;D:/unet/opencv/opencv/sources/modules/dnn/misc/onnx/opencv-onnx.pb.cc;D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/attr_value.pb.cc;D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/function.pb.cc;D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/graph.pb.cc;D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/op_def.pb.cc;D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/tensor.pb.cc;D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/tensor_shape.pb.cc;D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/types.pb.cc;D:/unet/opencv/opencv/sources/modules/dnn/misc/tensorflow/versions.pb.cc;D:/unet/opencv/opencv/sources/modules/dnn/src/caffe/caffe_importer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/caffe/caffe_io.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/caffe/caffe_shrinker.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/darknet/darknet_importer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/darknet/darknet_io.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/dnn.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/halide_scheduler.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/ie_ngraph.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/init.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/batch_norm_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/blank_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/concat_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/const_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/convolution_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/crop_and_resize_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/detection_output_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/elementwise_layers.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/eltwise_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/flatten_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/fully_connected_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/layers_common.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/lrn_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/max_unpooling_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/mvn_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/normalize_bbox_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/padding_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/permute_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/pooling_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/prior_box_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/proposal_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/recurrent_layers.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/region_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/reorg_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/reshape_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/resize_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/scale_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/shuffle_channel_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/slice_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/softmax_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/split_layer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/model.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/nms.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/common.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/math_functions.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/ocl4dnn_conv_spatial.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/ocl4dnn_inner_product.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/ocl4dnn_lrn.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/ocl4dnn_pool.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/src/ocl4dnn_softmax.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/onnx/onnx_importer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/op_halide.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/op_inf_engine.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/op_vkcom.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/tensorflow/tf_graph_simplifier.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/tensorflow/tf_importer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/tensorflow/tf_io.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THDiskFile.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THFile.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THGeneral.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/torch/torch_importer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/avg_pool_spv.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/concat_spv.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/conv48_spv.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/conv_spv.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/dw_conv_spv.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/lrn_spv.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/max_pool_spv.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/permute_spv.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/prior_box_spv.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/relu_spv.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/softmax_spv.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/buffer.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/context.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/internal.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_base.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_concat.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_conv.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_lrn.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_permute.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_pool.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_prior_box.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_relu.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/op_softmax.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/tensor.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/vulkan/vk_functions.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/vulkan/vk_loader.cpp;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/activations.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/batchnorm.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/col2im.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/concat.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/conv_layer_spatial.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/conv_spatial_helper.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/detection_output.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/dummy.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/eltwise.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/gemm_buffer.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/gemm_image.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/im2col.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/lrn.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/math.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/matvec_mul.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/mvn.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/ocl4dnn_lrn.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/ocl4dnn_pooling.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/permute.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/pooling.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/prior_box.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/region.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/slice.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/softmax.cl;D:/unet/opencv/opencv/sources/modules/dnn/src/opencl/softmax_loss.cl;D:/unet/opencv/opencv/mingw_build/modules/dnn/opencl_kernels_dnn.cpp;D:/unet/opencv/opencv/mingw_build/modules/dnn/opencl_kernels_dnn.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/caffe/caffe_io.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/caffe/glog_emulator.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/array.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/atomics.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/execution.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/grid_stride_range.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/kernel_dispatcher.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/limits.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/math.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/types.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/cuda/vector_traits.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/darknet/darknet_io.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/halide_scheduler.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/ie_ngraph.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/layers_common.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/layers/layers_common.simd.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/nms.inl.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/include/common.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/include/default_kernel_config.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/include/math_functions.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/ocl4dnn/include/ocl4dnn.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/op_cuda.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/op_halide.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/op_inf_engine.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/op_vkcom.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/precomp.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/tensorflow/tf_graph_simplifier.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/tensorflow/tf_io.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THDiskFile.h;D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THFile.h;D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THFilePrivate.h;D:/unet/opencv/opencv/sources/modules/dnn/src/torch/THGeneral.h;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/buffer.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_base.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_concat.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_conv.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_lrn.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_permute.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_pool.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_prior_box.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_relu.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/op_softmax.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/tensor.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/include/vkcom.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/shader/spv_shader.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/common.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/context.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/src/internal.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/vulkan/function_list.inl.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/vulkan/vk_functions.hpp;D:/unet/opencv/opencv/sources/modules/dnn/src/vkcom/vulkan/vk_loader.hpp;D:/unet/opencv/opencv/mingw_build/modules/dnn/layers/layers_common.avx.cpp;D:/unet/opencv/opencv/mingw_build/modules/dnn/layers/layers_common.avx2.cpp
//List of wrappers supporting module opencv_dnn
OPENCV_MODULE_opencv_dnn_WRAPPERS:INTERNAL=java;js;python_bindings_generator;python2;python3
OPENCV_MODULE_opencv_features2d_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/features2d
//The category of the module
OPENCV_MODULE_opencv_features2d_CLASS:INTERNAL=PUBLIC
//Flattened dependencies of opencv_features2d module
OPENCV_MODULE_opencv_features2d_DEPS:INTERNAL=opencv_core;opencv_flann;opencv_imgproc
//Extra dependencies of opencv_features2d module
OPENCV_MODULE_opencv_features2d_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_features2d module (for linker)
OPENCV_MODULE_opencv_features2d_DEPS_TO_LINK:INTERNAL=opencv_core;opencv_flann;opencv_imgproc
//Brief description of opencv_features2d module
OPENCV_MODULE_opencv_features2d_DESCRIPTION:INTERNAL=2D Features Framework
//List of header files for opencv_features2d
OPENCV_MODULE_opencv_features2d_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/features2d/include/opencv2/features2d.hpp;D:/unet/opencv/opencv/sources/modules/features2d/include/opencv2/features2d/features2d.hpp;D:/unet/opencv/opencv/sources/modules/features2d/include/opencv2/features2d/hal/interface.h
OPENCV_MODULE_opencv_features2d_IS_PART_OF_WORLD:INTERNAL=ON
OPENCV_MODULE_opencv_features2d_LABEL:INTERNAL=Main;opencv_features2d
OPENCV_MODULE_opencv_features2d_LINK_DEPS:INTERNAL=
//Location of opencv_features2d module sources
OPENCV_MODULE_opencv_features2d_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/features2d
//Optional dependencies of opencv_features2d module
OPENCV_MODULE_opencv_features2d_OPT_DEPS:INTERNAL=opencv_flann
//Optional private dependencies of opencv_features2d module
OPENCV_MODULE_opencv_features2d_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_features2d module
OPENCV_MODULE_opencv_features2d_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_features2d module
OPENCV_MODULE_opencv_features2d_REQ_DEPS:INTERNAL=opencv_imgproc
//List of source files for opencv_features2d
OPENCV_MODULE_opencv_features2d_SOURCES:INTERNAL=D:/unet/opencv/opencv/sources/modules/features2d/src/agast.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/agast_score.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/akaze.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/bagofwords.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/blobdetector.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/brisk.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/draw.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/dynamic.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/evaluation.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/fast.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/fast_score.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/feature2d.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/gftt.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/kaze.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/AKAZEFeatures.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/KAZEFeatures.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/fed.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/nldiffusion_functions.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/keypoint.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/main.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/matchers.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/mser.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/orb.cpp;D:/unet/opencv/opencv/sources/modules/features2d/src/opencl/akaze.cl;D:/unet/opencv/opencv/sources/modules/features2d/src/opencl/brute_force_match.cl;D:/unet/opencv/opencv/sources/modules/features2d/src/opencl/fast.cl;D:/unet/opencv/opencv/sources/modules/features2d/src/opencl/orb.cl;D:/unet/opencv/opencv/mingw_build/modules/features2d/opencl_kernels_features2d.cpp;D:/unet/opencv/opencv/mingw_build/modules/features2d/opencl_kernels_features2d.hpp;D:/unet/opencv/opencv/sources/modules/features2d/src/agast_score.hpp;D:/unet/opencv/opencv/sources/modules/features2d/src/fast.hpp;D:/unet/opencv/opencv/sources/modules/features2d/src/fast_score.hpp;D:/unet/opencv/opencv/sources/modules/features2d/src/hal_replacement.hpp;D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/AKAZEConfig.h;D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/AKAZEFeatures.h;D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/KAZEConfig.h;D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/KAZEFeatures.h;D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/TEvolution.h;D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/fed.h;D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/nldiffusion_functions.h;D:/unet/opencv/opencv/sources/modules/features2d/src/kaze/utils.h;D:/unet/opencv/opencv/sources/modules/features2d/src/precomp.hpp;D:/unet/opencv/opencv/sources/modules/features2d/src/fast.avx2.cpp
//List of wrappers supporting module opencv_features2d
OPENCV_MODULE_opencv_features2d_WRAPPERS:INTERNAL=java;js;python_bindings_generator;python2;python3
OPENCV_MODULE_opencv_flann_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/flann
//The category of the module
OPENCV_MODULE_opencv_flann_CLASS:INTERNAL=PUBLIC
//Flattened dependencies of opencv_flann module
OPENCV_MODULE_opencv_flann_DEPS:INTERNAL=opencv_core
//Extra dependencies of opencv_flann module
OPENCV_MODULE_opencv_flann_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_flann module (for linker)
OPENCV_MODULE_opencv_flann_DEPS_TO_LINK:INTERNAL=opencv_core
//Brief description of opencv_flann module
OPENCV_MODULE_opencv_flann_DESCRIPTION:INTERNAL=Clustering and Search in Multi-Dimensional Spaces
//List of header files for opencv_flann
OPENCV_MODULE_opencv_flann_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann.hpp;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/all_indices.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/allocator.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/any.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/autotuned_index.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/composite_index.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/config.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/defines.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/dist.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/dummy.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/dynamic_bitset.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/flann.hpp;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/flann_base.hpp;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/general.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/ground_truth.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/hdf5.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/heap.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/hierarchical_clustering_index.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/index_testing.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/kdtree_index.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/kdtree_single_index.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/kmeans_index.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/linear_index.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/logger.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/lsh_index.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/lsh_table.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/matrix.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/miniflann.hpp;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/nn_index.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/object_factory.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/params.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/random.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/result_set.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/sampling.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/saving.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/simplex_downhill.h;D:/unet/opencv/opencv/sources/modules/flann/include/opencv2/flann/timer.h
OPENCV_MODULE_opencv_flann_IS_PART_OF_WORLD:INTERNAL=ON
OPENCV_MODULE_opencv_flann_LABEL:INTERNAL=Main;opencv_flann
OPENCV_MODULE_opencv_flann_LINK_DEPS:INTERNAL=
//Location of opencv_flann module sources
OPENCV_MODULE_opencv_flann_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/flann
//Optional dependencies of opencv_flann module
OPENCV_MODULE_opencv_flann_OPT_DEPS:INTERNAL=
//Optional private dependencies of opencv_flann module
OPENCV_MODULE_opencv_flann_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_flann module
OPENCV_MODULE_opencv_flann_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_flann module
OPENCV_MODULE_opencv_flann_REQ_DEPS:INTERNAL=opencv_core
//List of source files for opencv_flann
OPENCV_MODULE_opencv_flann_SOURCES:INTERNAL=D:/unet/opencv/opencv/sources/modules/flann/src/flann.cpp;D:/unet/opencv/opencv/sources/modules/flann/src/miniflann.cpp;D:/unet/opencv/opencv/sources/modules/flann/src/precomp.hpp
//List of wrappers supporting module opencv_flann
OPENCV_MODULE_opencv_flann_WRAPPERS:INTERNAL=python_bindings_generator;python2;python3
OPENCV_MODULE_opencv_gapi_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/gapi
//The category of the module
OPENCV_MODULE_opencv_gapi_CLASS:INTERNAL=PUBLIC
//Flattened dependencies of opencv_gapi module
OPENCV_MODULE_opencv_gapi_DEPS:INTERNAL=opencv_core;opencv_imgproc
//Extra dependencies of opencv_gapi module
OPENCV_MODULE_opencv_gapi_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_gapi module (for linker)
OPENCV_MODULE_opencv_gapi_DEPS_TO_LINK:INTERNAL=opencv_core;opencv_imgproc
//Brief description of opencv_gapi module
OPENCV_MODULE_opencv_gapi_DESCRIPTION:INTERNAL=OpenCV G-API Core Module
//List of header files for opencv_gapi
OPENCV_MODULE_opencv_gapi_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/core.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/cpu/core.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/cpu/gcpukernel.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/cpu/imgproc.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/fluid/core.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/fluid/gfluidbuffer.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/fluid/gfluidkernel.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/fluid/imgproc.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/garg.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/garray.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gasync_context.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gcall.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gcommon.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gcompiled.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gcompiled_async.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gcompoundkernel.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gcomputation.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gcomputation_async.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gkernel.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gmat.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gmetaarg.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gproto.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gpu/core.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gpu/ggpukernel.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gpu/imgproc.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gscalar.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gstreaming.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gtransform.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gtype_traits.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/gtyped.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/imgproc.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/infer.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/infer/ie.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/ocl/core.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/ocl/goclkernel.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/ocl/imgproc.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/opencv_includes.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/operators.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/own/assert.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/own/convert.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/own/cvdefs.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/own/exports.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/own/mat.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/own/saturate.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/own/scalar.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/own/types.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/plaidml/core.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/plaidml/gplaidmlkernel.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/plaidml/plaidml.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/render.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/render/render.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/streaming/cap.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/streaming/source.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/util/any.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/util/compiler_hints.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/util/optional.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/util/throw.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/util/util.hpp;D:/unet/opencv/opencv/sources/modules/gapi/include/opencv2/gapi/util/variant.hpp
OPENCV_MODULE_opencv_gapi_IS_PART_OF_WORLD:INTERNAL=ON
OPENCV_MODULE_opencv_gapi_LABEL:INTERNAL=Main;opencv_gapi
OPENCV_MODULE_opencv_gapi_LINK_DEPS:INTERNAL=
//Location of opencv_gapi module sources
OPENCV_MODULE_opencv_gapi_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/gapi
//Optional dependencies of opencv_gapi module
OPENCV_MODULE_opencv_gapi_OPT_DEPS:INTERNAL=
//Optional private dependencies of opencv_gapi module
OPENCV_MODULE_opencv_gapi_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_gapi module
OPENCV_MODULE_opencv_gapi_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_gapi module
OPENCV_MODULE_opencv_gapi_REQ_DEPS:INTERNAL=opencv_imgproc
//List of source files for opencv_gapi
OPENCV_MODULE_opencv_gapi_SOURCES:INTERNAL=D:/unet/opencv/opencv/sources/modules/gapi/src/api/gorigin.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/gmat.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/garray.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/gscalar.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/gkernel.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/gbackend.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/gproto.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/gnode.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/gcall.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/gcomputation.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/operators.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/kernels_core.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/kernels_imgproc.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/render.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/render_ocv.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/ginfer.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/api/ft_render.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gmodel.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gmodelbuilder.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gislandmodel.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gcompiler.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gcompiled.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/gstreaming.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/helpers.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/dump_dot.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/islands.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/meta.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/kernels.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/exec.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/transformations.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/pattern_matching.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/perform_substitution.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/compiler/passes/streaming.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/executor/gexecutor.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/executor/gstreamingexecutor.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/executor/gasync.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/cpu/gcpubackend.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/cpu/gcpukernel.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/cpu/gcpuimgproc.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/cpu/gcpucore.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/fluid/gfluidbuffer.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/fluid/gfluidbackend.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/fluid/gfluidimgproc.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/fluid/gfluidimgproc_func.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/fluid/gfluidcore.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/ocl/goclbackend.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/ocl/goclkernel.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/ocl/goclimgproc.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/ocl/goclcore.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/ie/giebackend.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/render/grenderocvbackend.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/render/grenderocv.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/plaidml/gplaidmlcore.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/plaidml/gplaidmlbackend.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/common/gcompoundbackend.cpp;D:/unet/opencv/opencv/sources/modules/gapi/src/backends/common/gcompoundkernel.cpp;D:/unet/opencv/opencv/mingw_build/modules/gapi/backends/fluid/gfluidimgproc_func.sse4_1.cpp;D:/unet/opencv/opencv/mingw_build/modules/gapi/backends/fluid/gfluidimgproc_func.avx2.cpp
//List of wrappers supporting module opencv_gapi
OPENCV_MODULE_opencv_gapi_WRAPPERS:INTERNAL=
OPENCV_MODULE_opencv_highgui_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/highgui
//The category of the module
OPENCV_MODULE_opencv_highgui_CLASS:INTERNAL=PUBLIC
//Flattened dependencies of opencv_highgui module
OPENCV_MODULE_opencv_highgui_DEPS:INTERNAL=opencv_core;opencv_imgproc;opencv_imgcodecs;opencv_videoio
//Extra dependencies of opencv_highgui module
OPENCV_MODULE_opencv_highgui_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_highgui module (for linker)
OPENCV_MODULE_opencv_highgui_DEPS_TO_LINK:INTERNAL=opencv_core;opencv_imgproc;opencv_imgcodecs;opencv_videoio
//Brief description of opencv_highgui module
OPENCV_MODULE_opencv_highgui_DESCRIPTION:INTERNAL=High-level GUI
//List of header files for opencv_highgui
OPENCV_MODULE_opencv_highgui_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/highgui/include/opencv2/highgui.hpp;D:/unet/opencv/opencv/sources/modules/highgui/include/opencv2/highgui/highgui.hpp;D:/unet/opencv/opencv/sources/modules/highgui/include/opencv2/highgui/highgui_c.h
OPENCV_MODULE_opencv_highgui_IS_PART_OF_WORLD:INTERNAL=ON
OPENCV_MODULE_opencv_highgui_LABEL:INTERNAL=Main;opencv_highgui
OPENCV_MODULE_opencv_highgui_LINK_DEPS:INTERNAL=;comctl32;gdi32;ole32;setupapi;ws2_32
//Location of opencv_highgui module sources
OPENCV_MODULE_opencv_highgui_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/highgui
//Optional dependencies of opencv_highgui module
OPENCV_MODULE_opencv_highgui_OPT_DEPS:INTERNAL=opencv_videoio
//Optional private dependencies of opencv_highgui module
OPENCV_MODULE_opencv_highgui_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_highgui module
OPENCV_MODULE_opencv_highgui_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_highgui module
OPENCV_MODULE_opencv_highgui_REQ_DEPS:INTERNAL=opencv_imgproc;opencv_imgcodecs
//List of source files for opencv_highgui
OPENCV_MODULE_opencv_highgui_SOURCES:INTERNAL=D:/unet/opencv/opencv/sources/modules/highgui/src/window.cpp;D:/unet/opencv/opencv/sources/modules/highgui/src/roiSelector.cpp;D:/unet/opencv/opencv/sources/modules/highgui/src/window_w32.cpp;D:/unet/opencv/opencv/sources/modules/highgui/src/precomp.hpp
//List of wrappers supporting module opencv_highgui
OPENCV_MODULE_opencv_highgui_WRAPPERS:INTERNAL=java;python_bindings_generator;python2;python3
OPENCV_MODULE_opencv_imgcodecs_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/imgcodecs
//The category of the module
OPENCV_MODULE_opencv_imgcodecs_CLASS:INTERNAL=PUBLIC
//Flattened dependencies of opencv_imgcodecs module
OPENCV_MODULE_opencv_imgcodecs_DEPS:INTERNAL=opencv_core;opencv_imgproc
//Extra dependencies of opencv_imgcodecs module
OPENCV_MODULE_opencv_imgcodecs_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_imgcodecs module (for linker)
OPENCV_MODULE_opencv_imgcodecs_DEPS_TO_LINK:INTERNAL=opencv_core;opencv_imgproc
//Brief description of opencv_imgcodecs module
OPENCV_MODULE_opencv_imgcodecs_DESCRIPTION:INTERNAL=Image I/O
//List of header files for opencv_imgcodecs
OPENCV_MODULE_opencv_imgcodecs_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/imgcodecs/include/opencv2/imgcodecs.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/include/opencv2/imgcodecs/imgcodecs.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/include/opencv2/imgcodecs/imgcodecs_c.h;D:/unet/opencv/opencv/sources/modules/imgcodecs/include/opencv2/imgcodecs/ios.h;D:/unet/opencv/opencv/sources/modules/imgcodecs/include/opencv2/imgcodecs/legacy/constants_c.h
OPENCV_MODULE_opencv_imgcodecs_IS_PART_OF_WORLD:INTERNAL=ON
OPENCV_MODULE_opencv_imgcodecs_LABEL:INTERNAL=Main;opencv_imgcodecs
OPENCV_MODULE_opencv_imgcodecs_LINK_DEPS:INTERNAL=;zlib;libjpeg-turbo;libwebp;libpng;libtiff;libjasper;IlmImf
//Location of opencv_imgcodecs module sources
OPENCV_MODULE_opencv_imgcodecs_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/imgcodecs
//Optional dependencies of opencv_imgcodecs module
OPENCV_MODULE_opencv_imgcodecs_OPT_DEPS:INTERNAL=
//Optional private dependencies of opencv_imgcodecs module
OPENCV_MODULE_opencv_imgcodecs_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_imgcodecs module
OPENCV_MODULE_opencv_imgcodecs_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_imgcodecs module
OPENCV_MODULE_opencv_imgcodecs_REQ_DEPS:INTERNAL=opencv_imgproc
//List of source files for opencv_imgcodecs
OPENCV_MODULE_opencv_imgcodecs_SOURCES:INTERNAL=D:/unet/opencv/opencv/sources/modules/imgcodecs/src/loadsave.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/utils.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/precomp.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/utils.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_base.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_bmp.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_exr.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_gdal.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_gdcm.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_hdr.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_jpeg.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_jpeg2000.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_pam.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_pfm.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_png.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_pxm.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_sunras.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_tiff.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_webp.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/bitstrm.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/rgbe.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/exif.cpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_base.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_bmp.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_exr.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_gdal.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_gdcm.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_hdr.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_jpeg.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_jpeg2000.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_pam.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_pfm.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_png.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_pxm.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_sunras.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_tiff.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmt_webp.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/grfmts.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/bitstrm.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/rgbe.hpp;D:/unet/opencv/opencv/sources/modules/imgcodecs/src/exif.hpp
//List of wrappers supporting module opencv_imgcodecs
OPENCV_MODULE_opencv_imgcodecs_WRAPPERS:INTERNAL=java;python_bindings_generator;python2;python3
OPENCV_MODULE_opencv_imgproc_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/imgproc
//The category of the module
OPENCV_MODULE_opencv_imgproc_CLASS:INTERNAL=PUBLIC
//Flattened dependencies of opencv_imgproc module
OPENCV_MODULE_opencv_imgproc_DEPS:INTERNAL=opencv_core
//Extra dependencies of opencv_imgproc module
OPENCV_MODULE_opencv_imgproc_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_imgproc module (for linker)
OPENCV_MODULE_opencv_imgproc_DEPS_TO_LINK:INTERNAL=opencv_core
//Brief description of opencv_imgproc module
OPENCV_MODULE_opencv_imgproc_DESCRIPTION:INTERNAL=Image Processing
//List of header files for opencv_imgproc
OPENCV_MODULE_opencv_imgproc_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/hal/hal.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/hal/interface.h;D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/imgproc.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/imgproc_c.h;D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/types_c.h;D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc/detail/gcgraph.hpp
OPENCV_MODULE_opencv_imgproc_IS_PART_OF_WORLD:INTERNAL=ON
OPENCV_MODULE_opencv_imgproc_LABEL:INTERNAL=Main;opencv_imgproc
OPENCV_MODULE_opencv_imgproc_LINK_DEPS:INTERNAL=
//Location of opencv_imgproc module sources
OPENCV_MODULE_opencv_imgproc_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/imgproc
//Optional dependencies of opencv_imgproc module
OPENCV_MODULE_opencv_imgproc_OPT_DEPS:INTERNAL=
//Optional private dependencies of opencv_imgproc module
OPENCV_MODULE_opencv_imgproc_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_imgproc module
OPENCV_MODULE_opencv_imgproc_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_imgproc module
OPENCV_MODULE_opencv_imgproc_REQ_DEPS:INTERNAL=opencv_core
//List of source files for opencv_imgproc
OPENCV_MODULE_opencv_imgproc_SOURCES:INTERNAL=D:/unet/opencv/opencv/sources/modules/imgproc/src/accum.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/accum.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/approx.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/bilateral_filter.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/blend.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/box_filter.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/canny.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/clahe.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/color.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/color_hsv.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/color_lab.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/color_rgb.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/color_yuv.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/colormap.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/connectedcomponents.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/contours.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/convhull.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/corner.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/cornersubpix.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/demosaicing.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/deriv.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/distransform.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/drawing.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/emd.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/featureselect.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/filter.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/floodfill.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/gabor.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/generalized_hough.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/geometry.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/grabcut.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/hershey_fonts.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/histogram.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/hough.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/imgwarp.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/intersection.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/linefit.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/lsd.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/main.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/matchcontours.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/median_blur.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/min_enclosing_triangle.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/moments.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/morph.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/phasecorr.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/pyramids.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/resize.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/rotcalipers.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/samplers.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/segmentation.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/shapedescr.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/smooth.dispatch.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/spatialgradient.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/subdivision2d.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/sumpixels.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/tables.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/templmatch.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/thresh.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/utils.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/accumulate.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/bilateral.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/blend_linear.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/boxFilter.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/boxFilter3x3.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/calc_back_project.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/canny.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/clahe.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/color_hsv.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/color_lab.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/color_rgb.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/color_yuv.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/corner.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/covardata.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/filter2D.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/filter2DSmall.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/filterSepCol.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/filterSepRow.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/filterSep_singlePass.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/filterSmall.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/gaussianBlur3x3.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/gaussianBlur5x5.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/gftt.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/histogram.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/hough_lines.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/integral_sum.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/laplacian3.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/laplacian5.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/linearPolar.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/logPolar.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/match_template.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/medianFilter.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/moments.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/morph.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/morph3x3.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/precornerdetect.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/pyr_down.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/pyr_up.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/pyramid_up.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/remap.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/resize.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/sepFilter3x3.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/threshold.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/warp_affine.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/warp_perspective.cl;D:/unet/opencv/opencv/sources/modules/imgproc/src/opencl/warp_transform.cl;D:/unet/opencv/opencv/mingw_build/modules/imgproc/opencl_kernels_imgproc.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/opencl_kernels_imgproc.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/_geom.h;D:/unet/opencv/opencv/sources/modules/imgproc/src/accum.simd.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/bilateral_filter.simd.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/box_filter.simd.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/color.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/color.simd_helpers.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/color_hsv.simd.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/color_rgb.simd.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/color_yuv.simd.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/corner.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/filter.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/filter.simd.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/filterengine.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/fixedpoint.inl.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/hal_replacement.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/imgwarp.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/median_blur.simd.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/morph.simd.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/precomp.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/resize.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/smooth.simd.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/sumpixels.hpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/imgwarp.sse4_1.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/resize.sse4_1.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/accum.sse4_1.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/box_filter.sse4_1.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/filter.sse4_1.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/color_hsv.sse4_1.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/color_rgb.sse4_1.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/color_yuv.sse4_1.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/median_blur.sse4_1.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/morph.sse4_1.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/smooth.sse4_1.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/corner.avx.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/accum.avx.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/imgwarp.avx2.cpp;D:/unet/opencv/opencv/sources/modules/imgproc/src/resize.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/accum.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/bilateral_filter.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/box_filter.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/filter.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/color_hsv.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/color_rgb.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/color_yuv.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/median_blur.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/morph.avx2.cpp;D:/unet/opencv/opencv/mingw_build/modules/imgproc/smooth.avx2.cpp
//List of wrappers supporting module opencv_imgproc
OPENCV_MODULE_opencv_imgproc_WRAPPERS:INTERNAL=java;js;python_bindings_generator;python2;python3
//Location of opencv_java module sources
OPENCV_MODULE_opencv_java_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/java
//Optional dependencies of opencv_java module
OPENCV_MODULE_opencv_java_OPT_DEPS:INTERNAL=opencv_calib3d;opencv_core;opencv_dnn;opencv_features2d;opencv_highgui;opencv_imgcodecs;opencv_imgproc;opencv_ml;opencv_objdetect;opencv_photo;opencv_video;opencv_videoio
//Optional private dependencies of opencv_java module
OPENCV_MODULE_opencv_java_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_java module
OPENCV_MODULE_opencv_java_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_java module
OPENCV_MODULE_opencv_java_REQ_DEPS:INTERNAL=
//List of wrappers supporting module opencv_java
OPENCV_MODULE_opencv_java_WRAPPERS:INTERNAL=
OPENCV_MODULE_opencv_java_bindings_generator_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/java_bindings_generator
//The category of the module
OPENCV_MODULE_opencv_java_bindings_generator_CLASS:INTERNAL=INTERNAL
//Flattened dependencies of opencv_java_bindings_generator module
OPENCV_MODULE_opencv_java_bindings_generator_DEPS:INTERNAL=
//Extra dependencies of opencv_java_bindings_generator module
OPENCV_MODULE_opencv_java_bindings_generator_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_java_bindings_generator module
// (for linker)
OPENCV_MODULE_opencv_java_bindings_generator_DEPS_TO_LINK:INTERNAL=
//Brief description of opencv_java_bindings_generator module
OPENCV_MODULE_opencv_java_bindings_generator_DESCRIPTION:INTERNAL=The java_bindings_generator OpenCV module
OPENCV_MODULE_opencv_java_bindings_generator_IS_PART_OF_WORLD:INTERNAL=OFF
OPENCV_MODULE_opencv_java_bindings_generator_LABEL:INTERNAL=Main;opencv_java_bindings_generator
OPENCV_MODULE_opencv_java_bindings_generator_LINK_DEPS:INTERNAL=
//Location of opencv_java_bindings_generator module sources
OPENCV_MODULE_opencv_java_bindings_generator_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/java/generator
//Optional dependencies of opencv_java_bindings_generator module
OPENCV_MODULE_opencv_java_bindings_generator_OPT_DEPS:INTERNAL=
//Optional private dependencies of opencv_java_bindings_generator
// module
OPENCV_MODULE_opencv_java_bindings_generator_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_java_bindings_generator
// module
OPENCV_MODULE_opencv_java_bindings_generator_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_java_bindings_generator module
OPENCV_MODULE_opencv_java_bindings_generator_REQ_DEPS:INTERNAL=
//List of wrappers supporting module opencv_java_bindings_generator
OPENCV_MODULE_opencv_java_bindings_generator_WRAPPERS:INTERNAL=
//Location of opencv_js module sources
OPENCV_MODULE_opencv_js_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/js
//Optional dependencies of opencv_js module
OPENCV_MODULE_opencv_js_OPT_DEPS:INTERNAL=opencv_calib3d;opencv_core;opencv_dnn;opencv_features2d;opencv_imgproc;opencv_objdetect;opencv_photo;opencv_video
//Optional private dependencies of opencv_js module
OPENCV_MODULE_opencv_js_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_js module
OPENCV_MODULE_opencv_js_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_js module
OPENCV_MODULE_opencv_js_REQ_DEPS:INTERNAL=
//List of wrappers supporting module opencv_js
OPENCV_MODULE_opencv_js_WRAPPERS:INTERNAL=
OPENCV_MODULE_opencv_ml_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/ml
//The category of the module
OPENCV_MODULE_opencv_ml_CLASS:INTERNAL=PUBLIC
//Flattened dependencies of opencv_ml module
OPENCV_MODULE_opencv_ml_DEPS:INTERNAL=opencv_core
//Extra dependencies of opencv_ml module
OPENCV_MODULE_opencv_ml_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_ml module (for linker)
OPENCV_MODULE_opencv_ml_DEPS_TO_LINK:INTERNAL=opencv_core
//Brief description of opencv_ml module
OPENCV_MODULE_opencv_ml_DESCRIPTION:INTERNAL=Machine Learning
//List of header files for opencv_ml
OPENCV_MODULE_opencv_ml_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/ml/include/opencv2/ml.hpp;D:/unet/opencv/opencv/sources/modules/ml/include/opencv2/ml/ml.hpp;D:/unet/opencv/opencv/sources/modules/ml/include/opencv2/ml/ml.inl.hpp
OPENCV_MODULE_opencv_ml_IS_PART_OF_WORLD:INTERNAL=ON
OPENCV_MODULE_opencv_ml_LABEL:INTERNAL=Main;opencv_ml
OPENCV_MODULE_opencv_ml_LINK_DEPS:INTERNAL=
//Location of opencv_ml module sources
OPENCV_MODULE_opencv_ml_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/ml
//Optional dependencies of opencv_ml module
OPENCV_MODULE_opencv_ml_OPT_DEPS:INTERNAL=
//Optional private dependencies of opencv_ml module
OPENCV_MODULE_opencv_ml_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_ml module
OPENCV_MODULE_opencv_ml_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_ml module
OPENCV_MODULE_opencv_ml_REQ_DEPS:INTERNAL=opencv_core
//List of source files for opencv_ml
OPENCV_MODULE_opencv_ml_SOURCES:INTERNAL=D:/unet/opencv/opencv/sources/modules/ml/src/ann_mlp.cpp;D:/unet/opencv/opencv/sources/modules/ml/src/boost.cpp;D:/unet/opencv/opencv/sources/modules/ml/src/data.cpp;D:/unet/opencv/opencv/sources/modules/ml/src/em.cpp;D:/unet/opencv/opencv/sources/modules/ml/src/gbt.cpp;D:/unet/opencv/opencv/sources/modules/ml/src/inner_functions.cpp;D:/unet/opencv/opencv/sources/modules/ml/src/kdtree.cpp;D:/unet/opencv/opencv/sources/modules/ml/src/knearest.cpp;D:/unet/opencv/opencv/sources/modules/ml/src/lr.cpp;D:/unet/opencv/opencv/sources/modules/ml/src/nbayes.cpp;D:/unet/opencv/opencv/sources/modules/ml/src/rtrees.cpp;D:/unet/opencv/opencv/sources/modules/ml/src/svm.cpp;D:/unet/opencv/opencv/sources/modules/ml/src/svmsgd.cpp;D:/unet/opencv/opencv/sources/modules/ml/src/testset.cpp;D:/unet/opencv/opencv/sources/modules/ml/src/tree.cpp;D:/unet/opencv/opencv/sources/modules/ml/src/kdtree.hpp;D:/unet/opencv/opencv/sources/modules/ml/src/precomp.hpp
//List of wrappers supporting module opencv_ml
OPENCV_MODULE_opencv_ml_WRAPPERS:INTERNAL=java;python_bindings_generator;python2;python3
OPENCV_MODULE_opencv_objdetect_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/objdetect
//The category of the module
OPENCV_MODULE_opencv_objdetect_CLASS:INTERNAL=PUBLIC
//Flattened dependencies of opencv_objdetect module
OPENCV_MODULE_opencv_objdetect_DEPS:INTERNAL=opencv_core;opencv_flann;opencv_imgproc;opencv_features2d;opencv_calib3d
//Extra dependencies of opencv_objdetect module
OPENCV_MODULE_opencv_objdetect_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_objdetect module (for linker)
OPENCV_MODULE_opencv_objdetect_DEPS_TO_LINK:INTERNAL=opencv_core;opencv_flann;opencv_imgproc;opencv_features2d;opencv_calib3d
//Brief description of opencv_objdetect module
OPENCV_MODULE_opencv_objdetect_DESCRIPTION:INTERNAL=Object Detection
//List of header files for opencv_objdetect
OPENCV_MODULE_opencv_objdetect_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/objdetect/include/opencv2/objdetect.hpp;D:/unet/opencv/opencv/sources/modules/objdetect/include/opencv2/objdetect/detection_based_tracker.hpp;D:/unet/opencv/opencv/sources/modules/objdetect/include/opencv2/objdetect/objdetect.hpp
OPENCV_MODULE_opencv_objdetect_IS_PART_OF_WORLD:INTERNAL=ON
OPENCV_MODULE_opencv_objdetect_LABEL:INTERNAL=Main;opencv_objdetect
OPENCV_MODULE_opencv_objdetect_LINK_DEPS:INTERNAL=
//Location of opencv_objdetect module sources
OPENCV_MODULE_opencv_objdetect_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/objdetect
//Optional dependencies of opencv_objdetect module
OPENCV_MODULE_opencv_objdetect_OPT_DEPS:INTERNAL=
//Optional private dependencies of opencv_objdetect module
OPENCV_MODULE_opencv_objdetect_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_objdetect module
OPENCV_MODULE_opencv_objdetect_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_objdetect module
OPENCV_MODULE_opencv_objdetect_REQ_DEPS:INTERNAL=opencv_core;opencv_imgproc;opencv_calib3d
//List of source files for opencv_objdetect
OPENCV_MODULE_opencv_objdetect_SOURCES:INTERNAL=D:/unet/opencv/opencv/sources/modules/objdetect/src/cascadedetect.cpp;D:/unet/opencv/opencv/sources/modules/objdetect/src/cascadedetect_convert.cpp;D:/unet/opencv/opencv/sources/modules/objdetect/src/detection_based_tracker.cpp;D:/unet/opencv/opencv/sources/modules/objdetect/src/hog.cpp;D:/unet/opencv/opencv/sources/modules/objdetect/src/main.cpp;D:/unet/opencv/opencv/sources/modules/objdetect/src/qrcode.cpp;D:/unet/opencv/opencv/sources/modules/objdetect/src/opencl/cascadedetect.cl;D:/unet/opencv/opencv/sources/modules/objdetect/src/opencl/objdetect_hog.cl;D:/unet/opencv/opencv/mingw_build/modules/objdetect/opencl_kernels_objdetect.cpp;D:/unet/opencv/opencv/mingw_build/modules/objdetect/opencl_kernels_objdetect.hpp;D:/unet/opencv/opencv/sources/modules/objdetect/src/cascadedetect.hpp;D:/unet/opencv/opencv/sources/modules/objdetect/src/precomp.hpp
//List of wrappers supporting module opencv_objdetect
OPENCV_MODULE_opencv_objdetect_WRAPPERS:INTERNAL=java;js;python_bindings_generator;python2;python3
OPENCV_MODULE_opencv_photo_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/photo
//The category of the module
OPENCV_MODULE_opencv_photo_CLASS:INTERNAL=PUBLIC
//Flattened dependencies of opencv_photo module
OPENCV_MODULE_opencv_photo_DEPS:INTERNAL=opencv_core;opencv_imgproc
//Extra dependencies of opencv_photo module
OPENCV_MODULE_opencv_photo_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_photo module (for linker)
OPENCV_MODULE_opencv_photo_DEPS_TO_LINK:INTERNAL=opencv_core;opencv_imgproc
//Brief description of opencv_photo module
OPENCV_MODULE_opencv_photo_DESCRIPTION:INTERNAL=Computational Photography
//List of header files for opencv_photo
OPENCV_MODULE_opencv_photo_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo.hpp;D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo/cuda.hpp;D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo/legacy/constants_c.h;D:/unet/opencv/opencv/sources/modules/photo/include/opencv2/photo/photo.hpp
OPENCV_MODULE_opencv_photo_IS_PART_OF_WORLD:INTERNAL=ON
OPENCV_MODULE_opencv_photo_LABEL:INTERNAL=Main;opencv_photo
OPENCV_MODULE_opencv_photo_LINK_DEPS:INTERNAL=
//Location of opencv_photo module sources
OPENCV_MODULE_opencv_photo_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/photo
//Optional dependencies of opencv_photo module
OPENCV_MODULE_opencv_photo_OPT_DEPS:INTERNAL=opencv_cudaarithm;opencv_cudaimgproc
//Optional private dependencies of opencv_photo module
OPENCV_MODULE_opencv_photo_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_photo module
OPENCV_MODULE_opencv_photo_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_photo module
OPENCV_MODULE_opencv_photo_REQ_DEPS:INTERNAL=opencv_imgproc
//List of source files for opencv_photo
OPENCV_MODULE_opencv_photo_SOURCES:INTERNAL=D:/unet/opencv/opencv/sources/modules/photo/src/align.cpp;D:/unet/opencv/opencv/sources/modules/photo/src/calibrate.cpp;D:/unet/opencv/opencv/sources/modules/photo/src/contrast_preserve.cpp;D:/unet/opencv/opencv/sources/modules/photo/src/denoise_tvl1.cpp;D:/unet/opencv/opencv/sources/modules/photo/src/denoising.cpp;D:/unet/opencv/opencv/sources/modules/photo/src/denoising.cuda.cpp;D:/unet/opencv/opencv/sources/modules/photo/src/hdr_common.cpp;D:/unet/opencv/opencv/sources/modules/photo/src/inpaint.cpp;D:/unet/opencv/opencv/sources/modules/photo/src/merge.cpp;D:/unet/opencv/opencv/sources/modules/photo/src/npr.cpp;D:/unet/opencv/opencv/sources/modules/photo/src/seamless_cloning.cpp;D:/unet/opencv/opencv/sources/modules/photo/src/seamless_cloning_impl.cpp;D:/unet/opencv/opencv/sources/modules/photo/src/tonemap.cpp;D:/unet/opencv/opencv/sources/modules/photo/src/opencl/nlmeans.cl;D:/unet/opencv/opencv/mingw_build/modules/photo/opencl_kernels_photo.cpp;D:/unet/opencv/opencv/mingw_build/modules/photo/opencl_kernels_photo.hpp;D:/unet/opencv/opencv/sources/modules/photo/src/arrays.hpp;D:/unet/opencv/opencv/sources/modules/photo/src/contrast_preserve.hpp;D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_denoising_invoker.hpp;D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_denoising_invoker_commons.hpp;D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_denoising_opencl.hpp;D:/unet/opencv/opencv/sources/modules/photo/src/fast_nlmeans_multi_denoising_invoker.hpp;D:/unet/opencv/opencv/sources/modules/photo/src/hdr_common.hpp;D:/unet/opencv/opencv/sources/modules/photo/src/npr.hpp;D:/unet/opencv/opencv/sources/modules/photo/src/precomp.hpp;D:/unet/opencv/opencv/sources/modules/photo/src/seamless_cloning.hpp
//List of wrappers supporting module opencv_photo
OPENCV_MODULE_opencv_photo_WRAPPERS:INTERNAL=java;js;python_bindings_generator;python2;python3
//Location of opencv_python2 module sources
OPENCV_MODULE_opencv_python2_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/python/python2
//Optional dependencies of opencv_python2 module
OPENCV_MODULE_opencv_python2_OPT_DEPS:INTERNAL=opencv_calib3d;opencv_core;opencv_dnn;opencv_features2d;opencv_flann;opencv_highgui;opencv_imgcodecs;opencv_imgproc;opencv_ml;opencv_objdetect;opencv_photo;opencv_stitching;opencv_video;opencv_videoio
//Optional private dependencies of opencv_python2 module
OPENCV_MODULE_opencv_python2_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_python2 module
OPENCV_MODULE_opencv_python2_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_python2 module
OPENCV_MODULE_opencv_python2_REQ_DEPS:INTERNAL=
//List of wrappers supporting module opencv_python2
OPENCV_MODULE_opencv_python2_WRAPPERS:INTERNAL=
OPENCV_MODULE_opencv_python3_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/python3
//The category of the module
OPENCV_MODULE_opencv_python3_CLASS:INTERNAL=BINDINGS
//Flattened dependencies of opencv_python3 module
OPENCV_MODULE_opencv_python3_DEPS:INTERNAL=opencv_core;opencv_flann;opencv_imgproc;opencv_ml;opencv_photo;opencv_dnn;opencv_features2d;opencv_imgcodecs;opencv_videoio;opencv_calib3d;opencv_highgui;opencv_objdetect;opencv_stitching;opencv_video;opencv_python_bindings_generator
//Extra dependencies of opencv_python3 module
OPENCV_MODULE_opencv_python3_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_python3 module (for linker)
OPENCV_MODULE_opencv_python3_DEPS_TO_LINK:INTERNAL=opencv_core;opencv_flann;opencv_imgproc;opencv_ml;opencv_photo;opencv_dnn;opencv_features2d;opencv_imgcodecs;opencv_videoio;opencv_calib3d;opencv_highgui;opencv_objdetect;opencv_stitching;opencv_video;opencv_python_bindings_generator
//Brief description of opencv_python3 module
OPENCV_MODULE_opencv_python3_DESCRIPTION:INTERNAL=The python3 bindings
OPENCV_MODULE_opencv_python3_IS_PART_OF_WORLD:INTERNAL=OFF
OPENCV_MODULE_opencv_python3_LABEL:INTERNAL=Main;opencv_python3
OPENCV_MODULE_opencv_python3_LINK_DEPS:INTERNAL=
//Location of opencv_python3 module sources
OPENCV_MODULE_opencv_python3_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/python/python3
//Optional dependencies of opencv_python3 module
OPENCV_MODULE_opencv_python3_OPT_DEPS:INTERNAL=opencv_calib3d;opencv_core;opencv_dnn;opencv_features2d;opencv_flann;opencv_highgui;opencv_imgcodecs;opencv_imgproc;opencv_ml;opencv_objdetect;opencv_photo;opencv_stitching;opencv_video;opencv_videoio
//Optional private dependencies of opencv_python3 module
OPENCV_MODULE_opencv_python3_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_python3 module
OPENCV_MODULE_opencv_python3_PRIVATE_REQ_DEPS:INTERNAL=opencv_python_bindings_generator
//Required dependencies of opencv_python3 module
OPENCV_MODULE_opencv_python3_REQ_DEPS:INTERNAL=
//List of wrappers supporting module opencv_python3
OPENCV_MODULE_opencv_python3_WRAPPERS:INTERNAL=
OPENCV_MODULE_opencv_python_bindings_generator_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator
//The category of the module
OPENCV_MODULE_opencv_python_bindings_generator_CLASS:INTERNAL=INTERNAL
//Flattened dependencies of opencv_python_bindings_generator module
OPENCV_MODULE_opencv_python_bindings_generator_DEPS:INTERNAL=opencv_core;opencv_flann;opencv_imgproc;opencv_ml;opencv_photo;opencv_dnn;opencv_features2d;opencv_imgcodecs;opencv_videoio;opencv_calib3d;opencv_highgui;opencv_objdetect;opencv_stitching;opencv_video
//Extra dependencies of opencv_python_bindings_generator module
OPENCV_MODULE_opencv_python_bindings_generator_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_python_bindings_generator module
// (for linker)
OPENCV_MODULE_opencv_python_bindings_generator_DEPS_TO_LINK:INTERNAL=opencv_core;opencv_flann;opencv_imgproc;opencv_ml;opencv_photo;opencv_dnn;opencv_features2d;opencv_imgcodecs;opencv_videoio;opencv_calib3d;opencv_highgui;opencv_objdetect;opencv_stitching;opencv_video
//Brief description of opencv_python_bindings_generator module
OPENCV_MODULE_opencv_python_bindings_generator_DESCRIPTION:INTERNAL=The python_bindings_generator OpenCV module
OPENCV_MODULE_opencv_python_bindings_generator_IS_PART_OF_WORLD:INTERNAL=OFF
OPENCV_MODULE_opencv_python_bindings_generator_LABEL:INTERNAL=Main;opencv_python_bindings_generator
OPENCV_MODULE_opencv_python_bindings_generator_LINK_DEPS:INTERNAL=
//Location of opencv_python_bindings_generator module sources
OPENCV_MODULE_opencv_python_bindings_generator_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/python/bindings
//Optional dependencies of opencv_python_bindings_generator module
OPENCV_MODULE_opencv_python_bindings_generator_OPT_DEPS:INTERNAL=opencv_calib3d;opencv_core;opencv_dnn;opencv_features2d;opencv_flann;opencv_highgui;opencv_imgcodecs;opencv_imgproc;opencv_ml;opencv_objdetect;opencv_photo;opencv_stitching;opencv_video;opencv_videoio
//Optional private dependencies of opencv_python_bindings_generator
// module
OPENCV_MODULE_opencv_python_bindings_generator_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_python_bindings_generator
// module
OPENCV_MODULE_opencv_python_bindings_generator_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_python_bindings_generator module
OPENCV_MODULE_opencv_python_bindings_generator_REQ_DEPS:INTERNAL=
//List of wrappers supporting module opencv_python_bindings_generator
OPENCV_MODULE_opencv_python_bindings_generator_WRAPPERS:INTERNAL=
OPENCV_MODULE_opencv_python_tests_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/python_tests
//The category of the module
OPENCV_MODULE_opencv_python_tests_CLASS:INTERNAL=INTERNAL
//Flattened dependencies of opencv_python_tests module
OPENCV_MODULE_opencv_python_tests_DEPS:INTERNAL=
//Extra dependencies of opencv_python_tests module
OPENCV_MODULE_opencv_python_tests_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_python_tests module (for linker)
OPENCV_MODULE_opencv_python_tests_DEPS_TO_LINK:INTERNAL=
//Brief description of opencv_python_tests module
OPENCV_MODULE_opencv_python_tests_DESCRIPTION:INTERNAL=The python_tests OpenCV module
OPENCV_MODULE_opencv_python_tests_IS_PART_OF_WORLD:INTERNAL=OFF
OPENCV_MODULE_opencv_python_tests_LABEL:INTERNAL=Main;opencv_python_tests
OPENCV_MODULE_opencv_python_tests_LINK_DEPS:INTERNAL=
//Location of opencv_python_tests module sources
OPENCV_MODULE_opencv_python_tests_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/python/test
//Optional dependencies of opencv_python_tests module
OPENCV_MODULE_opencv_python_tests_OPT_DEPS:INTERNAL=
//Optional private dependencies of opencv_python_tests module
OPENCV_MODULE_opencv_python_tests_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_python_tests module
OPENCV_MODULE_opencv_python_tests_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_python_tests module
OPENCV_MODULE_opencv_python_tests_REQ_DEPS:INTERNAL=
//List of wrappers supporting module opencv_python_tests
OPENCV_MODULE_opencv_python_tests_WRAPPERS:INTERNAL=
OPENCV_MODULE_opencv_stitching_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/stitching
//The category of the module
OPENCV_MODULE_opencv_stitching_CLASS:INTERNAL=PUBLIC
//Flattened dependencies of opencv_stitching module
OPENCV_MODULE_opencv_stitching_DEPS:INTERNAL=opencv_core;opencv_flann;opencv_imgproc;opencv_features2d;opencv_calib3d
//Extra dependencies of opencv_stitching module
OPENCV_MODULE_opencv_stitching_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_stitching module (for linker)
OPENCV_MODULE_opencv_stitching_DEPS_TO_LINK:INTERNAL=opencv_core;opencv_flann;opencv_imgproc;opencv_features2d;opencv_calib3d
//Brief description of opencv_stitching module
OPENCV_MODULE_opencv_stitching_DESCRIPTION:INTERNAL=Images stitching
//List of header files for opencv_stitching
OPENCV_MODULE_opencv_stitching_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching.hpp;D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/warpers.hpp;D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/autocalib.hpp;D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/blenders.hpp;D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/camera.hpp;D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/exposure_compensate.hpp;D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/matchers.hpp;D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/motion_estimators.hpp;D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/seam_finders.hpp;D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/timelapsers.hpp;D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/util.hpp;D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/util_inl.hpp;D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/warpers.hpp;D:/unet/opencv/opencv/sources/modules/stitching/include/opencv2/stitching/detail/warpers_inl.hpp
OPENCV_MODULE_opencv_stitching_IS_PART_OF_WORLD:INTERNAL=ON
OPENCV_MODULE_opencv_stitching_LABEL:INTERNAL=Main;opencv_stitching
OPENCV_MODULE_opencv_stitching_LINK_DEPS:INTERNAL=
//Location of opencv_stitching module sources
OPENCV_MODULE_opencv_stitching_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/stitching
//Optional dependencies of opencv_stitching module
OPENCV_MODULE_opencv_stitching_OPT_DEPS:INTERNAL=opencv_cudaarithm;opencv_cudawarping;opencv_cudafeatures2d;opencv_cudalegacy;opencv_cudaimgproc;opencv_xfeatures2d
//Optional private dependencies of opencv_stitching module
OPENCV_MODULE_opencv_stitching_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_stitching module
OPENCV_MODULE_opencv_stitching_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_stitching module
OPENCV_MODULE_opencv_stitching_REQ_DEPS:INTERNAL=opencv_imgproc;opencv_features2d;opencv_calib3d;opencv_flann
//List of source files for opencv_stitching
OPENCV_MODULE_opencv_stitching_SOURCES:INTERNAL=D:/unet/opencv/opencv/sources/modules/stitching/src/autocalib.cpp;D:/unet/opencv/opencv/sources/modules/stitching/src/blenders.cpp;D:/unet/opencv/opencv/sources/modules/stitching/src/camera.cpp;D:/unet/opencv/opencv/sources/modules/stitching/src/exposure_compensate.cpp;D:/unet/opencv/opencv/sources/modules/stitching/src/matchers.cpp;D:/unet/opencv/opencv/sources/modules/stitching/src/motion_estimators.cpp;D:/unet/opencv/opencv/sources/modules/stitching/src/seam_finders.cpp;D:/unet/opencv/opencv/sources/modules/stitching/src/stitcher.cpp;D:/unet/opencv/opencv/sources/modules/stitching/src/timelapsers.cpp;D:/unet/opencv/opencv/sources/modules/stitching/src/util.cpp;D:/unet/opencv/opencv/sources/modules/stitching/src/warpers.cpp;D:/unet/opencv/opencv/sources/modules/stitching/src/warpers_cuda.cpp;D:/unet/opencv/opencv/sources/modules/stitching/src/opencl/multibandblend.cl;D:/unet/opencv/opencv/sources/modules/stitching/src/opencl/warpers.cl;D:/unet/opencv/opencv/mingw_build/modules/stitching/opencl_kernels_stitching.cpp;D:/unet/opencv/opencv/mingw_build/modules/stitching/opencl_kernels_stitching.hpp;D:/unet/opencv/opencv/sources/modules/stitching/src/precomp.hpp;D:/unet/opencv/opencv/sources/modules/stitching/src/util_log.hpp
//List of wrappers supporting module opencv_stitching
OPENCV_MODULE_opencv_stitching_WRAPPERS:INTERNAL=python_bindings_generator;python2;python3
OPENCV_MODULE_opencv_ts_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/ts
//The category of the module
OPENCV_MODULE_opencv_ts_CLASS:INTERNAL=INTERNAL
//Flattened dependencies of opencv_ts module (for linker)
OPENCV_MODULE_opencv_ts_DEPS_TO_LINK:INTERNAL=opencv_core;opencv_imgproc;opencv_imgcodecs;opencv_videoio;opencv_highgui
//Brief description of opencv_ts module
OPENCV_MODULE_opencv_ts_DESCRIPTION:INTERNAL=The ts module
//List of header files for opencv_ts
OPENCV_MODULE_opencv_ts_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/ts/include/opencv2/ts.hpp;D:/unet/opencv/opencv/sources/modules/ts/include/opencv2/ts/cuda_perf.hpp;D:/unet/opencv/opencv/sources/modules/ts/include/opencv2/ts/cuda_test.hpp;D:/unet/opencv/opencv/sources/modules/ts/include/opencv2/ts/ocl_perf.hpp;D:/unet/opencv/opencv/sources/modules/ts/include/opencv2/ts/ocl_test.hpp;D:/unet/opencv/opencv/sources/modules/ts/include/opencv2/ts/ts_ext.hpp;D:/unet/opencv/opencv/sources/modules/ts/include/opencv2/ts/ts_gtest.h;D:/unet/opencv/opencv/sources/modules/ts/include/opencv2/ts/ts_perf.hpp
OPENCV_MODULE_opencv_ts_IS_PART_OF_WORLD:INTERNAL=OFF
OPENCV_MODULE_opencv_ts_LABEL:INTERNAL=Main;opencv_ts
OPENCV_MODULE_opencv_ts_LINK_DEPS:INTERNAL=
//Location of opencv_ts module sources
OPENCV_MODULE_opencv_ts_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/ts
//Optional dependencies of opencv_ts module
OPENCV_MODULE_opencv_ts_OPT_DEPS:INTERNAL=
//Optional private dependencies of opencv_ts module
OPENCV_MODULE_opencv_ts_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_ts module
OPENCV_MODULE_opencv_ts_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_ts module
OPENCV_MODULE_opencv_ts_REQ_DEPS:INTERNAL=opencv_core;opencv_imgproc;opencv_imgcodecs;opencv_videoio;opencv_highgui
//List of source files for opencv_ts
OPENCV_MODULE_opencv_ts_SOURCES:INTERNAL=D:/unet/opencv/opencv/sources/modules/ts/src/cuda_perf.cpp;D:/unet/opencv/opencv/sources/modules/ts/src/cuda_test.cpp;D:/unet/opencv/opencv/sources/modules/ts/src/ocl_perf.cpp;D:/unet/opencv/opencv/sources/modules/ts/src/ocl_test.cpp;D:/unet/opencv/opencv/sources/modules/ts/src/ts.cpp;D:/unet/opencv/opencv/sources/modules/ts/src/ts_arrtest.cpp;D:/unet/opencv/opencv/sources/modules/ts/src/ts_func.cpp;D:/unet/opencv/opencv/sources/modules/ts/src/ts_gtest.cpp;D:/unet/opencv/opencv/sources/modules/ts/src/ts_perf.cpp;D:/unet/opencv/opencv/sources/modules/ts/src/ts_tags.cpp;D:/unet/opencv/opencv/sources/modules/ts/src/precomp.hpp;D:/unet/opencv/opencv/sources/modules/ts/src/ts_tags.hpp
//List of wrappers supporting module opencv_ts
OPENCV_MODULE_opencv_ts_WRAPPERS:INTERNAL=
OPENCV_MODULE_opencv_video_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/video
//The category of the module
OPENCV_MODULE_opencv_video_CLASS:INTERNAL=PUBLIC
//Flattened dependencies of opencv_video module
OPENCV_MODULE_opencv_video_DEPS:INTERNAL=opencv_core;opencv_flann;opencv_imgproc;opencv_features2d;opencv_calib3d
//Extra dependencies of opencv_video module
OPENCV_MODULE_opencv_video_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_video module (for linker)
OPENCV_MODULE_opencv_video_DEPS_TO_LINK:INTERNAL=opencv_core;opencv_flann;opencv_imgproc;opencv_features2d;opencv_calib3d
//Brief description of opencv_video module
OPENCV_MODULE_opencv_video_DESCRIPTION:INTERNAL=Video Analysis
//List of header files for opencv_video
OPENCV_MODULE_opencv_video_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video.hpp;D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video/background_segm.hpp;D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video/legacy/constants_c.h;D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video/tracking.hpp;D:/unet/opencv/opencv/sources/modules/video/include/opencv2/video/video.hpp
OPENCV_MODULE_opencv_video_IS_PART_OF_WORLD:INTERNAL=ON
OPENCV_MODULE_opencv_video_LABEL:INTERNAL=Main;opencv_video
OPENCV_MODULE_opencv_video_LINK_DEPS:INTERNAL=
//Location of opencv_video module sources
OPENCV_MODULE_opencv_video_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/video
//Optional dependencies of opencv_video module
OPENCV_MODULE_opencv_video_OPT_DEPS:INTERNAL=opencv_calib3d
//Optional private dependencies of opencv_video module
OPENCV_MODULE_opencv_video_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_video module
OPENCV_MODULE_opencv_video_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_video module
OPENCV_MODULE_opencv_video_REQ_DEPS:INTERNAL=opencv_imgproc
//List of source files for opencv_video
OPENCV_MODULE_opencv_video_SOURCES:INTERNAL=D:/unet/opencv/opencv/sources/modules/video/src/bgfg_KNN.cpp;D:/unet/opencv/opencv/sources/modules/video/src/bgfg_gaussmix2.cpp;D:/unet/opencv/opencv/sources/modules/video/src/camshift.cpp;D:/unet/opencv/opencv/sources/modules/video/src/dis_flow.cpp;D:/unet/opencv/opencv/sources/modules/video/src/ecc.cpp;D:/unet/opencv/opencv/sources/modules/video/src/kalman.cpp;D:/unet/opencv/opencv/sources/modules/video/src/lkpyramid.cpp;D:/unet/opencv/opencv/sources/modules/video/src/optflowgf.cpp;D:/unet/opencv/opencv/sources/modules/video/src/optical_flow_io.cpp;D:/unet/opencv/opencv/sources/modules/video/src/variational_refinement.cpp;D:/unet/opencv/opencv/sources/modules/video/src/opencl/bgfg_knn.cl;D:/unet/opencv/opencv/sources/modules/video/src/opencl/bgfg_mog2.cl;D:/unet/opencv/opencv/sources/modules/video/src/opencl/dis_flow.cl;D:/unet/opencv/opencv/sources/modules/video/src/opencl/optical_flow_farneback.cl;D:/unet/opencv/opencv/sources/modules/video/src/opencl/pyrlk.cl;D:/unet/opencv/opencv/mingw_build/modules/video/opencl_kernels_video.cpp;D:/unet/opencv/opencv/mingw_build/modules/video/opencl_kernels_video.hpp;D:/unet/opencv/opencv/sources/modules/video/src/lkpyramid.hpp;D:/unet/opencv/opencv/sources/modules/video/src/precomp.hpp
//List of wrappers supporting module opencv_video
OPENCV_MODULE_opencv_video_WRAPPERS:INTERNAL=java;js;python_bindings_generator;python2;python3
OPENCV_MODULE_opencv_videoio_BINARY_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/videoio
//The category of the module
OPENCV_MODULE_opencv_videoio_CLASS:INTERNAL=PUBLIC
//Flattened dependencies of opencv_videoio module
OPENCV_MODULE_opencv_videoio_DEPS:INTERNAL=opencv_core;opencv_imgproc;opencv_imgcodecs
//Extra dependencies of opencv_videoio module
OPENCV_MODULE_opencv_videoio_DEPS_EXT:INTERNAL=
//Flattened dependencies of opencv_videoio module (for linker)
OPENCV_MODULE_opencv_videoio_DEPS_TO_LINK:INTERNAL=opencv_core;opencv_imgproc;opencv_imgcodecs
//Brief description of opencv_videoio module
OPENCV_MODULE_opencv_videoio_DESCRIPTION:INTERNAL=The videoio OpenCV module
//List of header files for opencv_videoio
OPENCV_MODULE_opencv_videoio_HEADERS:INTERNAL=D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio.hpp;D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio/cap_ios.h;D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio/container_avi.private.hpp;D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio/legacy/constants_c.h;D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio/registry.hpp;D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio/videoio.hpp;D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio/videoio_c.h;D:/unet/opencv/opencv/sources/modules/videoio/src/precomp.hpp;D:/unet/opencv/opencv/sources/modules/videoio/src/cap_dshow.hpp
OPENCV_MODULE_opencv_videoio_IS_PART_OF_WORLD:INTERNAL=ON
OPENCV_MODULE_opencv_videoio_LABEL:INTERNAL=Main;opencv_videoio
OPENCV_MODULE_opencv_videoio_LINK_DEPS:INTERNAL=
//Location of opencv_videoio module sources
OPENCV_MODULE_opencv_videoio_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/videoio
//Optional dependencies of opencv_videoio module
OPENCV_MODULE_opencv_videoio_OPT_DEPS:INTERNAL=
//Optional private dependencies of opencv_videoio module
OPENCV_MODULE_opencv_videoio_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_videoio module
OPENCV_MODULE_opencv_videoio_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_videoio module
OPENCV_MODULE_opencv_videoio_REQ_DEPS:INTERNAL=opencv_imgproc;opencv_imgcodecs
//List of source files for opencv_videoio
OPENCV_MODULE_opencv_videoio_SOURCES:INTERNAL=D:/unet/opencv/opencv/sources/modules/videoio/src/videoio_registry.cpp;D:/unet/opencv/opencv/sources/modules/videoio/src/videoio_c.cpp;D:/unet/opencv/opencv/sources/modules/videoio/src/cap.cpp;D:/unet/opencv/opencv/sources/modules/videoio/src/cap_images.cpp;D:/unet/opencv/opencv/sources/modules/videoio/src/cap_mjpeg_encoder.cpp;D:/unet/opencv/opencv/sources/modules/videoio/src/cap_mjpeg_decoder.cpp;D:/unet/opencv/opencv/sources/modules/videoio/src/backend_plugin.cpp;D:/unet/opencv/opencv/sources/modules/videoio/src/backend_static.cpp;D:/unet/opencv/opencv/sources/modules/videoio/src/container_avi.cpp;D:/unet/opencv/opencv/sources/modules/videoio/src/cap_dshow.cpp
//List of wrappers supporting module opencv_videoio
OPENCV_MODULE_opencv_videoio_WRAPPERS:INTERNAL=java;python_bindings_generator;python2;python3
//The category of the module
OPENCV_MODULE_opencv_world_CLASS:INTERNAL=PUBLIC
//Brief description of opencv_world module
OPENCV_MODULE_opencv_world_DESCRIPTION:INTERNAL=All OpenCV modules
OPENCV_MODULE_opencv_world_IS_PART_OF_WORLD:INTERNAL=OFF
OPENCV_MODULE_opencv_world_LABEL:INTERNAL=Main;opencv_world
OPENCV_MODULE_opencv_world_LINK_DEPS:INTERNAL=
//Location of opencv_world module sources
OPENCV_MODULE_opencv_world_LOCATION:INTERNAL=D:/unet/opencv/opencv/sources/modules/world
//Optional dependencies of opencv_world module
OPENCV_MODULE_opencv_world_OPT_DEPS:INTERNAL=opencv_calib3d;opencv_core;opencv_dnn;opencv_features2d;opencv_flann;opencv_gapi;opencv_highgui;opencv_imgcodecs;opencv_imgproc;opencv_ml;opencv_objdetect;opencv_photo;opencv_stitching;opencv_video;opencv_videoio
//Optional private dependencies of opencv_world module
OPENCV_MODULE_opencv_world_PRIVATE_OPT_DEPS:INTERNAL=
//Required private dependencies of opencv_world module
OPENCV_MODULE_opencv_world_PRIVATE_REQ_DEPS:INTERNAL=
//Required dependencies of opencv_world module
OPENCV_MODULE_opencv_world_REQ_DEPS:INTERNAL=opencv_core
//List of wrappers supporting module opencv_world
OPENCV_MODULE_opencv_world_WRAPPERS:INTERNAL=
OPENCV_PYTHON_BINDINGS_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator
OPENCV_PYTHON_INSTALL_PATH_SETUPVARS:INTERNAL=D:/anaconda/Lib/site-packages
OPENCV_PYTHON_SIGNATURES_FILE:INTERNAL=D:/unet/opencv/opencv/mingw_build/modules/python_bindings_generator/pyopencv_signatures.json
OPENCV_PYTHON_TESTS_CONFIG_FILE:INTERNAL=D:/unet/opencv/opencv/mingw_build/opencv_python_tests.cfg
OPENCV_PYTHON_TESTS_CONFIG_FILE_DIR:INTERNAL=D:/unet/opencv/opencv/mingw_build
//List of OpenCV modules included into the world
OPENCV_WORLD_MODULES:INTERNAL=
//ADVANCED property for variable: OpenBLAS_INCLUDE_DIR
OpenBLAS_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OpenBLAS_LIB
OpenBLAS_LIB-ADVANCED:INTERNAL=1
//List of OpenCVModules targets
OpenCVModules_TARGETS:INTERNAL=opencv_core;opencv_flann;opencv_imgproc;opencv_ml;opencv_photo;opencv_dnn;opencv_features2d;opencv_gapi;opencv_imgcodecs;opencv_videoio;opencv_calib3d;opencv_highgui;opencv_objdetect;opencv_stitching;opencv_video
PYTHON2INTERP_FOUND:INTERNAL=
PYTHON2LIBS_FOUND:INTERNAL=
PYTHON2LIBS_VERSION_STRING:INTERNAL=
PYTHON2_DEBUG_LIBRARIES:INTERNAL=
PYTHON2_INCLUDE_PATH:INTERNAL=
//Python libraries
PYTHON2_LIBRARIES:INTERNAL=
PYTHON2_NUMPY_VERSION:INTERNAL=
PYTHON2_VERSION_MAJOR:INTERNAL=
PYTHON2_VERSION_MINOR:INTERNAL=
PYTHON2_VERSION_STRING:INTERNAL=
PYTHON3INTERP_FOUND:INTERNAL=TRUE
PYTHON3LIBS_FOUND:INTERNAL=TRUE
PYTHON3LIBS_VERSION_STRING:INTERNAL=3.8.8
PYTHON3_DEBUG_LIBRARIES:INTERNAL=PYTHON_DEBUG_LIBRARY-NOTFOUND
PYTHON3_INCLUDE_PATH:INTERNAL=D:/anaconda/include
//Python libraries
PYTHON3_LIBRARIES:INTERNAL=D:/anaconda/python38.dll
PYTHON3_NUMPY_VERSION:INTERNAL=1.19.5
PYTHON3_VERSION_MAJOR:INTERNAL=3
PYTHON3_VERSION_MINOR:INTERNAL=8
PYTHON3_VERSION_STRING:INTERNAL=3.8.8
Protobuf_VERSION:INTERNAL=3.5.1
Python2_EXECUTABLE:INTERNAL=Python2_EXECUTABLE-NOTFOUND
//CHECK_TYPE_SIZE: sizeof(ptrdiff_t)
SIZEOF_PTRDIFF_T:INTERNAL=8
//CHECK_TYPE_SIZE: sizeof(signed int)
SIZEOF_SIGNED_INT:INTERNAL=4
//CHECK_TYPE_SIZE: sizeof(signed long)
SIZEOF_SIGNED_LONG:INTERNAL=4
//CHECK_TYPE_SIZE: sizeof(signed long long)
SIZEOF_SIGNED_LONG_LONG:INTERNAL=8
//CHECK_TYPE_SIZE: sizeof(signed short)
SIZEOF_SIGNED_SHORT:INTERNAL=2
//CHECK_TYPE_SIZE: sizeof(size_t)
SIZEOF_SIZE_T:INTERNAL=8
//CHECK_TYPE_SIZE: sizeof(unsigned char *)
SIZEOF_UNSIGNED_CHAR_P:INTERNAL=8
//CHECK_TYPE_SIZE: sizeof(unsigned int)
SIZEOF_UNSIGNED_INT:INTERNAL=4
//CHECK_TYPE_SIZE: sizeof(unsigned long)
SIZEOF_UNSIGNED_LONG:INTERNAL=4
//CHECK_TYPE_SIZE: sizeof(unsigned long long)
SIZEOF_UNSIGNED_LONG_LONG:INTERNAL=8
//CHECK_TYPE_SIZE: sizeof(unsigned short)
SIZEOF_UNSIGNED_SHORT:INTERNAL=2
//ADVANCED property for variable: VIDEOIO_ENABLE_PLUGINS
VIDEOIO_ENABLE_PLUGINS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: VIDEOIO_ENABLE_STRICT_PLUGIN_CHECK
VIDEOIO_ENABLE_STRICT_PLUGIN_CHECK-ADVANCED:INTERNAL=1
//ADVANCED property for variable: VIDEOIO_PLUGIN_LIST
VIDEOIO_PLUGIN_LIST-ADVANCED:INTERNAL=1
//Result of TEST_BIG_ENDIAN
WORDS_BIGENDIAN:INTERNAL=0
//Have include unistd.h
Z_HAVE_UNISTD_H:INTERNAL=1
__INSTALL_PATH_PYTHON3:INTERNAL=D:/anaconda/Lib/site-packages/cv2/python-3.8
__OPENCV_CMAKE_HOOKS_INIT_MODULE_SOURCES_opencv_dnn:INTERNAL=D:/unet/opencv/opencv/sources/modules/dnn/cmake/hooks/INIT_MODULE_SOURCES_opencv_dnn.cmake
//Result of TRY_COMPILE
__VALID_DIRECTX:INTERNAL=FALSE
//Result of TRY_COMPILE
__VALID_OPENCL:INTERNAL=FALSE
//Result of TEST_BIG_ENDIAN
bigendian:INTERNAL=0
//CHECK_TYPE_SIZE: INT16 unknown
int16:INTERNAL=
//CHECK_TYPE_SIZE: INT32 unknown
int32:INTERNAL=
//CHECK_TYPE_SIZE: INT8 unknown
int8:INTERNAL=

