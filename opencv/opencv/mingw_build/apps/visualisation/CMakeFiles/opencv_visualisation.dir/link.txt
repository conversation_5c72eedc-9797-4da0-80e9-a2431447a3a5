"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\opencv_visualisation.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\opencv_visualisation.dir/objects.a @CMakeFiles\opencv_visualisation.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wundef -Winit-self -Wpointer-arith -Wshadow -Wsign-promo -Wuninitialized -Winit-self -Wsuggest-override -Wno-delete-non-virtual-dtor -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -fvisibility-inlines-hidden -O3 -DNDEBUG  -DNDEBUG    -Wl,--gc-sections   -Wl,--whole-archive CMakeFiles\opencv_visualisation.dir/objects.a -Wl,--no-whole-archive  -o ..\..\bin\opencv_visualisation.exe -Wl,--out-implib,..\..\lib\libopencv_visualisation.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\opencv_visualisation.dir\linklibs.rsp
