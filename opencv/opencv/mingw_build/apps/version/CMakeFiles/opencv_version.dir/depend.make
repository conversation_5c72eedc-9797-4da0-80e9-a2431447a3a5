# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/apps/version/opencv_version.cpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/base.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/bufferpool.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/check.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_dispatch.h
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_helper.h
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvdef.h
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.inl.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd_wrapper.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/fast_math.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/interface.h
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/msa_macros.h
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.inl.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/matx.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/neon_utils.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/opencl_info.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/operations.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/optim.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ovx.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/persistence.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/saturate.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/traits.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/types.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utility.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/instrumentation.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/tls.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/trace.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/version.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/vsx_utils.hpp
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: cv_cpu_config.h
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: cvconfig.h
apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj: opencv2/opencv_modules.hpp

