# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

apps/version/CMakeFiles/opencv_version.dir/opencv_version.cpp.obj
 D:/unet/opencv/opencv/sources/apps/version/opencv_version.cpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/base.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/bufferpool.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/check.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_dispatch.h
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_helper.h
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvdef.h
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.inl.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd_wrapper.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/fast_math.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/interface.h
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/msa_macros.h
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.inl.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/matx.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/neon_utils.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ocl.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencl/opencl_info.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/operations.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/optim.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ovx.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/persistence.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/saturate.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/traits.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/types.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utility.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/instrumentation.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/tls.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/trace.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/version.hpp
 D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/vsx_utils.hpp
 cv_cpu_config.h
 cvconfig.h
 opencv2/opencv_modules.hpp
