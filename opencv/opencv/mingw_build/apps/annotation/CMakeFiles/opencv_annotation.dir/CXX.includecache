#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

D:/unet/opencv/opencv/sources/apps/annotation/opencv_annotation.cpp
opencv2/core.hpp
-
opencv2/highgui.hpp
-
opencv2/imgcodecs.hpp
-
opencv2/videoio.hpp
-
opencv2/imgproc.hpp
-
fstream
-
iostream
-
map
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/cvdef.h
opencv2/core/version.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/version.hpp
opencv2/core/base.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/base.hpp
opencv2/core/cvstd.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/cvstd.hpp
opencv2/core/traits.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/traits.hpp
opencv2/core/matx.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/matx.hpp
opencv2/core/types.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/types.hpp
opencv2/core/mat.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/mat.hpp
opencv2/core/persistence.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/persistence.hpp
opencv2/core/operations.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/operations.hpp
opencv2/core/cvstd.inl.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/cvstd.inl.hpp
opencv2/core/utility.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/utility.hpp
opencv2/core/optim.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/optim.hpp
opencv2/core/ovx.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/opencv2/core/ovx.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/base.hpp
opencv2/opencv_modules.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/opencv_modules.hpp
climits
-
algorithm
-
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
opencv2/core/cvstd.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvstd.hpp
opencv2/core/neon_utils.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/neon_utils.hpp
opencv2/core/vsx_utils.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/vsx_utils.hpp
opencv2/core/check.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/check.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/bufferpool.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/check.hpp
opencv2/core/base.hpp
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_dispatch.h
cv_cpu_config.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_config.h
cv_cpu_helper.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_helper.h
emmintrin.h
-
pmmintrin.h
-
tmmintrin.h
-
smmintrin.h
-
nmmintrin.h
-
nmmintrin.h
-
popcntintrin.h
-
immintrin.h
-
arm_neon.h
-
immintrin.h
-
immintrin.h
-
immintrin.h
-
Intrin.h
-
arm_neon.h
-
arm_neon.h
-
arm_neon.h
-
altivec.h
-
hal/msa_macros.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/msa_macros.h
wasm_simd128.h
-
emmintrin.h
-
Intrin.h
-
arm_neon.h
-
arm_neon.h
-
altivec.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_helper.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvdef.h
cvconfig.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvconfig.h
limits.h
-
opencv2/core/hal/interface.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/hal/interface.h
cv_cpu_dispatch.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_dispatch.h
intrin.h
-
array
-
cstdint
-
stdint.h
-
stdint.h
-
opencv2/core/fast_math.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/fast_math.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
cstddef
-
cstring
-
cctype
-
string
-
algorithm
-
utility
-
cstdlib
-
cmath
-
cvstd_wrapper.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd_wrapper.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.inl.hpp
complex
-
ostream
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd_wrapper.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
string
-
memory
-
type_traits
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/fast_math.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
cmath
-
fastmath.h
-
math.h
-
emmintrin.h
-
altivec.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/interface.h
cstddef
-
stddef.h
-
stdbool.h
-
cstdint
-
stdint.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/msa_macros.h
msa.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/msa.h
stdint.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.hpp
opencv2/core/matx.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/matx.hpp
opencv2/core/types.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/types.hpp
opencv2/core/bufferpool.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/bufferpool.hpp
type_traits
-
opencv2/core/mat.inl.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/mat.inl.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.inl.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/matx.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
opencv2/core/base.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/base.hpp
opencv2/core/traits.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/traits.hpp
opencv2/core/saturate.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/saturate.hpp
initializer_list
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/neon_utils.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/operations.hpp
cstdio
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/optim.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ovx.hpp
cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvdef.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/persistence.hpp
opencv2/core/types.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/types.hpp
opencv2/core/mat.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/mat.hpp
opencv2/opencv.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/opencv.hpp
time.h
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/saturate.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
opencv2/core/fast_math.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/fast_math.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/traits.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/types.hpp
climits
-
cfloat
-
vector
-
limits
-
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
opencv2/core/cvstd.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvstd.hpp
opencv2/core/matx.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/matx.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utility.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core.hpp
ostream
-
functional
-
mutex
-
opencv2/core/utils/instrumentation.hpp
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/utils/instrumentation.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/instrumentation.hpp
opencv2/core/utility.hpp
-
opencv2/core/utils/tls.hpp
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/tls.hpp
opencv2/core/utility.hpp
-

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/version.hpp

D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/vsx_utils.hpp
opencv2/core/cvdef.h
D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/opencv2/core/cvdef.h
assert.h
-

D:/unet/opencv/opencv/sources/modules/highgui/include/opencv2/highgui.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/highgui/include/opencv2/opencv2/core.hpp
opencv2/imgcodecs.hpp
D:/unet/opencv/opencv/sources/modules/highgui/include/opencv2/opencv2/imgcodecs.hpp
opencv2/videoio.hpp
D:/unet/opencv/opencv/sources/modules/highgui/include/opencv2/opencv2/videoio.hpp

D:/unet/opencv/opencv/sources/modules/imgcodecs/include/opencv2/imgcodecs.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/imgcodecs/include/opencv2/opencv2/core.hpp

D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/opencv2/core.hpp

D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio.hpp
opencv2/core.hpp
D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/opencv2/core.hpp

cv_cpu_config.h

cvconfig.h

opencv2/opencv_modules.hpp

