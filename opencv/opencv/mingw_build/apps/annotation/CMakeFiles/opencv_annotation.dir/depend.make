# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/apps/annotation/opencv_annotation.cpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/base.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/bufferpool.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/check.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_dispatch.h
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cv_cpu_helper.h
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvdef.h
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd.inl.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/cvstd_wrapper.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/fast_math.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/interface.h
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/hal/msa_macros.h
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/mat.inl.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/matx.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/neon_utils.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/operations.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/optim.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/ovx.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/persistence.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/saturate.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/traits.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/types.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utility.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/instrumentation.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/utils/tls.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/version.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/core/include/opencv2/core/vsx_utils.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/highgui/include/opencv2/highgui.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/imgcodecs/include/opencv2/imgcodecs.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/imgproc/include/opencv2/imgproc.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: D:/unet/opencv/opencv/sources/modules/videoio/include/opencv2/videoio.hpp
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: cv_cpu_config.h
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: cvconfig.h
apps/annotation/CMakeFiles/opencv_annotation.dir/opencv_annotation.cpp.obj: opencv2/opencv_modules.hpp

