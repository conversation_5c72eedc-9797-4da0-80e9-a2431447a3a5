# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "C"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_C
  "D:/unet/opencv/opencv/sources/3rdparty/zlib/adler32.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/adler32.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib/compress.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/compress.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib/crc32.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/crc32.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib/deflate.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/deflate.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib/gzclose.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/gzclose.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib/gzlib.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/gzlib.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib/gzread.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/gzread.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib/gzwrite.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/gzwrite.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib/infback.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/infback.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib/inffast.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/inffast.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib/inflate.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/inflate.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib/inftrees.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/inftrees.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib/trees.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/trees.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib/uncompr.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/uncompr.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib/zutil.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/zutil.c.obj"
  )
set(CMAKE_C_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_C
  "Z_HAVE_UNISTD_H"
  "_LARGEFILE64_SOURCE=1"
  "_WIN32_WINNT=0x0601"
  )

# The include file search paths:
set(CMAKE_C_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/3rdparty/zlib"
  "."
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
