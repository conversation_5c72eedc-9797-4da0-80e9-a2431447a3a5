#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

D:/unet/opencv/opencv/sources/3rdparty/zlib/adler32.c
zutil.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zutil.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/compress.c
zlib.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zlib.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/crc32.c
stdio.h
-
zutil.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zutil.h
crc32.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/crc32.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/crc32.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/deflate.c
deflate.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/deflate.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/deflate.h
zutil.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zutil.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/gzclose.c
gzguts.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/gzguts.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/gzguts.h
stdio.h
-
zlib.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zlib.h
string.h
-
stdlib.h
-
limits.h
-
fcntl.h
-
stddef.h
-
io.h
-
windows.h
-
errno.h
-

D:/unet/opencv/opencv/sources/3rdparty/zlib/gzlib.c
gzguts.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/gzguts.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/gzread.c
gzguts.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/gzguts.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/gzwrite.c
gzguts.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/gzguts.h
stdarg.h
-

D:/unet/opencv/opencv/sources/3rdparty/zlib/infback.c
zutil.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zutil.h
inftrees.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/inftrees.h
inflate.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/inflate.h
inffast.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/inffast.h
inffixed.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/inffixed.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/inffast.c
zutil.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zutil.h
inftrees.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/inftrees.h
inflate.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/inflate.h
inffast.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/inffast.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/inffast.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/inffixed.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/inflate.c
zutil.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zutil.h
inftrees.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/inftrees.h
inflate.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/inflate.h
inffast.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/inffast.h
inffixed.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/inffixed.h
stdio.h
-

D:/unet/opencv/opencv/sources/3rdparty/zlib/inflate.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/inftrees.c
zutil.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zutil.h
inftrees.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/inftrees.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/inftrees.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/trees.c
deflate.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/deflate.h
ctype.h
-
trees.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/trees.h
stdio.h
-

D:/unet/opencv/opencv/sources/3rdparty/zlib/trees.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/uncompr.c
zlib.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zlib.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/zconf.h
stddef.h
-
windows.h
-
limits.h
-
sys/types.h
-
stdarg.h
-
stddef.h
-
unistd.h
-
unixio.h
-

D:/unet/opencv/opencv/sources/3rdparty/zlib/zlib.h
zconf.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zconf.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/zutil.c
zutil.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zutil.h
gzguts.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/gzguts.h
stdlib.h
-

D:/unet/opencv/opencv/sources/3rdparty/zlib/zutil.h
zlib.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zlib.h
stddef.h
-
string.h
-
stdlib.h
-
alloc.h
-
malloc.h
-
malloc.h
-
unix.h
-
stdio.h
-

