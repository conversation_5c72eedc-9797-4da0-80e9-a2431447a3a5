# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

# Include any dependencies generated for this target.
include 3rdparty/zlib/CMakeFiles/zlib.dir/depend.make

# Include the progress variables for this target.
include 3rdparty/zlib/CMakeFiles/zlib.dir/progress.make

# Include the compile flags for this target's objects.
include 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make

3rdparty/zlib/CMakeFiles/zlib.dir/adler32.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make
3rdparty/zlib/CMakeFiles/zlib.dir/adler32.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/includes_C.rsp
3rdparty/zlib/CMakeFiles/zlib.dir/adler32.c.obj: D:/unet/opencv/opencv/sources/3rdparty/zlib/adler32.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object 3rdparty/zlib/CMakeFiles/zlib.dir/adler32.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\zlib.dir\adler32.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\zlib\adler32.c

3rdparty/zlib/CMakeFiles/zlib.dir/adler32.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/zlib.dir/adler32.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\zlib\adler32.c > CMakeFiles\zlib.dir\adler32.c.i

3rdparty/zlib/CMakeFiles/zlib.dir/adler32.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/zlib.dir/adler32.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\zlib\adler32.c -o CMakeFiles\zlib.dir\adler32.c.s

3rdparty/zlib/CMakeFiles/zlib.dir/compress.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make
3rdparty/zlib/CMakeFiles/zlib.dir/compress.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/includes_C.rsp
3rdparty/zlib/CMakeFiles/zlib.dir/compress.c.obj: D:/unet/opencv/opencv/sources/3rdparty/zlib/compress.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object 3rdparty/zlib/CMakeFiles/zlib.dir/compress.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\zlib.dir\compress.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\zlib\compress.c

3rdparty/zlib/CMakeFiles/zlib.dir/compress.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/zlib.dir/compress.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\zlib\compress.c > CMakeFiles\zlib.dir\compress.c.i

3rdparty/zlib/CMakeFiles/zlib.dir/compress.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/zlib.dir/compress.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\zlib\compress.c -o CMakeFiles\zlib.dir\compress.c.s

3rdparty/zlib/CMakeFiles/zlib.dir/crc32.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make
3rdparty/zlib/CMakeFiles/zlib.dir/crc32.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/includes_C.rsp
3rdparty/zlib/CMakeFiles/zlib.dir/crc32.c.obj: D:/unet/opencv/opencv/sources/3rdparty/zlib/crc32.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object 3rdparty/zlib/CMakeFiles/zlib.dir/crc32.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\zlib.dir\crc32.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\zlib\crc32.c

3rdparty/zlib/CMakeFiles/zlib.dir/crc32.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/zlib.dir/crc32.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\zlib\crc32.c > CMakeFiles\zlib.dir\crc32.c.i

3rdparty/zlib/CMakeFiles/zlib.dir/crc32.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/zlib.dir/crc32.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\zlib\crc32.c -o CMakeFiles\zlib.dir\crc32.c.s

3rdparty/zlib/CMakeFiles/zlib.dir/deflate.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make
3rdparty/zlib/CMakeFiles/zlib.dir/deflate.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/includes_C.rsp
3rdparty/zlib/CMakeFiles/zlib.dir/deflate.c.obj: D:/unet/opencv/opencv/sources/3rdparty/zlib/deflate.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object 3rdparty/zlib/CMakeFiles/zlib.dir/deflate.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\zlib.dir\deflate.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\zlib\deflate.c

3rdparty/zlib/CMakeFiles/zlib.dir/deflate.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/zlib.dir/deflate.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\zlib\deflate.c > CMakeFiles\zlib.dir\deflate.c.i

3rdparty/zlib/CMakeFiles/zlib.dir/deflate.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/zlib.dir/deflate.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\zlib\deflate.c -o CMakeFiles\zlib.dir\deflate.c.s

3rdparty/zlib/CMakeFiles/zlib.dir/gzclose.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make
3rdparty/zlib/CMakeFiles/zlib.dir/gzclose.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/includes_C.rsp
3rdparty/zlib/CMakeFiles/zlib.dir/gzclose.c.obj: D:/unet/opencv/opencv/sources/3rdparty/zlib/gzclose.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object 3rdparty/zlib/CMakeFiles/zlib.dir/gzclose.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\zlib.dir\gzclose.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\zlib\gzclose.c

3rdparty/zlib/CMakeFiles/zlib.dir/gzclose.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/zlib.dir/gzclose.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\zlib\gzclose.c > CMakeFiles\zlib.dir\gzclose.c.i

3rdparty/zlib/CMakeFiles/zlib.dir/gzclose.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/zlib.dir/gzclose.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\zlib\gzclose.c -o CMakeFiles\zlib.dir\gzclose.c.s

3rdparty/zlib/CMakeFiles/zlib.dir/gzlib.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make
3rdparty/zlib/CMakeFiles/zlib.dir/gzlib.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/includes_C.rsp
3rdparty/zlib/CMakeFiles/zlib.dir/gzlib.c.obj: D:/unet/opencv/opencv/sources/3rdparty/zlib/gzlib.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object 3rdparty/zlib/CMakeFiles/zlib.dir/gzlib.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\zlib.dir\gzlib.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\zlib\gzlib.c

3rdparty/zlib/CMakeFiles/zlib.dir/gzlib.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/zlib.dir/gzlib.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\zlib\gzlib.c > CMakeFiles\zlib.dir\gzlib.c.i

3rdparty/zlib/CMakeFiles/zlib.dir/gzlib.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/zlib.dir/gzlib.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\zlib\gzlib.c -o CMakeFiles\zlib.dir\gzlib.c.s

3rdparty/zlib/CMakeFiles/zlib.dir/gzread.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make
3rdparty/zlib/CMakeFiles/zlib.dir/gzread.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/includes_C.rsp
3rdparty/zlib/CMakeFiles/zlib.dir/gzread.c.obj: D:/unet/opencv/opencv/sources/3rdparty/zlib/gzread.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object 3rdparty/zlib/CMakeFiles/zlib.dir/gzread.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\zlib.dir\gzread.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\zlib\gzread.c

3rdparty/zlib/CMakeFiles/zlib.dir/gzread.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/zlib.dir/gzread.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\zlib\gzread.c > CMakeFiles\zlib.dir\gzread.c.i

3rdparty/zlib/CMakeFiles/zlib.dir/gzread.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/zlib.dir/gzread.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\zlib\gzread.c -o CMakeFiles\zlib.dir\gzread.c.s

3rdparty/zlib/CMakeFiles/zlib.dir/gzwrite.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make
3rdparty/zlib/CMakeFiles/zlib.dir/gzwrite.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/includes_C.rsp
3rdparty/zlib/CMakeFiles/zlib.dir/gzwrite.c.obj: D:/unet/opencv/opencv/sources/3rdparty/zlib/gzwrite.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object 3rdparty/zlib/CMakeFiles/zlib.dir/gzwrite.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\zlib.dir\gzwrite.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\zlib\gzwrite.c

3rdparty/zlib/CMakeFiles/zlib.dir/gzwrite.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/zlib.dir/gzwrite.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\zlib\gzwrite.c > CMakeFiles\zlib.dir\gzwrite.c.i

3rdparty/zlib/CMakeFiles/zlib.dir/gzwrite.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/zlib.dir/gzwrite.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\zlib\gzwrite.c -o CMakeFiles\zlib.dir\gzwrite.c.s

3rdparty/zlib/CMakeFiles/zlib.dir/inflate.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make
3rdparty/zlib/CMakeFiles/zlib.dir/inflate.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/includes_C.rsp
3rdparty/zlib/CMakeFiles/zlib.dir/inflate.c.obj: D:/unet/opencv/opencv/sources/3rdparty/zlib/inflate.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object 3rdparty/zlib/CMakeFiles/zlib.dir/inflate.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\zlib.dir\inflate.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\zlib\inflate.c

3rdparty/zlib/CMakeFiles/zlib.dir/inflate.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/zlib.dir/inflate.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\zlib\inflate.c > CMakeFiles\zlib.dir\inflate.c.i

3rdparty/zlib/CMakeFiles/zlib.dir/inflate.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/zlib.dir/inflate.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\zlib\inflate.c -o CMakeFiles\zlib.dir\inflate.c.s

3rdparty/zlib/CMakeFiles/zlib.dir/infback.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make
3rdparty/zlib/CMakeFiles/zlib.dir/infback.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/includes_C.rsp
3rdparty/zlib/CMakeFiles/zlib.dir/infback.c.obj: D:/unet/opencv/opencv/sources/3rdparty/zlib/infback.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object 3rdparty/zlib/CMakeFiles/zlib.dir/infback.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\zlib.dir\infback.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\zlib\infback.c

3rdparty/zlib/CMakeFiles/zlib.dir/infback.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/zlib.dir/infback.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\zlib\infback.c > CMakeFiles\zlib.dir\infback.c.i

3rdparty/zlib/CMakeFiles/zlib.dir/infback.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/zlib.dir/infback.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\zlib\infback.c -o CMakeFiles\zlib.dir\infback.c.s

3rdparty/zlib/CMakeFiles/zlib.dir/inftrees.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make
3rdparty/zlib/CMakeFiles/zlib.dir/inftrees.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/includes_C.rsp
3rdparty/zlib/CMakeFiles/zlib.dir/inftrees.c.obj: D:/unet/opencv/opencv/sources/3rdparty/zlib/inftrees.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object 3rdparty/zlib/CMakeFiles/zlib.dir/inftrees.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\zlib.dir\inftrees.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\zlib\inftrees.c

3rdparty/zlib/CMakeFiles/zlib.dir/inftrees.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/zlib.dir/inftrees.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\zlib\inftrees.c > CMakeFiles\zlib.dir\inftrees.c.i

3rdparty/zlib/CMakeFiles/zlib.dir/inftrees.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/zlib.dir/inftrees.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\zlib\inftrees.c -o CMakeFiles\zlib.dir\inftrees.c.s

3rdparty/zlib/CMakeFiles/zlib.dir/inffast.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make
3rdparty/zlib/CMakeFiles/zlib.dir/inffast.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/includes_C.rsp
3rdparty/zlib/CMakeFiles/zlib.dir/inffast.c.obj: D:/unet/opencv/opencv/sources/3rdparty/zlib/inffast.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object 3rdparty/zlib/CMakeFiles/zlib.dir/inffast.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\zlib.dir\inffast.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\zlib\inffast.c

3rdparty/zlib/CMakeFiles/zlib.dir/inffast.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/zlib.dir/inffast.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\zlib\inffast.c > CMakeFiles\zlib.dir\inffast.c.i

3rdparty/zlib/CMakeFiles/zlib.dir/inffast.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/zlib.dir/inffast.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\zlib\inffast.c -o CMakeFiles\zlib.dir\inffast.c.s

3rdparty/zlib/CMakeFiles/zlib.dir/trees.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make
3rdparty/zlib/CMakeFiles/zlib.dir/trees.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/includes_C.rsp
3rdparty/zlib/CMakeFiles/zlib.dir/trees.c.obj: D:/unet/opencv/opencv/sources/3rdparty/zlib/trees.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object 3rdparty/zlib/CMakeFiles/zlib.dir/trees.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\zlib.dir\trees.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\zlib\trees.c

3rdparty/zlib/CMakeFiles/zlib.dir/trees.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/zlib.dir/trees.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\zlib\trees.c > CMakeFiles\zlib.dir\trees.c.i

3rdparty/zlib/CMakeFiles/zlib.dir/trees.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/zlib.dir/trees.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\zlib\trees.c -o CMakeFiles\zlib.dir\trees.c.s

3rdparty/zlib/CMakeFiles/zlib.dir/uncompr.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make
3rdparty/zlib/CMakeFiles/zlib.dir/uncompr.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/includes_C.rsp
3rdparty/zlib/CMakeFiles/zlib.dir/uncompr.c.obj: D:/unet/opencv/opencv/sources/3rdparty/zlib/uncompr.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object 3rdparty/zlib/CMakeFiles/zlib.dir/uncompr.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\zlib.dir\uncompr.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\zlib\uncompr.c

3rdparty/zlib/CMakeFiles/zlib.dir/uncompr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/zlib.dir/uncompr.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\zlib\uncompr.c > CMakeFiles\zlib.dir\uncompr.c.i

3rdparty/zlib/CMakeFiles/zlib.dir/uncompr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/zlib.dir/uncompr.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\zlib\uncompr.c -o CMakeFiles\zlib.dir\uncompr.c.s

3rdparty/zlib/CMakeFiles/zlib.dir/zutil.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/flags.make
3rdparty/zlib/CMakeFiles/zlib.dir/zutil.c.obj: 3rdparty/zlib/CMakeFiles/zlib.dir/includes_C.rsp
3rdparty/zlib/CMakeFiles/zlib.dir/zutil.c.obj: D:/unet/opencv/opencv/sources/3rdparty/zlib/zutil.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object 3rdparty/zlib/CMakeFiles/zlib.dir/zutil.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\zlib.dir\zutil.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\zlib\zutil.c

3rdparty/zlib/CMakeFiles/zlib.dir/zutil.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/zlib.dir/zutil.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\zlib\zutil.c > CMakeFiles\zlib.dir\zutil.c.i

3rdparty/zlib/CMakeFiles/zlib.dir/zutil.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/zlib.dir/zutil.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\zlib\zutil.c -o CMakeFiles\zlib.dir\zutil.c.s

# Object files for target zlib
zlib_OBJECTS = \
"CMakeFiles/zlib.dir/adler32.c.obj" \
"CMakeFiles/zlib.dir/compress.c.obj" \
"CMakeFiles/zlib.dir/crc32.c.obj" \
"CMakeFiles/zlib.dir/deflate.c.obj" \
"CMakeFiles/zlib.dir/gzclose.c.obj" \
"CMakeFiles/zlib.dir/gzlib.c.obj" \
"CMakeFiles/zlib.dir/gzread.c.obj" \
"CMakeFiles/zlib.dir/gzwrite.c.obj" \
"CMakeFiles/zlib.dir/inflate.c.obj" \
"CMakeFiles/zlib.dir/infback.c.obj" \
"CMakeFiles/zlib.dir/inftrees.c.obj" \
"CMakeFiles/zlib.dir/inffast.c.obj" \
"CMakeFiles/zlib.dir/trees.c.obj" \
"CMakeFiles/zlib.dir/uncompr.c.obj" \
"CMakeFiles/zlib.dir/zutil.c.obj"

# External object files for target zlib
zlib_EXTERNAL_OBJECTS =

3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/adler32.c.obj
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/compress.c.obj
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/crc32.c.obj
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/deflate.c.obj
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/gzclose.c.obj
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/gzlib.c.obj
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/gzread.c.obj
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/gzwrite.c.obj
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/inflate.c.obj
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/infback.c.obj
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/inftrees.c.obj
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/inffast.c.obj
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/trees.c.obj
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/uncompr.c.obj
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/zutil.c.obj
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/build.make
3rdparty/lib/libzlib.a: 3rdparty/zlib/CMakeFiles/zlib.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Linking C static library ..\lib\libzlib.a"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && $(CMAKE_COMMAND) -P CMakeFiles\zlib.dir\cmake_clean_target.cmake
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\zlib.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
3rdparty/zlib/CMakeFiles/zlib.dir/build: 3rdparty/lib/libzlib.a

.PHONY : 3rdparty/zlib/CMakeFiles/zlib.dir/build

3rdparty/zlib/CMakeFiles/zlib.dir/clean:
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\zlib && $(CMAKE_COMMAND) -P CMakeFiles\zlib.dir\cmake_clean.cmake
.PHONY : 3rdparty/zlib/CMakeFiles/zlib.dir/clean

3rdparty/zlib/CMakeFiles/zlib.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\unet\opencv\opencv\sources D:\unet\opencv\opencv\sources\3rdparty\zlib D:\unet\opencv\opencv\mingw_build D:\unet\opencv\opencv\mingw_build\3rdparty\zlib D:\unet\opencv\opencv\mingw_build\3rdparty\zlib\CMakeFiles\zlib.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : 3rdparty/zlib/CMakeFiles/zlib.dir/depend

