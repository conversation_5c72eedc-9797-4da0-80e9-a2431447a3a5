# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\3rdparty\zlib\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/zlib/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/zlib/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/zlib/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/zlib/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
3rdparty/zlib/CMakeFiles/zlib.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/zlib/CMakeFiles/zlib.dir/rule
.PHONY : 3rdparty/zlib/CMakeFiles/zlib.dir/rule

# Convenience name for target.
zlib: 3rdparty/zlib/CMakeFiles/zlib.dir/rule

.PHONY : zlib

# fast build rule for target.
zlib/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/build
.PHONY : zlib/fast

adler32.obj: adler32.c.obj

.PHONY : adler32.obj

# target to build an object file
adler32.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/adler32.c.obj
.PHONY : adler32.c.obj

adler32.i: adler32.c.i

.PHONY : adler32.i

# target to preprocess a source file
adler32.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/adler32.c.i
.PHONY : adler32.c.i

adler32.s: adler32.c.s

.PHONY : adler32.s

# target to generate assembly for a file
adler32.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/adler32.c.s
.PHONY : adler32.c.s

compress.obj: compress.c.obj

.PHONY : compress.obj

# target to build an object file
compress.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/compress.c.obj
.PHONY : compress.c.obj

compress.i: compress.c.i

.PHONY : compress.i

# target to preprocess a source file
compress.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/compress.c.i
.PHONY : compress.c.i

compress.s: compress.c.s

.PHONY : compress.s

# target to generate assembly for a file
compress.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/compress.c.s
.PHONY : compress.c.s

crc32.obj: crc32.c.obj

.PHONY : crc32.obj

# target to build an object file
crc32.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/crc32.c.obj
.PHONY : crc32.c.obj

crc32.i: crc32.c.i

.PHONY : crc32.i

# target to preprocess a source file
crc32.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/crc32.c.i
.PHONY : crc32.c.i

crc32.s: crc32.c.s

.PHONY : crc32.s

# target to generate assembly for a file
crc32.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/crc32.c.s
.PHONY : crc32.c.s

deflate.obj: deflate.c.obj

.PHONY : deflate.obj

# target to build an object file
deflate.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/deflate.c.obj
.PHONY : deflate.c.obj

deflate.i: deflate.c.i

.PHONY : deflate.i

# target to preprocess a source file
deflate.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/deflate.c.i
.PHONY : deflate.c.i

deflate.s: deflate.c.s

.PHONY : deflate.s

# target to generate assembly for a file
deflate.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/deflate.c.s
.PHONY : deflate.c.s

gzclose.obj: gzclose.c.obj

.PHONY : gzclose.obj

# target to build an object file
gzclose.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/gzclose.c.obj
.PHONY : gzclose.c.obj

gzclose.i: gzclose.c.i

.PHONY : gzclose.i

# target to preprocess a source file
gzclose.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/gzclose.c.i
.PHONY : gzclose.c.i

gzclose.s: gzclose.c.s

.PHONY : gzclose.s

# target to generate assembly for a file
gzclose.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/gzclose.c.s
.PHONY : gzclose.c.s

gzlib.obj: gzlib.c.obj

.PHONY : gzlib.obj

# target to build an object file
gzlib.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/gzlib.c.obj
.PHONY : gzlib.c.obj

gzlib.i: gzlib.c.i

.PHONY : gzlib.i

# target to preprocess a source file
gzlib.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/gzlib.c.i
.PHONY : gzlib.c.i

gzlib.s: gzlib.c.s

.PHONY : gzlib.s

# target to generate assembly for a file
gzlib.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/gzlib.c.s
.PHONY : gzlib.c.s

gzread.obj: gzread.c.obj

.PHONY : gzread.obj

# target to build an object file
gzread.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/gzread.c.obj
.PHONY : gzread.c.obj

gzread.i: gzread.c.i

.PHONY : gzread.i

# target to preprocess a source file
gzread.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/gzread.c.i
.PHONY : gzread.c.i

gzread.s: gzread.c.s

.PHONY : gzread.s

# target to generate assembly for a file
gzread.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/gzread.c.s
.PHONY : gzread.c.s

gzwrite.obj: gzwrite.c.obj

.PHONY : gzwrite.obj

# target to build an object file
gzwrite.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/gzwrite.c.obj
.PHONY : gzwrite.c.obj

gzwrite.i: gzwrite.c.i

.PHONY : gzwrite.i

# target to preprocess a source file
gzwrite.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/gzwrite.c.i
.PHONY : gzwrite.c.i

gzwrite.s: gzwrite.c.s

.PHONY : gzwrite.s

# target to generate assembly for a file
gzwrite.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/gzwrite.c.s
.PHONY : gzwrite.c.s

infback.obj: infback.c.obj

.PHONY : infback.obj

# target to build an object file
infback.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/infback.c.obj
.PHONY : infback.c.obj

infback.i: infback.c.i

.PHONY : infback.i

# target to preprocess a source file
infback.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/infback.c.i
.PHONY : infback.c.i

infback.s: infback.c.s

.PHONY : infback.s

# target to generate assembly for a file
infback.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/infback.c.s
.PHONY : infback.c.s

inffast.obj: inffast.c.obj

.PHONY : inffast.obj

# target to build an object file
inffast.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/inffast.c.obj
.PHONY : inffast.c.obj

inffast.i: inffast.c.i

.PHONY : inffast.i

# target to preprocess a source file
inffast.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/inffast.c.i
.PHONY : inffast.c.i

inffast.s: inffast.c.s

.PHONY : inffast.s

# target to generate assembly for a file
inffast.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/inffast.c.s
.PHONY : inffast.c.s

inflate.obj: inflate.c.obj

.PHONY : inflate.obj

# target to build an object file
inflate.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/inflate.c.obj
.PHONY : inflate.c.obj

inflate.i: inflate.c.i

.PHONY : inflate.i

# target to preprocess a source file
inflate.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/inflate.c.i
.PHONY : inflate.c.i

inflate.s: inflate.c.s

.PHONY : inflate.s

# target to generate assembly for a file
inflate.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/inflate.c.s
.PHONY : inflate.c.s

inftrees.obj: inftrees.c.obj

.PHONY : inftrees.obj

# target to build an object file
inftrees.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/inftrees.c.obj
.PHONY : inftrees.c.obj

inftrees.i: inftrees.c.i

.PHONY : inftrees.i

# target to preprocess a source file
inftrees.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/inftrees.c.i
.PHONY : inftrees.c.i

inftrees.s: inftrees.c.s

.PHONY : inftrees.s

# target to generate assembly for a file
inftrees.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/inftrees.c.s
.PHONY : inftrees.c.s

trees.obj: trees.c.obj

.PHONY : trees.obj

# target to build an object file
trees.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/trees.c.obj
.PHONY : trees.c.obj

trees.i: trees.c.i

.PHONY : trees.i

# target to preprocess a source file
trees.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/trees.c.i
.PHONY : trees.c.i

trees.s: trees.c.s

.PHONY : trees.s

# target to generate assembly for a file
trees.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/trees.c.s
.PHONY : trees.c.s

uncompr.obj: uncompr.c.obj

.PHONY : uncompr.obj

# target to build an object file
uncompr.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/uncompr.c.obj
.PHONY : uncompr.c.obj

uncompr.i: uncompr.c.i

.PHONY : uncompr.i

# target to preprocess a source file
uncompr.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/uncompr.c.i
.PHONY : uncompr.c.i

uncompr.s: uncompr.c.s

.PHONY : uncompr.s

# target to generate assembly for a file
uncompr.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/uncompr.c.s
.PHONY : uncompr.c.s

zutil.obj: zutil.c.obj

.PHONY : zutil.obj

# target to build an object file
zutil.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/zutil.c.obj
.PHONY : zutil.c.obj

zutil.i: zutil.c.i

.PHONY : zutil.i

# target to preprocess a source file
zutil.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/zutil.c.i
.PHONY : zutil.c.i

zutil.s: zutil.c.s

.PHONY : zutil.s

# target to generate assembly for a file
zutil.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/zutil.c.s
.PHONY : zutil.c.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... zlib
	@echo ... package
	@echo ... package_source
	@echo ... rebuild_cache
	@echo ... test
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... list_install_components
	@echo ... install/strip
	@echo ... adler32.obj
	@echo ... adler32.i
	@echo ... adler32.s
	@echo ... compress.obj
	@echo ... compress.i
	@echo ... compress.s
	@echo ... crc32.obj
	@echo ... crc32.i
	@echo ... crc32.s
	@echo ... deflate.obj
	@echo ... deflate.i
	@echo ... deflate.s
	@echo ... gzclose.obj
	@echo ... gzclose.i
	@echo ... gzclose.s
	@echo ... gzlib.obj
	@echo ... gzlib.i
	@echo ... gzlib.s
	@echo ... gzread.obj
	@echo ... gzread.i
	@echo ... gzread.s
	@echo ... gzwrite.obj
	@echo ... gzwrite.i
	@echo ... gzwrite.s
	@echo ... infback.obj
	@echo ... infback.i
	@echo ... infback.s
	@echo ... inffast.obj
	@echo ... inffast.i
	@echo ... inffast.s
	@echo ... inflate.obj
	@echo ... inflate.i
	@echo ... inflate.s
	@echo ... inftrees.obj
	@echo ... inftrees.i
	@echo ... inftrees.s
	@echo ... trees.obj
	@echo ... trees.i
	@echo ... trees.s
	@echo ... uncompr.obj
	@echo ... uncompr.i
	@echo ... uncompr.s
	@echo ... zutil.obj
	@echo ... zutil.i
	@echo ... zutil.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

