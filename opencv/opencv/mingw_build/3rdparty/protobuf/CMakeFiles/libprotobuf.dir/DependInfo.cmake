# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/any.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/any.pb.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/api.pb.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/api.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/arena.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arena.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/arenastring.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arenastring.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/descriptor.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/descriptor.pb.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/descriptor_database.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor_database.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/duration.pb.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/duration.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/dynamic_message.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/dynamic_message.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/empty.pb.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/empty.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/extension_set.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/extension_set_heavy.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set_heavy.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/field_mask.pb.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/field_mask.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_reflection.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_reflection.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_table_driven.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_table_driven_lite.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven_lite.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_util.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_util.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/coded_stream.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/coded_stream.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/gzip_stream.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/gzip_stream.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/printer.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/printer.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/strtod.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/strtod.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/tokenizer.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/tokenizer.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/zero_copy_stream.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/zero_copy_stream_impl.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/zero_copy_stream_impl_lite.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl_lite.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/map_field.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/map_field.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/message.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/message_lite.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message_lite.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/reflection_ops.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/reflection_ops.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/repeated_field.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/repeated_field.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/service.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/service.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/source_context.pb.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/source_context.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/struct.pb.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/struct.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops_internals_x86_gcc.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/atomicops_internals_x86_gcc.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/bytestream.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/bytestream.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/common.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/common.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/int128.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/int128.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/io_win32.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/io_win32.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/mathlimits.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/mathlimits.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/once.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/once.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/status.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/status.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/statusor.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/statusor.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/stringpiece.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringpiece.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/stringprintf.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringprintf.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/structurally_valid.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/structurally_valid.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/strutil.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/strutil.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/substitute.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/substitute.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/time.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/time.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/text_format.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/text_format.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/timestamp.pb.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/timestamp.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/type.pb.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/type.pb.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/unknown_field_set.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/unknown_field_set.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/delimited_message_util.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/delimited_message_util.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/field_comparator.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_comparator.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/field_mask_util.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_mask_util.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/datapiece.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/datapiece.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/default_value_objectwriter.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/default_value_objectwriter.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/field_mask_utility.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/field_mask_utility.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/json_escaping.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_escaping.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/json_objectwriter.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_objectwriter.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/json_stream_parser.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_stream_parser.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/object_writer.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/object_writer.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/proto_writer.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/proto_writer.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/protostream_objectsource.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectsource.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/protostream_objectwriter.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectwriter.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/type_info.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/type_info.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/utility.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/utility.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/json_util.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/json_util.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/message_differencer.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/message_differencer.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/time_util.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/time_util.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/type_resolver_util.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/type_resolver_util.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/wire_format.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/wire_format_lite.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format_lite.cc.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/wrappers.pb.cc" "D:/unet/opencv/opencv/mingw_build/3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wrappers.pb.cc.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "_WIN32_WINNT=0x0601"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "."
  "D:/unet/opencv/opencv/sources/3rdparty/protobuf/src"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
