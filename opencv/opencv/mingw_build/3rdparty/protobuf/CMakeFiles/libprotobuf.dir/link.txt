E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe qc ..\lib\liblibprotobuf.a  CMakeFiles/libprotobuf.dir/src/google/protobuf/arena.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/arenastring.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven_lite.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_util.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/io/coded_stream.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl_lite.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/message_lite.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/repeated_field.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/atomicops_internals_x86_gcc.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/bytestream.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/common.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/int128.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/io_win32.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/once.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/status.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/statusor.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringpiece.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringprintf.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/structurally_valid.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/strutil.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/time.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format_lite.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/any.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/any.pb.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/api.pb.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.pb.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor_database.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/duration.pb.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/dynamic_message.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/empty.pb.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set_heavy.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/field_mask.pb.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_reflection.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/io/gzip_stream.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/io/printer.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/io/strtod.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/io/tokenizer.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/map_field.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/message.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/reflection_ops.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/service.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/source_context.pb.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/struct.pb.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/mathlimits.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/substitute.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/text_format.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/timestamp.pb.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/type.pb.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/unknown_field_set.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/delimited_message_util.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_comparator.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_mask_util.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/datapiece.cc.obj
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe q  ..\lib\liblibprotobuf.a  CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/default_value_objectwriter.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/field_mask_utility.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_escaping.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_objectwriter.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_stream_parser.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/object_writer.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/proto_writer.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectsource.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectwriter.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/type_info.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/utility.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/json_util.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/message_differencer.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/time_util.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/util/type_resolver_util.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format.cc.obj CMakeFiles/libprotobuf.dir/src/google/protobuf/wrappers.pb.cc.obj
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ranlib.exe ..\lib\liblibprotobuf.a
