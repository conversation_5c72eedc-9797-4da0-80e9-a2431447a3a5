# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

# Include any dependencies generated for this target.
include 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/depend.make

# Include the progress variables for this target.
include 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/progress.make

# Include the compile flags for this target's objects.
include 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arena.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arena.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arena.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/arena.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arena.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\arena.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\arena.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arena.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/arena.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\arena.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\arena.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arena.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/arena.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\arena.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\arena.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arenastring.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arenastring.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arenastring.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/arenastring.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arenastring.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\arenastring.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\arenastring.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arenastring.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/arenastring.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\arenastring.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\arenastring.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arenastring.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/arenastring.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\arenastring.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\arenastring.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/extension_set.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\extension_set.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\extension_set.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\extension_set.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\extension_set.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\extension_set.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\extension_set.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven_lite.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven_lite.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven_lite.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_table_driven_lite.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven_lite.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\generated_message_table_driven_lite.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\generated_message_table_driven_lite.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven_lite.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven_lite.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\generated_message_table_driven_lite.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\generated_message_table_driven_lite.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven_lite.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven_lite.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\generated_message_table_driven_lite.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\generated_message_table_driven_lite.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_util.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_util.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_util.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_util.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_util.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\generated_message_util.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\generated_message_util.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_util.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_util.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\generated_message_util.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\generated_message_util.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_util.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_util.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\generated_message_util.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\generated_message_util.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/coded_stream.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/coded_stream.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/coded_stream.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/coded_stream.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/coded_stream.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\coded_stream.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\coded_stream.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/coded_stream.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/io/coded_stream.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\coded_stream.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\io\coded_stream.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/coded_stream.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/io/coded_stream.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\coded_stream.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\coded_stream.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/zero_copy_stream.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\zero_copy_stream.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\zero_copy_stream.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\zero_copy_stream.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\io\zero_copy_stream.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\zero_copy_stream.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\zero_copy_stream.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl_lite.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl_lite.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl_lite.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/zero_copy_stream_impl_lite.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl_lite.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\zero_copy_stream_impl_lite.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\zero_copy_stream_impl_lite.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl_lite.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl_lite.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\zero_copy_stream_impl_lite.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\io\zero_copy_stream_impl_lite.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl_lite.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl_lite.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\zero_copy_stream_impl_lite.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\zero_copy_stream_impl_lite.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message_lite.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message_lite.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message_lite.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/message_lite.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message_lite.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\message_lite.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\message_lite.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message_lite.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/message_lite.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\message_lite.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\message_lite.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message_lite.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/message_lite.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\message_lite.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\message_lite.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/repeated_field.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/repeated_field.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/repeated_field.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/repeated_field.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/repeated_field.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\repeated_field.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\repeated_field.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/repeated_field.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/repeated_field.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\repeated_field.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\repeated_field.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/repeated_field.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/repeated_field.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\repeated_field.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\repeated_field.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/atomicops_internals_x86_gcc.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/atomicops_internals_x86_gcc.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/atomicops_internals_x86_gcc.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops_internals_x86_gcc.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/atomicops_internals_x86_gcc.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\atomicops_internals_x86_gcc.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\atomicops_internals_x86_gcc.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/atomicops_internals_x86_gcc.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/atomicops_internals_x86_gcc.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\atomicops_internals_x86_gcc.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\atomicops_internals_x86_gcc.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/atomicops_internals_x86_gcc.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/atomicops_internals_x86_gcc.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\atomicops_internals_x86_gcc.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\atomicops_internals_x86_gcc.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/bytestream.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/bytestream.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/bytestream.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/bytestream.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/bytestream.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\bytestream.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\bytestream.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/bytestream.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/bytestream.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\bytestream.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\bytestream.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/bytestream.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/bytestream.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\bytestream.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\bytestream.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/common.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/common.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/common.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/common.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/common.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\common.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\common.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/common.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/common.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\common.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\common.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/common.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/common.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\common.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\common.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/int128.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/int128.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/int128.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/int128.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/int128.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\int128.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\int128.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/int128.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/int128.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\int128.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\int128.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/int128.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/int128.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\int128.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\int128.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/io_win32.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/io_win32.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/io_win32.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/io_win32.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/io_win32.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\io_win32.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\io_win32.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/io_win32.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/io_win32.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\io_win32.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\io_win32.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/io_win32.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/io_win32.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\io_win32.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\io_win32.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/once.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/once.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/once.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/once.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/once.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\once.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\once.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/once.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/once.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\once.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\once.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/once.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/once.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\once.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\once.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/status.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/status.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/status.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/status.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/status.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\status.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\status.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/status.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/status.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\status.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\status.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/status.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/status.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\status.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\status.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/statusor.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/statusor.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/statusor.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/statusor.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/statusor.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\statusor.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\statusor.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/statusor.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/statusor.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\statusor.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\statusor.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/statusor.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/statusor.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\statusor.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\statusor.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringpiece.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringpiece.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringpiece.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/stringpiece.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringpiece.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\stringpiece.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\stringpiece.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringpiece.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringpiece.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\stringpiece.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\stringpiece.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringpiece.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringpiece.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\stringpiece.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\stringpiece.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringprintf.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringprintf.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringprintf.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/stringprintf.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringprintf.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\stringprintf.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\stringprintf.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringprintf.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringprintf.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\stringprintf.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\stringprintf.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringprintf.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringprintf.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\stringprintf.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\stringprintf.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/structurally_valid.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/structurally_valid.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/structurally_valid.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/structurally_valid.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/structurally_valid.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\structurally_valid.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\structurally_valid.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/structurally_valid.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/structurally_valid.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\structurally_valid.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\structurally_valid.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/structurally_valid.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/structurally_valid.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\structurally_valid.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\structurally_valid.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/strutil.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/strutil.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/strutil.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/strutil.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/strutil.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\strutil.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\strutil.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/strutil.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/strutil.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\strutil.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\strutil.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/strutil.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/strutil.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\strutil.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\strutil.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/time.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/time.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/time.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/time.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/time.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\time.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\time.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/time.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/time.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\time.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\time.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/time.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/time.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\time.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\time.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format_lite.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format_lite.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format_lite.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/wire_format_lite.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format_lite.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\wire_format_lite.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\wire_format_lite.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format_lite.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format_lite.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\wire_format_lite.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\wire_format_lite.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format_lite.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format_lite.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\wire_format_lite.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\wire_format_lite.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/any.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\any.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\any.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/any.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\any.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\any.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/any.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\any.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\any.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.pb.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/any.pb.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.pb.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\any.pb.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\any.pb.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/any.pb.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\any.pb.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\any.pb.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/any.pb.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\any.pb.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\any.pb.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/api.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/api.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/api.pb.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/api.pb.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/api.pb.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\api.pb.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\api.pb.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/api.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/api.pb.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\api.pb.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\api.pb.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/api.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/api.pb.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\api.pb.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\api.pb.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/descriptor.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\descriptor.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\descriptor.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\descriptor.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\descriptor.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\descriptor.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\descriptor.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.pb.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/descriptor.pb.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.pb.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\descriptor.pb.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\descriptor.pb.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.pb.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\descriptor.pb.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\descriptor.pb.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.pb.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\descriptor.pb.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\descriptor.pb.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor_database.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor_database.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor_database.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/descriptor_database.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor_database.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\descriptor_database.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\descriptor_database.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor_database.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor_database.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\descriptor_database.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\descriptor_database.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor_database.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor_database.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\descriptor_database.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\descriptor_database.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/duration.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/duration.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/duration.pb.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/duration.pb.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/duration.pb.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\duration.pb.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\duration.pb.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/duration.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/duration.pb.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\duration.pb.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\duration.pb.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/duration.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/duration.pb.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\duration.pb.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\duration.pb.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/dynamic_message.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/dynamic_message.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/dynamic_message.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/dynamic_message.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/dynamic_message.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\dynamic_message.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\dynamic_message.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/dynamic_message.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/dynamic_message.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\dynamic_message.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\dynamic_message.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/dynamic_message.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/dynamic_message.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\dynamic_message.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\dynamic_message.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/empty.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/empty.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/empty.pb.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/empty.pb.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/empty.pb.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\empty.pb.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\empty.pb.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/empty.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/empty.pb.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\empty.pb.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\empty.pb.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/empty.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/empty.pb.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\empty.pb.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\empty.pb.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set_heavy.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set_heavy.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set_heavy.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/extension_set_heavy.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set_heavy.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\extension_set_heavy.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\extension_set_heavy.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set_heavy.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set_heavy.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\extension_set_heavy.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\extension_set_heavy.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set_heavy.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set_heavy.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\extension_set_heavy.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\extension_set_heavy.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/field_mask.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/field_mask.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/field_mask.pb.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/field_mask.pb.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/field_mask.pb.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\field_mask.pb.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\field_mask.pb.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/field_mask.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/field_mask.pb.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\field_mask.pb.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\field_mask.pb.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/field_mask.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/field_mask.pb.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\field_mask.pb.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\field_mask.pb.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_reflection.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_reflection.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_reflection.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_reflection.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_reflection.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\generated_message_reflection.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\generated_message_reflection.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_reflection.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_reflection.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\generated_message_reflection.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\generated_message_reflection.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_reflection.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_reflection.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\generated_message_reflection.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\generated_message_reflection.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_table_driven.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\generated_message_table_driven.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\generated_message_table_driven.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\generated_message_table_driven.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\generated_message_table_driven.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\generated_message_table_driven.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\generated_message_table_driven.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/gzip_stream.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/gzip_stream.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/gzip_stream.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/gzip_stream.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/gzip_stream.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\gzip_stream.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\gzip_stream.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/gzip_stream.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/io/gzip_stream.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\gzip_stream.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\io\gzip_stream.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/gzip_stream.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/io/gzip_stream.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\gzip_stream.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\gzip_stream.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/printer.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/printer.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/printer.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/printer.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/printer.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\printer.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\printer.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/printer.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/io/printer.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\printer.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\io\printer.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/printer.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/io/printer.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\printer.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\printer.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/strtod.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/strtod.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/strtod.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/strtod.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/strtod.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\strtod.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\strtod.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/strtod.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/io/strtod.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\strtod.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\io\strtod.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/strtod.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/io/strtod.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\strtod.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\strtod.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/tokenizer.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/tokenizer.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/tokenizer.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/tokenizer.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/tokenizer.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\tokenizer.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\tokenizer.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/tokenizer.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/io/tokenizer.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\tokenizer.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\io\tokenizer.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/tokenizer.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/io/tokenizer.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\tokenizer.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\tokenizer.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/zero_copy_stream_impl.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\zero_copy_stream_impl.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\zero_copy_stream_impl.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\zero_copy_stream_impl.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\io\zero_copy_stream_impl.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\io\zero_copy_stream_impl.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\io\zero_copy_stream_impl.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/map_field.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/map_field.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/map_field.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/map_field.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/map_field.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\map_field.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\map_field.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/map_field.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/map_field.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\map_field.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\map_field.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/map_field.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/map_field.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\map_field.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\map_field.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/message.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_44) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\message.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\message.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/message.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\message.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\message.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/message.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\message.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\message.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/reflection_ops.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/reflection_ops.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/reflection_ops.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/reflection_ops.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_45) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/reflection_ops.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\reflection_ops.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\reflection_ops.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/reflection_ops.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/reflection_ops.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\reflection_ops.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\reflection_ops.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/reflection_ops.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/reflection_ops.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\reflection_ops.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\reflection_ops.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/service.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/service.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/service.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/service.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_46) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/service.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\service.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\service.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/service.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/service.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\service.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\service.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/service.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/service.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\service.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\service.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/source_context.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/source_context.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/source_context.pb.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/source_context.pb.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_47) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/source_context.pb.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\source_context.pb.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\source_context.pb.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/source_context.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/source_context.pb.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\source_context.pb.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\source_context.pb.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/source_context.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/source_context.pb.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\source_context.pb.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\source_context.pb.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/struct.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/struct.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/struct.pb.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/struct.pb.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_48) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/struct.pb.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\struct.pb.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\struct.pb.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/struct.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/struct.pb.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\struct.pb.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\struct.pb.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/struct.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/struct.pb.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\struct.pb.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\struct.pb.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/mathlimits.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/mathlimits.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/mathlimits.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/mathlimits.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_49) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/mathlimits.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\mathlimits.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\mathlimits.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/mathlimits.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/mathlimits.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\mathlimits.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\mathlimits.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/mathlimits.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/mathlimits.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\mathlimits.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\mathlimits.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/substitute.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/substitute.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/substitute.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/substitute.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_50) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/substitute.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\substitute.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\substitute.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/substitute.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/substitute.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\substitute.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\substitute.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/substitute.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/substitute.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\stubs\substitute.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\stubs\substitute.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/text_format.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/text_format.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/text_format.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/text_format.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_51) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/text_format.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\text_format.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\text_format.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/text_format.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/text_format.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\text_format.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\text_format.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/text_format.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/text_format.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\text_format.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\text_format.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/timestamp.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/timestamp.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/timestamp.pb.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/timestamp.pb.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_52) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/timestamp.pb.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\timestamp.pb.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\timestamp.pb.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/timestamp.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/timestamp.pb.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\timestamp.pb.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\timestamp.pb.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/timestamp.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/timestamp.pb.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\timestamp.pb.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\timestamp.pb.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/type.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/type.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/type.pb.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/type.pb.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_53) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/type.pb.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\type.pb.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\type.pb.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/type.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/type.pb.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\type.pb.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\type.pb.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/type.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/type.pb.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\type.pb.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\type.pb.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/unknown_field_set.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/unknown_field_set.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/unknown_field_set.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/unknown_field_set.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_54) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/unknown_field_set.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\unknown_field_set.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\unknown_field_set.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/unknown_field_set.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/unknown_field_set.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\unknown_field_set.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\unknown_field_set.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/unknown_field_set.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/unknown_field_set.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\unknown_field_set.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\unknown_field_set.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/delimited_message_util.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/delimited_message_util.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/delimited_message_util.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/delimited_message_util.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_55) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/delimited_message_util.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\delimited_message_util.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\delimited_message_util.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/delimited_message_util.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/delimited_message_util.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\delimited_message_util.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\delimited_message_util.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/delimited_message_util.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/delimited_message_util.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\delimited_message_util.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\delimited_message_util.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_comparator.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_comparator.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_comparator.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/field_comparator.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_56) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_comparator.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\field_comparator.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\field_comparator.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_comparator.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_comparator.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\field_comparator.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\field_comparator.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_comparator.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_comparator.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\field_comparator.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\field_comparator.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_mask_util.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_mask_util.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_mask_util.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/field_mask_util.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_57) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_mask_util.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\field_mask_util.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\field_mask_util.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_mask_util.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_mask_util.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\field_mask_util.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\field_mask_util.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_mask_util.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_mask_util.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\field_mask_util.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\field_mask_util.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/datapiece.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/datapiece.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/datapiece.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/datapiece.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_58) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/datapiece.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\datapiece.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\datapiece.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/datapiece.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/datapiece.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\datapiece.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\datapiece.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/datapiece.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/datapiece.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\datapiece.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\datapiece.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/default_value_objectwriter.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/default_value_objectwriter.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/default_value_objectwriter.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/default_value_objectwriter.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_59) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/default_value_objectwriter.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\default_value_objectwriter.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\default_value_objectwriter.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/default_value_objectwriter.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/default_value_objectwriter.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\default_value_objectwriter.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\default_value_objectwriter.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/default_value_objectwriter.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/default_value_objectwriter.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\default_value_objectwriter.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\default_value_objectwriter.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/field_mask_utility.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/field_mask_utility.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/field_mask_utility.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/field_mask_utility.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_60) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/field_mask_utility.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\field_mask_utility.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\field_mask_utility.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/field_mask_utility.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/field_mask_utility.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\field_mask_utility.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\field_mask_utility.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/field_mask_utility.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/field_mask_utility.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\field_mask_utility.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\field_mask_utility.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_escaping.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_escaping.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_escaping.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/json_escaping.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_61) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_escaping.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\json_escaping.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\json_escaping.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_escaping.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_escaping.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\json_escaping.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\json_escaping.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_escaping.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_escaping.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\json_escaping.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\json_escaping.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_objectwriter.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_objectwriter.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_objectwriter.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/json_objectwriter.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_62) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_objectwriter.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\json_objectwriter.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\json_objectwriter.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_objectwriter.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_objectwriter.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\json_objectwriter.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\json_objectwriter.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_objectwriter.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_objectwriter.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\json_objectwriter.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\json_objectwriter.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_stream_parser.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_stream_parser.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_stream_parser.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/json_stream_parser.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_63) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_stream_parser.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\json_stream_parser.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\json_stream_parser.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_stream_parser.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_stream_parser.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\json_stream_parser.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\json_stream_parser.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_stream_parser.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_stream_parser.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\json_stream_parser.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\json_stream_parser.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/object_writer.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/object_writer.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/object_writer.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/object_writer.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_64) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/object_writer.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\object_writer.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\object_writer.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/object_writer.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/object_writer.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\object_writer.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\object_writer.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/object_writer.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/object_writer.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\object_writer.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\object_writer.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/proto_writer.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/proto_writer.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/proto_writer.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/proto_writer.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_65) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/proto_writer.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\proto_writer.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\proto_writer.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/proto_writer.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/proto_writer.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\proto_writer.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\proto_writer.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/proto_writer.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/proto_writer.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\proto_writer.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\proto_writer.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectsource.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectsource.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectsource.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/protostream_objectsource.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_66) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectsource.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\protostream_objectsource.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\protostream_objectsource.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectsource.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectsource.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\protostream_objectsource.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\protostream_objectsource.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectsource.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectsource.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\protostream_objectsource.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\protostream_objectsource.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectwriter.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectwriter.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectwriter.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/protostream_objectwriter.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_67) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectwriter.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\protostream_objectwriter.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\protostream_objectwriter.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectwriter.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectwriter.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\protostream_objectwriter.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\protostream_objectwriter.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectwriter.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectwriter.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\protostream_objectwriter.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\protostream_objectwriter.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/type_info.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/type_info.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/type_info.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/type_info.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_68) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/type_info.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\type_info.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\type_info.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/type_info.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/type_info.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\type_info.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\type_info.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/type_info.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/type_info.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\type_info.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\type_info.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/utility.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/utility.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/utility.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/utility.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_69) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/utility.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\utility.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\utility.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/utility.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/utility.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\utility.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\utility.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/utility.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/utility.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\internal\utility.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\internal\utility.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/json_util.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/json_util.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/json_util.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/json_util.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_70) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/json_util.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\json_util.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\json_util.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/json_util.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/json_util.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\json_util.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\json_util.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/json_util.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/json_util.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\json_util.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\json_util.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/message_differencer.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/message_differencer.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/message_differencer.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/message_differencer.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_71) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/message_differencer.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\message_differencer.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\message_differencer.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/message_differencer.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/message_differencer.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\message_differencer.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\message_differencer.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/message_differencer.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/message_differencer.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\message_differencer.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\message_differencer.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/time_util.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/time_util.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/time_util.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/time_util.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_72) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/time_util.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\time_util.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\time_util.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/time_util.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/time_util.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\time_util.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\time_util.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/time_util.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/time_util.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\time_util.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\time_util.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/type_resolver_util.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/type_resolver_util.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/type_resolver_util.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/type_resolver_util.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_73) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/type_resolver_util.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\type_resolver_util.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\type_resolver_util.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/type_resolver_util.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/util/type_resolver_util.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\type_resolver_util.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\util\type_resolver_util.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/type_resolver_util.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/util/type_resolver_util.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\util\type_resolver_util.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\util\type_resolver_util.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/wire_format.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_74) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\wire_format.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\wire_format.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\wire_format.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\wire_format.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\wire_format.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\wire_format.cc.s

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wrappers.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/flags.make
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wrappers.pb.cc.obj: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/includes_CXX.rsp
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wrappers.pb.cc.obj: D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/wrappers.pb.cc
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_75) "Building CXX object 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wrappers.pb.cc.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles\libprotobuf.dir\src\google\protobuf\wrappers.pb.cc.obj -c D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\wrappers.pb.cc

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wrappers.pb.cc.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/libprotobuf.dir/src/google/protobuf/wrappers.pb.cc.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\wrappers.pb.cc > CMakeFiles\libprotobuf.dir\src\google\protobuf\wrappers.pb.cc.i

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wrappers.pb.cc.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/libprotobuf.dir/src/google/protobuf/wrappers.pb.cc.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\protobuf\src\google\protobuf\wrappers.pb.cc -o CMakeFiles\libprotobuf.dir\src\google\protobuf\wrappers.pb.cc.s

# Object files for target libprotobuf
libprotobuf_OBJECTS = \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/arena.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/arenastring.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven_lite.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_util.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/io/coded_stream.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl_lite.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/message_lite.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/repeated_field.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/atomicops_internals_x86_gcc.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/bytestream.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/common.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/int128.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/io_win32.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/once.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/status.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/statusor.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringpiece.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringprintf.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/structurally_valid.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/strutil.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/time.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format_lite.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/any.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/any.pb.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/api.pb.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.pb.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor_database.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/duration.pb.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/dynamic_message.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/empty.pb.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set_heavy.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/field_mask.pb.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_reflection.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/io/gzip_stream.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/io/printer.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/io/strtod.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/io/tokenizer.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/map_field.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/message.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/reflection_ops.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/service.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/source_context.pb.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/struct.pb.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/mathlimits.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/substitute.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/text_format.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/timestamp.pb.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/type.pb.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/unknown_field_set.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/delimited_message_util.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_comparator.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_mask_util.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/datapiece.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/default_value_objectwriter.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/field_mask_utility.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_escaping.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_objectwriter.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_stream_parser.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/object_writer.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/proto_writer.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectsource.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectwriter.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/type_info.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/utility.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/json_util.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/message_differencer.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/time_util.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/util/type_resolver_util.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format.cc.obj" \
"CMakeFiles/libprotobuf.dir/src/google/protobuf/wrappers.pb.cc.obj"

# External object files for target libprotobuf
libprotobuf_EXTERNAL_OBJECTS =

3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arena.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/arenastring.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven_lite.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_util.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/coded_stream.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl_lite.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message_lite.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/repeated_field.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/atomicops_internals_x86_gcc.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/bytestream.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/common.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/int128.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/io_win32.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/once.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/status.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/statusor.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringpiece.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/stringprintf.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/structurally_valid.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/strutil.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/time.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format_lite.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/any.pb.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/api.pb.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor.pb.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/descriptor_database.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/duration.pb.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/dynamic_message.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/empty.pb.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/extension_set_heavy.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/field_mask.pb.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_reflection.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/generated_message_table_driven.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/gzip_stream.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/printer.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/strtod.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/tokenizer.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/io/zero_copy_stream_impl.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/map_field.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/message.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/reflection_ops.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/service.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/source_context.pb.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/struct.pb.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/mathlimits.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/stubs/substitute.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/text_format.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/timestamp.pb.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/type.pb.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/unknown_field_set.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/delimited_message_util.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_comparator.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/field_mask_util.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/datapiece.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/default_value_objectwriter.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/field_mask_utility.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_escaping.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_objectwriter.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/json_stream_parser.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/object_writer.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/proto_writer.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectsource.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/protostream_objectwriter.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/type_info.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/internal/utility.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/json_util.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/message_differencer.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/time_util.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/util/type_resolver_util.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wire_format.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/src/google/protobuf/wrappers.pb.cc.obj
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/build.make
3rdparty/lib/liblibprotobuf.a: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_76) "Linking CXX static library ..\lib\liblibprotobuf.a"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && $(CMAKE_COMMAND) -P CMakeFiles\libprotobuf.dir\cmake_clean_target.cmake
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\libprotobuf.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/build: 3rdparty/lib/liblibprotobuf.a

.PHONY : 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/build

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/clean:
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf && $(CMAKE_COMMAND) -P CMakeFiles\libprotobuf.dir\cmake_clean.cmake
.PHONY : 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/clean

3rdparty/protobuf/CMakeFiles/libprotobuf.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\unet\opencv\opencv\sources D:\unet\opencv\opencv\sources\3rdparty\protobuf D:\unet\opencv\opencv\mingw_build D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf D:\unet\opencv\opencv\mingw_build\3rdparty\protobuf\CMakeFiles\libprotobuf.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/depend

