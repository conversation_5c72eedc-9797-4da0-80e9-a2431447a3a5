#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/any.cc
google/protobuf/any.h
-
google/protobuf/generated_message_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/any.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/descriptor.h
-
google/protobuf/message.h
-
google/protobuf/arenastring.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/any.pb.cc
google/protobuf/any.pb.h
-
algorithm
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/port.h
-
google/protobuf/stubs/once.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/wire_format_lite_inl.h
-
google/protobuf/descriptor.h
-
google/protobuf/generated_message_reflection.h
-
google/protobuf/reflection_ops.h
-
google/protobuf/wire_format.h
-
third_party/protobuf/version.h
D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/third_party/protobuf/version.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/any.pb.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/arena.h
-
google/protobuf/arenastring.h
-
google/protobuf/generated_message_table_driven.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/metadata.h
-
google/protobuf/message.h
-
google/protobuf/repeated_field.h
-
google/protobuf/extension_set.h
-
google/protobuf/unknown_field_set.h
-
google/protobuf/any.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/api.pb.cc
google/protobuf/api.pb.h
-
algorithm
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/port.h
-
google/protobuf/stubs/once.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/wire_format_lite_inl.h
-
google/protobuf/descriptor.h
-
google/protobuf/generated_message_reflection.h
-
google/protobuf/reflection_ops.h
-
google/protobuf/wire_format.h
-
third_party/protobuf/version.h
D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/third_party/protobuf/version.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/api.pb.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/arena.h
-
google/protobuf/arenastring.h
-
google/protobuf/generated_message_table_driven.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/metadata.h
-
google/protobuf/message.h
-
google/protobuf/repeated_field.h
-
google/protobuf/extension_set.h
-
google/protobuf/unknown_field_set.h
-
google/protobuf/source_context.pb.h
-
google/protobuf/type.pb.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/arena.cc
google/protobuf/arena.h
-
algorithm
-
limits
-
sanitizer/asan_interface.h
-
google/protobuf/stubs/port.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/arena.h
limits
-
google/protobuf/stubs/type_traits.h
-
exception
-
typeinfo
-
typeinfo
-
google/protobuf/arena_impl.h
-
google/protobuf/stubs/port.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/arena_impl.h
limits
-
google/protobuf/stubs/atomic_sequence_num.h
-
google/protobuf/stubs/atomicops.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/mutex.h
-
google/protobuf/stubs/type_traits.h
-
google/protobuf/stubs/port.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/arenastring.cc
google/protobuf/arenastring.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/arenastring.h
string
-
google/protobuf/arena.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/fastmem.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/port.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/descriptor.cc
google/protobuf/stubs/hash.h
-
map
-
memory
-
google/protobuf/stubs/shared_ptr.h
-
set
-
string
-
vector
-
algorithm
-
limits
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/mutex.h
-
google/protobuf/stubs/once.h
-
google/protobuf/stubs/stringprintf.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/io/strtod.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/io/tokenizer.h
-
google/protobuf/io/zero_copy_stream_impl.h
-
google/protobuf/descriptor.pb.h
-
google/protobuf/descriptor.h
-
google/protobuf/descriptor_database.h
-
google/protobuf/dynamic_message.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/text_format.h
-
google/protobuf/unknown_field_set.h
-
google/protobuf/wire_format.h
-
google/protobuf/stubs/substitute.h
-
google/protobuf/stubs/map_util.h
-
google/protobuf/stubs/stl_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/descriptor.h
memory
-
google/protobuf/stubs/shared_ptr.h
-
set
-
string
-
vector
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/mutex.h
-
google/protobuf/stubs/once.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/descriptor.pb.cc
google/protobuf/descriptor.pb.h
-
algorithm
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/port.h
-
google/protobuf/stubs/once.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/wire_format_lite_inl.h
-
google/protobuf/descriptor.h
-
google/protobuf/generated_message_reflection.h
-
google/protobuf/reflection_ops.h
-
google/protobuf/wire_format.h
-
third_party/protobuf/version.h
D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/third_party/protobuf/version.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/descriptor.pb.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/arena.h
-
google/protobuf/arenastring.h
-
google/protobuf/generated_message_table_driven.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/metadata.h
-
google/protobuf/message.h
-
google/protobuf/repeated_field.h
-
google/protobuf/extension_set.h
-
google/protobuf/generated_enum_reflection.h
-
google/protobuf/unknown_field_set.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/descriptor_database.cc
google/protobuf/descriptor_database.h
-
set
-
google/protobuf/descriptor.pb.h
-
google/protobuf/wire_format_lite_inl.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/map_util.h
-
google/protobuf/stubs/stl_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/descriptor_database.h
map
-
string
-
utility
-
vector
-
google/protobuf/stubs/common.h
-
google/protobuf/descriptor.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/duration.pb.cc
google/protobuf/duration.pb.h
-
algorithm
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/port.h
-
google/protobuf/stubs/once.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/wire_format_lite_inl.h
-
google/protobuf/descriptor.h
-
google/protobuf/generated_message_reflection.h
-
google/protobuf/reflection_ops.h
-
google/protobuf/wire_format.h
-
third_party/protobuf/version.h
D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/third_party/protobuf/version.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/duration.pb.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/arena.h
-
google/protobuf/arenastring.h
-
google/protobuf/generated_message_table_driven.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/metadata.h
-
google/protobuf/message.h
-
google/protobuf/repeated_field.h
-
google/protobuf/extension_set.h
-
google/protobuf/unknown_field_set.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/dynamic_message.cc
algorithm
-
google/protobuf/stubs/hash.h
-
memory
-
google/protobuf/stubs/shared_ptr.h
-
google/protobuf/stubs/common.h
-
google/protobuf/dynamic_message.h
-
google/protobuf/descriptor.h
-
google/protobuf/descriptor.pb.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/generated_message_reflection.h
-
google/protobuf/arenastring.h
-
google/protobuf/extension_set.h
-
google/protobuf/map_field.h
-
google/protobuf/map_field_inl.h
-
google/protobuf/map_type_handler.h
-
google/protobuf/reflection_ops.h
-
google/protobuf/repeated_field.h
-
google/protobuf/wire_format.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/dynamic_message.h
algorithm
-
memory
-
google/protobuf/stubs/shared_ptr.h
-
vector
-
google/protobuf/message.h
-
google/protobuf/repeated_field.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/mutex.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/empty.pb.cc
google/protobuf/empty.pb.h
-
algorithm
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/port.h
-
google/protobuf/stubs/once.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/wire_format_lite_inl.h
-
google/protobuf/descriptor.h
-
google/protobuf/generated_message_reflection.h
-
google/protobuf/reflection_ops.h
-
google/protobuf/wire_format.h
-
third_party/protobuf/version.h
D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/third_party/protobuf/version.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/empty.pb.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/arena.h
-
google/protobuf/arenastring.h
-
google/protobuf/generated_message_table_driven.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/metadata.h
-
google/protobuf/message.h
-
google/protobuf/repeated_field.h
-
google/protobuf/extension_set.h
-
google/protobuf/unknown_field_set.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/extension_set.cc
google/protobuf/stubs/hash.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/once.h
-
google/protobuf/extension_set.h
-
google/protobuf/message_lite.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/wire_format_lite_inl.h
-
google/protobuf/repeated_field.h
-
google/protobuf/stubs/map_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/extension_set.h
vector
-
map
-
utility
-
string
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/once.h
-
google/protobuf/repeated_field.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/extension_set_heavy.cc
google/protobuf/io/coded_stream.h
-
google/protobuf/io/zero_copy_stream_impl_lite.h
-
google/protobuf/descriptor.pb.h
-
google/protobuf/descriptor.h
-
google/protobuf/extension_set.h
-
google/protobuf/message.h
-
google/protobuf/repeated_field.h
-
google/protobuf/unknown_field_set.h
-
google/protobuf/wire_format.h
-
google/protobuf/wire_format_lite_inl.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/field_mask.pb.cc
google/protobuf/field_mask.pb.h
-
algorithm
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/port.h
-
google/protobuf/stubs/once.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/wire_format_lite_inl.h
-
google/protobuf/descriptor.h
-
google/protobuf/generated_message_reflection.h
-
google/protobuf/reflection_ops.h
-
google/protobuf/wire_format.h
-
third_party/protobuf/version.h
D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/third_party/protobuf/version.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/field_mask.pb.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/arena.h
-
google/protobuf/arenastring.h
-
google/protobuf/generated_message_table_driven.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/metadata.h
-
google/protobuf/message.h
-
google/protobuf/repeated_field.h
-
google/protobuf/extension_set.h
-
google/protobuf/unknown_field_set.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_enum_reflection.h
string
-
google/protobuf/stubs/template_util.h
-
google/protobuf/generated_enum_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_enum_util.h
google/protobuf/stubs/template_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_reflection.cc
algorithm
-
set
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-
google/protobuf/descriptor.pb.h
-
google/protobuf/descriptor.h
-
google/protobuf/extension_set.h
-
google/protobuf/generated_message_reflection.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/map_field.h
-
google/protobuf/repeated_field.h
-
google/protobuf/wire_format.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_reflection.h
string
-
vector
-
google/protobuf/stubs/casts.h
-
google/protobuf/stubs/common.h
-
google/protobuf/generated_enum_reflection.h
-
google/protobuf/message.h
-
google/protobuf/metadata.h
-
google/protobuf/unknown_field_set.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_table_driven.cc
google/protobuf/generated_message_table_driven.h
-
google/protobuf/stubs/type_traits.h
-
google/protobuf/generated_message_table_driven_lite.h
-
google/protobuf/io/zero_copy_stream_impl_lite.h
-
google/protobuf/metadata.h
-
google/protobuf/repeated_field.h
-
google/protobuf/wire_format.h
-
google/protobuf/wire_format_lite.h
-
google/protobuf/wire_format_lite_inl.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_table_driven.h
google/protobuf/map.h
-
google/protobuf/map_entry_lite.h
-
google/protobuf/map_field_lite.h
-
google/protobuf/message_lite.h
-
google/protobuf/wire_format_lite.h
-
google/protobuf/wire_format_lite_inl.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_table_driven_lite.cc
google/protobuf/generated_message_table_driven_lite.h
-
google/protobuf/stubs/type_traits.h
-
google/protobuf/io/zero_copy_stream_impl_lite.h
-
google/protobuf/metadata_lite.h
-
google/protobuf/repeated_field.h
-
google/protobuf/wire_format_lite.h
-
google/protobuf/wire_format_lite_inl.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_table_driven_lite.h
google/protobuf/generated_message_table_driven.h
-
google/protobuf/stubs/type_traits.h
-
google/protobuf/io/zero_copy_stream_impl_lite.h
-
google/protobuf/extension_set.h
-
google/protobuf/metadata_lite.h
-
google/protobuf/repeated_field.h
-
google/protobuf/wire_format_lite.h
-
google/protobuf/wire_format_lite_inl.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_util.cc
google/protobuf/generated_message_util.h
-
limits
-
vector
-
google/protobuf/io/coded_stream_inl.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/arenastring.h
-
google/protobuf/extension_set.h
-
google/protobuf/message_lite.h
-
google/protobuf/metadata_lite.h
-
google/protobuf/stubs/port.h
-
google/protobuf/repeated_field.h
-
google/protobuf/wire_format_lite.h
-
google/protobuf/wire_format_lite_inl.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/generated_message_util.h
assert.h
-
climits
-
string
-
vector
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/once.h
-
google/protobuf/has_bits.h
-
google/protobuf/map_entry_lite.h
-
google/protobuf/message_lite.h
-
google/protobuf/wire_format_lite.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/has_bits.h
google/protobuf/stubs/common.h
-
google/protobuf/stubs/port.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/coded_stream.cc
google/protobuf/io/coded_stream_inl.h
-
algorithm
-
utility
-
limits.h
-
google/protobuf/io/zero_copy_stream.h
-
google/protobuf/arena.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/stl_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/coded_stream.h
assert.h
-
climits
-
string
-
utility
-
sys/param.h
-
google/protobuf/stubs/atomicops.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/port.h
-
google/protobuf/stubs/port.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/coded_stream_inl.h
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/io/zero_copy_stream_impl_lite.h
-
string
-
google/protobuf/stubs/stl_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/gzip_stream.cc
google/protobuf/io/gzip_stream.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/gzip_stream.h
google/protobuf/stubs/common.h
-
google/protobuf/io/zero_copy_stream.h
-
zlib.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/printer.cc
google/protobuf/io/printer.h
-
google/protobuf/io/zero_copy_stream.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/printer.h
string
-
map
-
vector
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/strtod.cc
google/protobuf/io/strtod.h
-
cstdio
-
cstring
-
limits
-
string
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/strtod.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/tokenizer.cc
google/protobuf/io/tokenizer.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/stringprintf.h
-
google/protobuf/io/strtod.h
-
google/protobuf/io/zero_copy_stream.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/stl_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/tokenizer.h
string
-
vector
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/zero_copy_stream.cc
google/protobuf/io/zero_copy_stream.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/zero_copy_stream.h
string
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/zero_copy_stream_impl.cc
unistd.h
-
sys/types.h
-
sys/stat.h
-
fcntl.h
-
errno.h
-
iostream
-
algorithm
-
google/protobuf/io/zero_copy_stream_impl.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/stl_util.h
-
google/protobuf/stubs/io_win32.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/zero_copy_stream_impl.h
string
-
iosfwd
-
google/protobuf/io/zero_copy_stream.h
-
google/protobuf/io/zero_copy_stream_impl_lite.h
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/zero_copy_stream_impl_lite.cc
google/protobuf/io/zero_copy_stream_impl_lite.h
-
algorithm
-
limits
-
google/protobuf/stubs/casts.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/stl_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/io/zero_copy_stream_impl_lite.h
memory
-
google/protobuf/stubs/shared_ptr.h
-
string
-
iosfwd
-
google/protobuf/io/zero_copy_stream.h
-
google/protobuf/stubs/callback.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/stl_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/map.h
iterator
-
limits
-
set
-
utility
-
google/protobuf/stubs/common.h
-
google/protobuf/arena.h
-
google/protobuf/generated_enum_util.h
-
google/protobuf/map_type_handler.h
-
google/protobuf/stubs/hash.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/map_entry.h
google/protobuf/generated_message_reflection.h
-
google/protobuf/map_entry_lite.h
-
google/protobuf/map_type_handler.h
-
google/protobuf/metadata.h
-
google/protobuf/reflection_ops.h
-
google/protobuf/unknown_field_set.h
-
google/protobuf/wire_format_lite_inl.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/map_entry_lite.h
assert.h
-
google/protobuf/arena.h
-
google/protobuf/map.h
-
google/protobuf/map_type_handler.h
-
google/protobuf/stubs/port.h
-
google/protobuf/wire_format_lite_inl.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/map_field.cc
google/protobuf/map_field.h
-
google/protobuf/map_field_inl.h
-
vector
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/map_field.h
google/protobuf/stubs/atomicops.h
-
google/protobuf/stubs/mutex.h
-
google/protobuf/stubs/common.h
-
google/protobuf/generated_message_reflection.h
-
google/protobuf/arena.h
-
google/protobuf/descriptor.h
-
google/protobuf/map_entry.h
-
google/protobuf/map_field_lite.h
-
google/protobuf/map_type_handler.h
-
google/protobuf/message.h
-
google/protobuf/repeated_field.h
-
google/protobuf/unknown_field_set.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/map_field_inl.h
memory
-
google/protobuf/stubs/shared_ptr.h
-
google/protobuf/map.h
-
google/protobuf/map_field.h
-
google/protobuf/map_type_handler.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/map_field_lite.h
google/protobuf/map.h
-
google/protobuf/map_entry_lite.h
-
google/protobuf/wire_format_lite.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/map_type_handler.h
google/protobuf/arena.h
-
google/protobuf/wire_format_lite_inl.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/message.cc
iostream
-
stack
-
google/protobuf/stubs/hash.h
-
google/protobuf/message.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/mutex.h
-
google/protobuf/stubs/once.h
-
google/protobuf/reflection_internal.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/io/zero_copy_stream_impl.h
-
google/protobuf/descriptor.pb.h
-
google/protobuf/descriptor.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/map_field.h
-
google/protobuf/reflection_ops.h
-
google/protobuf/wire_format.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/map_util.h
-
google/protobuf/stubs/singleton.h
-
google/protobuf/stubs/stl_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/message.h
iosfwd
-
string
-
google/protobuf/stubs/type_traits.h
-
vector
-
google/protobuf/arena.h
-
google/protobuf/message_lite.h
-
google/protobuf/stubs/common.h
-
google/protobuf/descriptor.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/message_lite.cc
climits
-
google/protobuf/arena.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/message_lite.h
-
google/protobuf/repeated_field.h
-
string
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/io/zero_copy_stream_impl_lite.h
-
google/protobuf/stubs/stl_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/message_lite.h
climits
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/once.h
-
google/protobuf/stubs/port.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/metadata.h
google/protobuf/metadata_lite.h
-
google/protobuf/unknown_field_set.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/metadata_lite.h
google/protobuf/stubs/common.h
-
google/protobuf/arena.h
-
google/protobuf/message_lite.h
-
google/protobuf/stubs/port.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/reflection.h
memory
-
google/protobuf/stubs/shared_ptr.h
-
google/protobuf/message.h
-
google/protobuf/generated_enum_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/reflection_internal.h
google/protobuf/map_field.h
-
google/protobuf/reflection.h
-
google/protobuf/repeated_field.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/reflection_ops.cc
string
-
vector
-
google/protobuf/reflection_ops.h
-
google/protobuf/descriptor.h
-
google/protobuf/descriptor.pb.h
-
google/protobuf/map_field.h
-
google/protobuf/unknown_field_set.h
-
google/protobuf/stubs/strutil.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/reflection_ops.h
google/protobuf/stubs/common.h
-
google/protobuf/message.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/repeated_field.cc
algorithm
-
google/protobuf/repeated_field.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/repeated_field.h
algorithm
-
iterator
-
limits
-
string
-
google/protobuf/stubs/casts.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/type_traits.h
-
google/protobuf/arena.h
-
google/protobuf/message_lite.h
-
google/protobuf/stubs/port.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/service.cc
google/protobuf/service.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/service.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/callback.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/source_context.pb.cc
google/protobuf/source_context.pb.h
-
algorithm
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/port.h
-
google/protobuf/stubs/once.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/wire_format_lite_inl.h
-
google/protobuf/descriptor.h
-
google/protobuf/generated_message_reflection.h
-
google/protobuf/reflection_ops.h
-
google/protobuf/wire_format.h
-
third_party/protobuf/version.h
D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/third_party/protobuf/version.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/source_context.pb.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/arena.h
-
google/protobuf/arenastring.h
-
google/protobuf/generated_message_table_driven.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/metadata.h
-
google/protobuf/message.h
-
google/protobuf/repeated_field.h
-
google/protobuf/extension_set.h
-
google/protobuf/unknown_field_set.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/struct.pb.cc
google/protobuf/struct.pb.h
-
algorithm
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/port.h
-
google/protobuf/stubs/once.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/wire_format_lite_inl.h
-
google/protobuf/descriptor.h
-
google/protobuf/generated_message_reflection.h
-
google/protobuf/reflection_ops.h
-
google/protobuf/wire_format.h
-
third_party/protobuf/version.h
D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/third_party/protobuf/version.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/struct.pb.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/arena.h
-
google/protobuf/arenastring.h
-
google/protobuf/generated_message_table_driven.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/metadata.h
-
google/protobuf/message.h
-
google/protobuf/repeated_field.h
-
google/protobuf/extension_set.h
-
google/protobuf/map.h
-
google/protobuf/map_entry.h
-
google/protobuf/map_field_inl.h
-
google/protobuf/generated_enum_reflection.h
-
google/protobuf/unknown_field_set.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomic_sequence_num.h
google/protobuf/stubs/atomicops.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops.h
google/protobuf/stubs/common.h
-
google/protobuf/stubs/platform_macros.h
-
google/protobuf/stubs/atomicops_internals_tsan.h
-
google/protobuf/stubs/atomicops_internals_x86_msvc.h
-
google/protobuf/stubs/atomicops_internals_solaris.h
-
google/protobuf/stubs/atomicops_internals_power.h
-
google/protobuf/stubs/atomicops_internals_x86_gcc.h
-
google/protobuf/stubs/atomicops_internals_generic_gcc.h
-
google/protobuf/stubs/atomicops_internals_arm_gcc.h
-
google/protobuf/stubs/atomicops_internals_arm64_gcc.h
-
google/protobuf/stubs/atomicops_internals_arm_qnx.h
-
google/protobuf/stubs/atomicops_internals_mips_gcc.h
-
google/protobuf/stubs/atomicops_internals_power.h
-
google/protobuf/stubs/atomicops_internals_generic_c11_atomic.h
-
google/protobuf/stubs/atomicops_internals_ppc_gcc.h
-
google/protobuf/stubs/atomicops_internals_generic_gcc.h
-
google/protobuf/stubs/atomicops_internals_generic_gcc.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops_internals_arm64_gcc.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops_internals_arm_gcc.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops_internals_arm_qnx.h
pthread.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops_internals_generic_c11_atomic.h
atomic
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops_internals_generic_gcc.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops_internals_mips_gcc.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops_internals_power.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops_internals_ppc_gcc.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops_internals_solaris.h
atomic.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops_internals_tsan.h
sanitizer/tsan_interface_atomic.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops_internals_x86_gcc.cc
cstring
-
google/protobuf/stubs/atomicops.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops_internals_x86_gcc.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/atomicops_internals_x86_msvc.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/bytestream.cc
google/protobuf/stubs/bytestream.h
-
string.h
-
algorithm
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/bytestream.h
stddef.h
-
string
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/stringpiece.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/callback.h
google/protobuf/stubs/macros.h
-
google/protobuf/stubs/type_traits.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/casts.h
google/protobuf/stubs/common.h
-
google/protobuf/stubs/type_traits.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/common.cc
google/protobuf/message_lite.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/once.h
-
google/protobuf/stubs/status.h
-
google/protobuf/stubs/stringpiece.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/int128.h
-
errno.h
-
sstream
-
stdio.h
-
vector
-
windows.h
-
pthread.h
-
android/log.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/common.h
algorithm
-
iostream
-
map
-
set
-
string
-
vector
-
google/protobuf/stubs/port.h
-
google/protobuf/stubs/macros.h
-
google/protobuf/stubs/platform_macros.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/scoped_ptr.h
-
google/protobuf/stubs/mutex.h
-
google/protobuf/stubs/callback.h
-
exception
-
TargetConditionals.h
-
pthread.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/fastmem.h
stddef.h
-
stdio.h
-
string.h
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/hash.h
string.h
-
google/protobuf/stubs/common.h
-
ext/hash_map
-
ext/hash_set
-
backward/hash_map
-
backward/hash_set
-
hash_map
-
hash_set
-
hash_map
-
hash_set
-
hash_map
-
hash_set
-
hash_map
-
hash_set
-
unordered_map
-
unordered_set
-
tr1/unordered_map
-
tr1/unordered_set
-
map
-
set
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/int128.cc
google/protobuf/stubs/int128.h
-
iomanip
-
ostream
-
sstream
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/int128.h
google/protobuf/stubs/common.h
-
iosfwd
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/io_win32.cc
ctype.h
-
direct.h
-
errno.h
-
fcntl.h
-
io.h
-
sys/stat.h
-
sys/types.h
-
wctype.h
-
windows.h
-
google/protobuf/stubs/io_win32.h
-
google/protobuf/stubs/scoped_ptr.h
-
memory
-
sstream
-
string
-
vector
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/io_win32.h
string
-
google/protobuf/stubs/port.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/logging.h
google/protobuf/stubs/macros.h
-
google/protobuf/stubs/port.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/macros.h
google/protobuf/stubs/port.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/map_util.h
stddef.h
-
iterator
-
string
-
utility
-
vector
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/mathlimits.cc
google/protobuf/stubs/mathlimits.h
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/mathlimits.h
cmath
-
cmath
-
math.h
-
string.h
-
cfloat
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/mathutil.h
float.h
-
math.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/mathlimits.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/mutex.h
pthread.h
-
google/protobuf/stubs/macros.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/once.cc
google/protobuf/stubs/once.h
-
windows.h
-
sched.h
-
google/protobuf/stubs/atomicops.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/once.h
google/protobuf/stubs/atomicops.h
-
google/protobuf/stubs/callback.h
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/platform_macros.h
TargetConditionals.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/port.h
assert.h
-
stdlib.h
-
cstddef
-
string
-
string.h
-
inttypes.h
-
stdint.h
-
google/protobuf/stubs/platform_macros.h
-
sys/param.h
-
endian.h
-
stdlib.h
-
libkern/OSByteOrder.h
-
byteswap.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/scoped_ptr.h
google/protobuf/stubs/port.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/shared_ptr.h
google/protobuf/stubs/atomicops.h
-
algorithm
-
stddef.h
-
memory
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/singleton.h
google/protobuf/stubs/atomicops.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/once.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/status.cc
google/protobuf/stubs/status.h
-
ostream
-
stdio.h
-
string
-
utility
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/status.h
iosfwd
-
string
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/stringpiece.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/status_macros.h
google/protobuf/stubs/common.h
-
google/protobuf/stubs/status.h
-
google/protobuf/stubs/statusor.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/statusor.cc
google/protobuf/stubs/statusor.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/statusor.h
new
-
string
-
utility
-
google/protobuf/stubs/status.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/stl_util.h
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/stringpiece.cc
google/protobuf/stubs/stringpiece.h
-
string.h
-
algorithm
-
climits
-
string
-
ostream
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/stringpiece.h
assert.h
-
stddef.h
-
string.h
-
iosfwd
-
limits
-
string
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/hash.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/stringprintf.cc
google/protobuf/stubs/stringprintf.h
-
errno.h
-
stdarg.h
-
stdio.h
-
vector
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/stringprintf.h
stdarg.h
-
string
-
vector
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/structurally_valid.cc
google/protobuf/stubs/common.h
-
google/protobuf/stubs/stringpiece.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/strutil.cc
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/mathlimits.h
-
errno.h
-
float.h
-
limits
-
limits.h
-
stdio.h
-
iterator
-
google/protobuf/stubs/stl_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/strutil.h
stdlib.h
-
vector
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/stringpiece.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/substitute.cc
google/protobuf/stubs/substitute.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/stl_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/substitute.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/strutil.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/template_util.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/time.cc
google/protobuf/stubs/time.h
-
ctime
-
google/protobuf/stubs/stringprintf.h
-
google/protobuf/stubs/strutil.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/time.h
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/stubs/type_traits.h
cstddef
-
utility
-
google/protobuf/stubs/template_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/text_format.cc
algorithm
-
float.h
-
math.h
-
stdio.h
-
stack
-
limits
-
vector
-
google/protobuf/text_format.h
-
google/protobuf/stubs/stringprintf.h
-
google/protobuf/any.h
-
google/protobuf/io/strtod.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/io/tokenizer.h
-
google/protobuf/io/zero_copy_stream.h
-
google/protobuf/io/zero_copy_stream_impl.h
-
google/protobuf/descriptor.pb.h
-
google/protobuf/descriptor.h
-
google/protobuf/dynamic_message.h
-
google/protobuf/repeated_field.h
-
google/protobuf/unknown_field_set.h
-
google/protobuf/wire_format_lite.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/map_util.h
-
google/protobuf/stubs/stl_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/text_format.h
map
-
memory
-
google/protobuf/stubs/shared_ptr.h
-
string
-
vector
-
google/protobuf/stubs/common.h
-
google/protobuf/descriptor.h
-
google/protobuf/message.h
-
google/protobuf/message_lite.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/timestamp.pb.cc
google/protobuf/timestamp.pb.h
-
algorithm
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/port.h
-
google/protobuf/stubs/once.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/wire_format_lite_inl.h
-
google/protobuf/descriptor.h
-
google/protobuf/generated_message_reflection.h
-
google/protobuf/reflection_ops.h
-
google/protobuf/wire_format.h
-
third_party/protobuf/version.h
D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/third_party/protobuf/version.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/timestamp.pb.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/arena.h
-
google/protobuf/arenastring.h
-
google/protobuf/generated_message_table_driven.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/metadata.h
-
google/protobuf/message.h
-
google/protobuf/repeated_field.h
-
google/protobuf/extension_set.h
-
google/protobuf/unknown_field_set.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/type.pb.cc
google/protobuf/type.pb.h
-
algorithm
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/port.h
-
google/protobuf/stubs/once.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/wire_format_lite_inl.h
-
google/protobuf/descriptor.h
-
google/protobuf/generated_message_reflection.h
-
google/protobuf/reflection_ops.h
-
google/protobuf/wire_format.h
-
third_party/protobuf/version.h
D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/third_party/protobuf/version.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/type.pb.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/arena.h
-
google/protobuf/arenastring.h
-
google/protobuf/generated_message_table_driven.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/metadata.h
-
google/protobuf/message.h
-
google/protobuf/repeated_field.h
-
google/protobuf/extension_set.h
-
google/protobuf/generated_enum_reflection.h
-
google/protobuf/unknown_field_set.h
-
google/protobuf/any.pb.h
-
google/protobuf/source_context.pb.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/unknown_field_set.cc
google/protobuf/unknown_field_set.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/io/zero_copy_stream.h
-
google/protobuf/io/zero_copy_stream_impl.h
-
google/protobuf/metadata.h
-
google/protobuf/wire_format.h
-
google/protobuf/stubs/stl_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/unknown_field_set.h
assert.h
-
string
-
vector
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/message_lite.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/delimited_message_util.cc
google/protobuf/util/delimited_message_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/delimited_message_util.h
ostream
-
google/protobuf/message_lite.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/io/zero_copy_stream_impl.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/field_comparator.cc
google/protobuf/util/field_comparator.h
-
string
-
google/protobuf/descriptor.h
-
google/protobuf/message.h
-
google/protobuf/stubs/map_util.h
-
google/protobuf/stubs/mathlimits.h
-
google/protobuf/stubs/mathutil.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/field_comparator.h
map
-
string
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/field_mask_util.cc
google/protobuf/util/field_mask_util.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/map_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/field_mask_util.h
string
-
google/protobuf/descriptor.h
-
google/protobuf/field_mask.pb.h
-
google/protobuf/stubs/stringpiece.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/constants.h
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/datapiece.cc
google/protobuf/util/internal/datapiece.h
-
google/protobuf/struct.pb.h
-
google/protobuf/type.pb.h
-
google/protobuf/descriptor.h
-
google/protobuf/util/internal/utility.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/mathlimits.h
-
google/protobuf/stubs/mathutil.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/datapiece.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/stringpiece.h
-
google/protobuf/stubs/statusor.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/default_value_objectwriter.cc
google/protobuf/util/internal/default_value_objectwriter.h
-
google/protobuf/stubs/hash.h
-
google/protobuf/util/internal/constants.h
-
google/protobuf/util/internal/utility.h
-
google/protobuf/stubs/map_util.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/default_value_objectwriter.h
memory
-
google/protobuf/stubs/shared_ptr.h
-
stack
-
vector
-
google/protobuf/stubs/callback.h
-
google/protobuf/stubs/common.h
-
google/protobuf/util/internal/type_info.h
-
google/protobuf/util/internal/datapiece.h
-
google/protobuf/util/internal/object_writer.h
-
google/protobuf/util/internal/utility.h
-
google/protobuf/util/type_resolver.h
-
google/protobuf/stubs/stringpiece.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/error_listener.h
algorithm
-
memory
-
google/protobuf/stubs/shared_ptr.h
-
string
-
vector
-
google/protobuf/stubs/callback.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/util/internal/location_tracker.h
-
google/protobuf/stubs/stringpiece.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/field_mask_utility.cc
google/protobuf/util/internal/field_mask_utility.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/status_macros.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/field_mask_utility.h
functional
-
stack
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/stringpiece.h
-
google/protobuf/stubs/status.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/json_escaping.cc
google/protobuf/util/internal/json_escaping.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/json_escaping.h
google/protobuf/stubs/common.h
-
google/protobuf/stubs/bytestream.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/json_objectwriter.cc
google/protobuf/util/internal/json_objectwriter.h
-
math.h
-
google/protobuf/stubs/casts.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-
google/protobuf/util/internal/utility.h
-
google/protobuf/util/internal/json_escaping.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/mathlimits.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/json_objectwriter.h
memory
-
google/protobuf/stubs/shared_ptr.h
-
string
-
google/protobuf/io/coded_stream.h
-
google/protobuf/util/internal/structured_objectwriter.h
-
google/protobuf/stubs/bytestream.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/json_stream_parser.cc
google/protobuf/util/internal/json_stream_parser.h
-
algorithm
-
cctype
-
cerrno
-
cstdlib
-
cstring
-
memory
-
google/protobuf/stubs/shared_ptr.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-
google/protobuf/util/internal/object_writer.h
-
google/protobuf/util/internal/json_escaping.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/mathlimits.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/json_stream_parser.h
stack
-
string
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/stringpiece.h
-
google/protobuf/stubs/status.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/location_tracker.h
string
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/object_location_tracker.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/util/internal/location_tracker.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/object_source.h
google/protobuf/stubs/common.h
-
google/protobuf/stubs/stringpiece.h
-
google/protobuf/stubs/status.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/object_writer.cc
google/protobuf/util/internal/object_writer.h
-
google/protobuf/util/internal/datapiece.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/object_writer.h
google/protobuf/stubs/common.h
-
google/protobuf/stubs/stringpiece.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/proto_writer.cc
google/protobuf/util/internal/proto_writer.h
-
functional
-
stack
-
google/protobuf/stubs/once.h
-
google/protobuf/stubs/time.h
-
google/protobuf/wire_format_lite.h
-
google/protobuf/util/internal/field_mask_utility.h
-
google/protobuf/util/internal/object_location_tracker.h
-
google/protobuf/util/internal/constants.h
-
google/protobuf/util/internal/utility.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/map_util.h
-
google/protobuf/stubs/statusor.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/proto_writer.h
deque
-
string
-
vector
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/io/zero_copy_stream_impl.h
-
google/protobuf/descriptor.h
-
google/protobuf/util/internal/type_info.h
-
google/protobuf/util/internal/datapiece.h
-
google/protobuf/util/internal/error_listener.h
-
google/protobuf/util/internal/structured_objectwriter.h
-
google/protobuf/util/type_resolver.h
-
google/protobuf/stubs/bytestream.h
-
google/protobuf/stubs/hash.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/protostream_objectsource.cc
google/protobuf/util/internal/protostream_objectsource.h
-
utility
-
google/protobuf/stubs/casts.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/stringprintf.h
-
google/protobuf/stubs/time.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/io/zero_copy_stream_impl.h
-
google/protobuf/descriptor.h
-
google/protobuf/wire_format.h
-
google/protobuf/wire_format_lite.h
-
google/protobuf/util/internal/field_mask_utility.h
-
google/protobuf/util/internal/constants.h
-
google/protobuf/util/internal/utility.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/map_util.h
-
google/protobuf/stubs/status_macros.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/protostream_objectsource.h
functional
-
google/protobuf/stubs/hash.h
-
string
-
google/protobuf/stubs/common.h
-
google/protobuf/type.pb.h
-
google/protobuf/util/internal/object_source.h
-
google/protobuf/util/internal/object_writer.h
-
google/protobuf/util/internal/type_info.h
-
google/protobuf/util/type_resolver.h
-
google/protobuf/stubs/stringpiece.h
-
google/protobuf/stubs/status.h
-
google/protobuf/stubs/statusor.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/protostream_objectwriter.cc
google/protobuf/util/internal/protostream_objectwriter.h
-
functional
-
stack
-
google/protobuf/stubs/once.h
-
google/protobuf/stubs/time.h
-
google/protobuf/wire_format_lite.h
-
google/protobuf/util/internal/field_mask_utility.h
-
google/protobuf/util/internal/object_location_tracker.h
-
google/protobuf/util/internal/constants.h
-
google/protobuf/util/internal/utility.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/map_util.h
-
google/protobuf/stubs/statusor.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/protostream_objectwriter.h
deque
-
google/protobuf/stubs/hash.h
-
string
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/io/zero_copy_stream_impl.h
-
google/protobuf/descriptor.h
-
google/protobuf/util/internal/type_info.h
-
google/protobuf/util/internal/datapiece.h
-
google/protobuf/util/internal/error_listener.h
-
google/protobuf/util/internal/proto_writer.h
-
google/protobuf/util/internal/structured_objectwriter.h
-
google/protobuf/util/type_resolver.h
-
google/protobuf/stubs/bytestream.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/structured_objectwriter.h
memory
-
google/protobuf/stubs/shared_ptr.h
-
google/protobuf/stubs/casts.h
-
google/protobuf/stubs/common.h
-
google/protobuf/util/internal/object_writer.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/type_info.cc
google/protobuf/util/internal/type_info.h
-
map
-
set
-
google/protobuf/stubs/common.h
-
google/protobuf/type.pb.h
-
google/protobuf/util/internal/utility.h
-
google/protobuf/stubs/stringpiece.h
-
google/protobuf/stubs/map_util.h
-
google/protobuf/stubs/status.h
-
google/protobuf/stubs/statusor.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/type_info.h
google/protobuf/stubs/common.h
-
google/protobuf/type.pb.h
-
google/protobuf/util/type_resolver.h
-
google/protobuf/stubs/stringpiece.h
-
google/protobuf/stubs/status.h
-
google/protobuf/stubs/statusor.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/utility.cc
google/protobuf/util/internal/utility.h
-
algorithm
-
google/protobuf/stubs/callback.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/wrappers.pb.h
-
google/protobuf/descriptor.pb.h
-
google/protobuf/descriptor.h
-
google/protobuf/util/internal/constants.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/map_util.h
-
google/protobuf/stubs/mathlimits.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/internal/utility.h
memory
-
google/protobuf/stubs/shared_ptr.h
-
string
-
utility
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/type.pb.h
-
google/protobuf/repeated_field.h
-
google/protobuf/stubs/stringpiece.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/status.h
-
google/protobuf/stubs/statusor.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/json_util.cc
google/protobuf/util/json_util.h
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/io/zero_copy_stream.h
-
google/protobuf/util/internal/default_value_objectwriter.h
-
google/protobuf/util/internal/error_listener.h
-
google/protobuf/util/internal/json_objectwriter.h
-
google/protobuf/util/internal/json_stream_parser.h
-
google/protobuf/util/internal/protostream_objectsource.h
-
google/protobuf/util/internal/protostream_objectwriter.h
-
google/protobuf/util/type_resolver.h
-
google/protobuf/util/type_resolver_util.h
-
google/protobuf/stubs/bytestream.h
-
google/protobuf/stubs/status_macros.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/json_util.h
google/protobuf/message.h
-
google/protobuf/util/type_resolver.h
-
google/protobuf/stubs/bytestream.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/message_differencer.cc
google/protobuf/util/message_differencer.h
-
algorithm
-
memory
-
google/protobuf/stubs/shared_ptr.h
-
utility
-
google/protobuf/stubs/callback.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/stringprintf.h
-
google/protobuf/any.h
-
google/protobuf/io/printer.h
-
google/protobuf/io/zero_copy_stream.h
-
google/protobuf/io/zero_copy_stream_impl.h
-
google/protobuf/descriptor.pb.h
-
google/protobuf/dynamic_message.h
-
google/protobuf/text_format.h
-
google/protobuf/util/field_comparator.h
-
google/protobuf/stubs/strutil.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/message_differencer.h
map
-
set
-
string
-
vector
-
google/protobuf/descriptor.h
-
google/protobuf/message.h
-
google/protobuf/unknown_field_set.h
-
google/protobuf/util/field_comparator.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/time_util.cc
google/protobuf/util/time_util.h
-
google/protobuf/stubs/time.h
-
google/protobuf/stubs/int128.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/stringprintf.h
-
google/protobuf/duration.pb.h
-
google/protobuf/timestamp.pb.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/time_util.h
ctime
-
ostream
-
string
-
winsock2.h
-
sys/time.h
-
google/protobuf/duration.pb.h
-
google/protobuf/timestamp.pb.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/type_resolver.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/status.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/type_resolver_util.cc
google/protobuf/util/type_resolver_util.h
-
google/protobuf/type.pb.h
-
google/protobuf/wrappers.pb.h
-
google/protobuf/descriptor.pb.h
-
google/protobuf/descriptor.h
-
google/protobuf/util/internal/utility.h
-
google/protobuf/util/type_resolver.h
-
google/protobuf/stubs/strutil.h
-
google/protobuf/stubs/status.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/util/type_resolver_util.h
string
-
google/protobuf/stubs/common.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/wire_format.cc
stack
-
string
-
vector
-
google/protobuf/wire_format.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/stringprintf.h
-
google/protobuf/descriptor.h
-
google/protobuf/dynamic_message.h
-
google/protobuf/map_field.h
-
google/protobuf/wire_format_lite_inl.h
-
google/protobuf/descriptor.pb.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/io/zero_copy_stream.h
-
google/protobuf/io/zero_copy_stream_impl.h
-
google/protobuf/unknown_field_set.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/wire_format.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/descriptor.h
-
google/protobuf/message.h
-
google/protobuf/wire_format_lite.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/wire_format_lite.cc
google/protobuf/wire_format_lite_inl.h
-
immintrin.h
-
stack
-
string
-
vector
-
google/protobuf/stubs/logging.h
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/stringprintf.h
-
google/protobuf/io/coded_stream_inl.h
-
google/protobuf/io/zero_copy_stream.h
-
google/protobuf/io/zero_copy_stream_impl_lite.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/wire_format_lite.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/message_lite.h
-
google/protobuf/stubs/port.h
-
google/protobuf/repeated_field.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/wire_format_lite_inl.h
algorithm
-
string
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/logging.h
-
google/protobuf/message_lite.h
-
google/protobuf/repeated_field.h
-
google/protobuf/wire_format_lite.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/arenastring.h
-

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/wrappers.pb.cc
google/protobuf/wrappers.pb.h
-
algorithm
-
google/protobuf/stubs/common.h
-
google/protobuf/stubs/port.h
-
google/protobuf/stubs/once.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/wire_format_lite_inl.h
-
google/protobuf/descriptor.h
-
google/protobuf/generated_message_reflection.h
-
google/protobuf/reflection_ops.h
-
google/protobuf/wire_format.h
-
third_party/protobuf/version.h
D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/third_party/protobuf/version.h

D:/unet/opencv/opencv/sources/3rdparty/protobuf/src/google/protobuf/wrappers.pb.h
string
-
google/protobuf/stubs/common.h
-
google/protobuf/io/coded_stream.h
-
google/protobuf/arena.h
-
google/protobuf/arenastring.h
-
google/protobuf/generated_message_table_driven.h
-
google/protobuf/generated_message_util.h
-
google/protobuf/metadata.h
-
google/protobuf/message.h
-
google/protobuf/repeated_field.h
-
google/protobuf/extension_set.h
-
google/protobuf/unknown_field_set.h
-

