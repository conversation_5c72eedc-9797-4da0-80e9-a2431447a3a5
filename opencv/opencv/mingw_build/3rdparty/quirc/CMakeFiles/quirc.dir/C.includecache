#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

D:/unet/opencv/opencv/sources/3rdparty/quirc/include/quirc.h
stdint.h
-

D:/unet/opencv/opencv/sources/3rdparty/quirc/include/quirc_internal.h
quirc.h
-

D:/unet/opencv/opencv/sources/3rdparty/quirc/src/decode.c
quirc_internal.h
-
string.h
-
stdlib.h
-

D:/unet/opencv/opencv/sources/3rdparty/quirc/src/quirc.c
stdlib.h
-
string.h
-
quirc_internal.h
-

D:/unet/opencv/opencv/sources/3rdparty/quirc/src/version_db.c
quirc_internal.h
-

