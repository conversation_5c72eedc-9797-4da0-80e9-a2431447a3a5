# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\3rdparty\libpng\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libpng/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libpng/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libpng/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libpng/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
3rdparty/libpng/CMakeFiles/libpng.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libpng/CMakeFiles/libpng.dir/rule
.PHONY : 3rdparty/libpng/CMakeFiles/libpng.dir/rule

# Convenience name for target.
libpng: 3rdparty/libpng/CMakeFiles/libpng.dir/rule

.PHONY : libpng

# fast build rule for target.
libpng/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/build
.PHONY : libpng/fast

intel/filter_sse2_intrinsics.obj: intel/filter_sse2_intrinsics.c.obj

.PHONY : intel/filter_sse2_intrinsics.obj

# target to build an object file
intel/filter_sse2_intrinsics.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/intel/filter_sse2_intrinsics.c.obj
.PHONY : intel/filter_sse2_intrinsics.c.obj

intel/filter_sse2_intrinsics.i: intel/filter_sse2_intrinsics.c.i

.PHONY : intel/filter_sse2_intrinsics.i

# target to preprocess a source file
intel/filter_sse2_intrinsics.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/intel/filter_sse2_intrinsics.c.i
.PHONY : intel/filter_sse2_intrinsics.c.i

intel/filter_sse2_intrinsics.s: intel/filter_sse2_intrinsics.c.s

.PHONY : intel/filter_sse2_intrinsics.s

# target to generate assembly for a file
intel/filter_sse2_intrinsics.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/intel/filter_sse2_intrinsics.c.s
.PHONY : intel/filter_sse2_intrinsics.c.s

intel/intel_init.obj: intel/intel_init.c.obj

.PHONY : intel/intel_init.obj

# target to build an object file
intel/intel_init.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/intel/intel_init.c.obj
.PHONY : intel/intel_init.c.obj

intel/intel_init.i: intel/intel_init.c.i

.PHONY : intel/intel_init.i

# target to preprocess a source file
intel/intel_init.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/intel/intel_init.c.i
.PHONY : intel/intel_init.c.i

intel/intel_init.s: intel/intel_init.c.s

.PHONY : intel/intel_init.s

# target to generate assembly for a file
intel/intel_init.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/intel/intel_init.c.s
.PHONY : intel/intel_init.c.s

png.obj: png.c.obj

.PHONY : png.obj

# target to build an object file
png.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/png.c.obj
.PHONY : png.c.obj

png.i: png.c.i

.PHONY : png.i

# target to preprocess a source file
png.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/png.c.i
.PHONY : png.c.i

png.s: png.c.s

.PHONY : png.s

# target to generate assembly for a file
png.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/png.c.s
.PHONY : png.c.s

pngerror.obj: pngerror.c.obj

.PHONY : pngerror.obj

# target to build an object file
pngerror.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngerror.c.obj
.PHONY : pngerror.c.obj

pngerror.i: pngerror.c.i

.PHONY : pngerror.i

# target to preprocess a source file
pngerror.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngerror.c.i
.PHONY : pngerror.c.i

pngerror.s: pngerror.c.s

.PHONY : pngerror.s

# target to generate assembly for a file
pngerror.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngerror.c.s
.PHONY : pngerror.c.s

pngget.obj: pngget.c.obj

.PHONY : pngget.obj

# target to build an object file
pngget.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngget.c.obj
.PHONY : pngget.c.obj

pngget.i: pngget.c.i

.PHONY : pngget.i

# target to preprocess a source file
pngget.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngget.c.i
.PHONY : pngget.c.i

pngget.s: pngget.c.s

.PHONY : pngget.s

# target to generate assembly for a file
pngget.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngget.c.s
.PHONY : pngget.c.s

pngmem.obj: pngmem.c.obj

.PHONY : pngmem.obj

# target to build an object file
pngmem.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngmem.c.obj
.PHONY : pngmem.c.obj

pngmem.i: pngmem.c.i

.PHONY : pngmem.i

# target to preprocess a source file
pngmem.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngmem.c.i
.PHONY : pngmem.c.i

pngmem.s: pngmem.c.s

.PHONY : pngmem.s

# target to generate assembly for a file
pngmem.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngmem.c.s
.PHONY : pngmem.c.s

pngpread.obj: pngpread.c.obj

.PHONY : pngpread.obj

# target to build an object file
pngpread.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngpread.c.obj
.PHONY : pngpread.c.obj

pngpread.i: pngpread.c.i

.PHONY : pngpread.i

# target to preprocess a source file
pngpread.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngpread.c.i
.PHONY : pngpread.c.i

pngpread.s: pngpread.c.s

.PHONY : pngpread.s

# target to generate assembly for a file
pngpread.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngpread.c.s
.PHONY : pngpread.c.s

pngread.obj: pngread.c.obj

.PHONY : pngread.obj

# target to build an object file
pngread.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngread.c.obj
.PHONY : pngread.c.obj

pngread.i: pngread.c.i

.PHONY : pngread.i

# target to preprocess a source file
pngread.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngread.c.i
.PHONY : pngread.c.i

pngread.s: pngread.c.s

.PHONY : pngread.s

# target to generate assembly for a file
pngread.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngread.c.s
.PHONY : pngread.c.s

pngrio.obj: pngrio.c.obj

.PHONY : pngrio.obj

# target to build an object file
pngrio.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngrio.c.obj
.PHONY : pngrio.c.obj

pngrio.i: pngrio.c.i

.PHONY : pngrio.i

# target to preprocess a source file
pngrio.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngrio.c.i
.PHONY : pngrio.c.i

pngrio.s: pngrio.c.s

.PHONY : pngrio.s

# target to generate assembly for a file
pngrio.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngrio.c.s
.PHONY : pngrio.c.s

pngrtran.obj: pngrtran.c.obj

.PHONY : pngrtran.obj

# target to build an object file
pngrtran.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngrtran.c.obj
.PHONY : pngrtran.c.obj

pngrtran.i: pngrtran.c.i

.PHONY : pngrtran.i

# target to preprocess a source file
pngrtran.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngrtran.c.i
.PHONY : pngrtran.c.i

pngrtran.s: pngrtran.c.s

.PHONY : pngrtran.s

# target to generate assembly for a file
pngrtran.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngrtran.c.s
.PHONY : pngrtran.c.s

pngrutil.obj: pngrutil.c.obj

.PHONY : pngrutil.obj

# target to build an object file
pngrutil.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngrutil.c.obj
.PHONY : pngrutil.c.obj

pngrutil.i: pngrutil.c.i

.PHONY : pngrutil.i

# target to preprocess a source file
pngrutil.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngrutil.c.i
.PHONY : pngrutil.c.i

pngrutil.s: pngrutil.c.s

.PHONY : pngrutil.s

# target to generate assembly for a file
pngrutil.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngrutil.c.s
.PHONY : pngrutil.c.s

pngset.obj: pngset.c.obj

.PHONY : pngset.obj

# target to build an object file
pngset.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngset.c.obj
.PHONY : pngset.c.obj

pngset.i: pngset.c.i

.PHONY : pngset.i

# target to preprocess a source file
pngset.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngset.c.i
.PHONY : pngset.c.i

pngset.s: pngset.c.s

.PHONY : pngset.s

# target to generate assembly for a file
pngset.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngset.c.s
.PHONY : pngset.c.s

pngtrans.obj: pngtrans.c.obj

.PHONY : pngtrans.obj

# target to build an object file
pngtrans.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngtrans.c.obj
.PHONY : pngtrans.c.obj

pngtrans.i: pngtrans.c.i

.PHONY : pngtrans.i

# target to preprocess a source file
pngtrans.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngtrans.c.i
.PHONY : pngtrans.c.i

pngtrans.s: pngtrans.c.s

.PHONY : pngtrans.s

# target to generate assembly for a file
pngtrans.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngtrans.c.s
.PHONY : pngtrans.c.s

pngwio.obj: pngwio.c.obj

.PHONY : pngwio.obj

# target to build an object file
pngwio.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngwio.c.obj
.PHONY : pngwio.c.obj

pngwio.i: pngwio.c.i

.PHONY : pngwio.i

# target to preprocess a source file
pngwio.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngwio.c.i
.PHONY : pngwio.c.i

pngwio.s: pngwio.c.s

.PHONY : pngwio.s

# target to generate assembly for a file
pngwio.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngwio.c.s
.PHONY : pngwio.c.s

pngwrite.obj: pngwrite.c.obj

.PHONY : pngwrite.obj

# target to build an object file
pngwrite.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngwrite.c.obj
.PHONY : pngwrite.c.obj

pngwrite.i: pngwrite.c.i

.PHONY : pngwrite.i

# target to preprocess a source file
pngwrite.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngwrite.c.i
.PHONY : pngwrite.c.i

pngwrite.s: pngwrite.c.s

.PHONY : pngwrite.s

# target to generate assembly for a file
pngwrite.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngwrite.c.s
.PHONY : pngwrite.c.s

pngwtran.obj: pngwtran.c.obj

.PHONY : pngwtran.obj

# target to build an object file
pngwtran.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngwtran.c.obj
.PHONY : pngwtran.c.obj

pngwtran.i: pngwtran.c.i

.PHONY : pngwtran.i

# target to preprocess a source file
pngwtran.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngwtran.c.i
.PHONY : pngwtran.c.i

pngwtran.s: pngwtran.c.s

.PHONY : pngwtran.s

# target to generate assembly for a file
pngwtran.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngwtran.c.s
.PHONY : pngwtran.c.s

pngwutil.obj: pngwutil.c.obj

.PHONY : pngwutil.obj

# target to build an object file
pngwutil.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngwutil.c.obj
.PHONY : pngwutil.c.obj

pngwutil.i: pngwutil.c.i

.PHONY : pngwutil.i

# target to preprocess a source file
pngwutil.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngwutil.c.i
.PHONY : pngwutil.c.i

pngwutil.s: pngwutil.c.s

.PHONY : pngwutil.s

# target to generate assembly for a file
pngwutil.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/pngwutil.c.s
.PHONY : pngwutil.c.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... package
	@echo ... libpng
	@echo ... package_source
	@echo ... rebuild_cache
	@echo ... test
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... list_install_components
	@echo ... install/strip
	@echo ... intel/filter_sse2_intrinsics.obj
	@echo ... intel/filter_sse2_intrinsics.i
	@echo ... intel/filter_sse2_intrinsics.s
	@echo ... intel/intel_init.obj
	@echo ... intel/intel_init.i
	@echo ... intel/intel_init.s
	@echo ... png.obj
	@echo ... png.i
	@echo ... png.s
	@echo ... pngerror.obj
	@echo ... pngerror.i
	@echo ... pngerror.s
	@echo ... pngget.obj
	@echo ... pngget.i
	@echo ... pngget.s
	@echo ... pngmem.obj
	@echo ... pngmem.i
	@echo ... pngmem.s
	@echo ... pngpread.obj
	@echo ... pngpread.i
	@echo ... pngpread.s
	@echo ... pngread.obj
	@echo ... pngread.i
	@echo ... pngread.s
	@echo ... pngrio.obj
	@echo ... pngrio.i
	@echo ... pngrio.s
	@echo ... pngrtran.obj
	@echo ... pngrtran.i
	@echo ... pngrtran.s
	@echo ... pngrutil.obj
	@echo ... pngrutil.i
	@echo ... pngrutil.s
	@echo ... pngset.obj
	@echo ... pngset.i
	@echo ... pngset.s
	@echo ... pngtrans.obj
	@echo ... pngtrans.i
	@echo ... pngtrans.s
	@echo ... pngwio.obj
	@echo ... pngwio.i
	@echo ... pngwio.s
	@echo ... pngwrite.obj
	@echo ... pngwrite.i
	@echo ... pngwrite.s
	@echo ... pngwtran.obj
	@echo ... pngwtran.i
	@echo ... pngwtran.s
	@echo ... pngwutil.obj
	@echo ... pngwutil.i
	@echo ... pngwutil.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

