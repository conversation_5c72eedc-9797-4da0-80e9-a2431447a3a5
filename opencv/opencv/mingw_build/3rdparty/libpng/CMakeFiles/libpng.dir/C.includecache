#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

D:/unet/opencv/opencv/sources/3rdparty/libpng/intel/filter_sse2_intrinsics.c
../pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h
immintrin.h
-

D:/unet/opencv/opencv/sources/3rdparty/libpng/intel/intel_init.c
../pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/png.c
pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/png.h
pnglibconf.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pnglibconf.h
pngconf.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngconf.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngconf.h
limits.h
-
stddef.h
-
stdio.h
-
setjmp.h
-
time.h
-

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngdebug.h
crtdbg.h
-
stdio.h
-

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngerror.c
pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngget.c
pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/pnginfo.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/pnglibconf.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngmem.c
pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpread.c
pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h
stdlib.h
-
string.h
-
config.h
-
pnglibconf.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pnglibconf.h
pngprefix.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngprefix.h
pngusr.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngusr.h
png.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/png.h
float.h
-
fp.h
-
math.h
-
m68881.h
-
mem.h
-
alloc.h
-
windows.h
-
pngstruct.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngstruct.h
pnginfo.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pnginfo.h
pngdebug.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngdebug.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngread.c
pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h
errno.h
-

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngrio.c
pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngrtran.c
pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h
arm64_neon.h
-
arm_neon.h
-

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngrutil.c
pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngset.c
pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngstruct.h
zlib.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/zlib.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngtrans.c
pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngwio.c
pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngwrite.c
pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h
errno.h
-

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngwtran.c
pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h

D:/unet/opencv/opencv/sources/3rdparty/libpng/pngwutil.c
pngpriv.h
D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpriv.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/zconf.h
stddef.h
-
windows.h
-
limits.h
-
sys/types.h
-
stdarg.h
-
stddef.h
-
unistd.h
-
unixio.h
-

D:/unet/opencv/opencv/sources/3rdparty/zlib/zlib.h
zconf.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zconf.h

