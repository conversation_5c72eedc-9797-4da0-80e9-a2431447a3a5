# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "C"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_C
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/intel/filter_sse2_intrinsics.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/intel/filter_sse2_intrinsics.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/intel/intel_init.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/intel/intel_init.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/png.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/png.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/pngerror.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/pngerror.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/pngget.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/pngget.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/pngmem.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/pngmem.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/pngpread.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/pngpread.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/pngread.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/pngread.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/pngrio.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/pngrio.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/pngrtran.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/pngrtran.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/pngrutil.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/pngrutil.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/pngset.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/pngset.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/pngtrans.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/pngtrans.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/pngwio.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/pngwio.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/pngwrite.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/pngwrite.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/pngwtran.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/pngwtran.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng/pngwutil.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libpng/CMakeFiles/libpng.dir/pngwutil.c.obj"
  )
set(CMAKE_C_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_C
  "PNG_INTEL_SSE"
  "PNG_MIPS_MSA_OPT=0"
  "_WIN32_WINNT=0x0601"
  )

# The include file search paths:
set(CMAKE_C_TARGET_INCLUDE_PATH
  "3rdparty/zlib"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib"
  "D:/unet/opencv/opencv/sources/3rdparty/libpng"
  "."
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
