# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libjasper/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libjasper/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libjasper/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libjasper/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
3rdparty/libjasper/CMakeFiles/libjasper.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libjasper/CMakeFiles/libjasper.dir/rule
.PHONY : 3rdparty/libjasper/CMakeFiles/libjasper.dir/rule

# Convenience name for target.
libjasper: 3rdparty/libjasper/CMakeFiles/libjasper.dir/rule

.PHONY : libjasper

# fast build rule for target.
libjasper/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/build
.PHONY : libjasper/fast

jas_cm.obj: jas_cm.c.obj

.PHONY : jas_cm.obj

# target to build an object file
jas_cm.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_cm.c.obj
.PHONY : jas_cm.c.obj

jas_cm.i: jas_cm.c.i

.PHONY : jas_cm.i

# target to preprocess a source file
jas_cm.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_cm.c.i
.PHONY : jas_cm.c.i

jas_cm.s: jas_cm.c.s

.PHONY : jas_cm.s

# target to generate assembly for a file
jas_cm.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_cm.c.s
.PHONY : jas_cm.c.s

jas_debug.obj: jas_debug.c.obj

.PHONY : jas_debug.obj

# target to build an object file
jas_debug.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_debug.c.obj
.PHONY : jas_debug.c.obj

jas_debug.i: jas_debug.c.i

.PHONY : jas_debug.i

# target to preprocess a source file
jas_debug.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_debug.c.i
.PHONY : jas_debug.c.i

jas_debug.s: jas_debug.c.s

.PHONY : jas_debug.s

# target to generate assembly for a file
jas_debug.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_debug.c.s
.PHONY : jas_debug.c.s

jas_getopt.obj: jas_getopt.c.obj

.PHONY : jas_getopt.obj

# target to build an object file
jas_getopt.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_getopt.c.obj
.PHONY : jas_getopt.c.obj

jas_getopt.i: jas_getopt.c.i

.PHONY : jas_getopt.i

# target to preprocess a source file
jas_getopt.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_getopt.c.i
.PHONY : jas_getopt.c.i

jas_getopt.s: jas_getopt.c.s

.PHONY : jas_getopt.s

# target to generate assembly for a file
jas_getopt.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_getopt.c.s
.PHONY : jas_getopt.c.s

jas_icc.obj: jas_icc.c.obj

.PHONY : jas_icc.obj

# target to build an object file
jas_icc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_icc.c.obj
.PHONY : jas_icc.c.obj

jas_icc.i: jas_icc.c.i

.PHONY : jas_icc.i

# target to preprocess a source file
jas_icc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_icc.c.i
.PHONY : jas_icc.c.i

jas_icc.s: jas_icc.c.s

.PHONY : jas_icc.s

# target to generate assembly for a file
jas_icc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_icc.c.s
.PHONY : jas_icc.c.s

jas_iccdata.obj: jas_iccdata.c.obj

.PHONY : jas_iccdata.obj

# target to build an object file
jas_iccdata.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_iccdata.c.obj
.PHONY : jas_iccdata.c.obj

jas_iccdata.i: jas_iccdata.c.i

.PHONY : jas_iccdata.i

# target to preprocess a source file
jas_iccdata.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_iccdata.c.i
.PHONY : jas_iccdata.c.i

jas_iccdata.s: jas_iccdata.c.s

.PHONY : jas_iccdata.s

# target to generate assembly for a file
jas_iccdata.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_iccdata.c.s
.PHONY : jas_iccdata.c.s

jas_image.obj: jas_image.c.obj

.PHONY : jas_image.obj

# target to build an object file
jas_image.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_image.c.obj
.PHONY : jas_image.c.obj

jas_image.i: jas_image.c.i

.PHONY : jas_image.i

# target to preprocess a source file
jas_image.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_image.c.i
.PHONY : jas_image.c.i

jas_image.s: jas_image.c.s

.PHONY : jas_image.s

# target to generate assembly for a file
jas_image.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_image.c.s
.PHONY : jas_image.c.s

jas_init.obj: jas_init.c.obj

.PHONY : jas_init.obj

# target to build an object file
jas_init.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_init.c.obj
.PHONY : jas_init.c.obj

jas_init.i: jas_init.c.i

.PHONY : jas_init.i

# target to preprocess a source file
jas_init.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_init.c.i
.PHONY : jas_init.c.i

jas_init.s: jas_init.c.s

.PHONY : jas_init.s

# target to generate assembly for a file
jas_init.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_init.c.s
.PHONY : jas_init.c.s

jas_malloc.obj: jas_malloc.c.obj

.PHONY : jas_malloc.obj

# target to build an object file
jas_malloc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_malloc.c.obj
.PHONY : jas_malloc.c.obj

jas_malloc.i: jas_malloc.c.i

.PHONY : jas_malloc.i

# target to preprocess a source file
jas_malloc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_malloc.c.i
.PHONY : jas_malloc.c.i

jas_malloc.s: jas_malloc.c.s

.PHONY : jas_malloc.s

# target to generate assembly for a file
jas_malloc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_malloc.c.s
.PHONY : jas_malloc.c.s

jas_seq.obj: jas_seq.c.obj

.PHONY : jas_seq.obj

# target to build an object file
jas_seq.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_seq.c.obj
.PHONY : jas_seq.c.obj

jas_seq.i: jas_seq.c.i

.PHONY : jas_seq.i

# target to preprocess a source file
jas_seq.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_seq.c.i
.PHONY : jas_seq.c.i

jas_seq.s: jas_seq.c.s

.PHONY : jas_seq.s

# target to generate assembly for a file
jas_seq.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_seq.c.s
.PHONY : jas_seq.c.s

jas_stream.obj: jas_stream.c.obj

.PHONY : jas_stream.obj

# target to build an object file
jas_stream.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_stream.c.obj
.PHONY : jas_stream.c.obj

jas_stream.i: jas_stream.c.i

.PHONY : jas_stream.i

# target to preprocess a source file
jas_stream.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_stream.c.i
.PHONY : jas_stream.c.i

jas_stream.s: jas_stream.c.s

.PHONY : jas_stream.s

# target to generate assembly for a file
jas_stream.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_stream.c.s
.PHONY : jas_stream.c.s

jas_string.obj: jas_string.c.obj

.PHONY : jas_string.obj

# target to build an object file
jas_string.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_string.c.obj
.PHONY : jas_string.c.obj

jas_string.i: jas_string.c.i

.PHONY : jas_string.i

# target to preprocess a source file
jas_string.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_string.c.i
.PHONY : jas_string.c.i

jas_string.s: jas_string.c.s

.PHONY : jas_string.s

# target to generate assembly for a file
jas_string.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_string.c.s
.PHONY : jas_string.c.s

jas_tmr.obj: jas_tmr.c.obj

.PHONY : jas_tmr.obj

# target to build an object file
jas_tmr.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tmr.c.obj
.PHONY : jas_tmr.c.obj

jas_tmr.i: jas_tmr.c.i

.PHONY : jas_tmr.i

# target to preprocess a source file
jas_tmr.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tmr.c.i
.PHONY : jas_tmr.c.i

jas_tmr.s: jas_tmr.c.s

.PHONY : jas_tmr.s

# target to generate assembly for a file
jas_tmr.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tmr.c.s
.PHONY : jas_tmr.c.s

jas_tvp.obj: jas_tvp.c.obj

.PHONY : jas_tvp.obj

# target to build an object file
jas_tvp.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tvp.c.obj
.PHONY : jas_tvp.c.obj

jas_tvp.i: jas_tvp.c.i

.PHONY : jas_tvp.i

# target to preprocess a source file
jas_tvp.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tvp.c.i
.PHONY : jas_tvp.c.i

jas_tvp.s: jas_tvp.c.s

.PHONY : jas_tvp.s

# target to generate assembly for a file
jas_tvp.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tvp.c.s
.PHONY : jas_tvp.c.s

jas_version.obj: jas_version.c.obj

.PHONY : jas_version.obj

# target to build an object file
jas_version.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_version.c.obj
.PHONY : jas_version.c.obj

jas_version.i: jas_version.c.i

.PHONY : jas_version.i

# target to preprocess a source file
jas_version.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_version.c.i
.PHONY : jas_version.c.i

jas_version.s: jas_version.c.s

.PHONY : jas_version.s

# target to generate assembly for a file
jas_version.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_version.c.s
.PHONY : jas_version.c.s

jp2_cod.obj: jp2_cod.c.obj

.PHONY : jp2_cod.obj

# target to build an object file
jp2_cod.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_cod.c.obj
.PHONY : jp2_cod.c.obj

jp2_cod.i: jp2_cod.c.i

.PHONY : jp2_cod.i

# target to preprocess a source file
jp2_cod.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_cod.c.i
.PHONY : jp2_cod.c.i

jp2_cod.s: jp2_cod.c.s

.PHONY : jp2_cod.s

# target to generate assembly for a file
jp2_cod.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_cod.c.s
.PHONY : jp2_cod.c.s

jp2_dec.obj: jp2_dec.c.obj

.PHONY : jp2_dec.obj

# target to build an object file
jp2_dec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_dec.c.obj
.PHONY : jp2_dec.c.obj

jp2_dec.i: jp2_dec.c.i

.PHONY : jp2_dec.i

# target to preprocess a source file
jp2_dec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_dec.c.i
.PHONY : jp2_dec.c.i

jp2_dec.s: jp2_dec.c.s

.PHONY : jp2_dec.s

# target to generate assembly for a file
jp2_dec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_dec.c.s
.PHONY : jp2_dec.c.s

jp2_enc.obj: jp2_enc.c.obj

.PHONY : jp2_enc.obj

# target to build an object file
jp2_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_enc.c.obj
.PHONY : jp2_enc.c.obj

jp2_enc.i: jp2_enc.c.i

.PHONY : jp2_enc.i

# target to preprocess a source file
jp2_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_enc.c.i
.PHONY : jp2_enc.c.i

jp2_enc.s: jp2_enc.c.s

.PHONY : jp2_enc.s

# target to generate assembly for a file
jp2_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_enc.c.s
.PHONY : jp2_enc.c.s

jpc_bs.obj: jpc_bs.c.obj

.PHONY : jpc_bs.obj

# target to build an object file
jpc_bs.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_bs.c.obj
.PHONY : jpc_bs.c.obj

jpc_bs.i: jpc_bs.c.i

.PHONY : jpc_bs.i

# target to preprocess a source file
jpc_bs.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_bs.c.i
.PHONY : jpc_bs.c.i

jpc_bs.s: jpc_bs.c.s

.PHONY : jpc_bs.s

# target to generate assembly for a file
jpc_bs.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_bs.c.s
.PHONY : jpc_bs.c.s

jpc_cs.obj: jpc_cs.c.obj

.PHONY : jpc_cs.obj

# target to build an object file
jpc_cs.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_cs.c.obj
.PHONY : jpc_cs.c.obj

jpc_cs.i: jpc_cs.c.i

.PHONY : jpc_cs.i

# target to preprocess a source file
jpc_cs.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_cs.c.i
.PHONY : jpc_cs.c.i

jpc_cs.s: jpc_cs.c.s

.PHONY : jpc_cs.s

# target to generate assembly for a file
jpc_cs.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_cs.c.s
.PHONY : jpc_cs.c.s

jpc_dec.obj: jpc_dec.c.obj

.PHONY : jpc_dec.obj

# target to build an object file
jpc_dec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_dec.c.obj
.PHONY : jpc_dec.c.obj

jpc_dec.i: jpc_dec.c.i

.PHONY : jpc_dec.i

# target to preprocess a source file
jpc_dec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_dec.c.i
.PHONY : jpc_dec.c.i

jpc_dec.s: jpc_dec.c.s

.PHONY : jpc_dec.s

# target to generate assembly for a file
jpc_dec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_dec.c.s
.PHONY : jpc_dec.c.s

jpc_enc.obj: jpc_enc.c.obj

.PHONY : jpc_enc.obj

# target to build an object file
jpc_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_enc.c.obj
.PHONY : jpc_enc.c.obj

jpc_enc.i: jpc_enc.c.i

.PHONY : jpc_enc.i

# target to preprocess a source file
jpc_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_enc.c.i
.PHONY : jpc_enc.c.i

jpc_enc.s: jpc_enc.c.s

.PHONY : jpc_enc.s

# target to generate assembly for a file
jpc_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_enc.c.s
.PHONY : jpc_enc.c.s

jpc_math.obj: jpc_math.c.obj

.PHONY : jpc_math.obj

# target to build an object file
jpc_math.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_math.c.obj
.PHONY : jpc_math.c.obj

jpc_math.i: jpc_math.c.i

.PHONY : jpc_math.i

# target to preprocess a source file
jpc_math.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_math.c.i
.PHONY : jpc_math.c.i

jpc_math.s: jpc_math.c.s

.PHONY : jpc_math.s

# target to generate assembly for a file
jpc_math.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_math.c.s
.PHONY : jpc_math.c.s

jpc_mct.obj: jpc_mct.c.obj

.PHONY : jpc_mct.obj

# target to build an object file
jpc_mct.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mct.c.obj
.PHONY : jpc_mct.c.obj

jpc_mct.i: jpc_mct.c.i

.PHONY : jpc_mct.i

# target to preprocess a source file
jpc_mct.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mct.c.i
.PHONY : jpc_mct.c.i

jpc_mct.s: jpc_mct.c.s

.PHONY : jpc_mct.s

# target to generate assembly for a file
jpc_mct.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mct.c.s
.PHONY : jpc_mct.c.s

jpc_mqcod.obj: jpc_mqcod.c.obj

.PHONY : jpc_mqcod.obj

# target to build an object file
jpc_mqcod.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqcod.c.obj
.PHONY : jpc_mqcod.c.obj

jpc_mqcod.i: jpc_mqcod.c.i

.PHONY : jpc_mqcod.i

# target to preprocess a source file
jpc_mqcod.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqcod.c.i
.PHONY : jpc_mqcod.c.i

jpc_mqcod.s: jpc_mqcod.c.s

.PHONY : jpc_mqcod.s

# target to generate assembly for a file
jpc_mqcod.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqcod.c.s
.PHONY : jpc_mqcod.c.s

jpc_mqdec.obj: jpc_mqdec.c.obj

.PHONY : jpc_mqdec.obj

# target to build an object file
jpc_mqdec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqdec.c.obj
.PHONY : jpc_mqdec.c.obj

jpc_mqdec.i: jpc_mqdec.c.i

.PHONY : jpc_mqdec.i

# target to preprocess a source file
jpc_mqdec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqdec.c.i
.PHONY : jpc_mqdec.c.i

jpc_mqdec.s: jpc_mqdec.c.s

.PHONY : jpc_mqdec.s

# target to generate assembly for a file
jpc_mqdec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqdec.c.s
.PHONY : jpc_mqdec.c.s

jpc_mqenc.obj: jpc_mqenc.c.obj

.PHONY : jpc_mqenc.obj

# target to build an object file
jpc_mqenc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqenc.c.obj
.PHONY : jpc_mqenc.c.obj

jpc_mqenc.i: jpc_mqenc.c.i

.PHONY : jpc_mqenc.i

# target to preprocess a source file
jpc_mqenc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqenc.c.i
.PHONY : jpc_mqenc.c.i

jpc_mqenc.s: jpc_mqenc.c.s

.PHONY : jpc_mqenc.s

# target to generate assembly for a file
jpc_mqenc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqenc.c.s
.PHONY : jpc_mqenc.c.s

jpc_qmfb.obj: jpc_qmfb.c.obj

.PHONY : jpc_qmfb.obj

# target to build an object file
jpc_qmfb.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_qmfb.c.obj
.PHONY : jpc_qmfb.c.obj

jpc_qmfb.i: jpc_qmfb.c.i

.PHONY : jpc_qmfb.i

# target to preprocess a source file
jpc_qmfb.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_qmfb.c.i
.PHONY : jpc_qmfb.c.i

jpc_qmfb.s: jpc_qmfb.c.s

.PHONY : jpc_qmfb.s

# target to generate assembly for a file
jpc_qmfb.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_qmfb.c.s
.PHONY : jpc_qmfb.c.s

jpc_t1cod.obj: jpc_t1cod.c.obj

.PHONY : jpc_t1cod.obj

# target to build an object file
jpc_t1cod.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1cod.c.obj
.PHONY : jpc_t1cod.c.obj

jpc_t1cod.i: jpc_t1cod.c.i

.PHONY : jpc_t1cod.i

# target to preprocess a source file
jpc_t1cod.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1cod.c.i
.PHONY : jpc_t1cod.c.i

jpc_t1cod.s: jpc_t1cod.c.s

.PHONY : jpc_t1cod.s

# target to generate assembly for a file
jpc_t1cod.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1cod.c.s
.PHONY : jpc_t1cod.c.s

jpc_t1dec.obj: jpc_t1dec.c.obj

.PHONY : jpc_t1dec.obj

# target to build an object file
jpc_t1dec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1dec.c.obj
.PHONY : jpc_t1dec.c.obj

jpc_t1dec.i: jpc_t1dec.c.i

.PHONY : jpc_t1dec.i

# target to preprocess a source file
jpc_t1dec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1dec.c.i
.PHONY : jpc_t1dec.c.i

jpc_t1dec.s: jpc_t1dec.c.s

.PHONY : jpc_t1dec.s

# target to generate assembly for a file
jpc_t1dec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1dec.c.s
.PHONY : jpc_t1dec.c.s

jpc_t1enc.obj: jpc_t1enc.c.obj

.PHONY : jpc_t1enc.obj

# target to build an object file
jpc_t1enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1enc.c.obj
.PHONY : jpc_t1enc.c.obj

jpc_t1enc.i: jpc_t1enc.c.i

.PHONY : jpc_t1enc.i

# target to preprocess a source file
jpc_t1enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1enc.c.i
.PHONY : jpc_t1enc.c.i

jpc_t1enc.s: jpc_t1enc.c.s

.PHONY : jpc_t1enc.s

# target to generate assembly for a file
jpc_t1enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1enc.c.s
.PHONY : jpc_t1enc.c.s

jpc_t2cod.obj: jpc_t2cod.c.obj

.PHONY : jpc_t2cod.obj

# target to build an object file
jpc_t2cod.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2cod.c.obj
.PHONY : jpc_t2cod.c.obj

jpc_t2cod.i: jpc_t2cod.c.i

.PHONY : jpc_t2cod.i

# target to preprocess a source file
jpc_t2cod.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2cod.c.i
.PHONY : jpc_t2cod.c.i

jpc_t2cod.s: jpc_t2cod.c.s

.PHONY : jpc_t2cod.s

# target to generate assembly for a file
jpc_t2cod.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2cod.c.s
.PHONY : jpc_t2cod.c.s

jpc_t2dec.obj: jpc_t2dec.c.obj

.PHONY : jpc_t2dec.obj

# target to build an object file
jpc_t2dec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2dec.c.obj
.PHONY : jpc_t2dec.c.obj

jpc_t2dec.i: jpc_t2dec.c.i

.PHONY : jpc_t2dec.i

# target to preprocess a source file
jpc_t2dec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2dec.c.i
.PHONY : jpc_t2dec.c.i

jpc_t2dec.s: jpc_t2dec.c.s

.PHONY : jpc_t2dec.s

# target to generate assembly for a file
jpc_t2dec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2dec.c.s
.PHONY : jpc_t2dec.c.s

jpc_t2enc.obj: jpc_t2enc.c.obj

.PHONY : jpc_t2enc.obj

# target to build an object file
jpc_t2enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2enc.c.obj
.PHONY : jpc_t2enc.c.obj

jpc_t2enc.i: jpc_t2enc.c.i

.PHONY : jpc_t2enc.i

# target to preprocess a source file
jpc_t2enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2enc.c.i
.PHONY : jpc_t2enc.c.i

jpc_t2enc.s: jpc_t2enc.c.s

.PHONY : jpc_t2enc.s

# target to generate assembly for a file
jpc_t2enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2enc.c.s
.PHONY : jpc_t2enc.c.s

jpc_tagtree.obj: jpc_tagtree.c.obj

.PHONY : jpc_tagtree.obj

# target to build an object file
jpc_tagtree.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tagtree.c.obj
.PHONY : jpc_tagtree.c.obj

jpc_tagtree.i: jpc_tagtree.c.i

.PHONY : jpc_tagtree.i

# target to preprocess a source file
jpc_tagtree.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tagtree.c.i
.PHONY : jpc_tagtree.c.i

jpc_tagtree.s: jpc_tagtree.c.s

.PHONY : jpc_tagtree.s

# target to generate assembly for a file
jpc_tagtree.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tagtree.c.s
.PHONY : jpc_tagtree.c.s

jpc_tsfb.obj: jpc_tsfb.c.obj

.PHONY : jpc_tsfb.obj

# target to build an object file
jpc_tsfb.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tsfb.c.obj
.PHONY : jpc_tsfb.c.obj

jpc_tsfb.i: jpc_tsfb.c.i

.PHONY : jpc_tsfb.i

# target to preprocess a source file
jpc_tsfb.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tsfb.c.i
.PHONY : jpc_tsfb.c.i

jpc_tsfb.s: jpc_tsfb.c.s

.PHONY : jpc_tsfb.s

# target to generate assembly for a file
jpc_tsfb.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tsfb.c.s
.PHONY : jpc_tsfb.c.s

jpc_util.obj: jpc_util.c.obj

.PHONY : jpc_util.obj

# target to build an object file
jpc_util.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_util.c.obj
.PHONY : jpc_util.c.obj

jpc_util.i: jpc_util.c.i

.PHONY : jpc_util.i

# target to preprocess a source file
jpc_util.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_util.c.i
.PHONY : jpc_util.c.i

jpc_util.s: jpc_util.c.s

.PHONY : jpc_util.s

# target to generate assembly for a file
jpc_util.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_util.c.s
.PHONY : jpc_util.c.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... rebuild_cache
	@echo ... test
	@echo ... libjasper
	@echo ... package
	@echo ... package_source
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... list_install_components
	@echo ... install/strip
	@echo ... jas_cm.obj
	@echo ... jas_cm.i
	@echo ... jas_cm.s
	@echo ... jas_debug.obj
	@echo ... jas_debug.i
	@echo ... jas_debug.s
	@echo ... jas_getopt.obj
	@echo ... jas_getopt.i
	@echo ... jas_getopt.s
	@echo ... jas_icc.obj
	@echo ... jas_icc.i
	@echo ... jas_icc.s
	@echo ... jas_iccdata.obj
	@echo ... jas_iccdata.i
	@echo ... jas_iccdata.s
	@echo ... jas_image.obj
	@echo ... jas_image.i
	@echo ... jas_image.s
	@echo ... jas_init.obj
	@echo ... jas_init.i
	@echo ... jas_init.s
	@echo ... jas_malloc.obj
	@echo ... jas_malloc.i
	@echo ... jas_malloc.s
	@echo ... jas_seq.obj
	@echo ... jas_seq.i
	@echo ... jas_seq.s
	@echo ... jas_stream.obj
	@echo ... jas_stream.i
	@echo ... jas_stream.s
	@echo ... jas_string.obj
	@echo ... jas_string.i
	@echo ... jas_string.s
	@echo ... jas_tmr.obj
	@echo ... jas_tmr.i
	@echo ... jas_tmr.s
	@echo ... jas_tvp.obj
	@echo ... jas_tvp.i
	@echo ... jas_tvp.s
	@echo ... jas_version.obj
	@echo ... jas_version.i
	@echo ... jas_version.s
	@echo ... jp2_cod.obj
	@echo ... jp2_cod.i
	@echo ... jp2_cod.s
	@echo ... jp2_dec.obj
	@echo ... jp2_dec.i
	@echo ... jp2_dec.s
	@echo ... jp2_enc.obj
	@echo ... jp2_enc.i
	@echo ... jp2_enc.s
	@echo ... jpc_bs.obj
	@echo ... jpc_bs.i
	@echo ... jpc_bs.s
	@echo ... jpc_cs.obj
	@echo ... jpc_cs.i
	@echo ... jpc_cs.s
	@echo ... jpc_dec.obj
	@echo ... jpc_dec.i
	@echo ... jpc_dec.s
	@echo ... jpc_enc.obj
	@echo ... jpc_enc.i
	@echo ... jpc_enc.s
	@echo ... jpc_math.obj
	@echo ... jpc_math.i
	@echo ... jpc_math.s
	@echo ... jpc_mct.obj
	@echo ... jpc_mct.i
	@echo ... jpc_mct.s
	@echo ... jpc_mqcod.obj
	@echo ... jpc_mqcod.i
	@echo ... jpc_mqcod.s
	@echo ... jpc_mqdec.obj
	@echo ... jpc_mqdec.i
	@echo ... jpc_mqdec.s
	@echo ... jpc_mqenc.obj
	@echo ... jpc_mqenc.i
	@echo ... jpc_mqenc.s
	@echo ... jpc_qmfb.obj
	@echo ... jpc_qmfb.i
	@echo ... jpc_qmfb.s
	@echo ... jpc_t1cod.obj
	@echo ... jpc_t1cod.i
	@echo ... jpc_t1cod.s
	@echo ... jpc_t1dec.obj
	@echo ... jpc_t1dec.i
	@echo ... jpc_t1dec.s
	@echo ... jpc_t1enc.obj
	@echo ... jpc_t1enc.i
	@echo ... jpc_t1enc.s
	@echo ... jpc_t2cod.obj
	@echo ... jpc_t2cod.i
	@echo ... jpc_t2cod.s
	@echo ... jpc_t2dec.obj
	@echo ... jpc_t2dec.i
	@echo ... jpc_t2dec.s
	@echo ... jpc_t2enc.obj
	@echo ... jpc_t2enc.i
	@echo ... jpc_t2enc.s
	@echo ... jpc_tagtree.obj
	@echo ... jpc_tagtree.i
	@echo ... jpc_tagtree.s
	@echo ... jpc_tsfb.obj
	@echo ... jpc_tsfb.i
	@echo ... jpc_tsfb.s
	@echo ... jpc_util.obj
	@echo ... jpc_util.i
	@echo ... jpc_util.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

