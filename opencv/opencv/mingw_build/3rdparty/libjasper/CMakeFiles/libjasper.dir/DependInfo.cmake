# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "C"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_C
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_cm.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_cm.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_debug.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_debug.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_getopt.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_getopt.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_icc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_icc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_iccdata.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_iccdata.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_image.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_image.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_init.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_init.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_malloc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_malloc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_seq.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_seq.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_stream.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_stream.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_string.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_string.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_tmr.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tmr.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_tvp.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tvp.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_version.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_version.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_cod.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_cod.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_dec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_dec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_bs.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_bs.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cs.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_cs.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_dec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_dec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_math.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_math.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mct.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mct.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqcod.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqcod.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqdec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqdec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqenc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqenc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_qmfb.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_qmfb.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1cod.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1cod.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1dec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1dec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2cod.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2cod.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2dec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2dec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tagtree.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tagtree.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tsfb.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tsfb.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_util.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_util.c.obj"
  )
set(CMAKE_C_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_C
  "EXCLUDE_BMP_SUPPORT"
  "EXCLUDE_JPG_SUPPORT"
  "EXCLUDE_MIF_SUPPORT"
  "EXCLUDE_PGX_SUPPORT"
  "EXCLUDE_PNM_SUPPORT"
  "EXCLUDE_RAS_SUPPORT"
  "_WIN32_WINNT=0x0601"
  )

# The include file search paths:
set(CMAKE_C_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/3rdparty/libjasper"
  "."
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
