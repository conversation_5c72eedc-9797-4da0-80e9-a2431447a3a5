#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_cm.c
jasper/jas_config.h
-
math.h
-
stdlib.h
-
assert.h
-
jasper/jas_cm.h
-
jasper/jas_icc.h
-
jasper/jas_init.h
-
jasper/jas_stream.h
-
jasper/jas_malloc.h
-
jasper/jas_math.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_debug.c
stdarg.h
-
stdio.h
-
jasper/jas_types.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_types.h
jasper/jas_debug.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_debug.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_getopt.c
stdio.h
-
string.h
-
jasper/jas_getopt.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_getopt.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_icc.c
assert.h
-
jasper/jas_config.h
-
jasper/jas_types.h
-
jasper/jas_malloc.h
-
jasper/jas_debug.h
-
jasper/jas_icc.h
-
jasper/jas_cm.h
-
jasper/jas_stream.h
-
jasper/jas_string.h
-
stdlib.h
-
ctype.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_iccdata.c
jasper/jas_config.h
-
jasper/jas_types.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_image.c
stdlib.h
-
stdio.h
-
string.h
-
assert.h
-
ctype.h
-
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jasper/jas_image.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_image.h
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_string.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_string.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_init.c
jasper/jas_types.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_types.h
jasper/jas_image.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_image.h
jasper/jas_init.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_init.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_malloc.c
stdio.h
-
stdlib.h
-
string.h
-
limits.h
-
errno.h
-
stdint.h
-
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
../../../local/src/memalloc.c
D:/unet/opencv/opencv/local/src/memalloc.c

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_seq.c
stdlib.h
-
assert.h
-
math.h
-
jasper/jas_seq.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_seq.h
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_stream.c
assert.h
-
fcntl.h
-
stdlib.h
-
stdarg.h
-
stdio.h
-
ctype.h
-
unistd.h
-
io.h
-
jasper/jas_types.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_types.h
jasper/jas_stream.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_stream.h
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_string.c
string.h
-
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_string.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_string.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_tmr.c
stdio.h
-
stdlib.h
-
time.h
-
jasper/jas_tmr.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_tmr.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_tvp.c
assert.h
-
stdio.h
-
ctype.h
-
stdlib.h
-
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_string.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_string.h
jasper/jas_tvp.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_tvp.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_version.c
jasper/jas_version.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_version.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_cm.h
jasper/jas_config.h
-
jasper/jas_icc.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_config.h
stdio.h
-
jasper/jas_config2.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_config2.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_debug.h
stdio.h
-
jasper/jas_config.h
-
jasper/jas_types.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jasper/jas_types.h
jasper/jas_debug.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jasper/jas_debug.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_fix.h
stdio.h
-
stdlib.h
-
math.h
-
jasper/jas_config.h
-
jasper/jas_types.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_getopt.h
jasper/jas_config.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_icc.h
jasper/jas_config.h
-
jasper/jas_types.h
-
jasper/jas_stream.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_image.h
jasper/jas_config.h
-
jasper/jas_stream.h
-
jasper/jas_seq.h
-
jasper/jas_cm.h
-
stdio.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_init.h
jasper/jas_config.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_config.h
-
stdlib.h
-
stdio.h
-
../../../../local/src/memalloc.h
D:/unet/opencv/opencv/local/src/memalloc.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jasper/jas_config.h
-
assert.h
-
stdio.h
-
string.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_seq.h
jasper/jas_config.h
-
jasper/jas_stream.h
-
jasper/jas_types.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_stream.h
jasper/jas_config.h
-
stdio.h
-
limits.h
-
fcntl.h
-
string.h
-
unistd.h
-
jasper/jas_types.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_string.h
jasper/jas_config.h
-
string.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_tmr.h
time.h
-
jasper/jas_config.h
-
sys/time.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_tvp.h
jasper/jas_config.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_types.h
jasper/jas_config.h
-
windows.h
-
stdlib.h
-
stddef.h
-
sys/types.h
-
stdbool.h
-
stdint.h
-
limits.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_version.h
jasper/jas_config.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_cod.c
assert.h
-
stdlib.h
-
jasper/jas_stream.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_stream.h
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_debug.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_debug.h
jp2_cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_cod.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_cod.h
jasper/jas_types.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_types.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_dec.c
jasper/jas_image.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_image.h
jasper/jas_stream.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_stream.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jasper/jas_debug.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_debug.h
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_version.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_version.h
jp2_cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_cod.h
jp2_dec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_dec.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_dec.h
jasper/jas_image.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_image.h
jasper/jas_stream.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_stream.h
jp2_cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_cod.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_enc.c
assert.h
-
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_image.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_image.h
jasper/jas_stream.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_stream.h
jasper/jas_cm.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_cm.h
jasper/jas_icc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_icc.h
jp2_cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_cod.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_bs.c
assert.h
-
stdlib.h
-
stdarg.h
-
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jasper/jas_debug.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_debug.h
jpc_bs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_bs.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_bs.h
stdio.h
-
jasper/jas_types.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_types.h
jasper/jas_stream.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_stream.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cod.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cs.c
stdlib.h
-
assert.h
-
ctype.h
-
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_debug.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_debug.h
jpc_cs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cs.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cs.h
jasper/jas_image.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_image.h
jasper/jas_stream.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_stream.h
jpc_cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cod.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_dec.c
stdio.h
-
stdlib.h
-
assert.h
-
jasper/jas_types.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_types.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jasper/jas_tvp.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_tvp.h
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_debug.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_debug.h
jpc_fix.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_fix.h
jpc_dec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_dec.h
jpc_cs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cs.h
jpc_mct.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mct.h
jpc_t2dec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2dec.h
jpc_t1dec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1dec.h
jpc_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_math.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_dec.h
jasper/jas_stream.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_stream.h
jpc_tsfb.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tsfb.h
jpc_bs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_bs.h
jpc_tagtree.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tagtree.h
jpc_cs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cs.h
jpc_cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cod.h
jpc_mqdec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqdec.h
jpc_t2cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2cod.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_enc.c
stdio.h
-
stdlib.h
-
assert.h
-
math.h
-
float.h
-
jasper/jas_types.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_types.h
jasper/jas_string.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_string.h
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_image.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_image.h
jasper/jas_fix.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_fix.h
jasper/jas_tvp.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_tvp.h
jasper/jas_version.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_version.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jasper/jas_debug.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_debug.h
jpc_flt.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_flt.h
jpc_fix.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_fix.h
jpc_tagtree.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tagtree.h
jpc_enc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_enc.h
jpc_cs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cs.h
jpc_mct.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mct.h
jpc_tsfb.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tsfb.h
jpc_qmfb.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_qmfb.h
jpc_t1enc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1enc.h
jpc_t2enc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2enc.h
jpc_cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cod.h
jpc_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_math.h
jpc_util.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_util.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_enc.h
jasper/jas_seq.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_seq.h
jpc_t2cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2cod.h
jpc_mqenc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqenc.h
jpc_cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cod.h
jpc_tagtree.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tagtree.h
jpc_cs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cs.h
jpc_flt.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_flt.h
jpc_tsfb.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tsfb.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_fix.h
jasper/jas_types.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_types.h
jasper/jas_fix.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_fix.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_flt.h
float.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_math.c
assert.h
-
stdio.h
-
string.h
-
math.h
-
stdlib.h
-
stdarg.h
-
jpc_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_math.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_math.h
assert.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mct.c
assert.h
-
jasper/jas_seq.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_seq.h
jpc_fix.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_fix.h
jpc_mct.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mct.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mct.h
jasper/jas_seq.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_seq.h
jasper/jas_fix.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_fix.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqcod.c
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jpc_mqcod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqcod.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqcod.h
jasper/jas_types.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_types.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqdec.c
assert.h
-
stdlib.h
-
stdarg.h
-
jasper/jas_types.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_types.h
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jasper/jas_debug.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_debug.h
jpc_mqdec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqdec.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqdec.h
jasper/jas_types.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_types.h
jasper/jas_stream.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_stream.h
jpc_mqcod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqcod.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqenc.c
assert.h
-
stdlib.h
-
jasper/jas_stream.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_stream.h
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jasper/jas_debug.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_debug.h
jpc_mqenc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqenc.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqenc.h
jasper/jas_types.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_types.h
jasper/jas_stream.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_stream.h
jpc_mqcod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqcod.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_qmfb.c
assert.h
-
jasper/jas_fix.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_fix.h
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jpc_qmfb.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_qmfb.h
jpc_tsfb.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tsfb.h
jpc_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_math.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_qmfb.h
jasper/jas_seq.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_seq.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1cod.c
stdio.h
-
stdlib.h
-
assert.h
-
math.h
-
jasper/jas_types.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_types.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jpc_bs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_bs.h
jpc_dec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_dec.h
jpc_cs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cs.h
jpc_mqcod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqcod.h
jpc_t1cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1cod.h
jpc_tsfb.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tsfb.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1cod.h
jasper/jas_fix.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_fix.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jpc_mqcod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqcod.h
jpc_tsfb.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tsfb.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1dec.c
stdio.h
-
stdlib.h
-
assert.h
-
jasper/jas_fix.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_fix.h
jasper/jas_stream.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_stream.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jpc_bs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_bs.h
jpc_mqdec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqdec.h
jpc_t1dec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1dec.h
jpc_t1cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1cod.h
jpc_dec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_dec.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1dec.h
jpc_dec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_dec.h
jpc_mqdec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqdec.h
jpc_t1cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1cod.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1enc.c
stdio.h
-
stdlib.h
-
assert.h
-
jasper/jas_fix.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_fix.h
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jpc_t1enc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1enc.h
jpc_t1cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1cod.h
jpc_enc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_enc.h
jpc_cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cod.h
jpc_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_math.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1enc.h
jasper/jas_seq.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_seq.h
jpc_enc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_enc.h
jpc_t1cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1cod.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2cod.c
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jpc_cs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cs.h
jpc_t2cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2cod.h
jpc_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_math.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2cod.h
jpc_cs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cs.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2dec.c
stdio.h
-
stdlib.h
-
assert.h
-
jasper/jas_types.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_types.h
jasper/jas_fix.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_fix.h
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jasper/jas_stream.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_stream.h
jasper/jas_debug.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_debug.h
jpc_bs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_bs.h
jpc_dec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_dec.h
jpc_cs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cs.h
jpc_mqdec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqdec.h
jpc_t2dec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2dec.h
jpc_t1cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1cod.h
jpc_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_math.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2dec.h
jasper/jas_fix.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_fix.h
jasper/jas_stream.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_stream.h
jpc_bs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_bs.h
jpc_dec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_dec.h
jpc_mqdec.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqdec.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2enc.c
stdio.h
-
stdlib.h
-
assert.h
-
jasper/jas_fix.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_fix.h
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jasper/jas_debug.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_debug.h
jpc_flt.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_flt.h
jpc_t2enc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2enc.h
jpc_t2cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2cod.h
jpc_tagtree.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tagtree.h
jpc_enc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_enc.h
jpc_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_math.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2enc.h
stdio.h
-
stdlib.h
-
assert.h
-
jpc_enc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_enc.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tagtree.c
limits.h
-
stdlib.h
-
assert.h
-
stdio.h
-
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jpc_tagtree.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tagtree.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tagtree.h
limits.h
-
stdio.h
-
jpc_bs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_bs.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tsfb.c
assert.h
-
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jasper/jas_seq.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_seq.h
jpc_tsfb.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tsfb.h
jpc_cod.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cod.h
jpc_cs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cs.h
jpc_util.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_util.h
jpc_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_math.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tsfb.h
jasper/jas_seq.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_seq.h
jpc_fix.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_fix.h
jpc_qmfb.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_qmfb.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_util.c
assert.h
-
stdio.h
-
string.h
-
math.h
-
stdlib.h
-
stdarg.h
-
jasper/jas_math.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_math.h
jasper/jas_malloc.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jasper/jas_malloc.h
jpc_fix.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_fix.h
jpc_cs.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cs.h
jpc_flt.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_flt.h
jpc_util.h
D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_util.h

D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_util.h

