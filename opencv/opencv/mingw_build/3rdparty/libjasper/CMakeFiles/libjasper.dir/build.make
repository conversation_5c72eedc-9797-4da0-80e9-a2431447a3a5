# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

# Include any dependencies generated for this target.
include 3rdparty/libjasper/CMakeFiles/libjasper.dir/depend.make

# Include the progress variables for this target.
include 3rdparty/libjasper/CMakeFiles/libjasper.dir/progress.make

# Include the compile flags for this target's objects.
include 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_cm.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_cm.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_cm.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_cm.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_cm.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jas_cm.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_cm.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_cm.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jas_cm.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_cm.c > CMakeFiles\libjasper.dir\jas_cm.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_cm.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jas_cm.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_cm.c -o CMakeFiles\libjasper.dir\jas_cm.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_debug.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_debug.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_debug.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_debug.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_debug.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jas_debug.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_debug.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_debug.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jas_debug.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_debug.c > CMakeFiles\libjasper.dir\jas_debug.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_debug.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jas_debug.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_debug.c -o CMakeFiles\libjasper.dir\jas_debug.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_getopt.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_getopt.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_getopt.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_getopt.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_getopt.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jas_getopt.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_getopt.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_getopt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jas_getopt.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_getopt.c > CMakeFiles\libjasper.dir\jas_getopt.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_getopt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jas_getopt.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_getopt.c -o CMakeFiles\libjasper.dir\jas_getopt.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_icc.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_icc.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_icc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_icc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_icc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jas_icc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_icc.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_icc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jas_icc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_icc.c > CMakeFiles\libjasper.dir\jas_icc.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_icc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jas_icc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_icc.c -o CMakeFiles\libjasper.dir\jas_icc.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_iccdata.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_iccdata.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_iccdata.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_iccdata.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_iccdata.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jas_iccdata.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_iccdata.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_iccdata.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jas_iccdata.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_iccdata.c > CMakeFiles\libjasper.dir\jas_iccdata.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_iccdata.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jas_iccdata.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_iccdata.c -o CMakeFiles\libjasper.dir\jas_iccdata.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_image.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_image.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_image.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_image.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_image.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jas_image.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_image.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_image.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jas_image.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_image.c > CMakeFiles\libjasper.dir\jas_image.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_image.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jas_image.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_image.c -o CMakeFiles\libjasper.dir\jas_image.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_init.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_init.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_init.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_init.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_init.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jas_init.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_init.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_init.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jas_init.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_init.c > CMakeFiles\libjasper.dir\jas_init.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_init.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jas_init.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_init.c -o CMakeFiles\libjasper.dir\jas_init.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_malloc.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_malloc.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_malloc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_malloc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_malloc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jas_malloc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_malloc.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_malloc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jas_malloc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_malloc.c > CMakeFiles\libjasper.dir\jas_malloc.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_malloc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jas_malloc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_malloc.c -o CMakeFiles\libjasper.dir\jas_malloc.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_seq.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_seq.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_seq.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_seq.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_seq.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jas_seq.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_seq.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_seq.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jas_seq.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_seq.c > CMakeFiles\libjasper.dir\jas_seq.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_seq.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jas_seq.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_seq.c -o CMakeFiles\libjasper.dir\jas_seq.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_stream.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_stream.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_stream.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_stream.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_stream.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jas_stream.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_stream.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_stream.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jas_stream.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_stream.c > CMakeFiles\libjasper.dir\jas_stream.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_stream.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jas_stream.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_stream.c -o CMakeFiles\libjasper.dir\jas_stream.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_string.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_string.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_string.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_string.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_string.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jas_string.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_string.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_string.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jas_string.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_string.c > CMakeFiles\libjasper.dir\jas_string.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_string.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jas_string.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_string.c -o CMakeFiles\libjasper.dir\jas_string.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tmr.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tmr.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tmr.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_tmr.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tmr.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jas_tmr.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_tmr.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tmr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jas_tmr.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_tmr.c > CMakeFiles\libjasper.dir\jas_tmr.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tmr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jas_tmr.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_tmr.c -o CMakeFiles\libjasper.dir\jas_tmr.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tvp.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tvp.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tvp.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_tvp.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tvp.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jas_tvp.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_tvp.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tvp.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jas_tvp.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_tvp.c > CMakeFiles\libjasper.dir\jas_tvp.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tvp.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jas_tvp.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_tvp.c -o CMakeFiles\libjasper.dir\jas_tvp.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_version.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_version.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_version.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jas_version.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_version.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jas_version.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_version.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_version.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jas_version.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_version.c > CMakeFiles\libjasper.dir\jas_version.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_version.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jas_version.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jas_version.c -o CMakeFiles\libjasper.dir\jas_version.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_cod.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_cod.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_cod.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_cod.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_cod.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jp2_cod.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jp2_cod.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_cod.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jp2_cod.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jp2_cod.c > CMakeFiles\libjasper.dir\jp2_cod.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_cod.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jp2_cod.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jp2_cod.c -o CMakeFiles\libjasper.dir\jp2_cod.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_dec.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_dec.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_dec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_dec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_dec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jp2_dec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jp2_dec.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jp2_dec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jp2_dec.c > CMakeFiles\libjasper.dir\jp2_dec.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jp2_dec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jp2_dec.c -o CMakeFiles\libjasper.dir\jp2_dec.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_enc.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_enc.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jp2_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jp2_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jp2_enc.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jp2_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jp2_enc.c > CMakeFiles\libjasper.dir\jp2_enc.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jp2_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jp2_enc.c -o CMakeFiles\libjasper.dir\jp2_enc.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_bs.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_bs.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_bs.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_bs.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_bs.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_bs.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_bs.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_bs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_bs.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_bs.c > CMakeFiles\libjasper.dir\jpc_bs.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_bs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_bs.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_bs.c -o CMakeFiles\libjasper.dir\jpc_bs.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_cs.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_cs.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_cs.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_cs.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_cs.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_cs.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_cs.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_cs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_cs.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_cs.c > CMakeFiles\libjasper.dir\jpc_cs.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_cs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_cs.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_cs.c -o CMakeFiles\libjasper.dir\jpc_cs.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_dec.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_dec.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_dec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_dec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_dec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_dec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_dec.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_dec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_dec.c > CMakeFiles\libjasper.dir\jpc_dec.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_dec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_dec.c -o CMakeFiles\libjasper.dir\jpc_dec.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_enc.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_enc.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_enc.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_enc.c > CMakeFiles\libjasper.dir\jpc_enc.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_enc.c -o CMakeFiles\libjasper.dir\jpc_enc.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_math.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_math.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_math.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_math.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_math.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_math.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_math.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_math.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_math.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_math.c > CMakeFiles\libjasper.dir\jpc_math.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_math.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_math.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_math.c -o CMakeFiles\libjasper.dir\jpc_math.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mct.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mct.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mct.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mct.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mct.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_mct.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_mct.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_mct.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_mct.c > CMakeFiles\libjasper.dir\jpc_mct.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_mct.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_mct.c -o CMakeFiles\libjasper.dir\jpc_mct.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqcod.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqcod.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqcod.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqcod.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqcod.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_mqcod.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_mqcod.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqcod.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_mqcod.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_mqcod.c > CMakeFiles\libjasper.dir\jpc_mqcod.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqcod.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_mqcod.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_mqcod.c -o CMakeFiles\libjasper.dir\jpc_mqcod.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqdec.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqdec.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqdec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqdec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqdec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_mqdec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_mqdec.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqdec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_mqdec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_mqdec.c > CMakeFiles\libjasper.dir\jpc_mqdec.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqdec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_mqdec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_mqdec.c -o CMakeFiles\libjasper.dir\jpc_mqdec.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqenc.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqenc.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqenc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_mqenc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqenc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_mqenc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_mqenc.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqenc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_mqenc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_mqenc.c > CMakeFiles\libjasper.dir\jpc_mqenc.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqenc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_mqenc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_mqenc.c -o CMakeFiles\libjasper.dir\jpc_mqenc.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_qmfb.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_qmfb.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_qmfb.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_qmfb.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_qmfb.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_qmfb.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_qmfb.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_qmfb.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_qmfb.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_qmfb.c > CMakeFiles\libjasper.dir\jpc_qmfb.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_qmfb.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_qmfb.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_qmfb.c -o CMakeFiles\libjasper.dir\jpc_qmfb.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1cod.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1cod.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1cod.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1cod.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1cod.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_t1cod.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t1cod.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1cod.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_t1cod.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t1cod.c > CMakeFiles\libjasper.dir\jpc_t1cod.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1cod.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_t1cod.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t1cod.c -o CMakeFiles\libjasper.dir\jpc_t1cod.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1dec.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1dec.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1dec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1dec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1dec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_t1dec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t1dec.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_t1dec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t1dec.c > CMakeFiles\libjasper.dir\jpc_t1dec.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_t1dec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t1dec.c -o CMakeFiles\libjasper.dir\jpc_t1dec.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1enc.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1enc.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t1enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_t1enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t1enc.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_t1enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t1enc.c > CMakeFiles\libjasper.dir\jpc_t1enc.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_t1enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t1enc.c -o CMakeFiles\libjasper.dir\jpc_t1enc.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2cod.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2cod.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2cod.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2cod.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2cod.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_t2cod.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t2cod.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2cod.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_t2cod.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t2cod.c > CMakeFiles\libjasper.dir\jpc_t2cod.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2cod.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_t2cod.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t2cod.c -o CMakeFiles\libjasper.dir\jpc_t2cod.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2dec.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2dec.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2dec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2dec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2dec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_t2dec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t2dec.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_t2dec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t2dec.c > CMakeFiles\libjasper.dir\jpc_t2dec.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_t2dec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t2dec.c -o CMakeFiles\libjasper.dir\jpc_t2dec.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2enc.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2enc.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_t2enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_t2enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t2enc.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_t2enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t2enc.c > CMakeFiles\libjasper.dir\jpc_t2enc.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_t2enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_t2enc.c -o CMakeFiles\libjasper.dir\jpc_t2enc.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tagtree.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tagtree.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tagtree.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tagtree.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tagtree.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_tagtree.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_tagtree.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tagtree.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_tagtree.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_tagtree.c > CMakeFiles\libjasper.dir\jpc_tagtree.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tagtree.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_tagtree.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_tagtree.c -o CMakeFiles\libjasper.dir\jpc_tagtree.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tsfb.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tsfb.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tsfb.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_tsfb.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tsfb.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_tsfb.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_tsfb.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tsfb.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_tsfb.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_tsfb.c > CMakeFiles\libjasper.dir\jpc_tsfb.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tsfb.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_tsfb.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_tsfb.c -o CMakeFiles\libjasper.dir\jpc_tsfb.c.s

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_util.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/flags.make
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_util.c.obj: 3rdparty/libjasper/CMakeFiles/libjasper.dir/includes_C.rsp
3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_util.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjasper/jpc_util.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building C object 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_util.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjasper.dir\jpc_util.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_util.c

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_util.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjasper.dir/jpc_util.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_util.c > CMakeFiles\libjasper.dir\jpc_util.c.i

3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_util.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjasper.dir/jpc_util.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjasper\jpc_util.c -o CMakeFiles\libjasper.dir\jpc_util.c.s

# Object files for target libjasper
libjasper_OBJECTS = \
"CMakeFiles/libjasper.dir/jas_cm.c.obj" \
"CMakeFiles/libjasper.dir/jas_debug.c.obj" \
"CMakeFiles/libjasper.dir/jas_getopt.c.obj" \
"CMakeFiles/libjasper.dir/jas_icc.c.obj" \
"CMakeFiles/libjasper.dir/jas_iccdata.c.obj" \
"CMakeFiles/libjasper.dir/jas_image.c.obj" \
"CMakeFiles/libjasper.dir/jas_init.c.obj" \
"CMakeFiles/libjasper.dir/jas_malloc.c.obj" \
"CMakeFiles/libjasper.dir/jas_seq.c.obj" \
"CMakeFiles/libjasper.dir/jas_stream.c.obj" \
"CMakeFiles/libjasper.dir/jas_string.c.obj" \
"CMakeFiles/libjasper.dir/jas_tmr.c.obj" \
"CMakeFiles/libjasper.dir/jas_tvp.c.obj" \
"CMakeFiles/libjasper.dir/jas_version.c.obj" \
"CMakeFiles/libjasper.dir/jp2_cod.c.obj" \
"CMakeFiles/libjasper.dir/jp2_dec.c.obj" \
"CMakeFiles/libjasper.dir/jp2_enc.c.obj" \
"CMakeFiles/libjasper.dir/jpc_bs.c.obj" \
"CMakeFiles/libjasper.dir/jpc_cs.c.obj" \
"CMakeFiles/libjasper.dir/jpc_dec.c.obj" \
"CMakeFiles/libjasper.dir/jpc_enc.c.obj" \
"CMakeFiles/libjasper.dir/jpc_math.c.obj" \
"CMakeFiles/libjasper.dir/jpc_mct.c.obj" \
"CMakeFiles/libjasper.dir/jpc_mqcod.c.obj" \
"CMakeFiles/libjasper.dir/jpc_mqdec.c.obj" \
"CMakeFiles/libjasper.dir/jpc_mqenc.c.obj" \
"CMakeFiles/libjasper.dir/jpc_qmfb.c.obj" \
"CMakeFiles/libjasper.dir/jpc_t1cod.c.obj" \
"CMakeFiles/libjasper.dir/jpc_t1dec.c.obj" \
"CMakeFiles/libjasper.dir/jpc_t1enc.c.obj" \
"CMakeFiles/libjasper.dir/jpc_t2cod.c.obj" \
"CMakeFiles/libjasper.dir/jpc_t2dec.c.obj" \
"CMakeFiles/libjasper.dir/jpc_t2enc.c.obj" \
"CMakeFiles/libjasper.dir/jpc_tagtree.c.obj" \
"CMakeFiles/libjasper.dir/jpc_tsfb.c.obj" \
"CMakeFiles/libjasper.dir/jpc_util.c.obj"

# External object files for target libjasper
libjasper_EXTERNAL_OBJECTS =

3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_cm.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_debug.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_getopt.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_icc.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_iccdata.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_image.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_init.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_malloc.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_seq.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_stream.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_string.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tmr.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_tvp.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jas_version.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_cod.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_dec.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jp2_enc.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_bs.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_cs.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_dec.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_enc.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_math.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mct.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqcod.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqdec.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_mqenc.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_qmfb.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1cod.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1dec.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t1enc.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2cod.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2dec.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_t2enc.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tagtree.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_tsfb.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/jpc_util.c.obj
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/build.make
3rdparty/lib/liblibjasper.a: 3rdparty/libjasper/CMakeFiles/libjasper.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Linking C static library ..\lib\liblibjasper.a"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && $(CMAKE_COMMAND) -P CMakeFiles\libjasper.dir\cmake_clean_target.cmake
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\libjasper.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
3rdparty/libjasper/CMakeFiles/libjasper.dir/build: 3rdparty/lib/liblibjasper.a

.PHONY : 3rdparty/libjasper/CMakeFiles/libjasper.dir/build

3rdparty/libjasper/CMakeFiles/libjasper.dir/clean:
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper && $(CMAKE_COMMAND) -P CMakeFiles\libjasper.dir\cmake_clean.cmake
.PHONY : 3rdparty/libjasper/CMakeFiles/libjasper.dir/clean

3rdparty/libjasper/CMakeFiles/libjasper.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\unet\opencv\opencv\sources D:\unet\opencv\opencv\sources\3rdparty\libjasper D:\unet\opencv\opencv\mingw_build D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper D:\unet\opencv\opencv\mingw_build\3rdparty\libjasper\CMakeFiles\libjasper.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : 3rdparty/libjasper/CMakeFiles/libjasper.dir/depend

