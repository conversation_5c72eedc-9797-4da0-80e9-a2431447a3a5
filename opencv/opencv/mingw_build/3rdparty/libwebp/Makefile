# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libwebp/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libwebp/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libwebp/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libwebp/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
3rdparty/libwebp/CMakeFiles/libwebp.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libwebp/CMakeFiles/libwebp.dir/rule
.PHONY : 3rdparty/libwebp/CMakeFiles/libwebp.dir/rule

# Convenience name for target.
libwebp: 3rdparty/libwebp/CMakeFiles/libwebp.dir/rule

.PHONY : libwebp

# fast build rule for target.
libwebp/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/build
.PHONY : libwebp/fast

src/dec/alpha_dec.obj: src/dec/alpha_dec.c.obj

.PHONY : src/dec/alpha_dec.obj

# target to build an object file
src/dec/alpha_dec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/alpha_dec.c.obj
.PHONY : src/dec/alpha_dec.c.obj

src/dec/alpha_dec.i: src/dec/alpha_dec.c.i

.PHONY : src/dec/alpha_dec.i

# target to preprocess a source file
src/dec/alpha_dec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/alpha_dec.c.i
.PHONY : src/dec/alpha_dec.c.i

src/dec/alpha_dec.s: src/dec/alpha_dec.c.s

.PHONY : src/dec/alpha_dec.s

# target to generate assembly for a file
src/dec/alpha_dec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/alpha_dec.c.s
.PHONY : src/dec/alpha_dec.c.s

src/dec/buffer_dec.obj: src/dec/buffer_dec.c.obj

.PHONY : src/dec/buffer_dec.obj

# target to build an object file
src/dec/buffer_dec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/buffer_dec.c.obj
.PHONY : src/dec/buffer_dec.c.obj

src/dec/buffer_dec.i: src/dec/buffer_dec.c.i

.PHONY : src/dec/buffer_dec.i

# target to preprocess a source file
src/dec/buffer_dec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/buffer_dec.c.i
.PHONY : src/dec/buffer_dec.c.i

src/dec/buffer_dec.s: src/dec/buffer_dec.c.s

.PHONY : src/dec/buffer_dec.s

# target to generate assembly for a file
src/dec/buffer_dec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/buffer_dec.c.s
.PHONY : src/dec/buffer_dec.c.s

src/dec/frame_dec.obj: src/dec/frame_dec.c.obj

.PHONY : src/dec/frame_dec.obj

# target to build an object file
src/dec/frame_dec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/frame_dec.c.obj
.PHONY : src/dec/frame_dec.c.obj

src/dec/frame_dec.i: src/dec/frame_dec.c.i

.PHONY : src/dec/frame_dec.i

# target to preprocess a source file
src/dec/frame_dec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/frame_dec.c.i
.PHONY : src/dec/frame_dec.c.i

src/dec/frame_dec.s: src/dec/frame_dec.c.s

.PHONY : src/dec/frame_dec.s

# target to generate assembly for a file
src/dec/frame_dec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/frame_dec.c.s
.PHONY : src/dec/frame_dec.c.s

src/dec/idec_dec.obj: src/dec/idec_dec.c.obj

.PHONY : src/dec/idec_dec.obj

# target to build an object file
src/dec/idec_dec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/idec_dec.c.obj
.PHONY : src/dec/idec_dec.c.obj

src/dec/idec_dec.i: src/dec/idec_dec.c.i

.PHONY : src/dec/idec_dec.i

# target to preprocess a source file
src/dec/idec_dec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/idec_dec.c.i
.PHONY : src/dec/idec_dec.c.i

src/dec/idec_dec.s: src/dec/idec_dec.c.s

.PHONY : src/dec/idec_dec.s

# target to generate assembly for a file
src/dec/idec_dec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/idec_dec.c.s
.PHONY : src/dec/idec_dec.c.s

src/dec/io_dec.obj: src/dec/io_dec.c.obj

.PHONY : src/dec/io_dec.obj

# target to build an object file
src/dec/io_dec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/io_dec.c.obj
.PHONY : src/dec/io_dec.c.obj

src/dec/io_dec.i: src/dec/io_dec.c.i

.PHONY : src/dec/io_dec.i

# target to preprocess a source file
src/dec/io_dec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/io_dec.c.i
.PHONY : src/dec/io_dec.c.i

src/dec/io_dec.s: src/dec/io_dec.c.s

.PHONY : src/dec/io_dec.s

# target to generate assembly for a file
src/dec/io_dec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/io_dec.c.s
.PHONY : src/dec/io_dec.c.s

src/dec/quant_dec.obj: src/dec/quant_dec.c.obj

.PHONY : src/dec/quant_dec.obj

# target to build an object file
src/dec/quant_dec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/quant_dec.c.obj
.PHONY : src/dec/quant_dec.c.obj

src/dec/quant_dec.i: src/dec/quant_dec.c.i

.PHONY : src/dec/quant_dec.i

# target to preprocess a source file
src/dec/quant_dec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/quant_dec.c.i
.PHONY : src/dec/quant_dec.c.i

src/dec/quant_dec.s: src/dec/quant_dec.c.s

.PHONY : src/dec/quant_dec.s

# target to generate assembly for a file
src/dec/quant_dec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/quant_dec.c.s
.PHONY : src/dec/quant_dec.c.s

src/dec/tree_dec.obj: src/dec/tree_dec.c.obj

.PHONY : src/dec/tree_dec.obj

# target to build an object file
src/dec/tree_dec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/tree_dec.c.obj
.PHONY : src/dec/tree_dec.c.obj

src/dec/tree_dec.i: src/dec/tree_dec.c.i

.PHONY : src/dec/tree_dec.i

# target to preprocess a source file
src/dec/tree_dec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/tree_dec.c.i
.PHONY : src/dec/tree_dec.c.i

src/dec/tree_dec.s: src/dec/tree_dec.c.s

.PHONY : src/dec/tree_dec.s

# target to generate assembly for a file
src/dec/tree_dec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/tree_dec.c.s
.PHONY : src/dec/tree_dec.c.s

src/dec/vp8_dec.obj: src/dec/vp8_dec.c.obj

.PHONY : src/dec/vp8_dec.obj

# target to build an object file
src/dec/vp8_dec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8_dec.c.obj
.PHONY : src/dec/vp8_dec.c.obj

src/dec/vp8_dec.i: src/dec/vp8_dec.c.i

.PHONY : src/dec/vp8_dec.i

# target to preprocess a source file
src/dec/vp8_dec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8_dec.c.i
.PHONY : src/dec/vp8_dec.c.i

src/dec/vp8_dec.s: src/dec/vp8_dec.c.s

.PHONY : src/dec/vp8_dec.s

# target to generate assembly for a file
src/dec/vp8_dec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8_dec.c.s
.PHONY : src/dec/vp8_dec.c.s

src/dec/vp8l_dec.obj: src/dec/vp8l_dec.c.obj

.PHONY : src/dec/vp8l_dec.obj

# target to build an object file
src/dec/vp8l_dec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8l_dec.c.obj
.PHONY : src/dec/vp8l_dec.c.obj

src/dec/vp8l_dec.i: src/dec/vp8l_dec.c.i

.PHONY : src/dec/vp8l_dec.i

# target to preprocess a source file
src/dec/vp8l_dec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8l_dec.c.i
.PHONY : src/dec/vp8l_dec.c.i

src/dec/vp8l_dec.s: src/dec/vp8l_dec.c.s

.PHONY : src/dec/vp8l_dec.s

# target to generate assembly for a file
src/dec/vp8l_dec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8l_dec.c.s
.PHONY : src/dec/vp8l_dec.c.s

src/dec/webp_dec.obj: src/dec/webp_dec.c.obj

.PHONY : src/dec/webp_dec.obj

# target to build an object file
src/dec/webp_dec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/webp_dec.c.obj
.PHONY : src/dec/webp_dec.c.obj

src/dec/webp_dec.i: src/dec/webp_dec.c.i

.PHONY : src/dec/webp_dec.i

# target to preprocess a source file
src/dec/webp_dec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/webp_dec.c.i
.PHONY : src/dec/webp_dec.c.i

src/dec/webp_dec.s: src/dec/webp_dec.c.s

.PHONY : src/dec/webp_dec.s

# target to generate assembly for a file
src/dec/webp_dec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/webp_dec.c.s
.PHONY : src/dec/webp_dec.c.s

src/demux/anim_decode.obj: src/demux/anim_decode.c.obj

.PHONY : src/demux/anim_decode.obj

# target to build an object file
src/demux/anim_decode.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/anim_decode.c.obj
.PHONY : src/demux/anim_decode.c.obj

src/demux/anim_decode.i: src/demux/anim_decode.c.i

.PHONY : src/demux/anim_decode.i

# target to preprocess a source file
src/demux/anim_decode.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/anim_decode.c.i
.PHONY : src/demux/anim_decode.c.i

src/demux/anim_decode.s: src/demux/anim_decode.c.s

.PHONY : src/demux/anim_decode.s

# target to generate assembly for a file
src/demux/anim_decode.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/anim_decode.c.s
.PHONY : src/demux/anim_decode.c.s

src/demux/demux.obj: src/demux/demux.c.obj

.PHONY : src/demux/demux.obj

# target to build an object file
src/demux/demux.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/demux.c.obj
.PHONY : src/demux/demux.c.obj

src/demux/demux.i: src/demux/demux.c.i

.PHONY : src/demux/demux.i

# target to preprocess a source file
src/demux/demux.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/demux.c.i
.PHONY : src/demux/demux.c.i

src/demux/demux.s: src/demux/demux.c.s

.PHONY : src/demux/demux.s

# target to generate assembly for a file
src/demux/demux.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/demux.c.s
.PHONY : src/demux/demux.c.s

src/dsp/alpha_processing.obj: src/dsp/alpha_processing.c.obj

.PHONY : src/dsp/alpha_processing.obj

# target to build an object file
src/dsp/alpha_processing.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing.c.obj
.PHONY : src/dsp/alpha_processing.c.obj

src/dsp/alpha_processing.i: src/dsp/alpha_processing.c.i

.PHONY : src/dsp/alpha_processing.i

# target to preprocess a source file
src/dsp/alpha_processing.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing.c.i
.PHONY : src/dsp/alpha_processing.c.i

src/dsp/alpha_processing.s: src/dsp/alpha_processing.c.s

.PHONY : src/dsp/alpha_processing.s

# target to generate assembly for a file
src/dsp/alpha_processing.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing.c.s
.PHONY : src/dsp/alpha_processing.c.s

src/dsp/alpha_processing_mips_dsp_r2.obj: src/dsp/alpha_processing_mips_dsp_r2.c.obj

.PHONY : src/dsp/alpha_processing_mips_dsp_r2.obj

# target to build an object file
src/dsp/alpha_processing_mips_dsp_r2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_mips_dsp_r2.c.obj
.PHONY : src/dsp/alpha_processing_mips_dsp_r2.c.obj

src/dsp/alpha_processing_mips_dsp_r2.i: src/dsp/alpha_processing_mips_dsp_r2.c.i

.PHONY : src/dsp/alpha_processing_mips_dsp_r2.i

# target to preprocess a source file
src/dsp/alpha_processing_mips_dsp_r2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_mips_dsp_r2.c.i
.PHONY : src/dsp/alpha_processing_mips_dsp_r2.c.i

src/dsp/alpha_processing_mips_dsp_r2.s: src/dsp/alpha_processing_mips_dsp_r2.c.s

.PHONY : src/dsp/alpha_processing_mips_dsp_r2.s

# target to generate assembly for a file
src/dsp/alpha_processing_mips_dsp_r2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_mips_dsp_r2.c.s
.PHONY : src/dsp/alpha_processing_mips_dsp_r2.c.s

src/dsp/alpha_processing_neon.obj: src/dsp/alpha_processing_neon.c.obj

.PHONY : src/dsp/alpha_processing_neon.obj

# target to build an object file
src/dsp/alpha_processing_neon.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_neon.c.obj
.PHONY : src/dsp/alpha_processing_neon.c.obj

src/dsp/alpha_processing_neon.i: src/dsp/alpha_processing_neon.c.i

.PHONY : src/dsp/alpha_processing_neon.i

# target to preprocess a source file
src/dsp/alpha_processing_neon.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_neon.c.i
.PHONY : src/dsp/alpha_processing_neon.c.i

src/dsp/alpha_processing_neon.s: src/dsp/alpha_processing_neon.c.s

.PHONY : src/dsp/alpha_processing_neon.s

# target to generate assembly for a file
src/dsp/alpha_processing_neon.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_neon.c.s
.PHONY : src/dsp/alpha_processing_neon.c.s

src/dsp/alpha_processing_sse2.obj: src/dsp/alpha_processing_sse2.c.obj

.PHONY : src/dsp/alpha_processing_sse2.obj

# target to build an object file
src/dsp/alpha_processing_sse2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse2.c.obj
.PHONY : src/dsp/alpha_processing_sse2.c.obj

src/dsp/alpha_processing_sse2.i: src/dsp/alpha_processing_sse2.c.i

.PHONY : src/dsp/alpha_processing_sse2.i

# target to preprocess a source file
src/dsp/alpha_processing_sse2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse2.c.i
.PHONY : src/dsp/alpha_processing_sse2.c.i

src/dsp/alpha_processing_sse2.s: src/dsp/alpha_processing_sse2.c.s

.PHONY : src/dsp/alpha_processing_sse2.s

# target to generate assembly for a file
src/dsp/alpha_processing_sse2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse2.c.s
.PHONY : src/dsp/alpha_processing_sse2.c.s

src/dsp/alpha_processing_sse41.obj: src/dsp/alpha_processing_sse41.c.obj

.PHONY : src/dsp/alpha_processing_sse41.obj

# target to build an object file
src/dsp/alpha_processing_sse41.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse41.c.obj
.PHONY : src/dsp/alpha_processing_sse41.c.obj

src/dsp/alpha_processing_sse41.i: src/dsp/alpha_processing_sse41.c.i

.PHONY : src/dsp/alpha_processing_sse41.i

# target to preprocess a source file
src/dsp/alpha_processing_sse41.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse41.c.i
.PHONY : src/dsp/alpha_processing_sse41.c.i

src/dsp/alpha_processing_sse41.s: src/dsp/alpha_processing_sse41.c.s

.PHONY : src/dsp/alpha_processing_sse41.s

# target to generate assembly for a file
src/dsp/alpha_processing_sse41.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse41.c.s
.PHONY : src/dsp/alpha_processing_sse41.c.s

src/dsp/cost.obj: src/dsp/cost.c.obj

.PHONY : src/dsp/cost.obj

# target to build an object file
src/dsp/cost.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost.c.obj
.PHONY : src/dsp/cost.c.obj

src/dsp/cost.i: src/dsp/cost.c.i

.PHONY : src/dsp/cost.i

# target to preprocess a source file
src/dsp/cost.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost.c.i
.PHONY : src/dsp/cost.c.i

src/dsp/cost.s: src/dsp/cost.c.s

.PHONY : src/dsp/cost.s

# target to generate assembly for a file
src/dsp/cost.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost.c.s
.PHONY : src/dsp/cost.c.s

src/dsp/cost_mips32.obj: src/dsp/cost_mips32.c.obj

.PHONY : src/dsp/cost_mips32.obj

# target to build an object file
src/dsp/cost_mips32.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips32.c.obj
.PHONY : src/dsp/cost_mips32.c.obj

src/dsp/cost_mips32.i: src/dsp/cost_mips32.c.i

.PHONY : src/dsp/cost_mips32.i

# target to preprocess a source file
src/dsp/cost_mips32.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips32.c.i
.PHONY : src/dsp/cost_mips32.c.i

src/dsp/cost_mips32.s: src/dsp/cost_mips32.c.s

.PHONY : src/dsp/cost_mips32.s

# target to generate assembly for a file
src/dsp/cost_mips32.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips32.c.s
.PHONY : src/dsp/cost_mips32.c.s

src/dsp/cost_mips_dsp_r2.obj: src/dsp/cost_mips_dsp_r2.c.obj

.PHONY : src/dsp/cost_mips_dsp_r2.obj

# target to build an object file
src/dsp/cost_mips_dsp_r2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips_dsp_r2.c.obj
.PHONY : src/dsp/cost_mips_dsp_r2.c.obj

src/dsp/cost_mips_dsp_r2.i: src/dsp/cost_mips_dsp_r2.c.i

.PHONY : src/dsp/cost_mips_dsp_r2.i

# target to preprocess a source file
src/dsp/cost_mips_dsp_r2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips_dsp_r2.c.i
.PHONY : src/dsp/cost_mips_dsp_r2.c.i

src/dsp/cost_mips_dsp_r2.s: src/dsp/cost_mips_dsp_r2.c.s

.PHONY : src/dsp/cost_mips_dsp_r2.s

# target to generate assembly for a file
src/dsp/cost_mips_dsp_r2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips_dsp_r2.c.s
.PHONY : src/dsp/cost_mips_dsp_r2.c.s

src/dsp/cost_neon.obj: src/dsp/cost_neon.c.obj

.PHONY : src/dsp/cost_neon.obj

# target to build an object file
src/dsp/cost_neon.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_neon.c.obj
.PHONY : src/dsp/cost_neon.c.obj

src/dsp/cost_neon.i: src/dsp/cost_neon.c.i

.PHONY : src/dsp/cost_neon.i

# target to preprocess a source file
src/dsp/cost_neon.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_neon.c.i
.PHONY : src/dsp/cost_neon.c.i

src/dsp/cost_neon.s: src/dsp/cost_neon.c.s

.PHONY : src/dsp/cost_neon.s

# target to generate assembly for a file
src/dsp/cost_neon.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_neon.c.s
.PHONY : src/dsp/cost_neon.c.s

src/dsp/cost_sse2.obj: src/dsp/cost_sse2.c.obj

.PHONY : src/dsp/cost_sse2.obj

# target to build an object file
src/dsp/cost_sse2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_sse2.c.obj
.PHONY : src/dsp/cost_sse2.c.obj

src/dsp/cost_sse2.i: src/dsp/cost_sse2.c.i

.PHONY : src/dsp/cost_sse2.i

# target to preprocess a source file
src/dsp/cost_sse2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_sse2.c.i
.PHONY : src/dsp/cost_sse2.c.i

src/dsp/cost_sse2.s: src/dsp/cost_sse2.c.s

.PHONY : src/dsp/cost_sse2.s

# target to generate assembly for a file
src/dsp/cost_sse2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_sse2.c.s
.PHONY : src/dsp/cost_sse2.c.s

src/dsp/cpu.obj: src/dsp/cpu.c.obj

.PHONY : src/dsp/cpu.obj

# target to build an object file
src/dsp/cpu.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cpu.c.obj
.PHONY : src/dsp/cpu.c.obj

src/dsp/cpu.i: src/dsp/cpu.c.i

.PHONY : src/dsp/cpu.i

# target to preprocess a source file
src/dsp/cpu.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cpu.c.i
.PHONY : src/dsp/cpu.c.i

src/dsp/cpu.s: src/dsp/cpu.c.s

.PHONY : src/dsp/cpu.s

# target to generate assembly for a file
src/dsp/cpu.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cpu.c.s
.PHONY : src/dsp/cpu.c.s

src/dsp/dec.obj: src/dsp/dec.c.obj

.PHONY : src/dsp/dec.obj

# target to build an object file
src/dsp/dec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec.c.obj
.PHONY : src/dsp/dec.c.obj

src/dsp/dec.i: src/dsp/dec.c.i

.PHONY : src/dsp/dec.i

# target to preprocess a source file
src/dsp/dec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec.c.i
.PHONY : src/dsp/dec.c.i

src/dsp/dec.s: src/dsp/dec.c.s

.PHONY : src/dsp/dec.s

# target to generate assembly for a file
src/dsp/dec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec.c.s
.PHONY : src/dsp/dec.c.s

src/dsp/dec_clip_tables.obj: src/dsp/dec_clip_tables.c.obj

.PHONY : src/dsp/dec_clip_tables.obj

# target to build an object file
src/dsp/dec_clip_tables.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_clip_tables.c.obj
.PHONY : src/dsp/dec_clip_tables.c.obj

src/dsp/dec_clip_tables.i: src/dsp/dec_clip_tables.c.i

.PHONY : src/dsp/dec_clip_tables.i

# target to preprocess a source file
src/dsp/dec_clip_tables.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_clip_tables.c.i
.PHONY : src/dsp/dec_clip_tables.c.i

src/dsp/dec_clip_tables.s: src/dsp/dec_clip_tables.c.s

.PHONY : src/dsp/dec_clip_tables.s

# target to generate assembly for a file
src/dsp/dec_clip_tables.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_clip_tables.c.s
.PHONY : src/dsp/dec_clip_tables.c.s

src/dsp/dec_mips32.obj: src/dsp/dec_mips32.c.obj

.PHONY : src/dsp/dec_mips32.obj

# target to build an object file
src/dsp/dec_mips32.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips32.c.obj
.PHONY : src/dsp/dec_mips32.c.obj

src/dsp/dec_mips32.i: src/dsp/dec_mips32.c.i

.PHONY : src/dsp/dec_mips32.i

# target to preprocess a source file
src/dsp/dec_mips32.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips32.c.i
.PHONY : src/dsp/dec_mips32.c.i

src/dsp/dec_mips32.s: src/dsp/dec_mips32.c.s

.PHONY : src/dsp/dec_mips32.s

# target to generate assembly for a file
src/dsp/dec_mips32.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips32.c.s
.PHONY : src/dsp/dec_mips32.c.s

src/dsp/dec_mips_dsp_r2.obj: src/dsp/dec_mips_dsp_r2.c.obj

.PHONY : src/dsp/dec_mips_dsp_r2.obj

# target to build an object file
src/dsp/dec_mips_dsp_r2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips_dsp_r2.c.obj
.PHONY : src/dsp/dec_mips_dsp_r2.c.obj

src/dsp/dec_mips_dsp_r2.i: src/dsp/dec_mips_dsp_r2.c.i

.PHONY : src/dsp/dec_mips_dsp_r2.i

# target to preprocess a source file
src/dsp/dec_mips_dsp_r2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips_dsp_r2.c.i
.PHONY : src/dsp/dec_mips_dsp_r2.c.i

src/dsp/dec_mips_dsp_r2.s: src/dsp/dec_mips_dsp_r2.c.s

.PHONY : src/dsp/dec_mips_dsp_r2.s

# target to generate assembly for a file
src/dsp/dec_mips_dsp_r2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips_dsp_r2.c.s
.PHONY : src/dsp/dec_mips_dsp_r2.c.s

src/dsp/dec_msa.obj: src/dsp/dec_msa.c.obj

.PHONY : src/dsp/dec_msa.obj

# target to build an object file
src/dsp/dec_msa.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_msa.c.obj
.PHONY : src/dsp/dec_msa.c.obj

src/dsp/dec_msa.i: src/dsp/dec_msa.c.i

.PHONY : src/dsp/dec_msa.i

# target to preprocess a source file
src/dsp/dec_msa.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_msa.c.i
.PHONY : src/dsp/dec_msa.c.i

src/dsp/dec_msa.s: src/dsp/dec_msa.c.s

.PHONY : src/dsp/dec_msa.s

# target to generate assembly for a file
src/dsp/dec_msa.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_msa.c.s
.PHONY : src/dsp/dec_msa.c.s

src/dsp/dec_neon.obj: src/dsp/dec_neon.c.obj

.PHONY : src/dsp/dec_neon.obj

# target to build an object file
src/dsp/dec_neon.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_neon.c.obj
.PHONY : src/dsp/dec_neon.c.obj

src/dsp/dec_neon.i: src/dsp/dec_neon.c.i

.PHONY : src/dsp/dec_neon.i

# target to preprocess a source file
src/dsp/dec_neon.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_neon.c.i
.PHONY : src/dsp/dec_neon.c.i

src/dsp/dec_neon.s: src/dsp/dec_neon.c.s

.PHONY : src/dsp/dec_neon.s

# target to generate assembly for a file
src/dsp/dec_neon.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_neon.c.s
.PHONY : src/dsp/dec_neon.c.s

src/dsp/dec_sse2.obj: src/dsp/dec_sse2.c.obj

.PHONY : src/dsp/dec_sse2.obj

# target to build an object file
src/dsp/dec_sse2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse2.c.obj
.PHONY : src/dsp/dec_sse2.c.obj

src/dsp/dec_sse2.i: src/dsp/dec_sse2.c.i

.PHONY : src/dsp/dec_sse2.i

# target to preprocess a source file
src/dsp/dec_sse2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse2.c.i
.PHONY : src/dsp/dec_sse2.c.i

src/dsp/dec_sse2.s: src/dsp/dec_sse2.c.s

.PHONY : src/dsp/dec_sse2.s

# target to generate assembly for a file
src/dsp/dec_sse2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse2.c.s
.PHONY : src/dsp/dec_sse2.c.s

src/dsp/dec_sse41.obj: src/dsp/dec_sse41.c.obj

.PHONY : src/dsp/dec_sse41.obj

# target to build an object file
src/dsp/dec_sse41.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse41.c.obj
.PHONY : src/dsp/dec_sse41.c.obj

src/dsp/dec_sse41.i: src/dsp/dec_sse41.c.i

.PHONY : src/dsp/dec_sse41.i

# target to preprocess a source file
src/dsp/dec_sse41.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse41.c.i
.PHONY : src/dsp/dec_sse41.c.i

src/dsp/dec_sse41.s: src/dsp/dec_sse41.c.s

.PHONY : src/dsp/dec_sse41.s

# target to generate assembly for a file
src/dsp/dec_sse41.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse41.c.s
.PHONY : src/dsp/dec_sse41.c.s

src/dsp/enc.obj: src/dsp/enc.c.obj

.PHONY : src/dsp/enc.obj

# target to build an object file
src/dsp/enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc.c.obj
.PHONY : src/dsp/enc.c.obj

src/dsp/enc.i: src/dsp/enc.c.i

.PHONY : src/dsp/enc.i

# target to preprocess a source file
src/dsp/enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc.c.i
.PHONY : src/dsp/enc.c.i

src/dsp/enc.s: src/dsp/enc.c.s

.PHONY : src/dsp/enc.s

# target to generate assembly for a file
src/dsp/enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc.c.s
.PHONY : src/dsp/enc.c.s

src/dsp/enc_mips32.obj: src/dsp/enc_mips32.c.obj

.PHONY : src/dsp/enc_mips32.obj

# target to build an object file
src/dsp/enc_mips32.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips32.c.obj
.PHONY : src/dsp/enc_mips32.c.obj

src/dsp/enc_mips32.i: src/dsp/enc_mips32.c.i

.PHONY : src/dsp/enc_mips32.i

# target to preprocess a source file
src/dsp/enc_mips32.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips32.c.i
.PHONY : src/dsp/enc_mips32.c.i

src/dsp/enc_mips32.s: src/dsp/enc_mips32.c.s

.PHONY : src/dsp/enc_mips32.s

# target to generate assembly for a file
src/dsp/enc_mips32.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips32.c.s
.PHONY : src/dsp/enc_mips32.c.s

src/dsp/enc_mips_dsp_r2.obj: src/dsp/enc_mips_dsp_r2.c.obj

.PHONY : src/dsp/enc_mips_dsp_r2.obj

# target to build an object file
src/dsp/enc_mips_dsp_r2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips_dsp_r2.c.obj
.PHONY : src/dsp/enc_mips_dsp_r2.c.obj

src/dsp/enc_mips_dsp_r2.i: src/dsp/enc_mips_dsp_r2.c.i

.PHONY : src/dsp/enc_mips_dsp_r2.i

# target to preprocess a source file
src/dsp/enc_mips_dsp_r2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips_dsp_r2.c.i
.PHONY : src/dsp/enc_mips_dsp_r2.c.i

src/dsp/enc_mips_dsp_r2.s: src/dsp/enc_mips_dsp_r2.c.s

.PHONY : src/dsp/enc_mips_dsp_r2.s

# target to generate assembly for a file
src/dsp/enc_mips_dsp_r2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips_dsp_r2.c.s
.PHONY : src/dsp/enc_mips_dsp_r2.c.s

src/dsp/enc_msa.obj: src/dsp/enc_msa.c.obj

.PHONY : src/dsp/enc_msa.obj

# target to build an object file
src/dsp/enc_msa.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_msa.c.obj
.PHONY : src/dsp/enc_msa.c.obj

src/dsp/enc_msa.i: src/dsp/enc_msa.c.i

.PHONY : src/dsp/enc_msa.i

# target to preprocess a source file
src/dsp/enc_msa.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_msa.c.i
.PHONY : src/dsp/enc_msa.c.i

src/dsp/enc_msa.s: src/dsp/enc_msa.c.s

.PHONY : src/dsp/enc_msa.s

# target to generate assembly for a file
src/dsp/enc_msa.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_msa.c.s
.PHONY : src/dsp/enc_msa.c.s

src/dsp/enc_neon.obj: src/dsp/enc_neon.c.obj

.PHONY : src/dsp/enc_neon.obj

# target to build an object file
src/dsp/enc_neon.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_neon.c.obj
.PHONY : src/dsp/enc_neon.c.obj

src/dsp/enc_neon.i: src/dsp/enc_neon.c.i

.PHONY : src/dsp/enc_neon.i

# target to preprocess a source file
src/dsp/enc_neon.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_neon.c.i
.PHONY : src/dsp/enc_neon.c.i

src/dsp/enc_neon.s: src/dsp/enc_neon.c.s

.PHONY : src/dsp/enc_neon.s

# target to generate assembly for a file
src/dsp/enc_neon.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_neon.c.s
.PHONY : src/dsp/enc_neon.c.s

src/dsp/enc_sse2.obj: src/dsp/enc_sse2.c.obj

.PHONY : src/dsp/enc_sse2.obj

# target to build an object file
src/dsp/enc_sse2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse2.c.obj
.PHONY : src/dsp/enc_sse2.c.obj

src/dsp/enc_sse2.i: src/dsp/enc_sse2.c.i

.PHONY : src/dsp/enc_sse2.i

# target to preprocess a source file
src/dsp/enc_sse2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse2.c.i
.PHONY : src/dsp/enc_sse2.c.i

src/dsp/enc_sse2.s: src/dsp/enc_sse2.c.s

.PHONY : src/dsp/enc_sse2.s

# target to generate assembly for a file
src/dsp/enc_sse2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse2.c.s
.PHONY : src/dsp/enc_sse2.c.s

src/dsp/enc_sse41.obj: src/dsp/enc_sse41.c.obj

.PHONY : src/dsp/enc_sse41.obj

# target to build an object file
src/dsp/enc_sse41.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse41.c.obj
.PHONY : src/dsp/enc_sse41.c.obj

src/dsp/enc_sse41.i: src/dsp/enc_sse41.c.i

.PHONY : src/dsp/enc_sse41.i

# target to preprocess a source file
src/dsp/enc_sse41.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse41.c.i
.PHONY : src/dsp/enc_sse41.c.i

src/dsp/enc_sse41.s: src/dsp/enc_sse41.c.s

.PHONY : src/dsp/enc_sse41.s

# target to generate assembly for a file
src/dsp/enc_sse41.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse41.c.s
.PHONY : src/dsp/enc_sse41.c.s

src/dsp/filters.obj: src/dsp/filters.c.obj

.PHONY : src/dsp/filters.obj

# target to build an object file
src/dsp/filters.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters.c.obj
.PHONY : src/dsp/filters.c.obj

src/dsp/filters.i: src/dsp/filters.c.i

.PHONY : src/dsp/filters.i

# target to preprocess a source file
src/dsp/filters.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters.c.i
.PHONY : src/dsp/filters.c.i

src/dsp/filters.s: src/dsp/filters.c.s

.PHONY : src/dsp/filters.s

# target to generate assembly for a file
src/dsp/filters.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters.c.s
.PHONY : src/dsp/filters.c.s

src/dsp/filters_mips_dsp_r2.obj: src/dsp/filters_mips_dsp_r2.c.obj

.PHONY : src/dsp/filters_mips_dsp_r2.obj

# target to build an object file
src/dsp/filters_mips_dsp_r2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_mips_dsp_r2.c.obj
.PHONY : src/dsp/filters_mips_dsp_r2.c.obj

src/dsp/filters_mips_dsp_r2.i: src/dsp/filters_mips_dsp_r2.c.i

.PHONY : src/dsp/filters_mips_dsp_r2.i

# target to preprocess a source file
src/dsp/filters_mips_dsp_r2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_mips_dsp_r2.c.i
.PHONY : src/dsp/filters_mips_dsp_r2.c.i

src/dsp/filters_mips_dsp_r2.s: src/dsp/filters_mips_dsp_r2.c.s

.PHONY : src/dsp/filters_mips_dsp_r2.s

# target to generate assembly for a file
src/dsp/filters_mips_dsp_r2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_mips_dsp_r2.c.s
.PHONY : src/dsp/filters_mips_dsp_r2.c.s

src/dsp/filters_msa.obj: src/dsp/filters_msa.c.obj

.PHONY : src/dsp/filters_msa.obj

# target to build an object file
src/dsp/filters_msa.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_msa.c.obj
.PHONY : src/dsp/filters_msa.c.obj

src/dsp/filters_msa.i: src/dsp/filters_msa.c.i

.PHONY : src/dsp/filters_msa.i

# target to preprocess a source file
src/dsp/filters_msa.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_msa.c.i
.PHONY : src/dsp/filters_msa.c.i

src/dsp/filters_msa.s: src/dsp/filters_msa.c.s

.PHONY : src/dsp/filters_msa.s

# target to generate assembly for a file
src/dsp/filters_msa.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_msa.c.s
.PHONY : src/dsp/filters_msa.c.s

src/dsp/filters_neon.obj: src/dsp/filters_neon.c.obj

.PHONY : src/dsp/filters_neon.obj

# target to build an object file
src/dsp/filters_neon.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_neon.c.obj
.PHONY : src/dsp/filters_neon.c.obj

src/dsp/filters_neon.i: src/dsp/filters_neon.c.i

.PHONY : src/dsp/filters_neon.i

# target to preprocess a source file
src/dsp/filters_neon.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_neon.c.i
.PHONY : src/dsp/filters_neon.c.i

src/dsp/filters_neon.s: src/dsp/filters_neon.c.s

.PHONY : src/dsp/filters_neon.s

# target to generate assembly for a file
src/dsp/filters_neon.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_neon.c.s
.PHONY : src/dsp/filters_neon.c.s

src/dsp/filters_sse2.obj: src/dsp/filters_sse2.c.obj

.PHONY : src/dsp/filters_sse2.obj

# target to build an object file
src/dsp/filters_sse2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_sse2.c.obj
.PHONY : src/dsp/filters_sse2.c.obj

src/dsp/filters_sse2.i: src/dsp/filters_sse2.c.i

.PHONY : src/dsp/filters_sse2.i

# target to preprocess a source file
src/dsp/filters_sse2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_sse2.c.i
.PHONY : src/dsp/filters_sse2.c.i

src/dsp/filters_sse2.s: src/dsp/filters_sse2.c.s

.PHONY : src/dsp/filters_sse2.s

# target to generate assembly for a file
src/dsp/filters_sse2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_sse2.c.s
.PHONY : src/dsp/filters_sse2.c.s

src/dsp/lossless.obj: src/dsp/lossless.c.obj

.PHONY : src/dsp/lossless.obj

# target to build an object file
src/dsp/lossless.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless.c.obj
.PHONY : src/dsp/lossless.c.obj

src/dsp/lossless.i: src/dsp/lossless.c.i

.PHONY : src/dsp/lossless.i

# target to preprocess a source file
src/dsp/lossless.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless.c.i
.PHONY : src/dsp/lossless.c.i

src/dsp/lossless.s: src/dsp/lossless.c.s

.PHONY : src/dsp/lossless.s

# target to generate assembly for a file
src/dsp/lossless.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless.c.s
.PHONY : src/dsp/lossless.c.s

src/dsp/lossless_enc.obj: src/dsp/lossless_enc.c.obj

.PHONY : src/dsp/lossless_enc.obj

# target to build an object file
src/dsp/lossless_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc.c.obj
.PHONY : src/dsp/lossless_enc.c.obj

src/dsp/lossless_enc.i: src/dsp/lossless_enc.c.i

.PHONY : src/dsp/lossless_enc.i

# target to preprocess a source file
src/dsp/lossless_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc.c.i
.PHONY : src/dsp/lossless_enc.c.i

src/dsp/lossless_enc.s: src/dsp/lossless_enc.c.s

.PHONY : src/dsp/lossless_enc.s

# target to generate assembly for a file
src/dsp/lossless_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc.c.s
.PHONY : src/dsp/lossless_enc.c.s

src/dsp/lossless_enc_mips32.obj: src/dsp/lossless_enc_mips32.c.obj

.PHONY : src/dsp/lossless_enc_mips32.obj

# target to build an object file
src/dsp/lossless_enc_mips32.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips32.c.obj
.PHONY : src/dsp/lossless_enc_mips32.c.obj

src/dsp/lossless_enc_mips32.i: src/dsp/lossless_enc_mips32.c.i

.PHONY : src/dsp/lossless_enc_mips32.i

# target to preprocess a source file
src/dsp/lossless_enc_mips32.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips32.c.i
.PHONY : src/dsp/lossless_enc_mips32.c.i

src/dsp/lossless_enc_mips32.s: src/dsp/lossless_enc_mips32.c.s

.PHONY : src/dsp/lossless_enc_mips32.s

# target to generate assembly for a file
src/dsp/lossless_enc_mips32.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips32.c.s
.PHONY : src/dsp/lossless_enc_mips32.c.s

src/dsp/lossless_enc_mips_dsp_r2.obj: src/dsp/lossless_enc_mips_dsp_r2.c.obj

.PHONY : src/dsp/lossless_enc_mips_dsp_r2.obj

# target to build an object file
src/dsp/lossless_enc_mips_dsp_r2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips_dsp_r2.c.obj
.PHONY : src/dsp/lossless_enc_mips_dsp_r2.c.obj

src/dsp/lossless_enc_mips_dsp_r2.i: src/dsp/lossless_enc_mips_dsp_r2.c.i

.PHONY : src/dsp/lossless_enc_mips_dsp_r2.i

# target to preprocess a source file
src/dsp/lossless_enc_mips_dsp_r2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips_dsp_r2.c.i
.PHONY : src/dsp/lossless_enc_mips_dsp_r2.c.i

src/dsp/lossless_enc_mips_dsp_r2.s: src/dsp/lossless_enc_mips_dsp_r2.c.s

.PHONY : src/dsp/lossless_enc_mips_dsp_r2.s

# target to generate assembly for a file
src/dsp/lossless_enc_mips_dsp_r2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips_dsp_r2.c.s
.PHONY : src/dsp/lossless_enc_mips_dsp_r2.c.s

src/dsp/lossless_enc_msa.obj: src/dsp/lossless_enc_msa.c.obj

.PHONY : src/dsp/lossless_enc_msa.obj

# target to build an object file
src/dsp/lossless_enc_msa.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_msa.c.obj
.PHONY : src/dsp/lossless_enc_msa.c.obj

src/dsp/lossless_enc_msa.i: src/dsp/lossless_enc_msa.c.i

.PHONY : src/dsp/lossless_enc_msa.i

# target to preprocess a source file
src/dsp/lossless_enc_msa.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_msa.c.i
.PHONY : src/dsp/lossless_enc_msa.c.i

src/dsp/lossless_enc_msa.s: src/dsp/lossless_enc_msa.c.s

.PHONY : src/dsp/lossless_enc_msa.s

# target to generate assembly for a file
src/dsp/lossless_enc_msa.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_msa.c.s
.PHONY : src/dsp/lossless_enc_msa.c.s

src/dsp/lossless_enc_neon.obj: src/dsp/lossless_enc_neon.c.obj

.PHONY : src/dsp/lossless_enc_neon.obj

# target to build an object file
src/dsp/lossless_enc_neon.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_neon.c.obj
.PHONY : src/dsp/lossless_enc_neon.c.obj

src/dsp/lossless_enc_neon.i: src/dsp/lossless_enc_neon.c.i

.PHONY : src/dsp/lossless_enc_neon.i

# target to preprocess a source file
src/dsp/lossless_enc_neon.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_neon.c.i
.PHONY : src/dsp/lossless_enc_neon.c.i

src/dsp/lossless_enc_neon.s: src/dsp/lossless_enc_neon.c.s

.PHONY : src/dsp/lossless_enc_neon.s

# target to generate assembly for a file
src/dsp/lossless_enc_neon.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_neon.c.s
.PHONY : src/dsp/lossless_enc_neon.c.s

src/dsp/lossless_enc_sse2.obj: src/dsp/lossless_enc_sse2.c.obj

.PHONY : src/dsp/lossless_enc_sse2.obj

# target to build an object file
src/dsp/lossless_enc_sse2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse2.c.obj
.PHONY : src/dsp/lossless_enc_sse2.c.obj

src/dsp/lossless_enc_sse2.i: src/dsp/lossless_enc_sse2.c.i

.PHONY : src/dsp/lossless_enc_sse2.i

# target to preprocess a source file
src/dsp/lossless_enc_sse2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse2.c.i
.PHONY : src/dsp/lossless_enc_sse2.c.i

src/dsp/lossless_enc_sse2.s: src/dsp/lossless_enc_sse2.c.s

.PHONY : src/dsp/lossless_enc_sse2.s

# target to generate assembly for a file
src/dsp/lossless_enc_sse2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse2.c.s
.PHONY : src/dsp/lossless_enc_sse2.c.s

src/dsp/lossless_enc_sse41.obj: src/dsp/lossless_enc_sse41.c.obj

.PHONY : src/dsp/lossless_enc_sse41.obj

# target to build an object file
src/dsp/lossless_enc_sse41.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse41.c.obj
.PHONY : src/dsp/lossless_enc_sse41.c.obj

src/dsp/lossless_enc_sse41.i: src/dsp/lossless_enc_sse41.c.i

.PHONY : src/dsp/lossless_enc_sse41.i

# target to preprocess a source file
src/dsp/lossless_enc_sse41.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse41.c.i
.PHONY : src/dsp/lossless_enc_sse41.c.i

src/dsp/lossless_enc_sse41.s: src/dsp/lossless_enc_sse41.c.s

.PHONY : src/dsp/lossless_enc_sse41.s

# target to generate assembly for a file
src/dsp/lossless_enc_sse41.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse41.c.s
.PHONY : src/dsp/lossless_enc_sse41.c.s

src/dsp/lossless_mips_dsp_r2.obj: src/dsp/lossless_mips_dsp_r2.c.obj

.PHONY : src/dsp/lossless_mips_dsp_r2.obj

# target to build an object file
src/dsp/lossless_mips_dsp_r2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_mips_dsp_r2.c.obj
.PHONY : src/dsp/lossless_mips_dsp_r2.c.obj

src/dsp/lossless_mips_dsp_r2.i: src/dsp/lossless_mips_dsp_r2.c.i

.PHONY : src/dsp/lossless_mips_dsp_r2.i

# target to preprocess a source file
src/dsp/lossless_mips_dsp_r2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_mips_dsp_r2.c.i
.PHONY : src/dsp/lossless_mips_dsp_r2.c.i

src/dsp/lossless_mips_dsp_r2.s: src/dsp/lossless_mips_dsp_r2.c.s

.PHONY : src/dsp/lossless_mips_dsp_r2.s

# target to generate assembly for a file
src/dsp/lossless_mips_dsp_r2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_mips_dsp_r2.c.s
.PHONY : src/dsp/lossless_mips_dsp_r2.c.s

src/dsp/lossless_msa.obj: src/dsp/lossless_msa.c.obj

.PHONY : src/dsp/lossless_msa.obj

# target to build an object file
src/dsp/lossless_msa.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_msa.c.obj
.PHONY : src/dsp/lossless_msa.c.obj

src/dsp/lossless_msa.i: src/dsp/lossless_msa.c.i

.PHONY : src/dsp/lossless_msa.i

# target to preprocess a source file
src/dsp/lossless_msa.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_msa.c.i
.PHONY : src/dsp/lossless_msa.c.i

src/dsp/lossless_msa.s: src/dsp/lossless_msa.c.s

.PHONY : src/dsp/lossless_msa.s

# target to generate assembly for a file
src/dsp/lossless_msa.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_msa.c.s
.PHONY : src/dsp/lossless_msa.c.s

src/dsp/lossless_neon.obj: src/dsp/lossless_neon.c.obj

.PHONY : src/dsp/lossless_neon.obj

# target to build an object file
src/dsp/lossless_neon.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_neon.c.obj
.PHONY : src/dsp/lossless_neon.c.obj

src/dsp/lossless_neon.i: src/dsp/lossless_neon.c.i

.PHONY : src/dsp/lossless_neon.i

# target to preprocess a source file
src/dsp/lossless_neon.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_neon.c.i
.PHONY : src/dsp/lossless_neon.c.i

src/dsp/lossless_neon.s: src/dsp/lossless_neon.c.s

.PHONY : src/dsp/lossless_neon.s

# target to generate assembly for a file
src/dsp/lossless_neon.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_neon.c.s
.PHONY : src/dsp/lossless_neon.c.s

src/dsp/lossless_sse2.obj: src/dsp/lossless_sse2.c.obj

.PHONY : src/dsp/lossless_sse2.obj

# target to build an object file
src/dsp/lossless_sse2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_sse2.c.obj
.PHONY : src/dsp/lossless_sse2.c.obj

src/dsp/lossless_sse2.i: src/dsp/lossless_sse2.c.i

.PHONY : src/dsp/lossless_sse2.i

# target to preprocess a source file
src/dsp/lossless_sse2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_sse2.c.i
.PHONY : src/dsp/lossless_sse2.c.i

src/dsp/lossless_sse2.s: src/dsp/lossless_sse2.c.s

.PHONY : src/dsp/lossless_sse2.s

# target to generate assembly for a file
src/dsp/lossless_sse2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_sse2.c.s
.PHONY : src/dsp/lossless_sse2.c.s

src/dsp/rescaler.obj: src/dsp/rescaler.c.obj

.PHONY : src/dsp/rescaler.obj

# target to build an object file
src/dsp/rescaler.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler.c.obj
.PHONY : src/dsp/rescaler.c.obj

src/dsp/rescaler.i: src/dsp/rescaler.c.i

.PHONY : src/dsp/rescaler.i

# target to preprocess a source file
src/dsp/rescaler.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler.c.i
.PHONY : src/dsp/rescaler.c.i

src/dsp/rescaler.s: src/dsp/rescaler.c.s

.PHONY : src/dsp/rescaler.s

# target to generate assembly for a file
src/dsp/rescaler.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler.c.s
.PHONY : src/dsp/rescaler.c.s

src/dsp/rescaler_mips32.obj: src/dsp/rescaler_mips32.c.obj

.PHONY : src/dsp/rescaler_mips32.obj

# target to build an object file
src/dsp/rescaler_mips32.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips32.c.obj
.PHONY : src/dsp/rescaler_mips32.c.obj

src/dsp/rescaler_mips32.i: src/dsp/rescaler_mips32.c.i

.PHONY : src/dsp/rescaler_mips32.i

# target to preprocess a source file
src/dsp/rescaler_mips32.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips32.c.i
.PHONY : src/dsp/rescaler_mips32.c.i

src/dsp/rescaler_mips32.s: src/dsp/rescaler_mips32.c.s

.PHONY : src/dsp/rescaler_mips32.s

# target to generate assembly for a file
src/dsp/rescaler_mips32.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips32.c.s
.PHONY : src/dsp/rescaler_mips32.c.s

src/dsp/rescaler_mips_dsp_r2.obj: src/dsp/rescaler_mips_dsp_r2.c.obj

.PHONY : src/dsp/rescaler_mips_dsp_r2.obj

# target to build an object file
src/dsp/rescaler_mips_dsp_r2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips_dsp_r2.c.obj
.PHONY : src/dsp/rescaler_mips_dsp_r2.c.obj

src/dsp/rescaler_mips_dsp_r2.i: src/dsp/rescaler_mips_dsp_r2.c.i

.PHONY : src/dsp/rescaler_mips_dsp_r2.i

# target to preprocess a source file
src/dsp/rescaler_mips_dsp_r2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips_dsp_r2.c.i
.PHONY : src/dsp/rescaler_mips_dsp_r2.c.i

src/dsp/rescaler_mips_dsp_r2.s: src/dsp/rescaler_mips_dsp_r2.c.s

.PHONY : src/dsp/rescaler_mips_dsp_r2.s

# target to generate assembly for a file
src/dsp/rescaler_mips_dsp_r2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips_dsp_r2.c.s
.PHONY : src/dsp/rescaler_mips_dsp_r2.c.s

src/dsp/rescaler_msa.obj: src/dsp/rescaler_msa.c.obj

.PHONY : src/dsp/rescaler_msa.obj

# target to build an object file
src/dsp/rescaler_msa.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_msa.c.obj
.PHONY : src/dsp/rescaler_msa.c.obj

src/dsp/rescaler_msa.i: src/dsp/rescaler_msa.c.i

.PHONY : src/dsp/rescaler_msa.i

# target to preprocess a source file
src/dsp/rescaler_msa.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_msa.c.i
.PHONY : src/dsp/rescaler_msa.c.i

src/dsp/rescaler_msa.s: src/dsp/rescaler_msa.c.s

.PHONY : src/dsp/rescaler_msa.s

# target to generate assembly for a file
src/dsp/rescaler_msa.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_msa.c.s
.PHONY : src/dsp/rescaler_msa.c.s

src/dsp/rescaler_neon.obj: src/dsp/rescaler_neon.c.obj

.PHONY : src/dsp/rescaler_neon.obj

# target to build an object file
src/dsp/rescaler_neon.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_neon.c.obj
.PHONY : src/dsp/rescaler_neon.c.obj

src/dsp/rescaler_neon.i: src/dsp/rescaler_neon.c.i

.PHONY : src/dsp/rescaler_neon.i

# target to preprocess a source file
src/dsp/rescaler_neon.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_neon.c.i
.PHONY : src/dsp/rescaler_neon.c.i

src/dsp/rescaler_neon.s: src/dsp/rescaler_neon.c.s

.PHONY : src/dsp/rescaler_neon.s

# target to generate assembly for a file
src/dsp/rescaler_neon.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_neon.c.s
.PHONY : src/dsp/rescaler_neon.c.s

src/dsp/rescaler_sse2.obj: src/dsp/rescaler_sse2.c.obj

.PHONY : src/dsp/rescaler_sse2.obj

# target to build an object file
src/dsp/rescaler_sse2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_sse2.c.obj
.PHONY : src/dsp/rescaler_sse2.c.obj

src/dsp/rescaler_sse2.i: src/dsp/rescaler_sse2.c.i

.PHONY : src/dsp/rescaler_sse2.i

# target to preprocess a source file
src/dsp/rescaler_sse2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_sse2.c.i
.PHONY : src/dsp/rescaler_sse2.c.i

src/dsp/rescaler_sse2.s: src/dsp/rescaler_sse2.c.s

.PHONY : src/dsp/rescaler_sse2.s

# target to generate assembly for a file
src/dsp/rescaler_sse2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_sse2.c.s
.PHONY : src/dsp/rescaler_sse2.c.s

src/dsp/ssim.obj: src/dsp/ssim.c.obj

.PHONY : src/dsp/ssim.obj

# target to build an object file
src/dsp/ssim.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim.c.obj
.PHONY : src/dsp/ssim.c.obj

src/dsp/ssim.i: src/dsp/ssim.c.i

.PHONY : src/dsp/ssim.i

# target to preprocess a source file
src/dsp/ssim.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim.c.i
.PHONY : src/dsp/ssim.c.i

src/dsp/ssim.s: src/dsp/ssim.c.s

.PHONY : src/dsp/ssim.s

# target to generate assembly for a file
src/dsp/ssim.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim.c.s
.PHONY : src/dsp/ssim.c.s

src/dsp/ssim_sse2.obj: src/dsp/ssim_sse2.c.obj

.PHONY : src/dsp/ssim_sse2.obj

# target to build an object file
src/dsp/ssim_sse2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim_sse2.c.obj
.PHONY : src/dsp/ssim_sse2.c.obj

src/dsp/ssim_sse2.i: src/dsp/ssim_sse2.c.i

.PHONY : src/dsp/ssim_sse2.i

# target to preprocess a source file
src/dsp/ssim_sse2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim_sse2.c.i
.PHONY : src/dsp/ssim_sse2.c.i

src/dsp/ssim_sse2.s: src/dsp/ssim_sse2.c.s

.PHONY : src/dsp/ssim_sse2.s

# target to generate assembly for a file
src/dsp/ssim_sse2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim_sse2.c.s
.PHONY : src/dsp/ssim_sse2.c.s

src/dsp/upsampling.obj: src/dsp/upsampling.c.obj

.PHONY : src/dsp/upsampling.obj

# target to build an object file
src/dsp/upsampling.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling.c.obj
.PHONY : src/dsp/upsampling.c.obj

src/dsp/upsampling.i: src/dsp/upsampling.c.i

.PHONY : src/dsp/upsampling.i

# target to preprocess a source file
src/dsp/upsampling.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling.c.i
.PHONY : src/dsp/upsampling.c.i

src/dsp/upsampling.s: src/dsp/upsampling.c.s

.PHONY : src/dsp/upsampling.s

# target to generate assembly for a file
src/dsp/upsampling.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling.c.s
.PHONY : src/dsp/upsampling.c.s

src/dsp/upsampling_mips_dsp_r2.obj: src/dsp/upsampling_mips_dsp_r2.c.obj

.PHONY : src/dsp/upsampling_mips_dsp_r2.obj

# target to build an object file
src/dsp/upsampling_mips_dsp_r2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_mips_dsp_r2.c.obj
.PHONY : src/dsp/upsampling_mips_dsp_r2.c.obj

src/dsp/upsampling_mips_dsp_r2.i: src/dsp/upsampling_mips_dsp_r2.c.i

.PHONY : src/dsp/upsampling_mips_dsp_r2.i

# target to preprocess a source file
src/dsp/upsampling_mips_dsp_r2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_mips_dsp_r2.c.i
.PHONY : src/dsp/upsampling_mips_dsp_r2.c.i

src/dsp/upsampling_mips_dsp_r2.s: src/dsp/upsampling_mips_dsp_r2.c.s

.PHONY : src/dsp/upsampling_mips_dsp_r2.s

# target to generate assembly for a file
src/dsp/upsampling_mips_dsp_r2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_mips_dsp_r2.c.s
.PHONY : src/dsp/upsampling_mips_dsp_r2.c.s

src/dsp/upsampling_msa.obj: src/dsp/upsampling_msa.c.obj

.PHONY : src/dsp/upsampling_msa.obj

# target to build an object file
src/dsp/upsampling_msa.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_msa.c.obj
.PHONY : src/dsp/upsampling_msa.c.obj

src/dsp/upsampling_msa.i: src/dsp/upsampling_msa.c.i

.PHONY : src/dsp/upsampling_msa.i

# target to preprocess a source file
src/dsp/upsampling_msa.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_msa.c.i
.PHONY : src/dsp/upsampling_msa.c.i

src/dsp/upsampling_msa.s: src/dsp/upsampling_msa.c.s

.PHONY : src/dsp/upsampling_msa.s

# target to generate assembly for a file
src/dsp/upsampling_msa.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_msa.c.s
.PHONY : src/dsp/upsampling_msa.c.s

src/dsp/upsampling_neon.obj: src/dsp/upsampling_neon.c.obj

.PHONY : src/dsp/upsampling_neon.obj

# target to build an object file
src/dsp/upsampling_neon.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_neon.c.obj
.PHONY : src/dsp/upsampling_neon.c.obj

src/dsp/upsampling_neon.i: src/dsp/upsampling_neon.c.i

.PHONY : src/dsp/upsampling_neon.i

# target to preprocess a source file
src/dsp/upsampling_neon.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_neon.c.i
.PHONY : src/dsp/upsampling_neon.c.i

src/dsp/upsampling_neon.s: src/dsp/upsampling_neon.c.s

.PHONY : src/dsp/upsampling_neon.s

# target to generate assembly for a file
src/dsp/upsampling_neon.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_neon.c.s
.PHONY : src/dsp/upsampling_neon.c.s

src/dsp/upsampling_sse2.obj: src/dsp/upsampling_sse2.c.obj

.PHONY : src/dsp/upsampling_sse2.obj

# target to build an object file
src/dsp/upsampling_sse2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse2.c.obj
.PHONY : src/dsp/upsampling_sse2.c.obj

src/dsp/upsampling_sse2.i: src/dsp/upsampling_sse2.c.i

.PHONY : src/dsp/upsampling_sse2.i

# target to preprocess a source file
src/dsp/upsampling_sse2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse2.c.i
.PHONY : src/dsp/upsampling_sse2.c.i

src/dsp/upsampling_sse2.s: src/dsp/upsampling_sse2.c.s

.PHONY : src/dsp/upsampling_sse2.s

# target to generate assembly for a file
src/dsp/upsampling_sse2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse2.c.s
.PHONY : src/dsp/upsampling_sse2.c.s

src/dsp/upsampling_sse41.obj: src/dsp/upsampling_sse41.c.obj

.PHONY : src/dsp/upsampling_sse41.obj

# target to build an object file
src/dsp/upsampling_sse41.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse41.c.obj
.PHONY : src/dsp/upsampling_sse41.c.obj

src/dsp/upsampling_sse41.i: src/dsp/upsampling_sse41.c.i

.PHONY : src/dsp/upsampling_sse41.i

# target to preprocess a source file
src/dsp/upsampling_sse41.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse41.c.i
.PHONY : src/dsp/upsampling_sse41.c.i

src/dsp/upsampling_sse41.s: src/dsp/upsampling_sse41.c.s

.PHONY : src/dsp/upsampling_sse41.s

# target to generate assembly for a file
src/dsp/upsampling_sse41.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse41.c.s
.PHONY : src/dsp/upsampling_sse41.c.s

src/dsp/yuv.obj: src/dsp/yuv.c.obj

.PHONY : src/dsp/yuv.obj

# target to build an object file
src/dsp/yuv.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv.c.obj
.PHONY : src/dsp/yuv.c.obj

src/dsp/yuv.i: src/dsp/yuv.c.i

.PHONY : src/dsp/yuv.i

# target to preprocess a source file
src/dsp/yuv.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv.c.i
.PHONY : src/dsp/yuv.c.i

src/dsp/yuv.s: src/dsp/yuv.c.s

.PHONY : src/dsp/yuv.s

# target to generate assembly for a file
src/dsp/yuv.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv.c.s
.PHONY : src/dsp/yuv.c.s

src/dsp/yuv_mips32.obj: src/dsp/yuv_mips32.c.obj

.PHONY : src/dsp/yuv_mips32.obj

# target to build an object file
src/dsp/yuv_mips32.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips32.c.obj
.PHONY : src/dsp/yuv_mips32.c.obj

src/dsp/yuv_mips32.i: src/dsp/yuv_mips32.c.i

.PHONY : src/dsp/yuv_mips32.i

# target to preprocess a source file
src/dsp/yuv_mips32.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips32.c.i
.PHONY : src/dsp/yuv_mips32.c.i

src/dsp/yuv_mips32.s: src/dsp/yuv_mips32.c.s

.PHONY : src/dsp/yuv_mips32.s

# target to generate assembly for a file
src/dsp/yuv_mips32.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips32.c.s
.PHONY : src/dsp/yuv_mips32.c.s

src/dsp/yuv_mips_dsp_r2.obj: src/dsp/yuv_mips_dsp_r2.c.obj

.PHONY : src/dsp/yuv_mips_dsp_r2.obj

# target to build an object file
src/dsp/yuv_mips_dsp_r2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips_dsp_r2.c.obj
.PHONY : src/dsp/yuv_mips_dsp_r2.c.obj

src/dsp/yuv_mips_dsp_r2.i: src/dsp/yuv_mips_dsp_r2.c.i

.PHONY : src/dsp/yuv_mips_dsp_r2.i

# target to preprocess a source file
src/dsp/yuv_mips_dsp_r2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips_dsp_r2.c.i
.PHONY : src/dsp/yuv_mips_dsp_r2.c.i

src/dsp/yuv_mips_dsp_r2.s: src/dsp/yuv_mips_dsp_r2.c.s

.PHONY : src/dsp/yuv_mips_dsp_r2.s

# target to generate assembly for a file
src/dsp/yuv_mips_dsp_r2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips_dsp_r2.c.s
.PHONY : src/dsp/yuv_mips_dsp_r2.c.s

src/dsp/yuv_neon.obj: src/dsp/yuv_neon.c.obj

.PHONY : src/dsp/yuv_neon.obj

# target to build an object file
src/dsp/yuv_neon.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_neon.c.obj
.PHONY : src/dsp/yuv_neon.c.obj

src/dsp/yuv_neon.i: src/dsp/yuv_neon.c.i

.PHONY : src/dsp/yuv_neon.i

# target to preprocess a source file
src/dsp/yuv_neon.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_neon.c.i
.PHONY : src/dsp/yuv_neon.c.i

src/dsp/yuv_neon.s: src/dsp/yuv_neon.c.s

.PHONY : src/dsp/yuv_neon.s

# target to generate assembly for a file
src/dsp/yuv_neon.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_neon.c.s
.PHONY : src/dsp/yuv_neon.c.s

src/dsp/yuv_sse2.obj: src/dsp/yuv_sse2.c.obj

.PHONY : src/dsp/yuv_sse2.obj

# target to build an object file
src/dsp/yuv_sse2.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse2.c.obj
.PHONY : src/dsp/yuv_sse2.c.obj

src/dsp/yuv_sse2.i: src/dsp/yuv_sse2.c.i

.PHONY : src/dsp/yuv_sse2.i

# target to preprocess a source file
src/dsp/yuv_sse2.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse2.c.i
.PHONY : src/dsp/yuv_sse2.c.i

src/dsp/yuv_sse2.s: src/dsp/yuv_sse2.c.s

.PHONY : src/dsp/yuv_sse2.s

# target to generate assembly for a file
src/dsp/yuv_sse2.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse2.c.s
.PHONY : src/dsp/yuv_sse2.c.s

src/dsp/yuv_sse41.obj: src/dsp/yuv_sse41.c.obj

.PHONY : src/dsp/yuv_sse41.obj

# target to build an object file
src/dsp/yuv_sse41.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse41.c.obj
.PHONY : src/dsp/yuv_sse41.c.obj

src/dsp/yuv_sse41.i: src/dsp/yuv_sse41.c.i

.PHONY : src/dsp/yuv_sse41.i

# target to preprocess a source file
src/dsp/yuv_sse41.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse41.c.i
.PHONY : src/dsp/yuv_sse41.c.i

src/dsp/yuv_sse41.s: src/dsp/yuv_sse41.c.s

.PHONY : src/dsp/yuv_sse41.s

# target to generate assembly for a file
src/dsp/yuv_sse41.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse41.c.s
.PHONY : src/dsp/yuv_sse41.c.s

src/enc/alpha_enc.obj: src/enc/alpha_enc.c.obj

.PHONY : src/enc/alpha_enc.obj

# target to build an object file
src/enc/alpha_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/alpha_enc.c.obj
.PHONY : src/enc/alpha_enc.c.obj

src/enc/alpha_enc.i: src/enc/alpha_enc.c.i

.PHONY : src/enc/alpha_enc.i

# target to preprocess a source file
src/enc/alpha_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/alpha_enc.c.i
.PHONY : src/enc/alpha_enc.c.i

src/enc/alpha_enc.s: src/enc/alpha_enc.c.s

.PHONY : src/enc/alpha_enc.s

# target to generate assembly for a file
src/enc/alpha_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/alpha_enc.c.s
.PHONY : src/enc/alpha_enc.c.s

src/enc/analysis_enc.obj: src/enc/analysis_enc.c.obj

.PHONY : src/enc/analysis_enc.obj

# target to build an object file
src/enc/analysis_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/analysis_enc.c.obj
.PHONY : src/enc/analysis_enc.c.obj

src/enc/analysis_enc.i: src/enc/analysis_enc.c.i

.PHONY : src/enc/analysis_enc.i

# target to preprocess a source file
src/enc/analysis_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/analysis_enc.c.i
.PHONY : src/enc/analysis_enc.c.i

src/enc/analysis_enc.s: src/enc/analysis_enc.c.s

.PHONY : src/enc/analysis_enc.s

# target to generate assembly for a file
src/enc/analysis_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/analysis_enc.c.s
.PHONY : src/enc/analysis_enc.c.s

src/enc/backward_references_cost_enc.obj: src/enc/backward_references_cost_enc.c.obj

.PHONY : src/enc/backward_references_cost_enc.obj

# target to build an object file
src/enc/backward_references_cost_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_cost_enc.c.obj
.PHONY : src/enc/backward_references_cost_enc.c.obj

src/enc/backward_references_cost_enc.i: src/enc/backward_references_cost_enc.c.i

.PHONY : src/enc/backward_references_cost_enc.i

# target to preprocess a source file
src/enc/backward_references_cost_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_cost_enc.c.i
.PHONY : src/enc/backward_references_cost_enc.c.i

src/enc/backward_references_cost_enc.s: src/enc/backward_references_cost_enc.c.s

.PHONY : src/enc/backward_references_cost_enc.s

# target to generate assembly for a file
src/enc/backward_references_cost_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_cost_enc.c.s
.PHONY : src/enc/backward_references_cost_enc.c.s

src/enc/backward_references_enc.obj: src/enc/backward_references_enc.c.obj

.PHONY : src/enc/backward_references_enc.obj

# target to build an object file
src/enc/backward_references_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_enc.c.obj
.PHONY : src/enc/backward_references_enc.c.obj

src/enc/backward_references_enc.i: src/enc/backward_references_enc.c.i

.PHONY : src/enc/backward_references_enc.i

# target to preprocess a source file
src/enc/backward_references_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_enc.c.i
.PHONY : src/enc/backward_references_enc.c.i

src/enc/backward_references_enc.s: src/enc/backward_references_enc.c.s

.PHONY : src/enc/backward_references_enc.s

# target to generate assembly for a file
src/enc/backward_references_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_enc.c.s
.PHONY : src/enc/backward_references_enc.c.s

src/enc/config_enc.obj: src/enc/config_enc.c.obj

.PHONY : src/enc/config_enc.obj

# target to build an object file
src/enc/config_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/config_enc.c.obj
.PHONY : src/enc/config_enc.c.obj

src/enc/config_enc.i: src/enc/config_enc.c.i

.PHONY : src/enc/config_enc.i

# target to preprocess a source file
src/enc/config_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/config_enc.c.i
.PHONY : src/enc/config_enc.c.i

src/enc/config_enc.s: src/enc/config_enc.c.s

.PHONY : src/enc/config_enc.s

# target to generate assembly for a file
src/enc/config_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/config_enc.c.s
.PHONY : src/enc/config_enc.c.s

src/enc/cost_enc.obj: src/enc/cost_enc.c.obj

.PHONY : src/enc/cost_enc.obj

# target to build an object file
src/enc/cost_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/cost_enc.c.obj
.PHONY : src/enc/cost_enc.c.obj

src/enc/cost_enc.i: src/enc/cost_enc.c.i

.PHONY : src/enc/cost_enc.i

# target to preprocess a source file
src/enc/cost_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/cost_enc.c.i
.PHONY : src/enc/cost_enc.c.i

src/enc/cost_enc.s: src/enc/cost_enc.c.s

.PHONY : src/enc/cost_enc.s

# target to generate assembly for a file
src/enc/cost_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/cost_enc.c.s
.PHONY : src/enc/cost_enc.c.s

src/enc/filter_enc.obj: src/enc/filter_enc.c.obj

.PHONY : src/enc/filter_enc.obj

# target to build an object file
src/enc/filter_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/filter_enc.c.obj
.PHONY : src/enc/filter_enc.c.obj

src/enc/filter_enc.i: src/enc/filter_enc.c.i

.PHONY : src/enc/filter_enc.i

# target to preprocess a source file
src/enc/filter_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/filter_enc.c.i
.PHONY : src/enc/filter_enc.c.i

src/enc/filter_enc.s: src/enc/filter_enc.c.s

.PHONY : src/enc/filter_enc.s

# target to generate assembly for a file
src/enc/filter_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/filter_enc.c.s
.PHONY : src/enc/filter_enc.c.s

src/enc/frame_enc.obj: src/enc/frame_enc.c.obj

.PHONY : src/enc/frame_enc.obj

# target to build an object file
src/enc/frame_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/frame_enc.c.obj
.PHONY : src/enc/frame_enc.c.obj

src/enc/frame_enc.i: src/enc/frame_enc.c.i

.PHONY : src/enc/frame_enc.i

# target to preprocess a source file
src/enc/frame_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/frame_enc.c.i
.PHONY : src/enc/frame_enc.c.i

src/enc/frame_enc.s: src/enc/frame_enc.c.s

.PHONY : src/enc/frame_enc.s

# target to generate assembly for a file
src/enc/frame_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/frame_enc.c.s
.PHONY : src/enc/frame_enc.c.s

src/enc/histogram_enc.obj: src/enc/histogram_enc.c.obj

.PHONY : src/enc/histogram_enc.obj

# target to build an object file
src/enc/histogram_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/histogram_enc.c.obj
.PHONY : src/enc/histogram_enc.c.obj

src/enc/histogram_enc.i: src/enc/histogram_enc.c.i

.PHONY : src/enc/histogram_enc.i

# target to preprocess a source file
src/enc/histogram_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/histogram_enc.c.i
.PHONY : src/enc/histogram_enc.c.i

src/enc/histogram_enc.s: src/enc/histogram_enc.c.s

.PHONY : src/enc/histogram_enc.s

# target to generate assembly for a file
src/enc/histogram_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/histogram_enc.c.s
.PHONY : src/enc/histogram_enc.c.s

src/enc/iterator_enc.obj: src/enc/iterator_enc.c.obj

.PHONY : src/enc/iterator_enc.obj

# target to build an object file
src/enc/iterator_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/iterator_enc.c.obj
.PHONY : src/enc/iterator_enc.c.obj

src/enc/iterator_enc.i: src/enc/iterator_enc.c.i

.PHONY : src/enc/iterator_enc.i

# target to preprocess a source file
src/enc/iterator_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/iterator_enc.c.i
.PHONY : src/enc/iterator_enc.c.i

src/enc/iterator_enc.s: src/enc/iterator_enc.c.s

.PHONY : src/enc/iterator_enc.s

# target to generate assembly for a file
src/enc/iterator_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/iterator_enc.c.s
.PHONY : src/enc/iterator_enc.c.s

src/enc/near_lossless_enc.obj: src/enc/near_lossless_enc.c.obj

.PHONY : src/enc/near_lossless_enc.obj

# target to build an object file
src/enc/near_lossless_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/near_lossless_enc.c.obj
.PHONY : src/enc/near_lossless_enc.c.obj

src/enc/near_lossless_enc.i: src/enc/near_lossless_enc.c.i

.PHONY : src/enc/near_lossless_enc.i

# target to preprocess a source file
src/enc/near_lossless_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/near_lossless_enc.c.i
.PHONY : src/enc/near_lossless_enc.c.i

src/enc/near_lossless_enc.s: src/enc/near_lossless_enc.c.s

.PHONY : src/enc/near_lossless_enc.s

# target to generate assembly for a file
src/enc/near_lossless_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/near_lossless_enc.c.s
.PHONY : src/enc/near_lossless_enc.c.s

src/enc/picture_csp_enc.obj: src/enc/picture_csp_enc.c.obj

.PHONY : src/enc/picture_csp_enc.obj

# target to build an object file
src/enc/picture_csp_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_csp_enc.c.obj
.PHONY : src/enc/picture_csp_enc.c.obj

src/enc/picture_csp_enc.i: src/enc/picture_csp_enc.c.i

.PHONY : src/enc/picture_csp_enc.i

# target to preprocess a source file
src/enc/picture_csp_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_csp_enc.c.i
.PHONY : src/enc/picture_csp_enc.c.i

src/enc/picture_csp_enc.s: src/enc/picture_csp_enc.c.s

.PHONY : src/enc/picture_csp_enc.s

# target to generate assembly for a file
src/enc/picture_csp_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_csp_enc.c.s
.PHONY : src/enc/picture_csp_enc.c.s

src/enc/picture_enc.obj: src/enc/picture_enc.c.obj

.PHONY : src/enc/picture_enc.obj

# target to build an object file
src/enc/picture_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_enc.c.obj
.PHONY : src/enc/picture_enc.c.obj

src/enc/picture_enc.i: src/enc/picture_enc.c.i

.PHONY : src/enc/picture_enc.i

# target to preprocess a source file
src/enc/picture_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_enc.c.i
.PHONY : src/enc/picture_enc.c.i

src/enc/picture_enc.s: src/enc/picture_enc.c.s

.PHONY : src/enc/picture_enc.s

# target to generate assembly for a file
src/enc/picture_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_enc.c.s
.PHONY : src/enc/picture_enc.c.s

src/enc/picture_psnr_enc.obj: src/enc/picture_psnr_enc.c.obj

.PHONY : src/enc/picture_psnr_enc.obj

# target to build an object file
src/enc/picture_psnr_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_psnr_enc.c.obj
.PHONY : src/enc/picture_psnr_enc.c.obj

src/enc/picture_psnr_enc.i: src/enc/picture_psnr_enc.c.i

.PHONY : src/enc/picture_psnr_enc.i

# target to preprocess a source file
src/enc/picture_psnr_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_psnr_enc.c.i
.PHONY : src/enc/picture_psnr_enc.c.i

src/enc/picture_psnr_enc.s: src/enc/picture_psnr_enc.c.s

.PHONY : src/enc/picture_psnr_enc.s

# target to generate assembly for a file
src/enc/picture_psnr_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_psnr_enc.c.s
.PHONY : src/enc/picture_psnr_enc.c.s

src/enc/picture_rescale_enc.obj: src/enc/picture_rescale_enc.c.obj

.PHONY : src/enc/picture_rescale_enc.obj

# target to build an object file
src/enc/picture_rescale_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_rescale_enc.c.obj
.PHONY : src/enc/picture_rescale_enc.c.obj

src/enc/picture_rescale_enc.i: src/enc/picture_rescale_enc.c.i

.PHONY : src/enc/picture_rescale_enc.i

# target to preprocess a source file
src/enc/picture_rescale_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_rescale_enc.c.i
.PHONY : src/enc/picture_rescale_enc.c.i

src/enc/picture_rescale_enc.s: src/enc/picture_rescale_enc.c.s

.PHONY : src/enc/picture_rescale_enc.s

# target to generate assembly for a file
src/enc/picture_rescale_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_rescale_enc.c.s
.PHONY : src/enc/picture_rescale_enc.c.s

src/enc/picture_tools_enc.obj: src/enc/picture_tools_enc.c.obj

.PHONY : src/enc/picture_tools_enc.obj

# target to build an object file
src/enc/picture_tools_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_tools_enc.c.obj
.PHONY : src/enc/picture_tools_enc.c.obj

src/enc/picture_tools_enc.i: src/enc/picture_tools_enc.c.i

.PHONY : src/enc/picture_tools_enc.i

# target to preprocess a source file
src/enc/picture_tools_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_tools_enc.c.i
.PHONY : src/enc/picture_tools_enc.c.i

src/enc/picture_tools_enc.s: src/enc/picture_tools_enc.c.s

.PHONY : src/enc/picture_tools_enc.s

# target to generate assembly for a file
src/enc/picture_tools_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_tools_enc.c.s
.PHONY : src/enc/picture_tools_enc.c.s

src/enc/predictor_enc.obj: src/enc/predictor_enc.c.obj

.PHONY : src/enc/predictor_enc.obj

# target to build an object file
src/enc/predictor_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/predictor_enc.c.obj
.PHONY : src/enc/predictor_enc.c.obj

src/enc/predictor_enc.i: src/enc/predictor_enc.c.i

.PHONY : src/enc/predictor_enc.i

# target to preprocess a source file
src/enc/predictor_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/predictor_enc.c.i
.PHONY : src/enc/predictor_enc.c.i

src/enc/predictor_enc.s: src/enc/predictor_enc.c.s

.PHONY : src/enc/predictor_enc.s

# target to generate assembly for a file
src/enc/predictor_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/predictor_enc.c.s
.PHONY : src/enc/predictor_enc.c.s

src/enc/quant_enc.obj: src/enc/quant_enc.c.obj

.PHONY : src/enc/quant_enc.obj

# target to build an object file
src/enc/quant_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/quant_enc.c.obj
.PHONY : src/enc/quant_enc.c.obj

src/enc/quant_enc.i: src/enc/quant_enc.c.i

.PHONY : src/enc/quant_enc.i

# target to preprocess a source file
src/enc/quant_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/quant_enc.c.i
.PHONY : src/enc/quant_enc.c.i

src/enc/quant_enc.s: src/enc/quant_enc.c.s

.PHONY : src/enc/quant_enc.s

# target to generate assembly for a file
src/enc/quant_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/quant_enc.c.s
.PHONY : src/enc/quant_enc.c.s

src/enc/syntax_enc.obj: src/enc/syntax_enc.c.obj

.PHONY : src/enc/syntax_enc.obj

# target to build an object file
src/enc/syntax_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/syntax_enc.c.obj
.PHONY : src/enc/syntax_enc.c.obj

src/enc/syntax_enc.i: src/enc/syntax_enc.c.i

.PHONY : src/enc/syntax_enc.i

# target to preprocess a source file
src/enc/syntax_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/syntax_enc.c.i
.PHONY : src/enc/syntax_enc.c.i

src/enc/syntax_enc.s: src/enc/syntax_enc.c.s

.PHONY : src/enc/syntax_enc.s

# target to generate assembly for a file
src/enc/syntax_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/syntax_enc.c.s
.PHONY : src/enc/syntax_enc.c.s

src/enc/token_enc.obj: src/enc/token_enc.c.obj

.PHONY : src/enc/token_enc.obj

# target to build an object file
src/enc/token_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/token_enc.c.obj
.PHONY : src/enc/token_enc.c.obj

src/enc/token_enc.i: src/enc/token_enc.c.i

.PHONY : src/enc/token_enc.i

# target to preprocess a source file
src/enc/token_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/token_enc.c.i
.PHONY : src/enc/token_enc.c.i

src/enc/token_enc.s: src/enc/token_enc.c.s

.PHONY : src/enc/token_enc.s

# target to generate assembly for a file
src/enc/token_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/token_enc.c.s
.PHONY : src/enc/token_enc.c.s

src/enc/tree_enc.obj: src/enc/tree_enc.c.obj

.PHONY : src/enc/tree_enc.obj

# target to build an object file
src/enc/tree_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/tree_enc.c.obj
.PHONY : src/enc/tree_enc.c.obj

src/enc/tree_enc.i: src/enc/tree_enc.c.i

.PHONY : src/enc/tree_enc.i

# target to preprocess a source file
src/enc/tree_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/tree_enc.c.i
.PHONY : src/enc/tree_enc.c.i

src/enc/tree_enc.s: src/enc/tree_enc.c.s

.PHONY : src/enc/tree_enc.s

# target to generate assembly for a file
src/enc/tree_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/tree_enc.c.s
.PHONY : src/enc/tree_enc.c.s

src/enc/vp8l_enc.obj: src/enc/vp8l_enc.c.obj

.PHONY : src/enc/vp8l_enc.obj

# target to build an object file
src/enc/vp8l_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/vp8l_enc.c.obj
.PHONY : src/enc/vp8l_enc.c.obj

src/enc/vp8l_enc.i: src/enc/vp8l_enc.c.i

.PHONY : src/enc/vp8l_enc.i

# target to preprocess a source file
src/enc/vp8l_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/vp8l_enc.c.i
.PHONY : src/enc/vp8l_enc.c.i

src/enc/vp8l_enc.s: src/enc/vp8l_enc.c.s

.PHONY : src/enc/vp8l_enc.s

# target to generate assembly for a file
src/enc/vp8l_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/vp8l_enc.c.s
.PHONY : src/enc/vp8l_enc.c.s

src/enc/webp_enc.obj: src/enc/webp_enc.c.obj

.PHONY : src/enc/webp_enc.obj

# target to build an object file
src/enc/webp_enc.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/webp_enc.c.obj
.PHONY : src/enc/webp_enc.c.obj

src/enc/webp_enc.i: src/enc/webp_enc.c.i

.PHONY : src/enc/webp_enc.i

# target to preprocess a source file
src/enc/webp_enc.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/webp_enc.c.i
.PHONY : src/enc/webp_enc.c.i

src/enc/webp_enc.s: src/enc/webp_enc.c.s

.PHONY : src/enc/webp_enc.s

# target to generate assembly for a file
src/enc/webp_enc.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/webp_enc.c.s
.PHONY : src/enc/webp_enc.c.s

src/mux/anim_encode.obj: src/mux/anim_encode.c.obj

.PHONY : src/mux/anim_encode.obj

# target to build an object file
src/mux/anim_encode.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/anim_encode.c.obj
.PHONY : src/mux/anim_encode.c.obj

src/mux/anim_encode.i: src/mux/anim_encode.c.i

.PHONY : src/mux/anim_encode.i

# target to preprocess a source file
src/mux/anim_encode.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/anim_encode.c.i
.PHONY : src/mux/anim_encode.c.i

src/mux/anim_encode.s: src/mux/anim_encode.c.s

.PHONY : src/mux/anim_encode.s

# target to generate assembly for a file
src/mux/anim_encode.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/anim_encode.c.s
.PHONY : src/mux/anim_encode.c.s

src/mux/muxedit.obj: src/mux/muxedit.c.obj

.PHONY : src/mux/muxedit.obj

# target to build an object file
src/mux/muxedit.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxedit.c.obj
.PHONY : src/mux/muxedit.c.obj

src/mux/muxedit.i: src/mux/muxedit.c.i

.PHONY : src/mux/muxedit.i

# target to preprocess a source file
src/mux/muxedit.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxedit.c.i
.PHONY : src/mux/muxedit.c.i

src/mux/muxedit.s: src/mux/muxedit.c.s

.PHONY : src/mux/muxedit.s

# target to generate assembly for a file
src/mux/muxedit.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxedit.c.s
.PHONY : src/mux/muxedit.c.s

src/mux/muxinternal.obj: src/mux/muxinternal.c.obj

.PHONY : src/mux/muxinternal.obj

# target to build an object file
src/mux/muxinternal.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxinternal.c.obj
.PHONY : src/mux/muxinternal.c.obj

src/mux/muxinternal.i: src/mux/muxinternal.c.i

.PHONY : src/mux/muxinternal.i

# target to preprocess a source file
src/mux/muxinternal.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxinternal.c.i
.PHONY : src/mux/muxinternal.c.i

src/mux/muxinternal.s: src/mux/muxinternal.c.s

.PHONY : src/mux/muxinternal.s

# target to generate assembly for a file
src/mux/muxinternal.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxinternal.c.s
.PHONY : src/mux/muxinternal.c.s

src/mux/muxread.obj: src/mux/muxread.c.obj

.PHONY : src/mux/muxread.obj

# target to build an object file
src/mux/muxread.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxread.c.obj
.PHONY : src/mux/muxread.c.obj

src/mux/muxread.i: src/mux/muxread.c.i

.PHONY : src/mux/muxread.i

# target to preprocess a source file
src/mux/muxread.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxread.c.i
.PHONY : src/mux/muxread.c.i

src/mux/muxread.s: src/mux/muxread.c.s

.PHONY : src/mux/muxread.s

# target to generate assembly for a file
src/mux/muxread.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxread.c.s
.PHONY : src/mux/muxread.c.s

src/utils/bit_reader_utils.obj: src/utils/bit_reader_utils.c.obj

.PHONY : src/utils/bit_reader_utils.obj

# target to build an object file
src/utils/bit_reader_utils.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_reader_utils.c.obj
.PHONY : src/utils/bit_reader_utils.c.obj

src/utils/bit_reader_utils.i: src/utils/bit_reader_utils.c.i

.PHONY : src/utils/bit_reader_utils.i

# target to preprocess a source file
src/utils/bit_reader_utils.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_reader_utils.c.i
.PHONY : src/utils/bit_reader_utils.c.i

src/utils/bit_reader_utils.s: src/utils/bit_reader_utils.c.s

.PHONY : src/utils/bit_reader_utils.s

# target to generate assembly for a file
src/utils/bit_reader_utils.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_reader_utils.c.s
.PHONY : src/utils/bit_reader_utils.c.s

src/utils/bit_writer_utils.obj: src/utils/bit_writer_utils.c.obj

.PHONY : src/utils/bit_writer_utils.obj

# target to build an object file
src/utils/bit_writer_utils.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_writer_utils.c.obj
.PHONY : src/utils/bit_writer_utils.c.obj

src/utils/bit_writer_utils.i: src/utils/bit_writer_utils.c.i

.PHONY : src/utils/bit_writer_utils.i

# target to preprocess a source file
src/utils/bit_writer_utils.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_writer_utils.c.i
.PHONY : src/utils/bit_writer_utils.c.i

src/utils/bit_writer_utils.s: src/utils/bit_writer_utils.c.s

.PHONY : src/utils/bit_writer_utils.s

# target to generate assembly for a file
src/utils/bit_writer_utils.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_writer_utils.c.s
.PHONY : src/utils/bit_writer_utils.c.s

src/utils/color_cache_utils.obj: src/utils/color_cache_utils.c.obj

.PHONY : src/utils/color_cache_utils.obj

# target to build an object file
src/utils/color_cache_utils.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/color_cache_utils.c.obj
.PHONY : src/utils/color_cache_utils.c.obj

src/utils/color_cache_utils.i: src/utils/color_cache_utils.c.i

.PHONY : src/utils/color_cache_utils.i

# target to preprocess a source file
src/utils/color_cache_utils.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/color_cache_utils.c.i
.PHONY : src/utils/color_cache_utils.c.i

src/utils/color_cache_utils.s: src/utils/color_cache_utils.c.s

.PHONY : src/utils/color_cache_utils.s

# target to generate assembly for a file
src/utils/color_cache_utils.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/color_cache_utils.c.s
.PHONY : src/utils/color_cache_utils.c.s

src/utils/filters_utils.obj: src/utils/filters_utils.c.obj

.PHONY : src/utils/filters_utils.obj

# target to build an object file
src/utils/filters_utils.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/filters_utils.c.obj
.PHONY : src/utils/filters_utils.c.obj

src/utils/filters_utils.i: src/utils/filters_utils.c.i

.PHONY : src/utils/filters_utils.i

# target to preprocess a source file
src/utils/filters_utils.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/filters_utils.c.i
.PHONY : src/utils/filters_utils.c.i

src/utils/filters_utils.s: src/utils/filters_utils.c.s

.PHONY : src/utils/filters_utils.s

# target to generate assembly for a file
src/utils/filters_utils.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/filters_utils.c.s
.PHONY : src/utils/filters_utils.c.s

src/utils/huffman_encode_utils.obj: src/utils/huffman_encode_utils.c.obj

.PHONY : src/utils/huffman_encode_utils.obj

# target to build an object file
src/utils/huffman_encode_utils.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_encode_utils.c.obj
.PHONY : src/utils/huffman_encode_utils.c.obj

src/utils/huffman_encode_utils.i: src/utils/huffman_encode_utils.c.i

.PHONY : src/utils/huffman_encode_utils.i

# target to preprocess a source file
src/utils/huffman_encode_utils.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_encode_utils.c.i
.PHONY : src/utils/huffman_encode_utils.c.i

src/utils/huffman_encode_utils.s: src/utils/huffman_encode_utils.c.s

.PHONY : src/utils/huffman_encode_utils.s

# target to generate assembly for a file
src/utils/huffman_encode_utils.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_encode_utils.c.s
.PHONY : src/utils/huffman_encode_utils.c.s

src/utils/huffman_utils.obj: src/utils/huffman_utils.c.obj

.PHONY : src/utils/huffman_utils.obj

# target to build an object file
src/utils/huffman_utils.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_utils.c.obj
.PHONY : src/utils/huffman_utils.c.obj

src/utils/huffman_utils.i: src/utils/huffman_utils.c.i

.PHONY : src/utils/huffman_utils.i

# target to preprocess a source file
src/utils/huffman_utils.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_utils.c.i
.PHONY : src/utils/huffman_utils.c.i

src/utils/huffman_utils.s: src/utils/huffman_utils.c.s

.PHONY : src/utils/huffman_utils.s

# target to generate assembly for a file
src/utils/huffman_utils.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_utils.c.s
.PHONY : src/utils/huffman_utils.c.s

src/utils/quant_levels_dec_utils.obj: src/utils/quant_levels_dec_utils.c.obj

.PHONY : src/utils/quant_levels_dec_utils.obj

# target to build an object file
src/utils/quant_levels_dec_utils.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_dec_utils.c.obj
.PHONY : src/utils/quant_levels_dec_utils.c.obj

src/utils/quant_levels_dec_utils.i: src/utils/quant_levels_dec_utils.c.i

.PHONY : src/utils/quant_levels_dec_utils.i

# target to preprocess a source file
src/utils/quant_levels_dec_utils.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_dec_utils.c.i
.PHONY : src/utils/quant_levels_dec_utils.c.i

src/utils/quant_levels_dec_utils.s: src/utils/quant_levels_dec_utils.c.s

.PHONY : src/utils/quant_levels_dec_utils.s

# target to generate assembly for a file
src/utils/quant_levels_dec_utils.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_dec_utils.c.s
.PHONY : src/utils/quant_levels_dec_utils.c.s

src/utils/quant_levels_utils.obj: src/utils/quant_levels_utils.c.obj

.PHONY : src/utils/quant_levels_utils.obj

# target to build an object file
src/utils/quant_levels_utils.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_utils.c.obj
.PHONY : src/utils/quant_levels_utils.c.obj

src/utils/quant_levels_utils.i: src/utils/quant_levels_utils.c.i

.PHONY : src/utils/quant_levels_utils.i

# target to preprocess a source file
src/utils/quant_levels_utils.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_utils.c.i
.PHONY : src/utils/quant_levels_utils.c.i

src/utils/quant_levels_utils.s: src/utils/quant_levels_utils.c.s

.PHONY : src/utils/quant_levels_utils.s

# target to generate assembly for a file
src/utils/quant_levels_utils.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_utils.c.s
.PHONY : src/utils/quant_levels_utils.c.s

src/utils/random_utils.obj: src/utils/random_utils.c.obj

.PHONY : src/utils/random_utils.obj

# target to build an object file
src/utils/random_utils.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/random_utils.c.obj
.PHONY : src/utils/random_utils.c.obj

src/utils/random_utils.i: src/utils/random_utils.c.i

.PHONY : src/utils/random_utils.i

# target to preprocess a source file
src/utils/random_utils.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/random_utils.c.i
.PHONY : src/utils/random_utils.c.i

src/utils/random_utils.s: src/utils/random_utils.c.s

.PHONY : src/utils/random_utils.s

# target to generate assembly for a file
src/utils/random_utils.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/random_utils.c.s
.PHONY : src/utils/random_utils.c.s

src/utils/rescaler_utils.obj: src/utils/rescaler_utils.c.obj

.PHONY : src/utils/rescaler_utils.obj

# target to build an object file
src/utils/rescaler_utils.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/rescaler_utils.c.obj
.PHONY : src/utils/rescaler_utils.c.obj

src/utils/rescaler_utils.i: src/utils/rescaler_utils.c.i

.PHONY : src/utils/rescaler_utils.i

# target to preprocess a source file
src/utils/rescaler_utils.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/rescaler_utils.c.i
.PHONY : src/utils/rescaler_utils.c.i

src/utils/rescaler_utils.s: src/utils/rescaler_utils.c.s

.PHONY : src/utils/rescaler_utils.s

# target to generate assembly for a file
src/utils/rescaler_utils.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/rescaler_utils.c.s
.PHONY : src/utils/rescaler_utils.c.s

src/utils/thread_utils.obj: src/utils/thread_utils.c.obj

.PHONY : src/utils/thread_utils.obj

# target to build an object file
src/utils/thread_utils.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/thread_utils.c.obj
.PHONY : src/utils/thread_utils.c.obj

src/utils/thread_utils.i: src/utils/thread_utils.c.i

.PHONY : src/utils/thread_utils.i

# target to preprocess a source file
src/utils/thread_utils.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/thread_utils.c.i
.PHONY : src/utils/thread_utils.c.i

src/utils/thread_utils.s: src/utils/thread_utils.c.s

.PHONY : src/utils/thread_utils.s

# target to generate assembly for a file
src/utils/thread_utils.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/thread_utils.c.s
.PHONY : src/utils/thread_utils.c.s

src/utils/utils.obj: src/utils/utils.c.obj

.PHONY : src/utils/utils.obj

# target to build an object file
src/utils/utils.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/utils.c.obj
.PHONY : src/utils/utils.c.obj

src/utils/utils.i: src/utils/utils.c.i

.PHONY : src/utils/utils.i

# target to preprocess a source file
src/utils/utils.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/utils.c.i
.PHONY : src/utils/utils.c.i

src/utils/utils.s: src/utils/utils.c.s

.PHONY : src/utils/utils.s

# target to generate assembly for a file
src/utils/utils.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/utils.c.s
.PHONY : src/utils/utils.c.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... list_install_components
	@echo ... libwebp
	@echo ... package
	@echo ... package_source
	@echo ... rebuild_cache
	@echo ... test
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... install/strip
	@echo ... src/dec/alpha_dec.obj
	@echo ... src/dec/alpha_dec.i
	@echo ... src/dec/alpha_dec.s
	@echo ... src/dec/buffer_dec.obj
	@echo ... src/dec/buffer_dec.i
	@echo ... src/dec/buffer_dec.s
	@echo ... src/dec/frame_dec.obj
	@echo ... src/dec/frame_dec.i
	@echo ... src/dec/frame_dec.s
	@echo ... src/dec/idec_dec.obj
	@echo ... src/dec/idec_dec.i
	@echo ... src/dec/idec_dec.s
	@echo ... src/dec/io_dec.obj
	@echo ... src/dec/io_dec.i
	@echo ... src/dec/io_dec.s
	@echo ... src/dec/quant_dec.obj
	@echo ... src/dec/quant_dec.i
	@echo ... src/dec/quant_dec.s
	@echo ... src/dec/tree_dec.obj
	@echo ... src/dec/tree_dec.i
	@echo ... src/dec/tree_dec.s
	@echo ... src/dec/vp8_dec.obj
	@echo ... src/dec/vp8_dec.i
	@echo ... src/dec/vp8_dec.s
	@echo ... src/dec/vp8l_dec.obj
	@echo ... src/dec/vp8l_dec.i
	@echo ... src/dec/vp8l_dec.s
	@echo ... src/dec/webp_dec.obj
	@echo ... src/dec/webp_dec.i
	@echo ... src/dec/webp_dec.s
	@echo ... src/demux/anim_decode.obj
	@echo ... src/demux/anim_decode.i
	@echo ... src/demux/anim_decode.s
	@echo ... src/demux/demux.obj
	@echo ... src/demux/demux.i
	@echo ... src/demux/demux.s
	@echo ... src/dsp/alpha_processing.obj
	@echo ... src/dsp/alpha_processing.i
	@echo ... src/dsp/alpha_processing.s
	@echo ... src/dsp/alpha_processing_mips_dsp_r2.obj
	@echo ... src/dsp/alpha_processing_mips_dsp_r2.i
	@echo ... src/dsp/alpha_processing_mips_dsp_r2.s
	@echo ... src/dsp/alpha_processing_neon.obj
	@echo ... src/dsp/alpha_processing_neon.i
	@echo ... src/dsp/alpha_processing_neon.s
	@echo ... src/dsp/alpha_processing_sse2.obj
	@echo ... src/dsp/alpha_processing_sse2.i
	@echo ... src/dsp/alpha_processing_sse2.s
	@echo ... src/dsp/alpha_processing_sse41.obj
	@echo ... src/dsp/alpha_processing_sse41.i
	@echo ... src/dsp/alpha_processing_sse41.s
	@echo ... src/dsp/cost.obj
	@echo ... src/dsp/cost.i
	@echo ... src/dsp/cost.s
	@echo ... src/dsp/cost_mips32.obj
	@echo ... src/dsp/cost_mips32.i
	@echo ... src/dsp/cost_mips32.s
	@echo ... src/dsp/cost_mips_dsp_r2.obj
	@echo ... src/dsp/cost_mips_dsp_r2.i
	@echo ... src/dsp/cost_mips_dsp_r2.s
	@echo ... src/dsp/cost_neon.obj
	@echo ... src/dsp/cost_neon.i
	@echo ... src/dsp/cost_neon.s
	@echo ... src/dsp/cost_sse2.obj
	@echo ... src/dsp/cost_sse2.i
	@echo ... src/dsp/cost_sse2.s
	@echo ... src/dsp/cpu.obj
	@echo ... src/dsp/cpu.i
	@echo ... src/dsp/cpu.s
	@echo ... src/dsp/dec.obj
	@echo ... src/dsp/dec.i
	@echo ... src/dsp/dec.s
	@echo ... src/dsp/dec_clip_tables.obj
	@echo ... src/dsp/dec_clip_tables.i
	@echo ... src/dsp/dec_clip_tables.s
	@echo ... src/dsp/dec_mips32.obj
	@echo ... src/dsp/dec_mips32.i
	@echo ... src/dsp/dec_mips32.s
	@echo ... src/dsp/dec_mips_dsp_r2.obj
	@echo ... src/dsp/dec_mips_dsp_r2.i
	@echo ... src/dsp/dec_mips_dsp_r2.s
	@echo ... src/dsp/dec_msa.obj
	@echo ... src/dsp/dec_msa.i
	@echo ... src/dsp/dec_msa.s
	@echo ... src/dsp/dec_neon.obj
	@echo ... src/dsp/dec_neon.i
	@echo ... src/dsp/dec_neon.s
	@echo ... src/dsp/dec_sse2.obj
	@echo ... src/dsp/dec_sse2.i
	@echo ... src/dsp/dec_sse2.s
	@echo ... src/dsp/dec_sse41.obj
	@echo ... src/dsp/dec_sse41.i
	@echo ... src/dsp/dec_sse41.s
	@echo ... src/dsp/enc.obj
	@echo ... src/dsp/enc.i
	@echo ... src/dsp/enc.s
	@echo ... src/dsp/enc_mips32.obj
	@echo ... src/dsp/enc_mips32.i
	@echo ... src/dsp/enc_mips32.s
	@echo ... src/dsp/enc_mips_dsp_r2.obj
	@echo ... src/dsp/enc_mips_dsp_r2.i
	@echo ... src/dsp/enc_mips_dsp_r2.s
	@echo ... src/dsp/enc_msa.obj
	@echo ... src/dsp/enc_msa.i
	@echo ... src/dsp/enc_msa.s
	@echo ... src/dsp/enc_neon.obj
	@echo ... src/dsp/enc_neon.i
	@echo ... src/dsp/enc_neon.s
	@echo ... src/dsp/enc_sse2.obj
	@echo ... src/dsp/enc_sse2.i
	@echo ... src/dsp/enc_sse2.s
	@echo ... src/dsp/enc_sse41.obj
	@echo ... src/dsp/enc_sse41.i
	@echo ... src/dsp/enc_sse41.s
	@echo ... src/dsp/filters.obj
	@echo ... src/dsp/filters.i
	@echo ... src/dsp/filters.s
	@echo ... src/dsp/filters_mips_dsp_r2.obj
	@echo ... src/dsp/filters_mips_dsp_r2.i
	@echo ... src/dsp/filters_mips_dsp_r2.s
	@echo ... src/dsp/filters_msa.obj
	@echo ... src/dsp/filters_msa.i
	@echo ... src/dsp/filters_msa.s
	@echo ... src/dsp/filters_neon.obj
	@echo ... src/dsp/filters_neon.i
	@echo ... src/dsp/filters_neon.s
	@echo ... src/dsp/filters_sse2.obj
	@echo ... src/dsp/filters_sse2.i
	@echo ... src/dsp/filters_sse2.s
	@echo ... src/dsp/lossless.obj
	@echo ... src/dsp/lossless.i
	@echo ... src/dsp/lossless.s
	@echo ... src/dsp/lossless_enc.obj
	@echo ... src/dsp/lossless_enc.i
	@echo ... src/dsp/lossless_enc.s
	@echo ... src/dsp/lossless_enc_mips32.obj
	@echo ... src/dsp/lossless_enc_mips32.i
	@echo ... src/dsp/lossless_enc_mips32.s
	@echo ... src/dsp/lossless_enc_mips_dsp_r2.obj
	@echo ... src/dsp/lossless_enc_mips_dsp_r2.i
	@echo ... src/dsp/lossless_enc_mips_dsp_r2.s
	@echo ... src/dsp/lossless_enc_msa.obj
	@echo ... src/dsp/lossless_enc_msa.i
	@echo ... src/dsp/lossless_enc_msa.s
	@echo ... src/dsp/lossless_enc_neon.obj
	@echo ... src/dsp/lossless_enc_neon.i
	@echo ... src/dsp/lossless_enc_neon.s
	@echo ... src/dsp/lossless_enc_sse2.obj
	@echo ... src/dsp/lossless_enc_sse2.i
	@echo ... src/dsp/lossless_enc_sse2.s
	@echo ... src/dsp/lossless_enc_sse41.obj
	@echo ... src/dsp/lossless_enc_sse41.i
	@echo ... src/dsp/lossless_enc_sse41.s
	@echo ... src/dsp/lossless_mips_dsp_r2.obj
	@echo ... src/dsp/lossless_mips_dsp_r2.i
	@echo ... src/dsp/lossless_mips_dsp_r2.s
	@echo ... src/dsp/lossless_msa.obj
	@echo ... src/dsp/lossless_msa.i
	@echo ... src/dsp/lossless_msa.s
	@echo ... src/dsp/lossless_neon.obj
	@echo ... src/dsp/lossless_neon.i
	@echo ... src/dsp/lossless_neon.s
	@echo ... src/dsp/lossless_sse2.obj
	@echo ... src/dsp/lossless_sse2.i
	@echo ... src/dsp/lossless_sse2.s
	@echo ... src/dsp/rescaler.obj
	@echo ... src/dsp/rescaler.i
	@echo ... src/dsp/rescaler.s
	@echo ... src/dsp/rescaler_mips32.obj
	@echo ... src/dsp/rescaler_mips32.i
	@echo ... src/dsp/rescaler_mips32.s
	@echo ... src/dsp/rescaler_mips_dsp_r2.obj
	@echo ... src/dsp/rescaler_mips_dsp_r2.i
	@echo ... src/dsp/rescaler_mips_dsp_r2.s
	@echo ... src/dsp/rescaler_msa.obj
	@echo ... src/dsp/rescaler_msa.i
	@echo ... src/dsp/rescaler_msa.s
	@echo ... src/dsp/rescaler_neon.obj
	@echo ... src/dsp/rescaler_neon.i
	@echo ... src/dsp/rescaler_neon.s
	@echo ... src/dsp/rescaler_sse2.obj
	@echo ... src/dsp/rescaler_sse2.i
	@echo ... src/dsp/rescaler_sse2.s
	@echo ... src/dsp/ssim.obj
	@echo ... src/dsp/ssim.i
	@echo ... src/dsp/ssim.s
	@echo ... src/dsp/ssim_sse2.obj
	@echo ... src/dsp/ssim_sse2.i
	@echo ... src/dsp/ssim_sse2.s
	@echo ... src/dsp/upsampling.obj
	@echo ... src/dsp/upsampling.i
	@echo ... src/dsp/upsampling.s
	@echo ... src/dsp/upsampling_mips_dsp_r2.obj
	@echo ... src/dsp/upsampling_mips_dsp_r2.i
	@echo ... src/dsp/upsampling_mips_dsp_r2.s
	@echo ... src/dsp/upsampling_msa.obj
	@echo ... src/dsp/upsampling_msa.i
	@echo ... src/dsp/upsampling_msa.s
	@echo ... src/dsp/upsampling_neon.obj
	@echo ... src/dsp/upsampling_neon.i
	@echo ... src/dsp/upsampling_neon.s
	@echo ... src/dsp/upsampling_sse2.obj
	@echo ... src/dsp/upsampling_sse2.i
	@echo ... src/dsp/upsampling_sse2.s
	@echo ... src/dsp/upsampling_sse41.obj
	@echo ... src/dsp/upsampling_sse41.i
	@echo ... src/dsp/upsampling_sse41.s
	@echo ... src/dsp/yuv.obj
	@echo ... src/dsp/yuv.i
	@echo ... src/dsp/yuv.s
	@echo ... src/dsp/yuv_mips32.obj
	@echo ... src/dsp/yuv_mips32.i
	@echo ... src/dsp/yuv_mips32.s
	@echo ... src/dsp/yuv_mips_dsp_r2.obj
	@echo ... src/dsp/yuv_mips_dsp_r2.i
	@echo ... src/dsp/yuv_mips_dsp_r2.s
	@echo ... src/dsp/yuv_neon.obj
	@echo ... src/dsp/yuv_neon.i
	@echo ... src/dsp/yuv_neon.s
	@echo ... src/dsp/yuv_sse2.obj
	@echo ... src/dsp/yuv_sse2.i
	@echo ... src/dsp/yuv_sse2.s
	@echo ... src/dsp/yuv_sse41.obj
	@echo ... src/dsp/yuv_sse41.i
	@echo ... src/dsp/yuv_sse41.s
	@echo ... src/enc/alpha_enc.obj
	@echo ... src/enc/alpha_enc.i
	@echo ... src/enc/alpha_enc.s
	@echo ... src/enc/analysis_enc.obj
	@echo ... src/enc/analysis_enc.i
	@echo ... src/enc/analysis_enc.s
	@echo ... src/enc/backward_references_cost_enc.obj
	@echo ... src/enc/backward_references_cost_enc.i
	@echo ... src/enc/backward_references_cost_enc.s
	@echo ... src/enc/backward_references_enc.obj
	@echo ... src/enc/backward_references_enc.i
	@echo ... src/enc/backward_references_enc.s
	@echo ... src/enc/config_enc.obj
	@echo ... src/enc/config_enc.i
	@echo ... src/enc/config_enc.s
	@echo ... src/enc/cost_enc.obj
	@echo ... src/enc/cost_enc.i
	@echo ... src/enc/cost_enc.s
	@echo ... src/enc/filter_enc.obj
	@echo ... src/enc/filter_enc.i
	@echo ... src/enc/filter_enc.s
	@echo ... src/enc/frame_enc.obj
	@echo ... src/enc/frame_enc.i
	@echo ... src/enc/frame_enc.s
	@echo ... src/enc/histogram_enc.obj
	@echo ... src/enc/histogram_enc.i
	@echo ... src/enc/histogram_enc.s
	@echo ... src/enc/iterator_enc.obj
	@echo ... src/enc/iterator_enc.i
	@echo ... src/enc/iterator_enc.s
	@echo ... src/enc/near_lossless_enc.obj
	@echo ... src/enc/near_lossless_enc.i
	@echo ... src/enc/near_lossless_enc.s
	@echo ... src/enc/picture_csp_enc.obj
	@echo ... src/enc/picture_csp_enc.i
	@echo ... src/enc/picture_csp_enc.s
	@echo ... src/enc/picture_enc.obj
	@echo ... src/enc/picture_enc.i
	@echo ... src/enc/picture_enc.s
	@echo ... src/enc/picture_psnr_enc.obj
	@echo ... src/enc/picture_psnr_enc.i
	@echo ... src/enc/picture_psnr_enc.s
	@echo ... src/enc/picture_rescale_enc.obj
	@echo ... src/enc/picture_rescale_enc.i
	@echo ... src/enc/picture_rescale_enc.s
	@echo ... src/enc/picture_tools_enc.obj
	@echo ... src/enc/picture_tools_enc.i
	@echo ... src/enc/picture_tools_enc.s
	@echo ... src/enc/predictor_enc.obj
	@echo ... src/enc/predictor_enc.i
	@echo ... src/enc/predictor_enc.s
	@echo ... src/enc/quant_enc.obj
	@echo ... src/enc/quant_enc.i
	@echo ... src/enc/quant_enc.s
	@echo ... src/enc/syntax_enc.obj
	@echo ... src/enc/syntax_enc.i
	@echo ... src/enc/syntax_enc.s
	@echo ... src/enc/token_enc.obj
	@echo ... src/enc/token_enc.i
	@echo ... src/enc/token_enc.s
	@echo ... src/enc/tree_enc.obj
	@echo ... src/enc/tree_enc.i
	@echo ... src/enc/tree_enc.s
	@echo ... src/enc/vp8l_enc.obj
	@echo ... src/enc/vp8l_enc.i
	@echo ... src/enc/vp8l_enc.s
	@echo ... src/enc/webp_enc.obj
	@echo ... src/enc/webp_enc.i
	@echo ... src/enc/webp_enc.s
	@echo ... src/mux/anim_encode.obj
	@echo ... src/mux/anim_encode.i
	@echo ... src/mux/anim_encode.s
	@echo ... src/mux/muxedit.obj
	@echo ... src/mux/muxedit.i
	@echo ... src/mux/muxedit.s
	@echo ... src/mux/muxinternal.obj
	@echo ... src/mux/muxinternal.i
	@echo ... src/mux/muxinternal.s
	@echo ... src/mux/muxread.obj
	@echo ... src/mux/muxread.i
	@echo ... src/mux/muxread.s
	@echo ... src/utils/bit_reader_utils.obj
	@echo ... src/utils/bit_reader_utils.i
	@echo ... src/utils/bit_reader_utils.s
	@echo ... src/utils/bit_writer_utils.obj
	@echo ... src/utils/bit_writer_utils.i
	@echo ... src/utils/bit_writer_utils.s
	@echo ... src/utils/color_cache_utils.obj
	@echo ... src/utils/color_cache_utils.i
	@echo ... src/utils/color_cache_utils.s
	@echo ... src/utils/filters_utils.obj
	@echo ... src/utils/filters_utils.i
	@echo ... src/utils/filters_utils.s
	@echo ... src/utils/huffman_encode_utils.obj
	@echo ... src/utils/huffman_encode_utils.i
	@echo ... src/utils/huffman_encode_utils.s
	@echo ... src/utils/huffman_utils.obj
	@echo ... src/utils/huffman_utils.i
	@echo ... src/utils/huffman_utils.s
	@echo ... src/utils/quant_levels_dec_utils.obj
	@echo ... src/utils/quant_levels_dec_utils.i
	@echo ... src/utils/quant_levels_dec_utils.s
	@echo ... src/utils/quant_levels_utils.obj
	@echo ... src/utils/quant_levels_utils.i
	@echo ... src/utils/quant_levels_utils.s
	@echo ... src/utils/random_utils.obj
	@echo ... src/utils/random_utils.i
	@echo ... src/utils/random_utils.s
	@echo ... src/utils/rescaler_utils.obj
	@echo ... src/utils/rescaler_utils.i
	@echo ... src/utils/rescaler_utils.s
	@echo ... src/utils/thread_utils.obj
	@echo ... src/utils/thread_utils.i
	@echo ... src/utils/thread_utils.s
	@echo ... src/utils/utils.obj
	@echo ... src/utils/utils.i
	@echo ... src/utils/utils.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

