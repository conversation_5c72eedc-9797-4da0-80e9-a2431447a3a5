# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "C"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_C
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/alpha_dec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/alpha_dec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/buffer_dec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/buffer_dec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/frame_dec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/frame_dec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/idec_dec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/idec_dec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/io_dec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/io_dec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/quant_dec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/quant_dec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/tree_dec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/tree_dec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/vp8_dec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8_dec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/vp8l_dec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8l_dec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/webp_dec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/webp_dec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/demux/anim_decode.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/anim_decode.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/demux/demux.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/demux.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/alpha_processing.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/alpha_processing_mips_dsp_r2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_mips_dsp_r2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/alpha_processing_neon.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_neon.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/alpha_processing_sse2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/alpha_processing_sse41.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse41.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/cost.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/cost_mips32.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips32.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/cost_mips_dsp_r2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips_dsp_r2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/cost_neon.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_neon.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/cost_sse2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_sse2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/cpu.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cpu.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec_clip_tables.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_clip_tables.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec_mips32.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips32.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec_mips_dsp_r2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips_dsp_r2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec_msa.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_msa.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec_neon.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_neon.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec_sse2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec_sse41.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse41.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/enc_mips32.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips32.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/enc_mips_dsp_r2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips_dsp_r2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/enc_msa.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_msa.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/enc_neon.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_neon.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/enc_sse2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/enc_sse41.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse41.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/filters.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/filters_mips_dsp_r2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_mips_dsp_r2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/filters_msa.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_msa.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/filters_neon.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_neon.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/filters_sse2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_sse2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_enc_mips32.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips32.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_enc_mips_dsp_r2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips_dsp_r2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_enc_msa.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_msa.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_enc_neon.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_neon.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_enc_sse2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_enc_sse41.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse41.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_mips_dsp_r2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_mips_dsp_r2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_msa.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_msa.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_neon.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_neon.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_sse2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_sse2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/rescaler.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/rescaler_mips32.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips32.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/rescaler_mips_dsp_r2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips_dsp_r2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/rescaler_msa.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_msa.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/rescaler_neon.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_neon.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/rescaler_sse2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_sse2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/ssim.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/ssim_sse2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim_sse2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/upsampling.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/upsampling_mips_dsp_r2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_mips_dsp_r2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/upsampling_msa.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_msa.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/upsampling_neon.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_neon.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/upsampling_sse2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/upsampling_sse41.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse41.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/yuv.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/yuv_mips32.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips32.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/yuv_mips_dsp_r2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips_dsp_r2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/yuv_neon.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_neon.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/yuv_sse2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/yuv_sse41.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse41.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/alpha_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/alpha_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/analysis_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/analysis_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/backward_references_cost_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_cost_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/backward_references_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/config_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/config_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/cost_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/cost_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/filter_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/filter_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/frame_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/frame_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/histogram_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/histogram_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/iterator_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/iterator_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/near_lossless_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/near_lossless_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/picture_csp_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_csp_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/picture_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/picture_psnr_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_psnr_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/picture_rescale_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_rescale_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/picture_tools_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_tools_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/predictor_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/predictor_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/quant_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/quant_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/syntax_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/syntax_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/token_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/token_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/tree_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/tree_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/vp8l_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/vp8l_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/webp_enc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/webp_enc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/mux/anim_encode.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/anim_encode.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/mux/muxedit.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxedit.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/mux/muxinternal.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxinternal.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/mux/muxread.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxread.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/bit_reader_utils.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_reader_utils.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/bit_writer_utils.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_writer_utils.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/color_cache_utils.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/color_cache_utils.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/filters_utils.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/filters_utils.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/huffman_encode_utils.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_encode_utils.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/huffman_utils.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_utils.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/quant_levels_dec_utils.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_dec_utils.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/quant_levels_utils.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_utils.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/random_utils.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/random_utils.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/rescaler_utils.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/rescaler_utils.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/thread_utils.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/thread_utils.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/utils.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/utils.c.obj"
  )
set(CMAKE_C_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_C
  "WEBP_USE_THREAD"
  "_WIN32_WINNT=0x0601"
  )

# The include file search paths:
set(CMAKE_C_TARGET_INCLUDE_PATH
  "D:/unet/opencv/opencv/sources/3rdparty/libwebp"
  "."
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
