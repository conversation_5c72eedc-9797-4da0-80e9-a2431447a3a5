# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

# Include any dependencies generated for this target.
include 3rdparty/libwebp/CMakeFiles/libwebp.dir/depend.make

# Include the progress variables for this target.
include 3rdparty/libwebp/CMakeFiles/libwebp.dir/progress.make

# Include the compile flags for this target's objects.
include 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/alpha_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/alpha_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/alpha_dec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/alpha_dec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/alpha_dec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dec\alpha_dec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\alpha_dec.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/alpha_dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dec/alpha_dec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\alpha_dec.c > CMakeFiles\libwebp.dir\src\dec\alpha_dec.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/alpha_dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dec/alpha_dec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\alpha_dec.c -o CMakeFiles\libwebp.dir\src\dec\alpha_dec.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/buffer_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/buffer_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/buffer_dec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/buffer_dec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/buffer_dec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dec\buffer_dec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\buffer_dec.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/buffer_dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dec/buffer_dec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\buffer_dec.c > CMakeFiles\libwebp.dir\src\dec\buffer_dec.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/buffer_dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dec/buffer_dec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\buffer_dec.c -o CMakeFiles\libwebp.dir\src\dec\buffer_dec.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/frame_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/frame_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/frame_dec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/frame_dec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/frame_dec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dec\frame_dec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\frame_dec.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/frame_dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dec/frame_dec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\frame_dec.c > CMakeFiles\libwebp.dir\src\dec\frame_dec.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/frame_dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dec/frame_dec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\frame_dec.c -o CMakeFiles\libwebp.dir\src\dec\frame_dec.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/idec_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/idec_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/idec_dec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/idec_dec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/idec_dec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dec\idec_dec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\idec_dec.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/idec_dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dec/idec_dec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\idec_dec.c > CMakeFiles\libwebp.dir\src\dec\idec_dec.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/idec_dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dec/idec_dec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\idec_dec.c -o CMakeFiles\libwebp.dir\src\dec\idec_dec.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/io_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/io_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/io_dec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/io_dec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/io_dec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dec\io_dec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\io_dec.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/io_dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dec/io_dec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\io_dec.c > CMakeFiles\libwebp.dir\src\dec\io_dec.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/io_dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dec/io_dec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\io_dec.c -o CMakeFiles\libwebp.dir\src\dec\io_dec.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/quant_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/quant_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/quant_dec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/quant_dec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/quant_dec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dec\quant_dec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\quant_dec.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/quant_dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dec/quant_dec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\quant_dec.c > CMakeFiles\libwebp.dir\src\dec\quant_dec.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/quant_dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dec/quant_dec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\quant_dec.c -o CMakeFiles\libwebp.dir\src\dec\quant_dec.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/tree_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/tree_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/tree_dec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/tree_dec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/tree_dec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dec\tree_dec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\tree_dec.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/tree_dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dec/tree_dec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\tree_dec.c > CMakeFiles\libwebp.dir\src\dec\tree_dec.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/tree_dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dec/tree_dec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\tree_dec.c -o CMakeFiles\libwebp.dir\src\dec\tree_dec.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8_dec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/vp8_dec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8_dec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dec\vp8_dec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\vp8_dec.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8_dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dec/vp8_dec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\vp8_dec.c > CMakeFiles\libwebp.dir\src\dec\vp8_dec.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8_dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dec/vp8_dec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\vp8_dec.c -o CMakeFiles\libwebp.dir\src\dec\vp8_dec.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8l_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8l_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8l_dec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/vp8l_dec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8l_dec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dec\vp8l_dec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\vp8l_dec.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8l_dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dec/vp8l_dec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\vp8l_dec.c > CMakeFiles\libwebp.dir\src\dec\vp8l_dec.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8l_dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dec/vp8l_dec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\vp8l_dec.c -o CMakeFiles\libwebp.dir\src\dec\vp8l_dec.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/webp_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/webp_dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/webp_dec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dec/webp_dec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/webp_dec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dec\webp_dec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\webp_dec.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/webp_dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dec/webp_dec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\webp_dec.c > CMakeFiles\libwebp.dir\src\dec\webp_dec.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/webp_dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dec/webp_dec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dec\webp_dec.c -o CMakeFiles\libwebp.dir\src\dec\webp_dec.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/anim_decode.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/anim_decode.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/anim_decode.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/demux/anim_decode.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/anim_decode.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\demux\anim_decode.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\demux\anim_decode.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/anim_decode.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/demux/anim_decode.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\demux\anim_decode.c > CMakeFiles\libwebp.dir\src\demux\anim_decode.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/anim_decode.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/demux/anim_decode.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\demux\anim_decode.c -o CMakeFiles\libwebp.dir\src\demux\anim_decode.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/demux.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/demux.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/demux.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/demux/demux.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/demux.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\demux\demux.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\demux\demux.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/demux.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/demux/demux.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\demux\demux.c > CMakeFiles\libwebp.dir\src\demux\demux.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/demux.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/demux/demux.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\demux\demux.c -o CMakeFiles\libwebp.dir\src\demux\demux.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/alpha_processing.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\alpha_processing.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\alpha_processing.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/alpha_processing.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\alpha_processing.c > CMakeFiles\libwebp.dir\src\dsp\alpha_processing.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/alpha_processing.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\alpha_processing.c -o CMakeFiles\libwebp.dir\src\dsp\alpha_processing.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_mips_dsp_r2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/alpha_processing_mips_dsp_r2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_mips_dsp_r2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\alpha_processing_mips_dsp_r2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\alpha_processing_mips_dsp_r2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_mips_dsp_r2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/alpha_processing_mips_dsp_r2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\alpha_processing_mips_dsp_r2.c > CMakeFiles\libwebp.dir\src\dsp\alpha_processing_mips_dsp_r2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_mips_dsp_r2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/alpha_processing_mips_dsp_r2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\alpha_processing_mips_dsp_r2.c -o CMakeFiles\libwebp.dir\src\dsp\alpha_processing_mips_dsp_r2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_neon.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/alpha_processing_neon.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_neon.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\alpha_processing_neon.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\alpha_processing_neon.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_neon.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/alpha_processing_neon.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\alpha_processing_neon.c > CMakeFiles\libwebp.dir\src\dsp\alpha_processing_neon.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_neon.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/alpha_processing_neon.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\alpha_processing_neon.c -o CMakeFiles\libwebp.dir\src\dsp\alpha_processing_neon.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/alpha_processing_sse2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\alpha_processing_sse2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\alpha_processing_sse2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\alpha_processing_sse2.c > CMakeFiles\libwebp.dir\src\dsp\alpha_processing_sse2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\alpha_processing_sse2.c -o CMakeFiles\libwebp.dir\src\dsp\alpha_processing_sse2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse41.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse41.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse41.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/alpha_processing_sse41.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse41.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\alpha_processing_sse41.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\alpha_processing_sse41.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse41.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse41.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\alpha_processing_sse41.c > CMakeFiles\libwebp.dir\src\dsp\alpha_processing_sse41.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse41.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse41.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\alpha_processing_sse41.c -o CMakeFiles\libwebp.dir\src\dsp\alpha_processing_sse41.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/cost.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\cost.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cost.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/cost.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cost.c > CMakeFiles\libwebp.dir\src\dsp\cost.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/cost.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cost.c -o CMakeFiles\libwebp.dir\src\dsp\cost.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips32.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips32.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips32.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/cost_mips32.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips32.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\cost_mips32.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cost_mips32.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips32.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/cost_mips32.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cost_mips32.c > CMakeFiles\libwebp.dir\src\dsp\cost_mips32.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips32.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/cost_mips32.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cost_mips32.c -o CMakeFiles\libwebp.dir\src\dsp\cost_mips32.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips_dsp_r2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/cost_mips_dsp_r2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips_dsp_r2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\cost_mips_dsp_r2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cost_mips_dsp_r2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips_dsp_r2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/cost_mips_dsp_r2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cost_mips_dsp_r2.c > CMakeFiles\libwebp.dir\src\dsp\cost_mips_dsp_r2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips_dsp_r2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/cost_mips_dsp_r2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cost_mips_dsp_r2.c -o CMakeFiles\libwebp.dir\src\dsp\cost_mips_dsp_r2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_neon.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/cost_neon.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_neon.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\cost_neon.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cost_neon.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_neon.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/cost_neon.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cost_neon.c > CMakeFiles\libwebp.dir\src\dsp\cost_neon.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_neon.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/cost_neon.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cost_neon.c -o CMakeFiles\libwebp.dir\src\dsp\cost_neon.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_sse2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/cost_sse2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_sse2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\cost_sse2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cost_sse2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_sse2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/cost_sse2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cost_sse2.c > CMakeFiles\libwebp.dir\src\dsp\cost_sse2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_sse2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/cost_sse2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cost_sse2.c -o CMakeFiles\libwebp.dir\src\dsp\cost_sse2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cpu.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cpu.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cpu.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/cpu.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cpu.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\cpu.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cpu.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cpu.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/cpu.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cpu.c > CMakeFiles\libwebp.dir\src\dsp\cpu.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cpu.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/cpu.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\cpu.c -o CMakeFiles\libwebp.dir\src\dsp\cpu.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\dec.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/dec.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec.c > CMakeFiles\libwebp.dir\src\dsp\dec.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/dec.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec.c -o CMakeFiles\libwebp.dir\src\dsp\dec.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_clip_tables.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_clip_tables.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_clip_tables.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec_clip_tables.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_clip_tables.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\dec_clip_tables.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_clip_tables.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_clip_tables.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/dec_clip_tables.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_clip_tables.c > CMakeFiles\libwebp.dir\src\dsp\dec_clip_tables.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_clip_tables.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/dec_clip_tables.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_clip_tables.c -o CMakeFiles\libwebp.dir\src\dsp\dec_clip_tables.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips32.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips32.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips32.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec_mips32.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips32.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\dec_mips32.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_mips32.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips32.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/dec_mips32.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_mips32.c > CMakeFiles\libwebp.dir\src\dsp\dec_mips32.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips32.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/dec_mips32.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_mips32.c -o CMakeFiles\libwebp.dir\src\dsp\dec_mips32.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips_dsp_r2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec_mips_dsp_r2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips_dsp_r2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\dec_mips_dsp_r2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_mips_dsp_r2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips_dsp_r2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/dec_mips_dsp_r2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_mips_dsp_r2.c > CMakeFiles\libwebp.dir\src\dsp\dec_mips_dsp_r2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips_dsp_r2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/dec_mips_dsp_r2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_mips_dsp_r2.c -o CMakeFiles\libwebp.dir\src\dsp\dec_mips_dsp_r2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_msa.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_msa.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_msa.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec_msa.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_msa.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\dec_msa.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_msa.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_msa.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/dec_msa.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_msa.c > CMakeFiles\libwebp.dir\src\dsp\dec_msa.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_msa.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/dec_msa.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_msa.c -o CMakeFiles\libwebp.dir\src\dsp\dec_msa.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_neon.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec_neon.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_neon.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\dec_neon.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_neon.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_neon.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/dec_neon.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_neon.c > CMakeFiles\libwebp.dir\src\dsp\dec_neon.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_neon.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/dec_neon.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_neon.c -o CMakeFiles\libwebp.dir\src\dsp\dec_neon.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec_sse2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\dec_sse2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_sse2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/dec_sse2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_sse2.c > CMakeFiles\libwebp.dir\src\dsp\dec_sse2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/dec_sse2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_sse2.c -o CMakeFiles\libwebp.dir\src\dsp\dec_sse2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse41.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse41.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse41.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/dec_sse41.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse41.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\dec_sse41.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_sse41.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse41.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/dec_sse41.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_sse41.c > CMakeFiles\libwebp.dir\src\dsp\dec_sse41.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse41.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/dec_sse41.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\dec_sse41.c -o CMakeFiles\libwebp.dir\src\dsp\dec_sse41.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc.c > CMakeFiles\libwebp.dir\src\dsp\enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc.c -o CMakeFiles\libwebp.dir\src\dsp\enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips32.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips32.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips32.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/enc_mips32.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips32.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\enc_mips32.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_mips32.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips32.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/enc_mips32.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_mips32.c > CMakeFiles\libwebp.dir\src\dsp\enc_mips32.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips32.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/enc_mips32.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_mips32.c -o CMakeFiles\libwebp.dir\src\dsp\enc_mips32.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips_dsp_r2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/enc_mips_dsp_r2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips_dsp_r2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\enc_mips_dsp_r2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_mips_dsp_r2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips_dsp_r2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/enc_mips_dsp_r2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_mips_dsp_r2.c > CMakeFiles\libwebp.dir\src\dsp\enc_mips_dsp_r2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips_dsp_r2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/enc_mips_dsp_r2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_mips_dsp_r2.c -o CMakeFiles\libwebp.dir\src\dsp\enc_mips_dsp_r2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_msa.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_msa.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_msa.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/enc_msa.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_msa.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\enc_msa.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_msa.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_msa.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/enc_msa.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_msa.c > CMakeFiles\libwebp.dir\src\dsp\enc_msa.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_msa.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/enc_msa.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_msa.c -o CMakeFiles\libwebp.dir\src\dsp\enc_msa.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_neon.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/enc_neon.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_neon.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\enc_neon.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_neon.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_neon.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/enc_neon.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_neon.c > CMakeFiles\libwebp.dir\src\dsp\enc_neon.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_neon.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/enc_neon.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_neon.c -o CMakeFiles\libwebp.dir\src\dsp\enc_neon.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/enc_sse2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\enc_sse2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_sse2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/enc_sse2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_sse2.c > CMakeFiles\libwebp.dir\src\dsp\enc_sse2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/enc_sse2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_sse2.c -o CMakeFiles\libwebp.dir\src\dsp\enc_sse2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse41.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse41.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse41.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/enc_sse41.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse41.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\enc_sse41.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_sse41.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse41.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/enc_sse41.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_sse41.c > CMakeFiles\libwebp.dir\src\dsp\enc_sse41.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse41.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/enc_sse41.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\enc_sse41.c -o CMakeFiles\libwebp.dir\src\dsp\enc_sse41.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/filters.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\filters.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\filters.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/filters.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\filters.c > CMakeFiles\libwebp.dir\src\dsp\filters.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/filters.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\filters.c -o CMakeFiles\libwebp.dir\src\dsp\filters.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_mips_dsp_r2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/filters_mips_dsp_r2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_mips_dsp_r2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\filters_mips_dsp_r2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\filters_mips_dsp_r2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_mips_dsp_r2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/filters_mips_dsp_r2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\filters_mips_dsp_r2.c > CMakeFiles\libwebp.dir\src\dsp\filters_mips_dsp_r2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_mips_dsp_r2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/filters_mips_dsp_r2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\filters_mips_dsp_r2.c -o CMakeFiles\libwebp.dir\src\dsp\filters_mips_dsp_r2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_msa.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_msa.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_msa.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/filters_msa.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_msa.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\filters_msa.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\filters_msa.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_msa.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/filters_msa.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\filters_msa.c > CMakeFiles\libwebp.dir\src\dsp\filters_msa.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_msa.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/filters_msa.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\filters_msa.c -o CMakeFiles\libwebp.dir\src\dsp\filters_msa.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_neon.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/filters_neon.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_neon.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\filters_neon.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\filters_neon.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_neon.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/filters_neon.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\filters_neon.c > CMakeFiles\libwebp.dir\src\dsp\filters_neon.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_neon.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/filters_neon.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\filters_neon.c -o CMakeFiles\libwebp.dir\src\dsp\filters_neon.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_sse2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/filters_sse2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_sse2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\filters_sse2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\filters_sse2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_sse2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/filters_sse2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\filters_sse2.c > CMakeFiles\libwebp.dir\src\dsp\filters_sse2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_sse2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/filters_sse2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\filters_sse2.c -o CMakeFiles\libwebp.dir\src\dsp\filters_sse2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_44) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\lossless.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/lossless.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless.c > CMakeFiles\libwebp.dir\src\dsp\lossless.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/lossless.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless.c -o CMakeFiles\libwebp.dir\src\dsp\lossless.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_45) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\lossless_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/lossless_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc.c > CMakeFiles\libwebp.dir\src\dsp\lossless_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/lossless_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc.c -o CMakeFiles\libwebp.dir\src\dsp\lossless_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips32.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips32.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips32.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_enc_mips32.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_46) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips32.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\lossless_enc_mips32.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_mips32.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips32.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips32.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_mips32.c > CMakeFiles\libwebp.dir\src\dsp\lossless_enc_mips32.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips32.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips32.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_mips32.c -o CMakeFiles\libwebp.dir\src\dsp\lossless_enc_mips32.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips_dsp_r2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_enc_mips_dsp_r2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_47) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips_dsp_r2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\lossless_enc_mips_dsp_r2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_mips_dsp_r2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips_dsp_r2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips_dsp_r2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_mips_dsp_r2.c > CMakeFiles\libwebp.dir\src\dsp\lossless_enc_mips_dsp_r2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips_dsp_r2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips_dsp_r2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_mips_dsp_r2.c -o CMakeFiles\libwebp.dir\src\dsp\lossless_enc_mips_dsp_r2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_msa.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_msa.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_msa.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_enc_msa.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_48) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_msa.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\lossless_enc_msa.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_msa.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_msa.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/lossless_enc_msa.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_msa.c > CMakeFiles\libwebp.dir\src\dsp\lossless_enc_msa.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_msa.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/lossless_enc_msa.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_msa.c -o CMakeFiles\libwebp.dir\src\dsp\lossless_enc_msa.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_neon.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_enc_neon.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_49) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_neon.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\lossless_enc_neon.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_neon.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_neon.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/lossless_enc_neon.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_neon.c > CMakeFiles\libwebp.dir\src\dsp\lossless_enc_neon.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_neon.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/lossless_enc_neon.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_neon.c -o CMakeFiles\libwebp.dir\src\dsp\lossless_enc_neon.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_enc_sse2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_50) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\lossless_enc_sse2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_sse2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_sse2.c > CMakeFiles\libwebp.dir\src\dsp\lossless_enc_sse2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_sse2.c -o CMakeFiles\libwebp.dir\src\dsp\lossless_enc_sse2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse41.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse41.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse41.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_enc_sse41.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_51) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse41.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\lossless_enc_sse41.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_sse41.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse41.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse41.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_sse41.c > CMakeFiles\libwebp.dir\src\dsp\lossless_enc_sse41.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse41.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse41.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_enc_sse41.c -o CMakeFiles\libwebp.dir\src\dsp\lossless_enc_sse41.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_mips_dsp_r2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_mips_dsp_r2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_52) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_mips_dsp_r2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\lossless_mips_dsp_r2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_mips_dsp_r2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_mips_dsp_r2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/lossless_mips_dsp_r2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_mips_dsp_r2.c > CMakeFiles\libwebp.dir\src\dsp\lossless_mips_dsp_r2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_mips_dsp_r2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/lossless_mips_dsp_r2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_mips_dsp_r2.c -o CMakeFiles\libwebp.dir\src\dsp\lossless_mips_dsp_r2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_msa.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_msa.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_msa.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_msa.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_53) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_msa.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\lossless_msa.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_msa.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_msa.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/lossless_msa.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_msa.c > CMakeFiles\libwebp.dir\src\dsp\lossless_msa.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_msa.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/lossless_msa.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_msa.c -o CMakeFiles\libwebp.dir\src\dsp\lossless_msa.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_neon.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_neon.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_54) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_neon.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\lossless_neon.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_neon.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_neon.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/lossless_neon.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_neon.c > CMakeFiles\libwebp.dir\src\dsp\lossless_neon.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_neon.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/lossless_neon.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_neon.c -o CMakeFiles\libwebp.dir\src\dsp\lossless_neon.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_sse2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/lossless_sse2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_55) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_sse2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\lossless_sse2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_sse2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_sse2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/lossless_sse2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_sse2.c > CMakeFiles\libwebp.dir\src\dsp\lossless_sse2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_sse2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/lossless_sse2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\lossless_sse2.c -o CMakeFiles\libwebp.dir\src\dsp\lossless_sse2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/rescaler.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_56) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\rescaler.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/rescaler.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler.c > CMakeFiles\libwebp.dir\src\dsp\rescaler.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/rescaler.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler.c -o CMakeFiles\libwebp.dir\src\dsp\rescaler.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips32.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips32.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips32.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/rescaler_mips32.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_57) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips32.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\rescaler_mips32.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler_mips32.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips32.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/rescaler_mips32.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler_mips32.c > CMakeFiles\libwebp.dir\src\dsp\rescaler_mips32.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips32.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/rescaler_mips32.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler_mips32.c -o CMakeFiles\libwebp.dir\src\dsp\rescaler_mips32.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips_dsp_r2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/rescaler_mips_dsp_r2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_58) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips_dsp_r2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\rescaler_mips_dsp_r2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler_mips_dsp_r2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips_dsp_r2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/rescaler_mips_dsp_r2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler_mips_dsp_r2.c > CMakeFiles\libwebp.dir\src\dsp\rescaler_mips_dsp_r2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips_dsp_r2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/rescaler_mips_dsp_r2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler_mips_dsp_r2.c -o CMakeFiles\libwebp.dir\src\dsp\rescaler_mips_dsp_r2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_msa.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_msa.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_msa.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/rescaler_msa.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_59) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_msa.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\rescaler_msa.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler_msa.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_msa.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/rescaler_msa.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler_msa.c > CMakeFiles\libwebp.dir\src\dsp\rescaler_msa.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_msa.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/rescaler_msa.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler_msa.c -o CMakeFiles\libwebp.dir\src\dsp\rescaler_msa.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_neon.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/rescaler_neon.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_60) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_neon.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\rescaler_neon.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler_neon.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_neon.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/rescaler_neon.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler_neon.c > CMakeFiles\libwebp.dir\src\dsp\rescaler_neon.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_neon.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/rescaler_neon.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler_neon.c -o CMakeFiles\libwebp.dir\src\dsp\rescaler_neon.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_sse2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/rescaler_sse2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_61) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_sse2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\rescaler_sse2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler_sse2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_sse2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/rescaler_sse2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler_sse2.c > CMakeFiles\libwebp.dir\src\dsp\rescaler_sse2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_sse2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/rescaler_sse2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\rescaler_sse2.c -o CMakeFiles\libwebp.dir\src\dsp\rescaler_sse2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/ssim.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_62) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\ssim.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\ssim.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/ssim.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\ssim.c > CMakeFiles\libwebp.dir\src\dsp\ssim.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/ssim.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\ssim.c -o CMakeFiles\libwebp.dir\src\dsp\ssim.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim_sse2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/ssim_sse2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_63) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim_sse2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\ssim_sse2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\ssim_sse2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim_sse2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/ssim_sse2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\ssim_sse2.c > CMakeFiles\libwebp.dir\src\dsp\ssim_sse2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim_sse2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/ssim_sse2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\ssim_sse2.c -o CMakeFiles\libwebp.dir\src\dsp\ssim_sse2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/upsampling.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_64) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\upsampling.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/upsampling.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling.c > CMakeFiles\libwebp.dir\src\dsp\upsampling.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/upsampling.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling.c -o CMakeFiles\libwebp.dir\src\dsp\upsampling.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_mips_dsp_r2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/upsampling_mips_dsp_r2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_65) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_mips_dsp_r2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\upsampling_mips_dsp_r2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling_mips_dsp_r2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_mips_dsp_r2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/upsampling_mips_dsp_r2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling_mips_dsp_r2.c > CMakeFiles\libwebp.dir\src\dsp\upsampling_mips_dsp_r2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_mips_dsp_r2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/upsampling_mips_dsp_r2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling_mips_dsp_r2.c -o CMakeFiles\libwebp.dir\src\dsp\upsampling_mips_dsp_r2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_msa.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_msa.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_msa.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/upsampling_msa.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_66) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_msa.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\upsampling_msa.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling_msa.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_msa.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/upsampling_msa.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling_msa.c > CMakeFiles\libwebp.dir\src\dsp\upsampling_msa.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_msa.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/upsampling_msa.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling_msa.c -o CMakeFiles\libwebp.dir\src\dsp\upsampling_msa.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_neon.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/upsampling_neon.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_67) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_neon.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\upsampling_neon.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling_neon.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_neon.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/upsampling_neon.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling_neon.c > CMakeFiles\libwebp.dir\src\dsp\upsampling_neon.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_neon.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/upsampling_neon.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling_neon.c -o CMakeFiles\libwebp.dir\src\dsp\upsampling_neon.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/upsampling_sse2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_68) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\upsampling_sse2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling_sse2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/upsampling_sse2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling_sse2.c > CMakeFiles\libwebp.dir\src\dsp\upsampling_sse2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/upsampling_sse2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling_sse2.c -o CMakeFiles\libwebp.dir\src\dsp\upsampling_sse2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse41.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse41.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse41.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/upsampling_sse41.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_69) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse41.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\upsampling_sse41.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling_sse41.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse41.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/upsampling_sse41.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling_sse41.c > CMakeFiles\libwebp.dir\src\dsp\upsampling_sse41.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse41.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/upsampling_sse41.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\upsampling_sse41.c -o CMakeFiles\libwebp.dir\src\dsp\upsampling_sse41.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/yuv.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_70) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\yuv.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/yuv.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv.c > CMakeFiles\libwebp.dir\src\dsp\yuv.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/yuv.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv.c -o CMakeFiles\libwebp.dir\src\dsp\yuv.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips32.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips32.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips32.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/yuv_mips32.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_71) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips32.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\yuv_mips32.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv_mips32.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips32.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/yuv_mips32.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv_mips32.c > CMakeFiles\libwebp.dir\src\dsp\yuv_mips32.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips32.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/yuv_mips32.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv_mips32.c -o CMakeFiles\libwebp.dir\src\dsp\yuv_mips32.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips_dsp_r2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips_dsp_r2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/yuv_mips_dsp_r2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_72) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips_dsp_r2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\yuv_mips_dsp_r2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv_mips_dsp_r2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips_dsp_r2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/yuv_mips_dsp_r2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv_mips_dsp_r2.c > CMakeFiles\libwebp.dir\src\dsp\yuv_mips_dsp_r2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips_dsp_r2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/yuv_mips_dsp_r2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv_mips_dsp_r2.c -o CMakeFiles\libwebp.dir\src\dsp\yuv_mips_dsp_r2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_neon.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_neon.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/yuv_neon.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_73) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_neon.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\yuv_neon.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv_neon.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_neon.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/yuv_neon.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv_neon.c > CMakeFiles\libwebp.dir\src\dsp\yuv_neon.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_neon.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/yuv_neon.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv_neon.c -o CMakeFiles\libwebp.dir\src\dsp\yuv_neon.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse2.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/yuv_sse2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_74) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\yuv_sse2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv_sse2.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/yuv_sse2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv_sse2.c > CMakeFiles\libwebp.dir\src\dsp\yuv_sse2.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/yuv_sse2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv_sse2.c -o CMakeFiles\libwebp.dir\src\dsp\yuv_sse2.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse41.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse41.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse41.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/dsp/yuv_sse41.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_75) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse41.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\dsp\yuv_sse41.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv_sse41.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse41.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/dsp/yuv_sse41.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv_sse41.c > CMakeFiles\libwebp.dir\src\dsp\yuv_sse41.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse41.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/dsp/yuv_sse41.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\dsp\yuv_sse41.c -o CMakeFiles\libwebp.dir\src\dsp\yuv_sse41.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/alpha_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/alpha_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/alpha_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/alpha_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_76) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/alpha_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\alpha_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\alpha_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/alpha_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/alpha_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\alpha_enc.c > CMakeFiles\libwebp.dir\src\enc\alpha_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/alpha_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/alpha_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\alpha_enc.c -o CMakeFiles\libwebp.dir\src\enc\alpha_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/analysis_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/analysis_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/analysis_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/analysis_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_77) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/analysis_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\analysis_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\analysis_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/analysis_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/analysis_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\analysis_enc.c > CMakeFiles\libwebp.dir\src\enc\analysis_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/analysis_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/analysis_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\analysis_enc.c -o CMakeFiles\libwebp.dir\src\enc\analysis_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_cost_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_cost_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_cost_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/backward_references_cost_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_78) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_cost_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\backward_references_cost_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\backward_references_cost_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_cost_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/backward_references_cost_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\backward_references_cost_enc.c > CMakeFiles\libwebp.dir\src\enc\backward_references_cost_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_cost_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/backward_references_cost_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\backward_references_cost_enc.c -o CMakeFiles\libwebp.dir\src\enc\backward_references_cost_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/backward_references_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_79) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\backward_references_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\backward_references_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/backward_references_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\backward_references_enc.c > CMakeFiles\libwebp.dir\src\enc\backward_references_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/backward_references_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\backward_references_enc.c -o CMakeFiles\libwebp.dir\src\enc\backward_references_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/config_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/config_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/config_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/config_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_80) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/config_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\config_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\config_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/config_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/config_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\config_enc.c > CMakeFiles\libwebp.dir\src\enc\config_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/config_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/config_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\config_enc.c -o CMakeFiles\libwebp.dir\src\enc\config_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/cost_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/cost_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/cost_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/cost_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_81) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/cost_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\cost_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\cost_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/cost_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/cost_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\cost_enc.c > CMakeFiles\libwebp.dir\src\enc\cost_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/cost_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/cost_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\cost_enc.c -o CMakeFiles\libwebp.dir\src\enc\cost_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/filter_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/filter_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/filter_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/filter_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_82) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/filter_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\filter_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\filter_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/filter_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/filter_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\filter_enc.c > CMakeFiles\libwebp.dir\src\enc\filter_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/filter_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/filter_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\filter_enc.c -o CMakeFiles\libwebp.dir\src\enc\filter_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/frame_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/frame_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/frame_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/frame_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_83) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/frame_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\frame_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\frame_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/frame_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/frame_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\frame_enc.c > CMakeFiles\libwebp.dir\src\enc\frame_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/frame_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/frame_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\frame_enc.c -o CMakeFiles\libwebp.dir\src\enc\frame_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/histogram_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/histogram_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/histogram_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/histogram_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_84) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/histogram_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\histogram_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\histogram_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/histogram_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/histogram_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\histogram_enc.c > CMakeFiles\libwebp.dir\src\enc\histogram_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/histogram_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/histogram_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\histogram_enc.c -o CMakeFiles\libwebp.dir\src\enc\histogram_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/iterator_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/iterator_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/iterator_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/iterator_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_85) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/iterator_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\iterator_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\iterator_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/iterator_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/iterator_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\iterator_enc.c > CMakeFiles\libwebp.dir\src\enc\iterator_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/iterator_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/iterator_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\iterator_enc.c -o CMakeFiles\libwebp.dir\src\enc\iterator_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/near_lossless_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/near_lossless_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/near_lossless_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/near_lossless_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_86) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/near_lossless_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\near_lossless_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\near_lossless_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/near_lossless_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/near_lossless_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\near_lossless_enc.c > CMakeFiles\libwebp.dir\src\enc\near_lossless_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/near_lossless_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/near_lossless_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\near_lossless_enc.c -o CMakeFiles\libwebp.dir\src\enc\near_lossless_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_csp_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_csp_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_csp_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/picture_csp_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_87) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_csp_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\picture_csp_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\picture_csp_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_csp_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/picture_csp_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\picture_csp_enc.c > CMakeFiles\libwebp.dir\src\enc\picture_csp_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_csp_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/picture_csp_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\picture_csp_enc.c -o CMakeFiles\libwebp.dir\src\enc\picture_csp_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/picture_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_88) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\picture_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\picture_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/picture_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\picture_enc.c > CMakeFiles\libwebp.dir\src\enc\picture_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/picture_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\picture_enc.c -o CMakeFiles\libwebp.dir\src\enc\picture_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_psnr_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_psnr_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_psnr_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/picture_psnr_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_89) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_psnr_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\picture_psnr_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\picture_psnr_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_psnr_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/picture_psnr_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\picture_psnr_enc.c > CMakeFiles\libwebp.dir\src\enc\picture_psnr_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_psnr_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/picture_psnr_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\picture_psnr_enc.c -o CMakeFiles\libwebp.dir\src\enc\picture_psnr_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_rescale_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_rescale_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_rescale_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/picture_rescale_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_90) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_rescale_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\picture_rescale_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\picture_rescale_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_rescale_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/picture_rescale_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\picture_rescale_enc.c > CMakeFiles\libwebp.dir\src\enc\picture_rescale_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_rescale_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/picture_rescale_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\picture_rescale_enc.c -o CMakeFiles\libwebp.dir\src\enc\picture_rescale_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_tools_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_tools_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_tools_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/picture_tools_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_91) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_tools_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\picture_tools_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\picture_tools_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_tools_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/picture_tools_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\picture_tools_enc.c > CMakeFiles\libwebp.dir\src\enc\picture_tools_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_tools_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/picture_tools_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\picture_tools_enc.c -o CMakeFiles\libwebp.dir\src\enc\picture_tools_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/predictor_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/predictor_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/predictor_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/predictor_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_92) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/predictor_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\predictor_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\predictor_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/predictor_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/predictor_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\predictor_enc.c > CMakeFiles\libwebp.dir\src\enc\predictor_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/predictor_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/predictor_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\predictor_enc.c -o CMakeFiles\libwebp.dir\src\enc\predictor_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/quant_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/quant_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/quant_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/quant_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_93) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/quant_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\quant_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\quant_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/quant_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/quant_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\quant_enc.c > CMakeFiles\libwebp.dir\src\enc\quant_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/quant_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/quant_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\quant_enc.c -o CMakeFiles\libwebp.dir\src\enc\quant_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/syntax_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/syntax_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/syntax_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/syntax_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_94) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/syntax_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\syntax_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\syntax_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/syntax_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/syntax_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\syntax_enc.c > CMakeFiles\libwebp.dir\src\enc\syntax_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/syntax_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/syntax_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\syntax_enc.c -o CMakeFiles\libwebp.dir\src\enc\syntax_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/token_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/token_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/token_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/token_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_95) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/token_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\token_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\token_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/token_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/token_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\token_enc.c > CMakeFiles\libwebp.dir\src\enc\token_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/token_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/token_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\token_enc.c -o CMakeFiles\libwebp.dir\src\enc\token_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/tree_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/tree_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/tree_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/tree_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_96) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/tree_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\tree_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\tree_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/tree_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/tree_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\tree_enc.c > CMakeFiles\libwebp.dir\src\enc\tree_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/tree_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/tree_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\tree_enc.c -o CMakeFiles\libwebp.dir\src\enc\tree_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/vp8l_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/vp8l_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/vp8l_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/vp8l_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_97) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/vp8l_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\vp8l_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\vp8l_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/vp8l_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/vp8l_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\vp8l_enc.c > CMakeFiles\libwebp.dir\src\enc\vp8l_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/vp8l_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/vp8l_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\vp8l_enc.c -o CMakeFiles\libwebp.dir\src\enc\vp8l_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/webp_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/webp_enc.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/webp_enc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/enc/webp_enc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_98) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/webp_enc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\enc\webp_enc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\webp_enc.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/webp_enc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/enc/webp_enc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\webp_enc.c > CMakeFiles\libwebp.dir\src\enc\webp_enc.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/webp_enc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/enc/webp_enc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\enc\webp_enc.c -o CMakeFiles\libwebp.dir\src\enc\webp_enc.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/anim_encode.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/anim_encode.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/anim_encode.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/mux/anim_encode.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_99) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/anim_encode.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\mux\anim_encode.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\mux\anim_encode.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/anim_encode.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/mux/anim_encode.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\mux\anim_encode.c > CMakeFiles\libwebp.dir\src\mux\anim_encode.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/anim_encode.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/mux/anim_encode.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\mux\anim_encode.c -o CMakeFiles\libwebp.dir\src\mux\anim_encode.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxedit.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxedit.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxedit.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/mux/muxedit.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_100) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxedit.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\mux\muxedit.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\mux\muxedit.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxedit.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/mux/muxedit.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\mux\muxedit.c > CMakeFiles\libwebp.dir\src\mux\muxedit.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxedit.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/mux/muxedit.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\mux\muxedit.c -o CMakeFiles\libwebp.dir\src\mux\muxedit.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxinternal.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxinternal.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxinternal.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/mux/muxinternal.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_101) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxinternal.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\mux\muxinternal.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\mux\muxinternal.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxinternal.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/mux/muxinternal.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\mux\muxinternal.c > CMakeFiles\libwebp.dir\src\mux\muxinternal.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxinternal.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/mux/muxinternal.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\mux\muxinternal.c -o CMakeFiles\libwebp.dir\src\mux\muxinternal.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxread.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxread.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxread.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/mux/muxread.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_102) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxread.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\mux\muxread.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\mux\muxread.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxread.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/mux/muxread.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\mux\muxread.c > CMakeFiles\libwebp.dir\src\mux\muxread.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxread.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/mux/muxread.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\mux\muxread.c -o CMakeFiles\libwebp.dir\src\mux\muxread.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_reader_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_reader_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_reader_utils.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/bit_reader_utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_103) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_reader_utils.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\utils\bit_reader_utils.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\bit_reader_utils.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_reader_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/utils/bit_reader_utils.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\bit_reader_utils.c > CMakeFiles\libwebp.dir\src\utils\bit_reader_utils.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_reader_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/utils/bit_reader_utils.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\bit_reader_utils.c -o CMakeFiles\libwebp.dir\src\utils\bit_reader_utils.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_writer_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_writer_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_writer_utils.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/bit_writer_utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_104) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_writer_utils.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\utils\bit_writer_utils.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\bit_writer_utils.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_writer_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/utils/bit_writer_utils.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\bit_writer_utils.c > CMakeFiles\libwebp.dir\src\utils\bit_writer_utils.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_writer_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/utils/bit_writer_utils.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\bit_writer_utils.c -o CMakeFiles\libwebp.dir\src\utils\bit_writer_utils.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/color_cache_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/color_cache_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/color_cache_utils.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/color_cache_utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_105) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/color_cache_utils.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\utils\color_cache_utils.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\color_cache_utils.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/color_cache_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/utils/color_cache_utils.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\color_cache_utils.c > CMakeFiles\libwebp.dir\src\utils\color_cache_utils.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/color_cache_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/utils/color_cache_utils.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\color_cache_utils.c -o CMakeFiles\libwebp.dir\src\utils\color_cache_utils.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/filters_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/filters_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/filters_utils.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/filters_utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_106) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/filters_utils.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\utils\filters_utils.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\filters_utils.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/filters_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/utils/filters_utils.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\filters_utils.c > CMakeFiles\libwebp.dir\src\utils\filters_utils.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/filters_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/utils/filters_utils.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\filters_utils.c -o CMakeFiles\libwebp.dir\src\utils\filters_utils.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_encode_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_encode_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_encode_utils.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/huffman_encode_utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_107) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_encode_utils.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\utils\huffman_encode_utils.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\huffman_encode_utils.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_encode_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/utils/huffman_encode_utils.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\huffman_encode_utils.c > CMakeFiles\libwebp.dir\src\utils\huffman_encode_utils.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_encode_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/utils/huffman_encode_utils.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\huffman_encode_utils.c -o CMakeFiles\libwebp.dir\src\utils\huffman_encode_utils.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_utils.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/huffman_utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_108) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_utils.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\utils\huffman_utils.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\huffman_utils.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/utils/huffman_utils.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\huffman_utils.c > CMakeFiles\libwebp.dir\src\utils\huffman_utils.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/utils/huffman_utils.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\huffman_utils.c -o CMakeFiles\libwebp.dir\src\utils\huffman_utils.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_dec_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_dec_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_dec_utils.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/quant_levels_dec_utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_109) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_dec_utils.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\utils\quant_levels_dec_utils.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\quant_levels_dec_utils.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_dec_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/utils/quant_levels_dec_utils.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\quant_levels_dec_utils.c > CMakeFiles\libwebp.dir\src\utils\quant_levels_dec_utils.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_dec_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/utils/quant_levels_dec_utils.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\quant_levels_dec_utils.c -o CMakeFiles\libwebp.dir\src\utils\quant_levels_dec_utils.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_utils.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/quant_levels_utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_110) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_utils.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\utils\quant_levels_utils.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\quant_levels_utils.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/utils/quant_levels_utils.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\quant_levels_utils.c > CMakeFiles\libwebp.dir\src\utils\quant_levels_utils.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/utils/quant_levels_utils.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\quant_levels_utils.c -o CMakeFiles\libwebp.dir\src\utils\quant_levels_utils.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/random_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/random_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/random_utils.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/random_utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_111) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/random_utils.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\utils\random_utils.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\random_utils.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/random_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/utils/random_utils.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\random_utils.c > CMakeFiles\libwebp.dir\src\utils\random_utils.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/random_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/utils/random_utils.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\random_utils.c -o CMakeFiles\libwebp.dir\src\utils\random_utils.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/rescaler_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/rescaler_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/rescaler_utils.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/rescaler_utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_112) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/rescaler_utils.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\utils\rescaler_utils.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\rescaler_utils.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/rescaler_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/utils/rescaler_utils.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\rescaler_utils.c > CMakeFiles\libwebp.dir\src\utils\rescaler_utils.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/rescaler_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/utils/rescaler_utils.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\rescaler_utils.c -o CMakeFiles\libwebp.dir\src\utils\rescaler_utils.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/thread_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/thread_utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/thread_utils.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/thread_utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_113) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/thread_utils.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\utils\thread_utils.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\thread_utils.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/thread_utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/utils/thread_utils.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\thread_utils.c > CMakeFiles\libwebp.dir\src\utils\thread_utils.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/thread_utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/utils/thread_utils.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\thread_utils.c -o CMakeFiles\libwebp.dir\src\utils\thread_utils.c.s

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/flags.make
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/utils.c.obj: 3rdparty/libwebp/CMakeFiles/libwebp.dir/includes_C.rsp
3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/utils.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libwebp/src/utils/utils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_114) "Building C object 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/utils.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libwebp.dir\src\utils\utils.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\utils.c

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/utils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libwebp.dir/src/utils/utils.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\utils.c > CMakeFiles\libwebp.dir\src\utils\utils.c.i

3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/utils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libwebp.dir/src/utils/utils.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libwebp\src\utils\utils.c -o CMakeFiles\libwebp.dir\src\utils\utils.c.s

# Object files for target libwebp
libwebp_OBJECTS = \
"CMakeFiles/libwebp.dir/src/dec/alpha_dec.c.obj" \
"CMakeFiles/libwebp.dir/src/dec/buffer_dec.c.obj" \
"CMakeFiles/libwebp.dir/src/dec/frame_dec.c.obj" \
"CMakeFiles/libwebp.dir/src/dec/idec_dec.c.obj" \
"CMakeFiles/libwebp.dir/src/dec/io_dec.c.obj" \
"CMakeFiles/libwebp.dir/src/dec/quant_dec.c.obj" \
"CMakeFiles/libwebp.dir/src/dec/tree_dec.c.obj" \
"CMakeFiles/libwebp.dir/src/dec/vp8_dec.c.obj" \
"CMakeFiles/libwebp.dir/src/dec/vp8l_dec.c.obj" \
"CMakeFiles/libwebp.dir/src/dec/webp_dec.c.obj" \
"CMakeFiles/libwebp.dir/src/demux/anim_decode.c.obj" \
"CMakeFiles/libwebp.dir/src/demux/demux.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/alpha_processing.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/alpha_processing_mips_dsp_r2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/alpha_processing_neon.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse41.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/cost.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/cost_mips32.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/cost_mips_dsp_r2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/cost_neon.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/cost_sse2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/cpu.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/dec.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/dec_clip_tables.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/dec_mips32.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/dec_mips_dsp_r2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/dec_msa.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/dec_neon.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/dec_sse2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/dec_sse41.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/enc.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/enc_mips32.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/enc_mips_dsp_r2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/enc_msa.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/enc_neon.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/enc_sse2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/enc_sse41.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/filters.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/filters_mips_dsp_r2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/filters_msa.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/filters_neon.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/filters_sse2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/lossless.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/lossless_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips32.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips_dsp_r2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/lossless_enc_msa.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/lossless_enc_neon.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse41.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/lossless_mips_dsp_r2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/lossless_msa.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/lossless_neon.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/lossless_sse2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/rescaler.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/rescaler_mips32.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/rescaler_mips_dsp_r2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/rescaler_msa.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/rescaler_neon.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/rescaler_sse2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/ssim.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/ssim_sse2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/upsampling.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/upsampling_mips_dsp_r2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/upsampling_msa.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/upsampling_neon.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/upsampling_sse2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/upsampling_sse41.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/yuv.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/yuv_mips32.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/yuv_mips_dsp_r2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/yuv_neon.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/yuv_sse2.c.obj" \
"CMakeFiles/libwebp.dir/src/dsp/yuv_sse41.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/alpha_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/analysis_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/backward_references_cost_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/backward_references_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/config_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/cost_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/filter_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/frame_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/histogram_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/iterator_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/near_lossless_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/picture_csp_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/picture_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/picture_psnr_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/picture_rescale_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/picture_tools_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/predictor_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/quant_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/syntax_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/token_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/tree_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/vp8l_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/enc/webp_enc.c.obj" \
"CMakeFiles/libwebp.dir/src/mux/anim_encode.c.obj" \
"CMakeFiles/libwebp.dir/src/mux/muxedit.c.obj" \
"CMakeFiles/libwebp.dir/src/mux/muxinternal.c.obj" \
"CMakeFiles/libwebp.dir/src/mux/muxread.c.obj" \
"CMakeFiles/libwebp.dir/src/utils/bit_reader_utils.c.obj" \
"CMakeFiles/libwebp.dir/src/utils/bit_writer_utils.c.obj" \
"CMakeFiles/libwebp.dir/src/utils/color_cache_utils.c.obj" \
"CMakeFiles/libwebp.dir/src/utils/filters_utils.c.obj" \
"CMakeFiles/libwebp.dir/src/utils/huffman_encode_utils.c.obj" \
"CMakeFiles/libwebp.dir/src/utils/huffman_utils.c.obj" \
"CMakeFiles/libwebp.dir/src/utils/quant_levels_dec_utils.c.obj" \
"CMakeFiles/libwebp.dir/src/utils/quant_levels_utils.c.obj" \
"CMakeFiles/libwebp.dir/src/utils/random_utils.c.obj" \
"CMakeFiles/libwebp.dir/src/utils/rescaler_utils.c.obj" \
"CMakeFiles/libwebp.dir/src/utils/thread_utils.c.obj" \
"CMakeFiles/libwebp.dir/src/utils/utils.c.obj"

# External object files for target libwebp
libwebp_EXTERNAL_OBJECTS =

3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/alpha_dec.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/buffer_dec.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/frame_dec.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/idec_dec.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/io_dec.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/quant_dec.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/tree_dec.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8_dec.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/vp8l_dec.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dec/webp_dec.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/anim_decode.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/demux/demux.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_mips_dsp_r2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_neon.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse41.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips32.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_mips_dsp_r2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_neon.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cost_sse2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/cpu.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_clip_tables.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips32.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_mips_dsp_r2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_msa.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_neon.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/dec_sse41.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips32.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_mips_dsp_r2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_msa.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_neon.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/enc_sse41.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_mips_dsp_r2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_msa.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_neon.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/filters_sse2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips32.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips_dsp_r2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_msa.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_neon.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse41.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_mips_dsp_r2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_msa.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_neon.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/lossless_sse2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips32.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_mips_dsp_r2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_msa.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_neon.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/rescaler_sse2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/ssim_sse2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_mips_dsp_r2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_msa.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_neon.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/upsampling_sse41.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips32.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_mips_dsp_r2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_neon.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse2.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/dsp/yuv_sse41.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/alpha_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/analysis_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_cost_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/backward_references_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/config_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/cost_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/filter_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/frame_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/histogram_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/iterator_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/near_lossless_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_csp_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_psnr_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_rescale_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/picture_tools_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/predictor_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/quant_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/syntax_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/token_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/tree_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/vp8l_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/enc/webp_enc.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/anim_encode.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxedit.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxinternal.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/mux/muxread.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_reader_utils.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/bit_writer_utils.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/color_cache_utils.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/filters_utils.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_encode_utils.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/huffman_utils.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_dec_utils.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/quant_levels_utils.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/random_utils.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/rescaler_utils.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/thread_utils.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/src/utils/utils.c.obj
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/build.make
3rdparty/lib/liblibwebp.a: 3rdparty/libwebp/CMakeFiles/libwebp.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_115) "Linking C static library ..\lib\liblibwebp.a"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && $(CMAKE_COMMAND) -P CMakeFiles\libwebp.dir\cmake_clean_target.cmake
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\libwebp.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
3rdparty/libwebp/CMakeFiles/libwebp.dir/build: 3rdparty/lib/liblibwebp.a

.PHONY : 3rdparty/libwebp/CMakeFiles/libwebp.dir/build

3rdparty/libwebp/CMakeFiles/libwebp.dir/clean:
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp && $(CMAKE_COMMAND) -P CMakeFiles\libwebp.dir\cmake_clean.cmake
.PHONY : 3rdparty/libwebp/CMakeFiles/libwebp.dir/clean

3rdparty/libwebp/CMakeFiles/libwebp.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\unet\opencv\opencv\sources D:\unet\opencv\opencv\sources\3rdparty\libwebp D:\unet\opencv\opencv\mingw_build D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp D:\unet\opencv\opencv\mingw_build\3rdparty\libwebp\CMakeFiles\libwebp.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : 3rdparty/libwebp/CMakeFiles/libwebp.dir/depend

