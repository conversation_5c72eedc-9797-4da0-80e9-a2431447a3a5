file(REMOVE_RECURSE
  "CMakeFiles/libwebp.dir/src/dec/alpha_dec.c.obj"
  "CMakeFiles/libwebp.dir/src/dec/buffer_dec.c.obj"
  "CMakeFiles/libwebp.dir/src/dec/frame_dec.c.obj"
  "CMakeFiles/libwebp.dir/src/dec/idec_dec.c.obj"
  "CMakeFiles/libwebp.dir/src/dec/io_dec.c.obj"
  "CMakeFiles/libwebp.dir/src/dec/quant_dec.c.obj"
  "CMakeFiles/libwebp.dir/src/dec/tree_dec.c.obj"
  "CMakeFiles/libwebp.dir/src/dec/vp8_dec.c.obj"
  "CMakeFiles/libwebp.dir/src/dec/vp8l_dec.c.obj"
  "CMakeFiles/libwebp.dir/src/dec/webp_dec.c.obj"
  "CMakeFiles/libwebp.dir/src/demux/anim_decode.c.obj"
  "CMakeFiles/libwebp.dir/src/demux/demux.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/alpha_processing.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/alpha_processing_mips_dsp_r2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/alpha_processing_neon.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/alpha_processing_sse41.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/cost.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/cost_mips32.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/cost_mips_dsp_r2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/cost_neon.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/cost_sse2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/cpu.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/dec.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/dec_clip_tables.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/dec_mips32.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/dec_mips_dsp_r2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/dec_msa.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/dec_neon.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/dec_sse2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/dec_sse41.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/enc.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/enc_mips32.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/enc_mips_dsp_r2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/enc_msa.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/enc_neon.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/enc_sse2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/enc_sse41.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/filters.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/filters_mips_dsp_r2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/filters_msa.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/filters_neon.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/filters_sse2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/lossless.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/lossless_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips32.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/lossless_enc_mips_dsp_r2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/lossless_enc_msa.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/lossless_enc_neon.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/lossless_enc_sse41.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/lossless_mips_dsp_r2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/lossless_msa.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/lossless_neon.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/lossless_sse2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/rescaler.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/rescaler_mips32.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/rescaler_mips_dsp_r2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/rescaler_msa.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/rescaler_neon.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/rescaler_sse2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/ssim.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/ssim_sse2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/upsampling.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/upsampling_mips_dsp_r2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/upsampling_msa.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/upsampling_neon.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/upsampling_sse2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/upsampling_sse41.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/yuv.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/yuv_mips32.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/yuv_mips_dsp_r2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/yuv_neon.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/yuv_sse2.c.obj"
  "CMakeFiles/libwebp.dir/src/dsp/yuv_sse41.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/alpha_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/analysis_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/backward_references_cost_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/backward_references_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/config_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/cost_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/filter_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/frame_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/histogram_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/iterator_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/near_lossless_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/picture_csp_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/picture_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/picture_psnr_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/picture_rescale_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/picture_tools_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/predictor_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/quant_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/syntax_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/token_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/tree_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/vp8l_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/enc/webp_enc.c.obj"
  "CMakeFiles/libwebp.dir/src/mux/anim_encode.c.obj"
  "CMakeFiles/libwebp.dir/src/mux/muxedit.c.obj"
  "CMakeFiles/libwebp.dir/src/mux/muxinternal.c.obj"
  "CMakeFiles/libwebp.dir/src/mux/muxread.c.obj"
  "CMakeFiles/libwebp.dir/src/utils/bit_reader_utils.c.obj"
  "CMakeFiles/libwebp.dir/src/utils/bit_writer_utils.c.obj"
  "CMakeFiles/libwebp.dir/src/utils/color_cache_utils.c.obj"
  "CMakeFiles/libwebp.dir/src/utils/filters_utils.c.obj"
  "CMakeFiles/libwebp.dir/src/utils/huffman_encode_utils.c.obj"
  "CMakeFiles/libwebp.dir/src/utils/huffman_utils.c.obj"
  "CMakeFiles/libwebp.dir/src/utils/quant_levels_dec_utils.c.obj"
  "CMakeFiles/libwebp.dir/src/utils/quant_levels_utils.c.obj"
  "CMakeFiles/libwebp.dir/src/utils/random_utils.c.obj"
  "CMakeFiles/libwebp.dir/src/utils/rescaler_utils.c.obj"
  "CMakeFiles/libwebp.dir/src/utils/thread_utils.c.obj"
  "CMakeFiles/libwebp.dir/src/utils/utils.c.obj"
  "../lib/liblibwebp.pdb"
  "../lib/liblibwebp.a"
)

# Per-language clean rules from dependency scanning.
foreach(lang C)
  include(CMakeFiles/libwebp.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
