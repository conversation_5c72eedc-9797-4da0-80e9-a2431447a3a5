#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

3rdparty/libjpeg-turbo/jconfig.h

3rdparty/libjpeg-turbo/jconfigint.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jaricom.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcapimin.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcapistd.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcarith.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jccoefct.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jccolext.c

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jccolor.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jsimd.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimd.h
jconfigint.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jconfigint.h
jccolext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jccolext.c
jccolext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jccolext.c
jccolext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jccolext.c
jccolext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jccolext.c
jccolext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jccolext.c
jccolext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jccolext.c
jccolext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jccolext.c

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcdctmgr.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jdct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdct.h
jsimddct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimddct.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jchuff.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jsimd.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimd.h
jconfigint.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jconfigint.h
limits.h
-
jpeg_nbits_table.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeg_nbits_table.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jchuff.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcicc.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jerror.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jerror.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcinit.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcmainct.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcmarker.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jpegcomp.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpegcomp.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcmaster.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jpegcomp.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpegcomp.h
jconfigint.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jconfigint.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcomapi.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcparam.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jstdhuff.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jstdhuff.c

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcphuff.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jsimd.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimd.h
jconfigint.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jconfigint.h
limits.h
-
intrin.h
-
jpeg_nbits_table.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeg_nbits_table.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcprepct.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcsample.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jsimd.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimd.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jctrans.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdapimin.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jdmaster.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmaster.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdapistd.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jdmainct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmainct.h
jdcoefct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcoefct.h
jdsample.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdsample.h
jmemsys.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jmemsys.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdarith.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdatadst.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jerror.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jerror.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdatasrc.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jerror.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jerror.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcoefct.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jdcoefct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcoefct.h
jpegcomp.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpegcomp.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcoefct.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcol565.c

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcolext.c

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcolor.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jsimd.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimd.h
jconfigint.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jconfigint.h
jdcolext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcolext.c
jdcolext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcolext.c
jdcolext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcolext.c
jdcolext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcolext.c
jdcolext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcolext.c
jdcolext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcolext.c
jdcolext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcolext.c
jdcol565.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcol565.c
jdcol565.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcol565.c

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdct.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jddctmgr.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jdct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdct.h
jsimddct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimddct.h
jpegcomp.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpegcomp.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdhuff.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jdhuff.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdhuff.h
jpegcomp.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpegcomp.h
jstdhuff.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jstdhuff.c

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdhuff.h
jconfigint.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jconfigint.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdicc.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jerror.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jerror.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdinput.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jpegcomp.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpegcomp.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmainct.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jdmainct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmainct.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmainct.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jpegcomp.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpegcomp.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmarker.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmaster.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jpegcomp.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpegcomp.h
jdmaster.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmaster.h
jsimd.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimd.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmaster.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmerge.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jsimd.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimd.h
jconfigint.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jconfigint.h
jdmrgext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmrgext.c
jdmrgext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmrgext.c
jdmrgext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmrgext.c
jdmrgext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmrgext.c
jdmrgext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmrgext.c
jdmrgext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmrgext.c
jdmrgext.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmrgext.c
jdmrg565.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmrg565.c
jdmrg565.c
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmrg565.c

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmrg565.c

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmrgext.c

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdphuff.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jdhuff.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdhuff.h
limits.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdpostct.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdsample.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jdsample.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdsample.h
jsimd.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimd.h
jpegcomp.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpegcomp.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdsample.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdtrans.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jerror.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jversion.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jversion.h
jerror.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jerror.h
windows.h
-
jerror.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jerror.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jerror.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jfdctflt.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jdct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdct.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jfdctfst.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jdct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdct.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jfdctint.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jdct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdct.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jidctflt.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jdct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdct.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jidctfst.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jdct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdct.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jidctint.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jdct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdct.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jidctred.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jdct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdct.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jconfig.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jconfig.h
stddef.h
-
stdlib.h
-
sys/types.h
-
stdio.h
-
strings.h
-
string.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jmemmgr.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jmemsys.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jmemsys.h
stdint.h
-
limits.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jmemnobs.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jmemsys.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jmemsys.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jmemsys.h
Files.h
-

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jmorecfg.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeg_nbits_table.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpegcomp.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpegint.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jconfig.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jconfig.h
jmorecfg.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jmorecfg.h
jpegint.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpegint.h
jerror.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jerror.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jquant1.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jquant2.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimd.h
jchuff.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jchuff.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimd_none.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jsimd.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimd.h
jdct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdct.h
jsimddct.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimddct.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimddct.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jstdhuff.c

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jutils.c
jinclude.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jinclude.h
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jversion.h

