# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

# Include any dependencies generated for this target.
include 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/depend.make

# Include the progress variables for this target.
include 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/progress.make

# Include the compile flags for this target's objects.
include 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapimin.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapimin.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapimin.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcapimin.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapimin.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jcapimin.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcapimin.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapimin.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jcapimin.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcapimin.c > CMakeFiles\libjpeg-turbo.dir\src\jcapimin.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapimin.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jcapimin.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcapimin.c -o CMakeFiles\libjpeg-turbo.dir\src\jcapimin.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapistd.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapistd.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapistd.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcapistd.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapistd.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jcapistd.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcapistd.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapistd.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jcapistd.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcapistd.c > CMakeFiles\libjpeg-turbo.dir\src\jcapistd.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapistd.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jcapistd.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcapistd.c -o CMakeFiles\libjpeg-turbo.dir\src\jcapistd.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccoefct.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccoefct.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccoefct.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jccoefct.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccoefct.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jccoefct.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jccoefct.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccoefct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jccoefct.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jccoefct.c > CMakeFiles\libjpeg-turbo.dir\src\jccoefct.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccoefct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jccoefct.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jccoefct.c -o CMakeFiles\libjpeg-turbo.dir\src\jccoefct.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccolor.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccolor.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccolor.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jccolor.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccolor.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jccolor.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jccolor.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccolor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jccolor.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jccolor.c > CMakeFiles\libjpeg-turbo.dir\src\jccolor.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccolor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jccolor.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jccolor.c -o CMakeFiles\libjpeg-turbo.dir\src\jccolor.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcdctmgr.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcdctmgr.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcdctmgr.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcdctmgr.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcdctmgr.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jcdctmgr.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcdctmgr.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcdctmgr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jcdctmgr.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcdctmgr.c > CMakeFiles\libjpeg-turbo.dir\src\jcdctmgr.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcdctmgr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jcdctmgr.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcdctmgr.c -o CMakeFiles\libjpeg-turbo.dir\src\jcdctmgr.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jchuff.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jchuff.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jchuff.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jchuff.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jchuff.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jchuff.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jchuff.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jchuff.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jchuff.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jchuff.c > CMakeFiles\libjpeg-turbo.dir\src\jchuff.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jchuff.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jchuff.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jchuff.c -o CMakeFiles\libjpeg-turbo.dir\src\jchuff.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcicc.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcicc.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcicc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcicc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcicc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jcicc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcicc.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcicc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jcicc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcicc.c > CMakeFiles\libjpeg-turbo.dir\src\jcicc.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcicc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jcicc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcicc.c -o CMakeFiles\libjpeg-turbo.dir\src\jcicc.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcinit.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcinit.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcinit.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcinit.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcinit.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jcinit.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcinit.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcinit.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jcinit.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcinit.c > CMakeFiles\libjpeg-turbo.dir\src\jcinit.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcinit.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jcinit.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcinit.c -o CMakeFiles\libjpeg-turbo.dir\src\jcinit.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmainct.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmainct.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmainct.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcmainct.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmainct.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jcmainct.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcmainct.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmainct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jcmainct.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcmainct.c > CMakeFiles\libjpeg-turbo.dir\src\jcmainct.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmainct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jcmainct.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcmainct.c -o CMakeFiles\libjpeg-turbo.dir\src\jcmainct.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmarker.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmarker.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmarker.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcmarker.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmarker.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jcmarker.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcmarker.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmarker.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jcmarker.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcmarker.c > CMakeFiles\libjpeg-turbo.dir\src\jcmarker.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmarker.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jcmarker.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcmarker.c -o CMakeFiles\libjpeg-turbo.dir\src\jcmarker.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmaster.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmaster.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmaster.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcmaster.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmaster.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jcmaster.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcmaster.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmaster.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jcmaster.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcmaster.c > CMakeFiles\libjpeg-turbo.dir\src\jcmaster.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmaster.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jcmaster.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcmaster.c -o CMakeFiles\libjpeg-turbo.dir\src\jcmaster.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcomapi.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcomapi.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcomapi.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcomapi.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcomapi.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jcomapi.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcomapi.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcomapi.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jcomapi.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcomapi.c > CMakeFiles\libjpeg-turbo.dir\src\jcomapi.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcomapi.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jcomapi.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcomapi.c -o CMakeFiles\libjpeg-turbo.dir\src\jcomapi.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcparam.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcparam.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcparam.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcparam.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcparam.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jcparam.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcparam.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcparam.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jcparam.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcparam.c > CMakeFiles\libjpeg-turbo.dir\src\jcparam.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcparam.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jcparam.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcparam.c -o CMakeFiles\libjpeg-turbo.dir\src\jcparam.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcphuff.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcphuff.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcphuff.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcphuff.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcphuff.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jcphuff.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcphuff.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcphuff.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jcphuff.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcphuff.c > CMakeFiles\libjpeg-turbo.dir\src\jcphuff.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcphuff.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jcphuff.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcphuff.c -o CMakeFiles\libjpeg-turbo.dir\src\jcphuff.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcprepct.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcprepct.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcprepct.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcprepct.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcprepct.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jcprepct.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcprepct.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcprepct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jcprepct.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcprepct.c > CMakeFiles\libjpeg-turbo.dir\src\jcprepct.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcprepct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jcprepct.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcprepct.c -o CMakeFiles\libjpeg-turbo.dir\src\jcprepct.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcsample.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcsample.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcsample.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcsample.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcsample.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jcsample.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcsample.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcsample.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jcsample.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcsample.c > CMakeFiles\libjpeg-turbo.dir\src\jcsample.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcsample.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jcsample.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcsample.c -o CMakeFiles\libjpeg-turbo.dir\src\jcsample.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jctrans.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jctrans.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jctrans.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jctrans.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jctrans.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jctrans.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jctrans.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jctrans.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jctrans.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jctrans.c > CMakeFiles\libjpeg-turbo.dir\src\jctrans.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jctrans.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jctrans.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jctrans.c -o CMakeFiles\libjpeg-turbo.dir\src\jctrans.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapimin.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapimin.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapimin.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdapimin.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapimin.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdapimin.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdapimin.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapimin.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdapimin.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdapimin.c > CMakeFiles\libjpeg-turbo.dir\src\jdapimin.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapimin.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdapimin.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdapimin.c -o CMakeFiles\libjpeg-turbo.dir\src\jdapimin.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapistd.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapistd.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapistd.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdapistd.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapistd.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdapistd.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdapistd.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapistd.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdapistd.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdapistd.c > CMakeFiles\libjpeg-turbo.dir\src\jdapistd.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapistd.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdapistd.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdapistd.c -o CMakeFiles\libjpeg-turbo.dir\src\jdapistd.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatadst.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatadst.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatadst.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdatadst.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatadst.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdatadst.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdatadst.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatadst.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdatadst.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdatadst.c > CMakeFiles\libjpeg-turbo.dir\src\jdatadst.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatadst.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdatadst.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdatadst.c -o CMakeFiles\libjpeg-turbo.dir\src\jdatadst.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatasrc.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatasrc.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatasrc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdatasrc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatasrc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdatasrc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdatasrc.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatasrc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdatasrc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdatasrc.c > CMakeFiles\libjpeg-turbo.dir\src\jdatasrc.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatasrc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdatasrc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdatasrc.c -o CMakeFiles\libjpeg-turbo.dir\src\jdatasrc.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcoefct.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcoefct.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcoefct.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcoefct.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcoefct.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdcoefct.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdcoefct.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcoefct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdcoefct.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdcoefct.c > CMakeFiles\libjpeg-turbo.dir\src\jdcoefct.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcoefct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdcoefct.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdcoefct.c -o CMakeFiles\libjpeg-turbo.dir\src\jdcoefct.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcolor.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcolor.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcolor.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcolor.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcolor.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdcolor.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdcolor.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcolor.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdcolor.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdcolor.c > CMakeFiles\libjpeg-turbo.dir\src\jdcolor.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcolor.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdcolor.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdcolor.c -o CMakeFiles\libjpeg-turbo.dir\src\jdcolor.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jddctmgr.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jddctmgr.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jddctmgr.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jddctmgr.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jddctmgr.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jddctmgr.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jddctmgr.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jddctmgr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jddctmgr.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jddctmgr.c > CMakeFiles\libjpeg-turbo.dir\src\jddctmgr.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jddctmgr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jddctmgr.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jddctmgr.c -o CMakeFiles\libjpeg-turbo.dir\src\jddctmgr.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdhuff.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdhuff.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdhuff.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdhuff.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdhuff.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdhuff.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdhuff.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdhuff.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdhuff.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdhuff.c > CMakeFiles\libjpeg-turbo.dir\src\jdhuff.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdhuff.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdhuff.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdhuff.c -o CMakeFiles\libjpeg-turbo.dir\src\jdhuff.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdicc.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdicc.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdicc.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdicc.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdicc.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdicc.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdicc.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdicc.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdicc.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdicc.c > CMakeFiles\libjpeg-turbo.dir\src\jdicc.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdicc.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdicc.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdicc.c -o CMakeFiles\libjpeg-turbo.dir\src\jdicc.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdinput.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdinput.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdinput.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdinput.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdinput.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdinput.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdinput.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdinput.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdinput.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdinput.c > CMakeFiles\libjpeg-turbo.dir\src\jdinput.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdinput.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdinput.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdinput.c -o CMakeFiles\libjpeg-turbo.dir\src\jdinput.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmainct.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmainct.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmainct.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmainct.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmainct.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdmainct.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdmainct.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmainct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdmainct.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdmainct.c > CMakeFiles\libjpeg-turbo.dir\src\jdmainct.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmainct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdmainct.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdmainct.c -o CMakeFiles\libjpeg-turbo.dir\src\jdmainct.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmarker.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmarker.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmarker.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmarker.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmarker.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdmarker.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdmarker.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmarker.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdmarker.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdmarker.c > CMakeFiles\libjpeg-turbo.dir\src\jdmarker.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmarker.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdmarker.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdmarker.c -o CMakeFiles\libjpeg-turbo.dir\src\jdmarker.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmaster.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmaster.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmaster.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmaster.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmaster.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdmaster.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdmaster.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmaster.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdmaster.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdmaster.c > CMakeFiles\libjpeg-turbo.dir\src\jdmaster.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmaster.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdmaster.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdmaster.c -o CMakeFiles\libjpeg-turbo.dir\src\jdmaster.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmerge.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmerge.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmerge.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmerge.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmerge.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdmerge.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdmerge.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmerge.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdmerge.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdmerge.c > CMakeFiles\libjpeg-turbo.dir\src\jdmerge.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmerge.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdmerge.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdmerge.c -o CMakeFiles\libjpeg-turbo.dir\src\jdmerge.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdphuff.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdphuff.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdphuff.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdphuff.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdphuff.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdphuff.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdphuff.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdphuff.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdphuff.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdphuff.c > CMakeFiles\libjpeg-turbo.dir\src\jdphuff.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdphuff.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdphuff.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdphuff.c -o CMakeFiles\libjpeg-turbo.dir\src\jdphuff.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdpostct.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdpostct.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdpostct.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdpostct.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdpostct.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdpostct.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdpostct.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdpostct.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdpostct.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdpostct.c > CMakeFiles\libjpeg-turbo.dir\src\jdpostct.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdpostct.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdpostct.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdpostct.c -o CMakeFiles\libjpeg-turbo.dir\src\jdpostct.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdsample.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdsample.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdsample.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdsample.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdsample.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdsample.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdsample.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdsample.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdsample.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdsample.c > CMakeFiles\libjpeg-turbo.dir\src\jdsample.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdsample.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdsample.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdsample.c -o CMakeFiles\libjpeg-turbo.dir\src\jdsample.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdtrans.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdtrans.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdtrans.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdtrans.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdtrans.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdtrans.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdtrans.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdtrans.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdtrans.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdtrans.c > CMakeFiles\libjpeg-turbo.dir\src\jdtrans.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdtrans.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdtrans.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdtrans.c -o CMakeFiles\libjpeg-turbo.dir\src\jdtrans.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jerror.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jerror.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jerror.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jerror.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jerror.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jerror.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jerror.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jerror.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jerror.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jerror.c > CMakeFiles\libjpeg-turbo.dir\src\jerror.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jerror.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jerror.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jerror.c -o CMakeFiles\libjpeg-turbo.dir\src\jerror.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctflt.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctflt.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctflt.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jfdctflt.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctflt.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jfdctflt.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jfdctflt.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctflt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jfdctflt.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jfdctflt.c > CMakeFiles\libjpeg-turbo.dir\src\jfdctflt.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctflt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jfdctflt.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jfdctflt.c -o CMakeFiles\libjpeg-turbo.dir\src\jfdctflt.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctfst.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctfst.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctfst.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jfdctfst.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctfst.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jfdctfst.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jfdctfst.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctfst.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jfdctfst.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jfdctfst.c > CMakeFiles\libjpeg-turbo.dir\src\jfdctfst.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctfst.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jfdctfst.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jfdctfst.c -o CMakeFiles\libjpeg-turbo.dir\src\jfdctfst.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctint.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctint.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctint.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jfdctint.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctint.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jfdctint.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jfdctint.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctint.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jfdctint.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jfdctint.c > CMakeFiles\libjpeg-turbo.dir\src\jfdctint.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctint.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jfdctint.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jfdctint.c -o CMakeFiles\libjpeg-turbo.dir\src\jfdctint.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctflt.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctflt.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctflt.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jidctflt.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctflt.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jidctflt.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jidctflt.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctflt.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jidctflt.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jidctflt.c > CMakeFiles\libjpeg-turbo.dir\src\jidctflt.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctflt.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jidctflt.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jidctflt.c -o CMakeFiles\libjpeg-turbo.dir\src\jidctflt.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctfst.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctfst.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctfst.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jidctfst.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctfst.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jidctfst.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jidctfst.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctfst.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jidctfst.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jidctfst.c > CMakeFiles\libjpeg-turbo.dir\src\jidctfst.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctfst.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jidctfst.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jidctfst.c -o CMakeFiles\libjpeg-turbo.dir\src\jidctfst.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctint.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctint.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctint.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jidctint.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctint.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jidctint.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jidctint.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctint.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jidctint.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jidctint.c > CMakeFiles\libjpeg-turbo.dir\src\jidctint.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctint.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jidctint.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jidctint.c -o CMakeFiles\libjpeg-turbo.dir\src\jidctint.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctred.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctred.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctred.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jidctred.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctred.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jidctred.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jidctred.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctred.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jidctred.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jidctred.c > CMakeFiles\libjpeg-turbo.dir\src\jidctred.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctred.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jidctred.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jidctred.c -o CMakeFiles\libjpeg-turbo.dir\src\jidctred.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant1.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant1.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant1.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jquant1.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_44) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant1.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jquant1.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jquant1.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant1.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jquant1.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jquant1.c > CMakeFiles\libjpeg-turbo.dir\src\jquant1.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant1.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jquant1.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jquant1.c -o CMakeFiles\libjpeg-turbo.dir\src\jquant1.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant2.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant2.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant2.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jquant2.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_45) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant2.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jquant2.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jquant2.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant2.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jquant2.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jquant2.c > CMakeFiles\libjpeg-turbo.dir\src\jquant2.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant2.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jquant2.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jquant2.c -o CMakeFiles\libjpeg-turbo.dir\src\jquant2.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jutils.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jutils.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jutils.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jutils.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_46) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jutils.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jutils.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jutils.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jutils.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jutils.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jutils.c > CMakeFiles\libjpeg-turbo.dir\src\jutils.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jutils.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jutils.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jutils.c -o CMakeFiles\libjpeg-turbo.dir\src\jutils.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemmgr.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemmgr.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemmgr.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jmemmgr.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_47) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemmgr.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jmemmgr.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jmemmgr.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemmgr.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jmemmgr.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jmemmgr.c > CMakeFiles\libjpeg-turbo.dir\src\jmemmgr.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemmgr.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jmemmgr.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jmemmgr.c -o CMakeFiles\libjpeg-turbo.dir\src\jmemmgr.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemnobs.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemnobs.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemnobs.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jmemnobs.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_48) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemnobs.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jmemnobs.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jmemnobs.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemnobs.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jmemnobs.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jmemnobs.c > CMakeFiles\libjpeg-turbo.dir\src\jmemnobs.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemnobs.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jmemnobs.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jmemnobs.c -o CMakeFiles\libjpeg-turbo.dir\src\jmemnobs.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jaricom.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jaricom.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jaricom.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jaricom.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_49) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jaricom.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jaricom.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jaricom.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jaricom.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jaricom.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jaricom.c > CMakeFiles\libjpeg-turbo.dir\src\jaricom.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jaricom.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jaricom.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jaricom.c -o CMakeFiles\libjpeg-turbo.dir\src\jaricom.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcarith.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcarith.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcarith.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcarith.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_50) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcarith.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jcarith.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcarith.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcarith.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jcarith.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcarith.c > CMakeFiles\libjpeg-turbo.dir\src\jcarith.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcarith.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jcarith.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jcarith.c -o CMakeFiles\libjpeg-turbo.dir\src\jcarith.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdarith.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdarith.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdarith.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdarith.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_51) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdarith.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jdarith.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdarith.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdarith.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jdarith.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdarith.c > CMakeFiles\libjpeg-turbo.dir\src\jdarith.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdarith.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jdarith.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jdarith.c -o CMakeFiles\libjpeg-turbo.dir\src\jdarith.c.s

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jsimd_none.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/flags.make
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jsimd_none.c.obj: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/includes_C.rsp
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jsimd_none.c.obj: D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimd_none.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_52) "Building C object 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jsimd_none.c.obj"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles\libjpeg-turbo.dir\src\jsimd_none.c.obj   -c D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jsimd_none.c

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jsimd_none.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/libjpeg-turbo.dir/src/jsimd_none.c.i"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jsimd_none.c > CMakeFiles\libjpeg-turbo.dir\src\jsimd_none.c.i

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jsimd_none.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/libjpeg-turbo.dir/src/jsimd_none.c.s"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo\src\jsimd_none.c -o CMakeFiles\libjpeg-turbo.dir\src\jsimd_none.c.s

# Object files for target libjpeg-turbo
libjpeg__turbo_OBJECTS = \
"CMakeFiles/libjpeg-turbo.dir/src/jcapimin.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jcapistd.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jccoefct.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jccolor.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jcdctmgr.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jchuff.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jcicc.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jcinit.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jcmainct.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jcmarker.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jcmaster.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jcomapi.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jcparam.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jcphuff.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jcprepct.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jcsample.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jctrans.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdapimin.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdapistd.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdatadst.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdatasrc.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdcoefct.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdcolor.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jddctmgr.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdhuff.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdicc.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdinput.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdmainct.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdmarker.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdmaster.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdmerge.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdphuff.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdpostct.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdsample.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdtrans.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jerror.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jfdctflt.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jfdctfst.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jfdctint.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jidctflt.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jidctfst.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jidctint.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jidctred.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jquant1.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jquant2.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jutils.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jmemmgr.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jmemnobs.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jaricom.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jcarith.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jdarith.c.obj" \
"CMakeFiles/libjpeg-turbo.dir/src/jsimd_none.c.obj"

# External object files for target libjpeg-turbo
libjpeg__turbo_EXTERNAL_OBJECTS =

3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapimin.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapistd.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccoefct.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccolor.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcdctmgr.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jchuff.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcicc.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcinit.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmainct.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmarker.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmaster.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcomapi.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcparam.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcphuff.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcprepct.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcsample.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jctrans.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapimin.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapistd.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatadst.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatasrc.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcoefct.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcolor.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jddctmgr.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdhuff.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdicc.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdinput.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmainct.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmarker.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmaster.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmerge.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdphuff.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdpostct.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdsample.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdtrans.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jerror.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctflt.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctfst.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctint.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctflt.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctfst.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctint.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctred.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant1.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant2.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jutils.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemmgr.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemnobs.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jaricom.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcarith.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdarith.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jsimd_none.c.obj
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/build.make
3rdparty/lib/liblibjpeg-turbo.a: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_53) "Linking C static library ..\lib\liblibjpeg-turbo.a"
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && $(CMAKE_COMMAND) -P CMakeFiles\libjpeg-turbo.dir\cmake_clean_target.cmake
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\libjpeg-turbo.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/build: 3rdparty/lib/liblibjpeg-turbo.a

.PHONY : 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/build

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/clean:
	cd /d D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo && $(CMAKE_COMMAND) -P CMakeFiles\libjpeg-turbo.dir\cmake_clean.cmake
.PHONY : 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/clean

3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\unet\opencv\opencv\sources D:\unet\opencv\opencv\sources\3rdparty\libjpeg-turbo D:\unet\opencv\opencv\mingw_build D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo D:\unet\opencv\opencv\mingw_build\3rdparty\libjpeg-turbo\CMakeFiles\libjpeg-turbo.dir\DependInfo.cmake --color=$(COLOR)
.PHONY : 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/depend

