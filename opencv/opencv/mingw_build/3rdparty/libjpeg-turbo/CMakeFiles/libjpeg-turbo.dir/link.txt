E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe qc ..\lib\liblibjpeg-turbo.a  CMakeFiles/libjpeg-turbo.dir/src/jcapimin.c.obj CMakeFiles/libjpeg-turbo.dir/src/jcapistd.c.obj CMakeFiles/libjpeg-turbo.dir/src/jccoefct.c.obj CMakeFiles/libjpeg-turbo.dir/src/jccolor.c.obj CMakeFiles/libjpeg-turbo.dir/src/jcdctmgr.c.obj CMakeFiles/libjpeg-turbo.dir/src/jchuff.c.obj CMakeFiles/libjpeg-turbo.dir/src/jcicc.c.obj CMakeFiles/libjpeg-turbo.dir/src/jcinit.c.obj CMakeFiles/libjpeg-turbo.dir/src/jcmainct.c.obj CMakeFiles/libjpeg-turbo.dir/src/jcmarker.c.obj CMakeFiles/libjpeg-turbo.dir/src/jcmaster.c.obj CMakeFiles/libjpeg-turbo.dir/src/jcomapi.c.obj CMakeFiles/libjpeg-turbo.dir/src/jcparam.c.obj CMakeFiles/libjpeg-turbo.dir/src/jcphuff.c.obj CMakeFiles/libjpeg-turbo.dir/src/jcprepct.c.obj CMakeFiles/libjpeg-turbo.dir/src/jcsample.c.obj CMakeFiles/libjpeg-turbo.dir/src/jctrans.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdapimin.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdapistd.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdatadst.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdatasrc.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdcoefct.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdcolor.c.obj CMakeFiles/libjpeg-turbo.dir/src/jddctmgr.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdhuff.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdicc.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdinput.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdmainct.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdmarker.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdmaster.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdmerge.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdphuff.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdpostct.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdsample.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdtrans.c.obj CMakeFiles/libjpeg-turbo.dir/src/jerror.c.obj CMakeFiles/libjpeg-turbo.dir/src/jfdctflt.c.obj CMakeFiles/libjpeg-turbo.dir/src/jfdctfst.c.obj CMakeFiles/libjpeg-turbo.dir/src/jfdctint.c.obj CMakeFiles/libjpeg-turbo.dir/src/jidctflt.c.obj CMakeFiles/libjpeg-turbo.dir/src/jidctfst.c.obj CMakeFiles/libjpeg-turbo.dir/src/jidctint.c.obj CMakeFiles/libjpeg-turbo.dir/src/jidctred.c.obj CMakeFiles/libjpeg-turbo.dir/src/jquant1.c.obj CMakeFiles/libjpeg-turbo.dir/src/jquant2.c.obj CMakeFiles/libjpeg-turbo.dir/src/jutils.c.obj CMakeFiles/libjpeg-turbo.dir/src/jmemmgr.c.obj CMakeFiles/libjpeg-turbo.dir/src/jmemnobs.c.obj CMakeFiles/libjpeg-turbo.dir/src/jaricom.c.obj CMakeFiles/libjpeg-turbo.dir/src/jcarith.c.obj CMakeFiles/libjpeg-turbo.dir/src/jdarith.c.obj CMakeFiles/libjpeg-turbo.dir/src/jsimd_none.c.obj
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ranlib.exe ..\lib\liblibjpeg-turbo.a
