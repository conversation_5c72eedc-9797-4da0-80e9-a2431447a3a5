# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "C"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_C
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jaricom.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jaricom.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcapimin.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapimin.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcapistd.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcapistd.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcarith.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcarith.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jccoefct.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccoefct.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jccolor.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jccolor.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcdctmgr.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcdctmgr.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jchuff.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jchuff.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcicc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcicc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcinit.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcinit.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcmainct.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmainct.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcmarker.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmarker.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcmaster.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcmaster.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcomapi.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcomapi.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcparam.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcparam.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcphuff.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcphuff.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcprepct.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcprepct.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jcsample.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jcsample.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jctrans.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jctrans.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdapimin.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapimin.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdapistd.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdapistd.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdarith.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdarith.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdatadst.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatadst.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdatasrc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdatasrc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcoefct.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcoefct.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdcolor.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdcolor.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jddctmgr.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jddctmgr.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdhuff.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdhuff.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdicc.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdicc.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdinput.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdinput.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmainct.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmainct.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmarker.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmarker.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmaster.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmaster.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdmerge.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdmerge.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdphuff.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdphuff.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdpostct.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdpostct.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdsample.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdsample.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jdtrans.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jdtrans.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jerror.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jerror.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jfdctflt.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctflt.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jfdctfst.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctfst.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jfdctint.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jfdctint.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jidctflt.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctflt.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jidctfst.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctfst.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jidctint.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctint.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jidctred.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jidctred.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jmemmgr.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemmgr.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jmemnobs.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jmemnobs.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jquant1.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant1.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jquant2.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jquant2.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jsimd_none.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jsimd_none.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jutils.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/src/jutils.c.obj"
  )
set(CMAKE_C_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_C
  "_WIN32_WINNT=0x0601"
  )

# The include file search paths:
set(CMAKE_C_TARGET_INCLUDE_PATH
  "3rdparty/libjpeg-turbo"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src"
  "."
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
