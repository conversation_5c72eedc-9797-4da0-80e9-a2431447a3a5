#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

3rdparty/libtiff/tif_config.h

3rdparty/libtiff/tiffconf.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_dir.h
tiff.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiff.h
tiffio.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffio.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_stream.cxx
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
iostream
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiff.h
tiffconf.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffconf.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffio.h
tiff.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiff.h
tiffvers.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffvers.h
windows.h
-
stdio.h
-
stdarg.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
tif_config.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_config.h
fcntl.h
-
sys/types.h
-
string.h
-
assert.h
-
search.h
-
tiffio.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffio.h
tif_dir.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_dir.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffvers.h

