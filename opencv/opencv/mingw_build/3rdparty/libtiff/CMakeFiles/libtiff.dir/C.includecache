#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

3rdparty/libjpeg-turbo/jconfig.h

3rdparty/libtiff/tif_config.h

3rdparty/libtiff/tiffconf.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jerror.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jmorecfg.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpegint.h

D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpeglib.h
jconfig.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jconfig.h
jmorecfg.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jmorecfg.h
jpegint.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jpegint.h
jerror.h
D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src/jerror.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/t4.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_aux.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
tif_predict.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_predict.h
math.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_close.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
string.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_codec.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_color.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
math.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_compress.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_dir.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
float.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_dir.h
tiff.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiff.h
tiffio.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffio.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_dirinfo.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
stdlib.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_dirread.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
float.h
-
stdlib.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_dirwrite.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
float.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_dumpmode.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_error.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_extension.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_fax3.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
tif_fax3.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_fax3.h
t4.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/t4.h
stdio.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_fax3.h
tiff.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiff.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_fax3sm.c
tiff.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiff.h
tif_fax3.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_fax3.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_flush.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_getimage.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
stdio.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_jbig.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
jbig.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/jbig.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_jpeg.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
stdlib.h
-
setjmp.h
-
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/jpeglib.h
jerror.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/jerror.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_jpeg_12.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
tif_jpeg.c
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_jpeg.c

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_luv.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
stdio.h
-
stdlib.h
-
math.h
-
uvcode.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/uvcode.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_lzma.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
tif_predict.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_predict.h
lzma.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/lzma.h
stdio.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_lzw.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
tif_predict.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_predict.h
stdio.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_next.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_ojpeg.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
setjmp.h
-
jpeglib.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/jpeglib.h
jerror.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/jerror.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_open.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_packbits.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
stdio.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_pixarlog.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
tif_predict.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_predict.h
zlib.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/zlib.h
stdio.h
-
stdlib.h
-
math.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_predict.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
tif_predict.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_predict.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_predict.h
tiffio.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffio.h
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_print.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
stdio.h
-
ctype.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_read.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
stdio.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_strip.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_swab.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_thunder.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
assert.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_tile.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_version.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_warning.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_webp.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
webp/decode.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/webp/decode.h
webp/encode.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/webp/encode.h
stdio.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_win32.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
windows.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_write.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
stdio.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_zip.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
tif_predict.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_predict.h
zlib.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/zlib.h
stdio.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_zstd.c
tiffiop.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
tif_predict.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_predict.h
zstd.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/zstd.h
stdio.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiff.h
tiffconf.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffconf.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffio.h
tiff.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiff.h
tiffvers.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffvers.h
windows.h
-
stdio.h
-
stdarg.h
-

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffiop.h
tif_config.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_config.h
fcntl.h
-
sys/types.h
-
string.h
-
assert.h
-
search.h
-
tiffio.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffio.h
tif_dir.h
D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_dir.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/tiffvers.h

D:/unet/opencv/opencv/sources/3rdparty/libtiff/uvcode.h

D:/unet/opencv/opencv/sources/3rdparty/zlib/zconf.h
stddef.h
-
windows.h
-
limits.h
-
sys/types.h
-
stdarg.h
-
stddef.h
-
unistd.h
-
unixio.h
-

D:/unet/opencv/opencv/sources/3rdparty/zlib/zlib.h
zconf.h
D:/unet/opencv/opencv/sources/3rdparty/zlib/zconf.h

