# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "C"
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_C
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_aux.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_aux.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_close.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_close.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_codec.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_codec.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_color.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_color.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_compress.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_compress.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_dir.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dir.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_dirinfo.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dirinfo.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_dirread.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dirread.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_dirwrite.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dirwrite.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_dumpmode.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dumpmode.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_error.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_error.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_extension.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_extension.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_fax3.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_fax3.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_fax3sm.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_fax3sm.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_flush.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_flush.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_getimage.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_getimage.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_jbig.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_jbig.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_jpeg.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_jpeg.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_jpeg_12.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_jpeg_12.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_luv.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_luv.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_lzma.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_lzma.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_lzw.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_lzw.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_next.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_next.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_ojpeg.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_ojpeg.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_open.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_open.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_packbits.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_packbits.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_pixarlog.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_pixarlog.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_predict.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_predict.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_print.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_print.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_read.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_read.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_strip.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_strip.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_swab.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_swab.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_thunder.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_thunder.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_tile.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_tile.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_version.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_version.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_warning.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_warning.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_webp.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_webp.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_win32.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_win32.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_write.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_write.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_zip.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_zip.c.obj"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_zstd.c" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_zstd.c.obj"
  )
set(CMAKE_C_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_C
  "_FILE_OFFSET_BITS=64"
  "_WIN32_WINNT=0x0601"
  )

# The include file search paths:
set(CMAKE_C_TARGET_INCLUDE_PATH
  "3rdparty/zlib"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib"
  "3rdparty/libtiff"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff"
  "3rdparty/libjpeg-turbo"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src"
  "."
  )
set(CMAKE_DEPENDS_CHECK_CXX
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff/tif_stream.cxx" "D:/unet/opencv/opencv/mingw_build/3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_stream.cxx.obj"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "_FILE_OFFSET_BITS=64"
  "_WIN32_WINNT=0x0601"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "3rdparty/zlib"
  "D:/unet/opencv/opencv/sources/3rdparty/zlib"
  "3rdparty/libtiff"
  "D:/unet/opencv/opencv/sources/3rdparty/libtiff"
  "3rdparty/libjpeg-turbo"
  "D:/unet/opencv/opencv/sources/3rdparty/libjpeg-turbo/src"
  "."
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "D:/unet/opencv/opencv/mingw_build/3rdparty/zlib/CMakeFiles/zlib.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
