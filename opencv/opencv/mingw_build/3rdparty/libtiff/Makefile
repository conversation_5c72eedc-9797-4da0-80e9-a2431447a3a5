# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package

.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /d D:\unet\opencv\opencv\mingw_build && "C:\Program Files\CMake\bin\cpack.exe" --config ./CPackSourceConfig.cmake D:/unet/opencv/opencv/mingw_build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source

.PHONY : package_source/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	"C:\Program Files\CMake\bin\ctest.exe" --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test

.PHONY : test/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	"C:\Program Files\CMake\bin\cmake.exe" -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"dev\" \"libs\" \"licenses\" \"python\" \"scripts\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components

.PHONY : list_install_components/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles D:\unet\opencv\opencv\mingw_build\3rdparty\libtiff\CMakeFiles\progress.marks
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libtiff/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libtiff/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libtiff/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libtiff/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
3rdparty/libtiff/CMakeFiles/libtiff.dir/rule:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f CMakeFiles\Makefile2 3rdparty/libtiff/CMakeFiles/libtiff.dir/rule
.PHONY : 3rdparty/libtiff/CMakeFiles/libtiff.dir/rule

# Convenience name for target.
libtiff: 3rdparty/libtiff/CMakeFiles/libtiff.dir/rule

.PHONY : libtiff

# fast build rule for target.
libtiff/fast:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/build
.PHONY : libtiff/fast

tif_aux.obj: tif_aux.c.obj

.PHONY : tif_aux.obj

# target to build an object file
tif_aux.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_aux.c.obj
.PHONY : tif_aux.c.obj

tif_aux.i: tif_aux.c.i

.PHONY : tif_aux.i

# target to preprocess a source file
tif_aux.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_aux.c.i
.PHONY : tif_aux.c.i

tif_aux.s: tif_aux.c.s

.PHONY : tif_aux.s

# target to generate assembly for a file
tif_aux.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_aux.c.s
.PHONY : tif_aux.c.s

tif_close.obj: tif_close.c.obj

.PHONY : tif_close.obj

# target to build an object file
tif_close.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_close.c.obj
.PHONY : tif_close.c.obj

tif_close.i: tif_close.c.i

.PHONY : tif_close.i

# target to preprocess a source file
tif_close.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_close.c.i
.PHONY : tif_close.c.i

tif_close.s: tif_close.c.s

.PHONY : tif_close.s

# target to generate assembly for a file
tif_close.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_close.c.s
.PHONY : tif_close.c.s

tif_codec.obj: tif_codec.c.obj

.PHONY : tif_codec.obj

# target to build an object file
tif_codec.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_codec.c.obj
.PHONY : tif_codec.c.obj

tif_codec.i: tif_codec.c.i

.PHONY : tif_codec.i

# target to preprocess a source file
tif_codec.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_codec.c.i
.PHONY : tif_codec.c.i

tif_codec.s: tif_codec.c.s

.PHONY : tif_codec.s

# target to generate assembly for a file
tif_codec.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_codec.c.s
.PHONY : tif_codec.c.s

tif_color.obj: tif_color.c.obj

.PHONY : tif_color.obj

# target to build an object file
tif_color.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_color.c.obj
.PHONY : tif_color.c.obj

tif_color.i: tif_color.c.i

.PHONY : tif_color.i

# target to preprocess a source file
tif_color.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_color.c.i
.PHONY : tif_color.c.i

tif_color.s: tif_color.c.s

.PHONY : tif_color.s

# target to generate assembly for a file
tif_color.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_color.c.s
.PHONY : tif_color.c.s

tif_compress.obj: tif_compress.c.obj

.PHONY : tif_compress.obj

# target to build an object file
tif_compress.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_compress.c.obj
.PHONY : tif_compress.c.obj

tif_compress.i: tif_compress.c.i

.PHONY : tif_compress.i

# target to preprocess a source file
tif_compress.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_compress.c.i
.PHONY : tif_compress.c.i

tif_compress.s: tif_compress.c.s

.PHONY : tif_compress.s

# target to generate assembly for a file
tif_compress.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_compress.c.s
.PHONY : tif_compress.c.s

tif_dir.obj: tif_dir.c.obj

.PHONY : tif_dir.obj

# target to build an object file
tif_dir.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dir.c.obj
.PHONY : tif_dir.c.obj

tif_dir.i: tif_dir.c.i

.PHONY : tif_dir.i

# target to preprocess a source file
tif_dir.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dir.c.i
.PHONY : tif_dir.c.i

tif_dir.s: tif_dir.c.s

.PHONY : tif_dir.s

# target to generate assembly for a file
tif_dir.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dir.c.s
.PHONY : tif_dir.c.s

tif_dirinfo.obj: tif_dirinfo.c.obj

.PHONY : tif_dirinfo.obj

# target to build an object file
tif_dirinfo.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dirinfo.c.obj
.PHONY : tif_dirinfo.c.obj

tif_dirinfo.i: tif_dirinfo.c.i

.PHONY : tif_dirinfo.i

# target to preprocess a source file
tif_dirinfo.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dirinfo.c.i
.PHONY : tif_dirinfo.c.i

tif_dirinfo.s: tif_dirinfo.c.s

.PHONY : tif_dirinfo.s

# target to generate assembly for a file
tif_dirinfo.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dirinfo.c.s
.PHONY : tif_dirinfo.c.s

tif_dirread.obj: tif_dirread.c.obj

.PHONY : tif_dirread.obj

# target to build an object file
tif_dirread.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dirread.c.obj
.PHONY : tif_dirread.c.obj

tif_dirread.i: tif_dirread.c.i

.PHONY : tif_dirread.i

# target to preprocess a source file
tif_dirread.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dirread.c.i
.PHONY : tif_dirread.c.i

tif_dirread.s: tif_dirread.c.s

.PHONY : tif_dirread.s

# target to generate assembly for a file
tif_dirread.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dirread.c.s
.PHONY : tif_dirread.c.s

tif_dirwrite.obj: tif_dirwrite.c.obj

.PHONY : tif_dirwrite.obj

# target to build an object file
tif_dirwrite.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dirwrite.c.obj
.PHONY : tif_dirwrite.c.obj

tif_dirwrite.i: tif_dirwrite.c.i

.PHONY : tif_dirwrite.i

# target to preprocess a source file
tif_dirwrite.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dirwrite.c.i
.PHONY : tif_dirwrite.c.i

tif_dirwrite.s: tif_dirwrite.c.s

.PHONY : tif_dirwrite.s

# target to generate assembly for a file
tif_dirwrite.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dirwrite.c.s
.PHONY : tif_dirwrite.c.s

tif_dumpmode.obj: tif_dumpmode.c.obj

.PHONY : tif_dumpmode.obj

# target to build an object file
tif_dumpmode.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dumpmode.c.obj
.PHONY : tif_dumpmode.c.obj

tif_dumpmode.i: tif_dumpmode.c.i

.PHONY : tif_dumpmode.i

# target to preprocess a source file
tif_dumpmode.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dumpmode.c.i
.PHONY : tif_dumpmode.c.i

tif_dumpmode.s: tif_dumpmode.c.s

.PHONY : tif_dumpmode.s

# target to generate assembly for a file
tif_dumpmode.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_dumpmode.c.s
.PHONY : tif_dumpmode.c.s

tif_error.obj: tif_error.c.obj

.PHONY : tif_error.obj

# target to build an object file
tif_error.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_error.c.obj
.PHONY : tif_error.c.obj

tif_error.i: tif_error.c.i

.PHONY : tif_error.i

# target to preprocess a source file
tif_error.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_error.c.i
.PHONY : tif_error.c.i

tif_error.s: tif_error.c.s

.PHONY : tif_error.s

# target to generate assembly for a file
tif_error.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_error.c.s
.PHONY : tif_error.c.s

tif_extension.obj: tif_extension.c.obj

.PHONY : tif_extension.obj

# target to build an object file
tif_extension.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_extension.c.obj
.PHONY : tif_extension.c.obj

tif_extension.i: tif_extension.c.i

.PHONY : tif_extension.i

# target to preprocess a source file
tif_extension.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_extension.c.i
.PHONY : tif_extension.c.i

tif_extension.s: tif_extension.c.s

.PHONY : tif_extension.s

# target to generate assembly for a file
tif_extension.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_extension.c.s
.PHONY : tif_extension.c.s

tif_fax3.obj: tif_fax3.c.obj

.PHONY : tif_fax3.obj

# target to build an object file
tif_fax3.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_fax3.c.obj
.PHONY : tif_fax3.c.obj

tif_fax3.i: tif_fax3.c.i

.PHONY : tif_fax3.i

# target to preprocess a source file
tif_fax3.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_fax3.c.i
.PHONY : tif_fax3.c.i

tif_fax3.s: tif_fax3.c.s

.PHONY : tif_fax3.s

# target to generate assembly for a file
tif_fax3.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_fax3.c.s
.PHONY : tif_fax3.c.s

tif_fax3sm.obj: tif_fax3sm.c.obj

.PHONY : tif_fax3sm.obj

# target to build an object file
tif_fax3sm.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_fax3sm.c.obj
.PHONY : tif_fax3sm.c.obj

tif_fax3sm.i: tif_fax3sm.c.i

.PHONY : tif_fax3sm.i

# target to preprocess a source file
tif_fax3sm.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_fax3sm.c.i
.PHONY : tif_fax3sm.c.i

tif_fax3sm.s: tif_fax3sm.c.s

.PHONY : tif_fax3sm.s

# target to generate assembly for a file
tif_fax3sm.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_fax3sm.c.s
.PHONY : tif_fax3sm.c.s

tif_flush.obj: tif_flush.c.obj

.PHONY : tif_flush.obj

# target to build an object file
tif_flush.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_flush.c.obj
.PHONY : tif_flush.c.obj

tif_flush.i: tif_flush.c.i

.PHONY : tif_flush.i

# target to preprocess a source file
tif_flush.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_flush.c.i
.PHONY : tif_flush.c.i

tif_flush.s: tif_flush.c.s

.PHONY : tif_flush.s

# target to generate assembly for a file
tif_flush.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_flush.c.s
.PHONY : tif_flush.c.s

tif_getimage.obj: tif_getimage.c.obj

.PHONY : tif_getimage.obj

# target to build an object file
tif_getimage.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_getimage.c.obj
.PHONY : tif_getimage.c.obj

tif_getimage.i: tif_getimage.c.i

.PHONY : tif_getimage.i

# target to preprocess a source file
tif_getimage.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_getimage.c.i
.PHONY : tif_getimage.c.i

tif_getimage.s: tif_getimage.c.s

.PHONY : tif_getimage.s

# target to generate assembly for a file
tif_getimage.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_getimage.c.s
.PHONY : tif_getimage.c.s

tif_jbig.obj: tif_jbig.c.obj

.PHONY : tif_jbig.obj

# target to build an object file
tif_jbig.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_jbig.c.obj
.PHONY : tif_jbig.c.obj

tif_jbig.i: tif_jbig.c.i

.PHONY : tif_jbig.i

# target to preprocess a source file
tif_jbig.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_jbig.c.i
.PHONY : tif_jbig.c.i

tif_jbig.s: tif_jbig.c.s

.PHONY : tif_jbig.s

# target to generate assembly for a file
tif_jbig.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_jbig.c.s
.PHONY : tif_jbig.c.s

tif_jpeg.obj: tif_jpeg.c.obj

.PHONY : tif_jpeg.obj

# target to build an object file
tif_jpeg.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_jpeg.c.obj
.PHONY : tif_jpeg.c.obj

tif_jpeg.i: tif_jpeg.c.i

.PHONY : tif_jpeg.i

# target to preprocess a source file
tif_jpeg.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_jpeg.c.i
.PHONY : tif_jpeg.c.i

tif_jpeg.s: tif_jpeg.c.s

.PHONY : tif_jpeg.s

# target to generate assembly for a file
tif_jpeg.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_jpeg.c.s
.PHONY : tif_jpeg.c.s

tif_jpeg_12.obj: tif_jpeg_12.c.obj

.PHONY : tif_jpeg_12.obj

# target to build an object file
tif_jpeg_12.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_jpeg_12.c.obj
.PHONY : tif_jpeg_12.c.obj

tif_jpeg_12.i: tif_jpeg_12.c.i

.PHONY : tif_jpeg_12.i

# target to preprocess a source file
tif_jpeg_12.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_jpeg_12.c.i
.PHONY : tif_jpeg_12.c.i

tif_jpeg_12.s: tif_jpeg_12.c.s

.PHONY : tif_jpeg_12.s

# target to generate assembly for a file
tif_jpeg_12.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_jpeg_12.c.s
.PHONY : tif_jpeg_12.c.s

tif_luv.obj: tif_luv.c.obj

.PHONY : tif_luv.obj

# target to build an object file
tif_luv.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_luv.c.obj
.PHONY : tif_luv.c.obj

tif_luv.i: tif_luv.c.i

.PHONY : tif_luv.i

# target to preprocess a source file
tif_luv.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_luv.c.i
.PHONY : tif_luv.c.i

tif_luv.s: tif_luv.c.s

.PHONY : tif_luv.s

# target to generate assembly for a file
tif_luv.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_luv.c.s
.PHONY : tif_luv.c.s

tif_lzma.obj: tif_lzma.c.obj

.PHONY : tif_lzma.obj

# target to build an object file
tif_lzma.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_lzma.c.obj
.PHONY : tif_lzma.c.obj

tif_lzma.i: tif_lzma.c.i

.PHONY : tif_lzma.i

# target to preprocess a source file
tif_lzma.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_lzma.c.i
.PHONY : tif_lzma.c.i

tif_lzma.s: tif_lzma.c.s

.PHONY : tif_lzma.s

# target to generate assembly for a file
tif_lzma.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_lzma.c.s
.PHONY : tif_lzma.c.s

tif_lzw.obj: tif_lzw.c.obj

.PHONY : tif_lzw.obj

# target to build an object file
tif_lzw.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_lzw.c.obj
.PHONY : tif_lzw.c.obj

tif_lzw.i: tif_lzw.c.i

.PHONY : tif_lzw.i

# target to preprocess a source file
tif_lzw.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_lzw.c.i
.PHONY : tif_lzw.c.i

tif_lzw.s: tif_lzw.c.s

.PHONY : tif_lzw.s

# target to generate assembly for a file
tif_lzw.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_lzw.c.s
.PHONY : tif_lzw.c.s

tif_next.obj: tif_next.c.obj

.PHONY : tif_next.obj

# target to build an object file
tif_next.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_next.c.obj
.PHONY : tif_next.c.obj

tif_next.i: tif_next.c.i

.PHONY : tif_next.i

# target to preprocess a source file
tif_next.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_next.c.i
.PHONY : tif_next.c.i

tif_next.s: tif_next.c.s

.PHONY : tif_next.s

# target to generate assembly for a file
tif_next.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_next.c.s
.PHONY : tif_next.c.s

tif_ojpeg.obj: tif_ojpeg.c.obj

.PHONY : tif_ojpeg.obj

# target to build an object file
tif_ojpeg.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_ojpeg.c.obj
.PHONY : tif_ojpeg.c.obj

tif_ojpeg.i: tif_ojpeg.c.i

.PHONY : tif_ojpeg.i

# target to preprocess a source file
tif_ojpeg.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_ojpeg.c.i
.PHONY : tif_ojpeg.c.i

tif_ojpeg.s: tif_ojpeg.c.s

.PHONY : tif_ojpeg.s

# target to generate assembly for a file
tif_ojpeg.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_ojpeg.c.s
.PHONY : tif_ojpeg.c.s

tif_open.obj: tif_open.c.obj

.PHONY : tif_open.obj

# target to build an object file
tif_open.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_open.c.obj
.PHONY : tif_open.c.obj

tif_open.i: tif_open.c.i

.PHONY : tif_open.i

# target to preprocess a source file
tif_open.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_open.c.i
.PHONY : tif_open.c.i

tif_open.s: tif_open.c.s

.PHONY : tif_open.s

# target to generate assembly for a file
tif_open.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_open.c.s
.PHONY : tif_open.c.s

tif_packbits.obj: tif_packbits.c.obj

.PHONY : tif_packbits.obj

# target to build an object file
tif_packbits.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_packbits.c.obj
.PHONY : tif_packbits.c.obj

tif_packbits.i: tif_packbits.c.i

.PHONY : tif_packbits.i

# target to preprocess a source file
tif_packbits.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_packbits.c.i
.PHONY : tif_packbits.c.i

tif_packbits.s: tif_packbits.c.s

.PHONY : tif_packbits.s

# target to generate assembly for a file
tif_packbits.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_packbits.c.s
.PHONY : tif_packbits.c.s

tif_pixarlog.obj: tif_pixarlog.c.obj

.PHONY : tif_pixarlog.obj

# target to build an object file
tif_pixarlog.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_pixarlog.c.obj
.PHONY : tif_pixarlog.c.obj

tif_pixarlog.i: tif_pixarlog.c.i

.PHONY : tif_pixarlog.i

# target to preprocess a source file
tif_pixarlog.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_pixarlog.c.i
.PHONY : tif_pixarlog.c.i

tif_pixarlog.s: tif_pixarlog.c.s

.PHONY : tif_pixarlog.s

# target to generate assembly for a file
tif_pixarlog.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_pixarlog.c.s
.PHONY : tif_pixarlog.c.s

tif_predict.obj: tif_predict.c.obj

.PHONY : tif_predict.obj

# target to build an object file
tif_predict.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_predict.c.obj
.PHONY : tif_predict.c.obj

tif_predict.i: tif_predict.c.i

.PHONY : tif_predict.i

# target to preprocess a source file
tif_predict.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_predict.c.i
.PHONY : tif_predict.c.i

tif_predict.s: tif_predict.c.s

.PHONY : tif_predict.s

# target to generate assembly for a file
tif_predict.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_predict.c.s
.PHONY : tif_predict.c.s

tif_print.obj: tif_print.c.obj

.PHONY : tif_print.obj

# target to build an object file
tif_print.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_print.c.obj
.PHONY : tif_print.c.obj

tif_print.i: tif_print.c.i

.PHONY : tif_print.i

# target to preprocess a source file
tif_print.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_print.c.i
.PHONY : tif_print.c.i

tif_print.s: tif_print.c.s

.PHONY : tif_print.s

# target to generate assembly for a file
tif_print.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_print.c.s
.PHONY : tif_print.c.s

tif_read.obj: tif_read.c.obj

.PHONY : tif_read.obj

# target to build an object file
tif_read.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_read.c.obj
.PHONY : tif_read.c.obj

tif_read.i: tif_read.c.i

.PHONY : tif_read.i

# target to preprocess a source file
tif_read.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_read.c.i
.PHONY : tif_read.c.i

tif_read.s: tif_read.c.s

.PHONY : tif_read.s

# target to generate assembly for a file
tif_read.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_read.c.s
.PHONY : tif_read.c.s

tif_stream.obj: tif_stream.cxx.obj

.PHONY : tif_stream.obj

# target to build an object file
tif_stream.cxx.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_stream.cxx.obj
.PHONY : tif_stream.cxx.obj

tif_stream.i: tif_stream.cxx.i

.PHONY : tif_stream.i

# target to preprocess a source file
tif_stream.cxx.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_stream.cxx.i
.PHONY : tif_stream.cxx.i

tif_stream.s: tif_stream.cxx.s

.PHONY : tif_stream.s

# target to generate assembly for a file
tif_stream.cxx.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_stream.cxx.s
.PHONY : tif_stream.cxx.s

tif_strip.obj: tif_strip.c.obj

.PHONY : tif_strip.obj

# target to build an object file
tif_strip.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_strip.c.obj
.PHONY : tif_strip.c.obj

tif_strip.i: tif_strip.c.i

.PHONY : tif_strip.i

# target to preprocess a source file
tif_strip.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_strip.c.i
.PHONY : tif_strip.c.i

tif_strip.s: tif_strip.c.s

.PHONY : tif_strip.s

# target to generate assembly for a file
tif_strip.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_strip.c.s
.PHONY : tif_strip.c.s

tif_swab.obj: tif_swab.c.obj

.PHONY : tif_swab.obj

# target to build an object file
tif_swab.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_swab.c.obj
.PHONY : tif_swab.c.obj

tif_swab.i: tif_swab.c.i

.PHONY : tif_swab.i

# target to preprocess a source file
tif_swab.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_swab.c.i
.PHONY : tif_swab.c.i

tif_swab.s: tif_swab.c.s

.PHONY : tif_swab.s

# target to generate assembly for a file
tif_swab.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_swab.c.s
.PHONY : tif_swab.c.s

tif_thunder.obj: tif_thunder.c.obj

.PHONY : tif_thunder.obj

# target to build an object file
tif_thunder.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_thunder.c.obj
.PHONY : tif_thunder.c.obj

tif_thunder.i: tif_thunder.c.i

.PHONY : tif_thunder.i

# target to preprocess a source file
tif_thunder.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_thunder.c.i
.PHONY : tif_thunder.c.i

tif_thunder.s: tif_thunder.c.s

.PHONY : tif_thunder.s

# target to generate assembly for a file
tif_thunder.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_thunder.c.s
.PHONY : tif_thunder.c.s

tif_tile.obj: tif_tile.c.obj

.PHONY : tif_tile.obj

# target to build an object file
tif_tile.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_tile.c.obj
.PHONY : tif_tile.c.obj

tif_tile.i: tif_tile.c.i

.PHONY : tif_tile.i

# target to preprocess a source file
tif_tile.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_tile.c.i
.PHONY : tif_tile.c.i

tif_tile.s: tif_tile.c.s

.PHONY : tif_tile.s

# target to generate assembly for a file
tif_tile.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_tile.c.s
.PHONY : tif_tile.c.s

tif_version.obj: tif_version.c.obj

.PHONY : tif_version.obj

# target to build an object file
tif_version.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_version.c.obj
.PHONY : tif_version.c.obj

tif_version.i: tif_version.c.i

.PHONY : tif_version.i

# target to preprocess a source file
tif_version.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_version.c.i
.PHONY : tif_version.c.i

tif_version.s: tif_version.c.s

.PHONY : tif_version.s

# target to generate assembly for a file
tif_version.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_version.c.s
.PHONY : tif_version.c.s

tif_warning.obj: tif_warning.c.obj

.PHONY : tif_warning.obj

# target to build an object file
tif_warning.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_warning.c.obj
.PHONY : tif_warning.c.obj

tif_warning.i: tif_warning.c.i

.PHONY : tif_warning.i

# target to preprocess a source file
tif_warning.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_warning.c.i
.PHONY : tif_warning.c.i

tif_warning.s: tif_warning.c.s

.PHONY : tif_warning.s

# target to generate assembly for a file
tif_warning.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_warning.c.s
.PHONY : tif_warning.c.s

tif_webp.obj: tif_webp.c.obj

.PHONY : tif_webp.obj

# target to build an object file
tif_webp.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_webp.c.obj
.PHONY : tif_webp.c.obj

tif_webp.i: tif_webp.c.i

.PHONY : tif_webp.i

# target to preprocess a source file
tif_webp.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_webp.c.i
.PHONY : tif_webp.c.i

tif_webp.s: tif_webp.c.s

.PHONY : tif_webp.s

# target to generate assembly for a file
tif_webp.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_webp.c.s
.PHONY : tif_webp.c.s

tif_win32.obj: tif_win32.c.obj

.PHONY : tif_win32.obj

# target to build an object file
tif_win32.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_win32.c.obj
.PHONY : tif_win32.c.obj

tif_win32.i: tif_win32.c.i

.PHONY : tif_win32.i

# target to preprocess a source file
tif_win32.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_win32.c.i
.PHONY : tif_win32.c.i

tif_win32.s: tif_win32.c.s

.PHONY : tif_win32.s

# target to generate assembly for a file
tif_win32.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_win32.c.s
.PHONY : tif_win32.c.s

tif_write.obj: tif_write.c.obj

.PHONY : tif_write.obj

# target to build an object file
tif_write.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_write.c.obj
.PHONY : tif_write.c.obj

tif_write.i: tif_write.c.i

.PHONY : tif_write.i

# target to preprocess a source file
tif_write.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_write.c.i
.PHONY : tif_write.c.i

tif_write.s: tif_write.c.s

.PHONY : tif_write.s

# target to generate assembly for a file
tif_write.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_write.c.s
.PHONY : tif_write.c.s

tif_zip.obj: tif_zip.c.obj

.PHONY : tif_zip.obj

# target to build an object file
tif_zip.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_zip.c.obj
.PHONY : tif_zip.c.obj

tif_zip.i: tif_zip.c.i

.PHONY : tif_zip.i

# target to preprocess a source file
tif_zip.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_zip.c.i
.PHONY : tif_zip.c.i

tif_zip.s: tif_zip.c.s

.PHONY : tif_zip.s

# target to generate assembly for a file
tif_zip.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_zip.c.s
.PHONY : tif_zip.c.s

tif_zstd.obj: tif_zstd.c.obj

.PHONY : tif_zstd.obj

# target to build an object file
tif_zstd.c.obj:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_zstd.c.obj
.PHONY : tif_zstd.c.obj

tif_zstd.i: tif_zstd.c.i

.PHONY : tif_zstd.i

# target to preprocess a source file
tif_zstd.c.i:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_zstd.c.i
.PHONY : tif_zstd.c.i

tif_zstd.s: tif_zstd.c.s

.PHONY : tif_zstd.s

# target to generate assembly for a file
tif_zstd.c.s:
	cd /d D:\unet\opencv\opencv\mingw_build && $(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/tif_zstd.c.s
.PHONY : tif_zstd.c.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... package
	@echo ... libtiff
	@echo ... package_source
	@echo ... rebuild_cache
	@echo ... test
	@echo ... install
	@echo ... install/local
	@echo ... edit_cache
	@echo ... list_install_components
	@echo ... install/strip
	@echo ... tif_aux.obj
	@echo ... tif_aux.i
	@echo ... tif_aux.s
	@echo ... tif_close.obj
	@echo ... tif_close.i
	@echo ... tif_close.s
	@echo ... tif_codec.obj
	@echo ... tif_codec.i
	@echo ... tif_codec.s
	@echo ... tif_color.obj
	@echo ... tif_color.i
	@echo ... tif_color.s
	@echo ... tif_compress.obj
	@echo ... tif_compress.i
	@echo ... tif_compress.s
	@echo ... tif_dir.obj
	@echo ... tif_dir.i
	@echo ... tif_dir.s
	@echo ... tif_dirinfo.obj
	@echo ... tif_dirinfo.i
	@echo ... tif_dirinfo.s
	@echo ... tif_dirread.obj
	@echo ... tif_dirread.i
	@echo ... tif_dirread.s
	@echo ... tif_dirwrite.obj
	@echo ... tif_dirwrite.i
	@echo ... tif_dirwrite.s
	@echo ... tif_dumpmode.obj
	@echo ... tif_dumpmode.i
	@echo ... tif_dumpmode.s
	@echo ... tif_error.obj
	@echo ... tif_error.i
	@echo ... tif_error.s
	@echo ... tif_extension.obj
	@echo ... tif_extension.i
	@echo ... tif_extension.s
	@echo ... tif_fax3.obj
	@echo ... tif_fax3.i
	@echo ... tif_fax3.s
	@echo ... tif_fax3sm.obj
	@echo ... tif_fax3sm.i
	@echo ... tif_fax3sm.s
	@echo ... tif_flush.obj
	@echo ... tif_flush.i
	@echo ... tif_flush.s
	@echo ... tif_getimage.obj
	@echo ... tif_getimage.i
	@echo ... tif_getimage.s
	@echo ... tif_jbig.obj
	@echo ... tif_jbig.i
	@echo ... tif_jbig.s
	@echo ... tif_jpeg.obj
	@echo ... tif_jpeg.i
	@echo ... tif_jpeg.s
	@echo ... tif_jpeg_12.obj
	@echo ... tif_jpeg_12.i
	@echo ... tif_jpeg_12.s
	@echo ... tif_luv.obj
	@echo ... tif_luv.i
	@echo ... tif_luv.s
	@echo ... tif_lzma.obj
	@echo ... tif_lzma.i
	@echo ... tif_lzma.s
	@echo ... tif_lzw.obj
	@echo ... tif_lzw.i
	@echo ... tif_lzw.s
	@echo ... tif_next.obj
	@echo ... tif_next.i
	@echo ... tif_next.s
	@echo ... tif_ojpeg.obj
	@echo ... tif_ojpeg.i
	@echo ... tif_ojpeg.s
	@echo ... tif_open.obj
	@echo ... tif_open.i
	@echo ... tif_open.s
	@echo ... tif_packbits.obj
	@echo ... tif_packbits.i
	@echo ... tif_packbits.s
	@echo ... tif_pixarlog.obj
	@echo ... tif_pixarlog.i
	@echo ... tif_pixarlog.s
	@echo ... tif_predict.obj
	@echo ... tif_predict.i
	@echo ... tif_predict.s
	@echo ... tif_print.obj
	@echo ... tif_print.i
	@echo ... tif_print.s
	@echo ... tif_read.obj
	@echo ... tif_read.i
	@echo ... tif_read.s
	@echo ... tif_stream.obj
	@echo ... tif_stream.i
	@echo ... tif_stream.s
	@echo ... tif_strip.obj
	@echo ... tif_strip.i
	@echo ... tif_strip.s
	@echo ... tif_swab.obj
	@echo ... tif_swab.i
	@echo ... tif_swab.s
	@echo ... tif_thunder.obj
	@echo ... tif_thunder.i
	@echo ... tif_thunder.s
	@echo ... tif_tile.obj
	@echo ... tif_tile.i
	@echo ... tif_tile.s
	@echo ... tif_version.obj
	@echo ... tif_version.i
	@echo ... tif_version.s
	@echo ... tif_warning.obj
	@echo ... tif_warning.i
	@echo ... tif_warning.s
	@echo ... tif_webp.obj
	@echo ... tif_webp.i
	@echo ... tif_webp.s
	@echo ... tif_win32.obj
	@echo ... tif_win32.i
	@echo ... tif_win32.s
	@echo ... tif_write.obj
	@echo ... tif_write.i
	@echo ... tif_write.s
	@echo ... tif_zip.obj
	@echo ... tif_zip.i
	@echo ... tif_zip.s
	@echo ... tif_zstd.obj
	@echo ... tif_zstd.i
	@echo ... tif_zstd.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /d D:\unet\opencv\opencv\mingw_build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

