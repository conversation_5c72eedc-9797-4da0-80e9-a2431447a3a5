The system is: Windows - 10.0.19042 - AMD64
Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/g++.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"

The CXX compiler identification is GNU, found in "D:/unet/opencv/opencv/mingw_build/CMakeFiles/3.13.1/CompilerIdCXX/a.exe"

Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/gcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"

The C compiler identification is G<PERSON>, found in "D:/unet/opencv/opencv/mingw_build/CMakeFiles/3.13.1/CompilerIdC/a.exe"

Determining if the CXX compiler works passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_11ac3/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_11ac3.dir\build.make CMakeFiles/cmTC_11ac3.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building CXX object CMakeFiles/cmTC_11ac3.dir/testCXXCompiler.cxx.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe     -o CMakeFiles\cmTC_11ac3.dir\testCXXCompiler.cxx.obj -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\testCXXCompiler.cxx

Linking CXX executable cmTC_11ac3.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_11ac3.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_11ac3.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_11ac3.dir/objects.a @CMakeFiles\cmTC_11ac3.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe      -Wl,--whole-archive CMakeFiles\cmTC_11ac3.dir/objects.a -Wl,--no-whole-archive  -o cmTC_11ac3.exe -Wl,--out-implib,libcmTC_11ac3.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_11ac3.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Detecting CXX compiler ABI info compiled with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_3d3a1/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_3d3a1.dir\build.make CMakeFiles/cmTC_3d3a1.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building CXX object CMakeFiles/cmTC_3d3a1.dir/CMakeCXXCompilerABI.cpp.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe     -o CMakeFiles\cmTC_3d3a1.dir\CMakeCXXCompilerABI.cpp.obj -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CMakeCXXCompilerABI.cpp"

Linking CXX executable cmTC_3d3a1.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_3d3a1.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_3d3a1.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_3d3a1.dir/objects.a @CMakeFiles\cmTC_3d3a1.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe     -v -Wl,--whole-archive CMakeFiles\cmTC_3d3a1.dir/objects.a -Wl,--no-whole-archive  -o cmTC_3d3a1.exe -Wl,--out-implib,libcmTC_3d3a1.dll.a -Wl,--major-image-version,0,--minor-image-version,0 
Using built-in specs.

COLLECT_GCC=E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe

COLLECT_LTO_WRAPPER=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe

Target: x86_64-w64-mingw32

Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-posix-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib '

Thread model: posix

gcc version 8.1.0 (x86_64-posix-seh-rev0, Built by MinGW-W64 project) 

COMPILER_PATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/

LIBRARY_PATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../

COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_3d3a1.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona'

 E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe -plugin E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\Users\<USER>\AppData\Local\Temp\cckv10OP.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_3d3a1.exe E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. --whole-archive CMakeFiles\cmTC_3d3a1.dir/objects.a --no-whole-archive --out-implib libcmTC_3d3a1.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o

COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_3d3a1.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona'

mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_3d3a1/fast"]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_3d3a1.dir\build.make CMakeFiles/cmTC_3d3a1.dir/build]
  ignore line: [mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp']
  ignore line: [Building CXX object CMakeFiles/cmTC_3d3a1.dir/CMakeCXXCompilerABI.cpp.obj]
  ignore line: [E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe     -o CMakeFiles\cmTC_3d3a1.dir\CMakeCXXCompilerABI.cpp.obj -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CMakeCXXCompilerABI.cpp"]
  ignore line: [Linking CXX executable cmTC_3d3a1.exe]
  ignore line: ["C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_3d3a1.dir\link.txt --verbose=1]
  ignore line: ["C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_3d3a1.dir/objects.a]
  ignore line: [E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_3d3a1.dir/objects.a @CMakeFiles\cmTC_3d3a1.dir\objects1.rsp]
  ignore line: [E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe     -v -Wl,--whole-archive CMakeFiles\cmTC_3d3a1.dir/objects.a -Wl,--no-whole-archive  -o cmTC_3d3a1.exe -Wl,--out-implib,libcmTC_3d3a1.dll.a -Wl,--major-image-version,0,--minor-image-version,0 ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe]
  ignore line: [COLLECT_LTO_WRAPPER=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe]
  ignore line: [Target: x86_64-w64-mingw32]
  ignore line: [Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-posix-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib ']
  ignore line: [Thread model: posix]
  ignore line: [gcc version 8.1.0 (x86_64-posix-seh-rev0, Built by MinGW-W64 project) ]
  ignore line: [COMPILER_PATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/]
  ignore line: [LIBRARY_PATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_3d3a1.exe' '-shared-libgcc' '-mtune=core2' '-march=nocona']
  link line: [ E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe -plugin E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\Users\<USER>\AppData\Local\Temp\cckv10OP.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc_s -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_3d3a1.exe E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. --whole-archive CMakeFiles\cmTC_3d3a1.dir/objects.a --no-whole-archive --out-implib libcmTC_3d3a1.dll.a --major-image-version 0 --minor-image-version 0 -lstdc++ -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc_s -lgcc -lmoldname -lmingwex -lmsvcrt E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o]
    arg [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe] ==> ignore
    arg [-plugin] ==> ignore
    arg [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll] ==> ignore
    arg [-plugin-opt=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe] ==> ignore
    arg [-plugin-opt=-fresolution=C:\Users\<USER>\AppData\Local\Temp\cckv10OP.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
    arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
    arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
    arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
    arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
    arg [-plugin-opt=-pass-through=-luser32] ==> ignore
    arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
    arg [-plugin-opt=-pass-through=-liconv] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_s] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
    arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
    arg [--sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64] ==> ignore
    arg [-m] ==> ignore
    arg [i386pep] ==> ignore
    arg [-Bdynamic] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_3d3a1.exe] ==> ignore
    arg [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> ignore
    arg [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o] ==> ignore
    arg [-LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0] ==> dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0]
    arg [-LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc] ==> dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc]
    arg [-LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
    arg [-LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib] ==> dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib]
    arg [-LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib]
    arg [-LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..] ==> dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..]
    arg [--whole-archive] ==> ignore
    arg [CMakeFiles\cmTC_3d3a1.dir/objects.a] ==> ignore
    arg [--no-whole-archive] ==> ignore
    arg [--out-implib] ==> ignore
    arg [libcmTC_3d3a1.dll.a] ==> ignore
    arg [--major-image-version] ==> ignore
    arg [0] ==> ignore
    arg [--minor-image-version] ==> ignore
    arg [0] ==> ignore
    arg [-lstdc++] ==> lib [stdc++]
    arg [-lmingw32] ==> lib [mingw32]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [-lmoldname] ==> lib [moldname]
    arg [-lmingwex] ==> lib [mingwex]
    arg [-lmsvcrt] ==> lib [msvcrt]
    arg [-lpthread] ==> lib [pthread]
    arg [-ladvapi32] ==> lib [advapi32]
    arg [-lshell32] ==> lib [shell32]
    arg [-luser32] ==> lib [user32]
    arg [-lkernel32] ==> lib [kernel32]
    arg [-liconv] ==> lib [iconv]
    arg [-lmingw32] ==> lib [mingw32]
    arg [-lgcc_s] ==> lib [gcc_s]
    arg [-lgcc] ==> lib [gcc]
    arg [-lmoldname] ==> lib [moldname]
    arg [-lmingwex] ==> lib [mingwex]
    arg [-lmsvcrt] ==> lib [msvcrt]
    arg [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o] ==> ignore
  remove lib [msvcrt]
  remove lib [msvcrt]
  collapse library dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0] ==> [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0]
  collapse library dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc] ==> [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc]
  collapse library dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/x86_64-w64-mingw32/lib]
  collapse library dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib] ==> [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib]
  collapse library dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib] ==> [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/x86_64-w64-mingw32/lib]
  collapse library dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..] ==> [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib]
  implicit libs: [stdc++;mingw32;gcc_s;gcc;moldname;mingwex;pthread;advapi32;shell32;user32;kernel32;iconv;mingw32;gcc_s;gcc;moldname;mingwex]
  implicit dirs: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/x86_64-w64-mingw32/lib;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib]
  implicit fwks: []




Detecting CXX [-std=c++2a] compiler features compiled with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_6c570/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_6c570.dir\build.make CMakeFiles/cmTC_6c570.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building CXX object CMakeFiles/cmTC_6c570.dir/feature_tests.cxx.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe    -std=c++2a -o CMakeFiles\cmTC_6c570.dir\feature_tests.cxx.obj -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\feature_tests.cxx

Linking CXX executable cmTC_6c570.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_6c570.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_6c570.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_6c570.dir/objects.a @CMakeFiles\cmTC_6c570.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe      -Wl,--whole-archive CMakeFiles\cmTC_6c570.dir/objects.a -Wl,--no-whole-archive  -o cmTC_6c570.exe -Wl,--out-implib,libcmTC_6c570.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_6c570.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



    Feature record: CXX_FEATURE:1cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:1cxx_alias_templates
    Feature record: CXX_FEATURE:1cxx_alignas
    Feature record: CXX_FEATURE:1cxx_alignof
    Feature record: CXX_FEATURE:1cxx_attributes
    Feature record: CXX_FEATURE:1cxx_attribute_deprecated
    Feature record: CXX_FEATURE:1cxx_auto_type
    Feature record: CXX_FEATURE:1cxx_binary_literals
    Feature record: CXX_FEATURE:1cxx_constexpr
    Feature record: CXX_FEATURE:1cxx_contextual_conversions
    Feature record: CXX_FEATURE:1cxx_decltype
    Feature record: CXX_FEATURE:1cxx_decltype_auto
    Feature record: CXX_FEATURE:1cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:1cxx_default_function_template_args
    Feature record: CXX_FEATURE:1cxx_defaulted_functions
    Feature record: CXX_FEATURE:1cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:1cxx_delegating_constructors
    Feature record: CXX_FEATURE:1cxx_deleted_functions
    Feature record: CXX_FEATURE:1cxx_digit_separators
    Feature record: CXX_FEATURE:1cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:1cxx_explicit_conversions
    Feature record: CXX_FEATURE:1cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:1cxx_extern_templates
    Feature record: CXX_FEATURE:1cxx_final
    Feature record: CXX_FEATURE:1cxx_func_identifier
    Feature record: CXX_FEATURE:1cxx_generalized_initializers
    Feature record: CXX_FEATURE:1cxx_generic_lambdas
    Feature record: CXX_FEATURE:1cxx_inheriting_constructors
    Feature record: CXX_FEATURE:1cxx_inline_namespaces
    Feature record: CXX_FEATURE:1cxx_lambdas
    Feature record: CXX_FEATURE:1cxx_lambda_init_captures
    Feature record: CXX_FEATURE:1cxx_local_type_template_args
    Feature record: CXX_FEATURE:1cxx_long_long_type
    Feature record: CXX_FEATURE:1cxx_noexcept
    Feature record: CXX_FEATURE:1cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:1cxx_nullptr
    Feature record: CXX_FEATURE:1cxx_override
    Feature record: CXX_FEATURE:1cxx_range_for
    Feature record: CXX_FEATURE:1cxx_raw_string_literals
    Feature record: CXX_FEATURE:1cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:1cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:1cxx_return_type_deduction
    Feature record: CXX_FEATURE:1cxx_right_angle_brackets
    Feature record: CXX_FEATURE:1cxx_rvalue_references
    Feature record: CXX_FEATURE:1cxx_sizeof_member
    Feature record: CXX_FEATURE:1cxx_static_assert
    Feature record: CXX_FEATURE:1cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:1cxx_thread_local
    Feature record: CXX_FEATURE:1cxx_trailing_return_types
    Feature record: CXX_FEATURE:1cxx_unicode_literals
    Feature record: CXX_FEATURE:1cxx_uniform_initialization
    Feature record: CXX_FEATURE:1cxx_unrestricted_unions
    Feature record: CXX_FEATURE:1cxx_user_literals
    Feature record: CXX_FEATURE:1cxx_variable_templates
    Feature record: CXX_FEATURE:1cxx_variadic_macros
    Feature record: CXX_FEATURE:1cxx_variadic_templates


Detecting CXX [-std=c++17] compiler features compiled with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_6ca7d/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_6ca7d.dir\build.make CMakeFiles/cmTC_6ca7d.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building CXX object CMakeFiles/cmTC_6ca7d.dir/feature_tests.cxx.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe    -std=c++17 -o CMakeFiles\cmTC_6ca7d.dir\feature_tests.cxx.obj -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\feature_tests.cxx

Linking CXX executable cmTC_6ca7d.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_6ca7d.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_6ca7d.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_6ca7d.dir/objects.a @CMakeFiles\cmTC_6ca7d.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe      -Wl,--whole-archive CMakeFiles\cmTC_6ca7d.dir/objects.a -Wl,--no-whole-archive  -o cmTC_6ca7d.exe -Wl,--out-implib,libcmTC_6ca7d.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_6ca7d.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



    Feature record: CXX_FEATURE:1cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:1cxx_alias_templates
    Feature record: CXX_FEATURE:1cxx_alignas
    Feature record: CXX_FEATURE:1cxx_alignof
    Feature record: CXX_FEATURE:1cxx_attributes
    Feature record: CXX_FEATURE:1cxx_attribute_deprecated
    Feature record: CXX_FEATURE:1cxx_auto_type
    Feature record: CXX_FEATURE:1cxx_binary_literals
    Feature record: CXX_FEATURE:1cxx_constexpr
    Feature record: CXX_FEATURE:1cxx_contextual_conversions
    Feature record: CXX_FEATURE:1cxx_decltype
    Feature record: CXX_FEATURE:1cxx_decltype_auto
    Feature record: CXX_FEATURE:1cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:1cxx_default_function_template_args
    Feature record: CXX_FEATURE:1cxx_defaulted_functions
    Feature record: CXX_FEATURE:1cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:1cxx_delegating_constructors
    Feature record: CXX_FEATURE:1cxx_deleted_functions
    Feature record: CXX_FEATURE:1cxx_digit_separators
    Feature record: CXX_FEATURE:1cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:1cxx_explicit_conversions
    Feature record: CXX_FEATURE:1cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:1cxx_extern_templates
    Feature record: CXX_FEATURE:1cxx_final
    Feature record: CXX_FEATURE:1cxx_func_identifier
    Feature record: CXX_FEATURE:1cxx_generalized_initializers
    Feature record: CXX_FEATURE:1cxx_generic_lambdas
    Feature record: CXX_FEATURE:1cxx_inheriting_constructors
    Feature record: CXX_FEATURE:1cxx_inline_namespaces
    Feature record: CXX_FEATURE:1cxx_lambdas
    Feature record: CXX_FEATURE:1cxx_lambda_init_captures
    Feature record: CXX_FEATURE:1cxx_local_type_template_args
    Feature record: CXX_FEATURE:1cxx_long_long_type
    Feature record: CXX_FEATURE:1cxx_noexcept
    Feature record: CXX_FEATURE:1cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:1cxx_nullptr
    Feature record: CXX_FEATURE:1cxx_override
    Feature record: CXX_FEATURE:1cxx_range_for
    Feature record: CXX_FEATURE:1cxx_raw_string_literals
    Feature record: CXX_FEATURE:1cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:1cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:1cxx_return_type_deduction
    Feature record: CXX_FEATURE:1cxx_right_angle_brackets
    Feature record: CXX_FEATURE:1cxx_rvalue_references
    Feature record: CXX_FEATURE:1cxx_sizeof_member
    Feature record: CXX_FEATURE:1cxx_static_assert
    Feature record: CXX_FEATURE:1cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:1cxx_thread_local
    Feature record: CXX_FEATURE:1cxx_trailing_return_types
    Feature record: CXX_FEATURE:1cxx_unicode_literals
    Feature record: CXX_FEATURE:1cxx_uniform_initialization
    Feature record: CXX_FEATURE:1cxx_unrestricted_unions
    Feature record: CXX_FEATURE:1cxx_user_literals
    Feature record: CXX_FEATURE:1cxx_variable_templates
    Feature record: CXX_FEATURE:1cxx_variadic_macros
    Feature record: CXX_FEATURE:1cxx_variadic_templates


Detecting CXX [-std=c++14] compiler features compiled with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_37b55/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_37b55.dir\build.make CMakeFiles/cmTC_37b55.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building CXX object CMakeFiles/cmTC_37b55.dir/feature_tests.cxx.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe    -std=c++14 -o CMakeFiles\cmTC_37b55.dir\feature_tests.cxx.obj -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\feature_tests.cxx

Linking CXX executable cmTC_37b55.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_37b55.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_37b55.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_37b55.dir/objects.a @CMakeFiles\cmTC_37b55.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe      -Wl,--whole-archive CMakeFiles\cmTC_37b55.dir/objects.a -Wl,--no-whole-archive  -o cmTC_37b55.exe -Wl,--out-implib,libcmTC_37b55.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_37b55.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



    Feature record: CXX_FEATURE:1cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:1cxx_alias_templates
    Feature record: CXX_FEATURE:1cxx_alignas
    Feature record: CXX_FEATURE:1cxx_alignof
    Feature record: CXX_FEATURE:1cxx_attributes
    Feature record: CXX_FEATURE:1cxx_attribute_deprecated
    Feature record: CXX_FEATURE:1cxx_auto_type
    Feature record: CXX_FEATURE:1cxx_binary_literals
    Feature record: CXX_FEATURE:1cxx_constexpr
    Feature record: CXX_FEATURE:1cxx_contextual_conversions
    Feature record: CXX_FEATURE:1cxx_decltype
    Feature record: CXX_FEATURE:1cxx_decltype_auto
    Feature record: CXX_FEATURE:1cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:1cxx_default_function_template_args
    Feature record: CXX_FEATURE:1cxx_defaulted_functions
    Feature record: CXX_FEATURE:1cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:1cxx_delegating_constructors
    Feature record: CXX_FEATURE:1cxx_deleted_functions
    Feature record: CXX_FEATURE:1cxx_digit_separators
    Feature record: CXX_FEATURE:1cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:1cxx_explicit_conversions
    Feature record: CXX_FEATURE:1cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:1cxx_extern_templates
    Feature record: CXX_FEATURE:1cxx_final
    Feature record: CXX_FEATURE:1cxx_func_identifier
    Feature record: CXX_FEATURE:1cxx_generalized_initializers
    Feature record: CXX_FEATURE:1cxx_generic_lambdas
    Feature record: CXX_FEATURE:1cxx_inheriting_constructors
    Feature record: CXX_FEATURE:1cxx_inline_namespaces
    Feature record: CXX_FEATURE:1cxx_lambdas
    Feature record: CXX_FEATURE:1cxx_lambda_init_captures
    Feature record: CXX_FEATURE:1cxx_local_type_template_args
    Feature record: CXX_FEATURE:1cxx_long_long_type
    Feature record: CXX_FEATURE:1cxx_noexcept
    Feature record: CXX_FEATURE:1cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:1cxx_nullptr
    Feature record: CXX_FEATURE:1cxx_override
    Feature record: CXX_FEATURE:1cxx_range_for
    Feature record: CXX_FEATURE:1cxx_raw_string_literals
    Feature record: CXX_FEATURE:1cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:1cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:1cxx_return_type_deduction
    Feature record: CXX_FEATURE:1cxx_right_angle_brackets
    Feature record: CXX_FEATURE:1cxx_rvalue_references
    Feature record: CXX_FEATURE:1cxx_sizeof_member
    Feature record: CXX_FEATURE:1cxx_static_assert
    Feature record: CXX_FEATURE:1cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:1cxx_thread_local
    Feature record: CXX_FEATURE:1cxx_trailing_return_types
    Feature record: CXX_FEATURE:1cxx_unicode_literals
    Feature record: CXX_FEATURE:1cxx_uniform_initialization
    Feature record: CXX_FEATURE:1cxx_unrestricted_unions
    Feature record: CXX_FEATURE:1cxx_user_literals
    Feature record: CXX_FEATURE:1cxx_variable_templates
    Feature record: CXX_FEATURE:1cxx_variadic_macros
    Feature record: CXX_FEATURE:1cxx_variadic_templates


Detecting CXX [-std=c++11] compiler features compiled with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_d3141/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_d3141.dir\build.make CMakeFiles/cmTC_d3141.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building CXX object CMakeFiles/cmTC_d3141.dir/feature_tests.cxx.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe    -std=c++11 -o CMakeFiles\cmTC_d3141.dir\feature_tests.cxx.obj -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\feature_tests.cxx

Linking CXX executable cmTC_d3141.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_d3141.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_d3141.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_d3141.dir/objects.a @CMakeFiles\cmTC_d3141.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe      -Wl,--whole-archive CMakeFiles\cmTC_d3141.dir/objects.a -Wl,--no-whole-archive  -o cmTC_d3141.exe -Wl,--out-implib,libcmTC_d3141.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_d3141.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



    Feature record: CXX_FEATURE:0cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:1cxx_alias_templates
    Feature record: CXX_FEATURE:1cxx_alignas
    Feature record: CXX_FEATURE:1cxx_alignof
    Feature record: CXX_FEATURE:1cxx_attributes
    Feature record: CXX_FEATURE:0cxx_attribute_deprecated
    Feature record: CXX_FEATURE:1cxx_auto_type
    Feature record: CXX_FEATURE:0cxx_binary_literals
    Feature record: CXX_FEATURE:1cxx_constexpr
    Feature record: CXX_FEATURE:0cxx_contextual_conversions
    Feature record: CXX_FEATURE:1cxx_decltype
    Feature record: CXX_FEATURE:0cxx_decltype_auto
    Feature record: CXX_FEATURE:1cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:1cxx_default_function_template_args
    Feature record: CXX_FEATURE:1cxx_defaulted_functions
    Feature record: CXX_FEATURE:1cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:1cxx_delegating_constructors
    Feature record: CXX_FEATURE:1cxx_deleted_functions
    Feature record: CXX_FEATURE:0cxx_digit_separators
    Feature record: CXX_FEATURE:1cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:1cxx_explicit_conversions
    Feature record: CXX_FEATURE:1cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:1cxx_extern_templates
    Feature record: CXX_FEATURE:1cxx_final
    Feature record: CXX_FEATURE:1cxx_func_identifier
    Feature record: CXX_FEATURE:1cxx_generalized_initializers
    Feature record: CXX_FEATURE:0cxx_generic_lambdas
    Feature record: CXX_FEATURE:1cxx_inheriting_constructors
    Feature record: CXX_FEATURE:1cxx_inline_namespaces
    Feature record: CXX_FEATURE:1cxx_lambdas
    Feature record: CXX_FEATURE:0cxx_lambda_init_captures
    Feature record: CXX_FEATURE:1cxx_local_type_template_args
    Feature record: CXX_FEATURE:1cxx_long_long_type
    Feature record: CXX_FEATURE:1cxx_noexcept
    Feature record: CXX_FEATURE:1cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:1cxx_nullptr
    Feature record: CXX_FEATURE:1cxx_override
    Feature record: CXX_FEATURE:1cxx_range_for
    Feature record: CXX_FEATURE:1cxx_raw_string_literals
    Feature record: CXX_FEATURE:1cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:0cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:0cxx_return_type_deduction
    Feature record: CXX_FEATURE:1cxx_right_angle_brackets
    Feature record: CXX_FEATURE:1cxx_rvalue_references
    Feature record: CXX_FEATURE:1cxx_sizeof_member
    Feature record: CXX_FEATURE:1cxx_static_assert
    Feature record: CXX_FEATURE:1cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:1cxx_thread_local
    Feature record: CXX_FEATURE:1cxx_trailing_return_types
    Feature record: CXX_FEATURE:1cxx_unicode_literals
    Feature record: CXX_FEATURE:1cxx_uniform_initialization
    Feature record: CXX_FEATURE:1cxx_unrestricted_unions
    Feature record: CXX_FEATURE:1cxx_user_literals
    Feature record: CXX_FEATURE:0cxx_variable_templates
    Feature record: CXX_FEATURE:1cxx_variadic_macros
    Feature record: CXX_FEATURE:1cxx_variadic_templates


Detecting CXX [-std=c++98] compiler features compiled with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_791a3/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_791a3.dir\build.make CMakeFiles/cmTC_791a3.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building CXX object CMakeFiles/cmTC_791a3.dir/feature_tests.cxx.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe    -std=c++98 -o CMakeFiles\cmTC_791a3.dir\feature_tests.cxx.obj -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\feature_tests.cxx

Linking CXX executable cmTC_791a3.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_791a3.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_791a3.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_791a3.dir/objects.a @CMakeFiles\cmTC_791a3.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\g++.exe      -Wl,--whole-archive CMakeFiles\cmTC_791a3.dir/objects.a -Wl,--no-whole-archive  -o cmTC_791a3.exe -Wl,--out-implib,libcmTC_791a3.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_791a3.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



    Feature record: CXX_FEATURE:0cxx_aggregate_default_initializers
    Feature record: CXX_FEATURE:0cxx_alias_templates
    Feature record: CXX_FEATURE:0cxx_alignas
    Feature record: CXX_FEATURE:0cxx_alignof
    Feature record: CXX_FEATURE:0cxx_attributes
    Feature record: CXX_FEATURE:0cxx_attribute_deprecated
    Feature record: CXX_FEATURE:0cxx_auto_type
    Feature record: CXX_FEATURE:0cxx_binary_literals
    Feature record: CXX_FEATURE:0cxx_constexpr
    Feature record: CXX_FEATURE:0cxx_contextual_conversions
    Feature record: CXX_FEATURE:0cxx_decltype
    Feature record: CXX_FEATURE:0cxx_decltype_auto
    Feature record: CXX_FEATURE:0cxx_decltype_incomplete_return_types
    Feature record: CXX_FEATURE:0cxx_default_function_template_args
    Feature record: CXX_FEATURE:0cxx_defaulted_functions
    Feature record: CXX_FEATURE:0cxx_defaulted_move_initializers
    Feature record: CXX_FEATURE:0cxx_delegating_constructors
    Feature record: CXX_FEATURE:0cxx_deleted_functions
    Feature record: CXX_FEATURE:0cxx_digit_separators
    Feature record: CXX_FEATURE:0cxx_enum_forward_declarations
    Feature record: CXX_FEATURE:0cxx_explicit_conversions
    Feature record: CXX_FEATURE:0cxx_extended_friend_declarations
    Feature record: CXX_FEATURE:0cxx_extern_templates
    Feature record: CXX_FEATURE:0cxx_final
    Feature record: CXX_FEATURE:0cxx_func_identifier
    Feature record: CXX_FEATURE:0cxx_generalized_initializers
    Feature record: CXX_FEATURE:0cxx_generic_lambdas
    Feature record: CXX_FEATURE:0cxx_inheriting_constructors
    Feature record: CXX_FEATURE:0cxx_inline_namespaces
    Feature record: CXX_FEATURE:0cxx_lambdas
    Feature record: CXX_FEATURE:0cxx_lambda_init_captures
    Feature record: CXX_FEATURE:0cxx_local_type_template_args
    Feature record: CXX_FEATURE:0cxx_long_long_type
    Feature record: CXX_FEATURE:0cxx_noexcept
    Feature record: CXX_FEATURE:0cxx_nonstatic_member_init
    Feature record: CXX_FEATURE:0cxx_nullptr
    Feature record: CXX_FEATURE:0cxx_override
    Feature record: CXX_FEATURE:0cxx_range_for
    Feature record: CXX_FEATURE:0cxx_raw_string_literals
    Feature record: CXX_FEATURE:0cxx_reference_qualified_functions
    Feature record: CXX_FEATURE:0cxx_relaxed_constexpr
    Feature record: CXX_FEATURE:0cxx_return_type_deduction
    Feature record: CXX_FEATURE:0cxx_right_angle_brackets
    Feature record: CXX_FEATURE:0cxx_rvalue_references
    Feature record: CXX_FEATURE:0cxx_sizeof_member
    Feature record: CXX_FEATURE:0cxx_static_assert
    Feature record: CXX_FEATURE:0cxx_strong_enums
    Feature record: CXX_FEATURE:1cxx_template_template_parameters
    Feature record: CXX_FEATURE:0cxx_thread_local
    Feature record: CXX_FEATURE:0cxx_trailing_return_types
    Feature record: CXX_FEATURE:0cxx_unicode_literals
    Feature record: CXX_FEATURE:0cxx_uniform_initialization
    Feature record: CXX_FEATURE:0cxx_unrestricted_unions
    Feature record: CXX_FEATURE:0cxx_user_literals
    Feature record: CXX_FEATURE:0cxx_variable_templates
    Feature record: CXX_FEATURE:0cxx_variadic_macros
    Feature record: CXX_FEATURE:0cxx_variadic_templates
Determining if the C compiler works passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_62f3f/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_62f3f.dir\build.make CMakeFiles/cmTC_62f3f.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_62f3f.dir/testCCompiler.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -o CMakeFiles\cmTC_62f3f.dir\testCCompiler.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\testCCompiler.c

Linking C executable cmTC_62f3f.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_62f3f.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_62f3f.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_62f3f.dir/objects.a @CMakeFiles\cmTC_62f3f.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe      -Wl,--whole-archive CMakeFiles\cmTC_62f3f.dir/objects.a -Wl,--no-whole-archive  -o cmTC_62f3f.exe -Wl,--out-implib,libcmTC_62f3f.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_62f3f.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Detecting C compiler ABI info compiled with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_d47de/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_d47de.dir\build.make CMakeFiles/cmTC_d47de.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_d47de.dir/CMakeCCompilerABI.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -o CMakeFiles\cmTC_d47de.dir\CMakeCCompilerABI.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CMakeCCompilerABI.c"

Linking C executable cmTC_d47de.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_d47de.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_d47de.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_d47de.dir/objects.a @CMakeFiles\cmTC_d47de.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe     -v -Wl,--whole-archive CMakeFiles\cmTC_d47de.dir/objects.a -Wl,--no-whole-archive  -o cmTC_d47de.exe -Wl,--out-implib,libcmTC_d47de.dll.a -Wl,--major-image-version,0,--minor-image-version,0 
Using built-in specs.

COLLECT_GCC=E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe

COLLECT_LTO_WRAPPER=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe

Target: x86_64-w64-mingw32

Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-posix-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib '

Thread model: posix

gcc version 8.1.0 (x86_64-posix-seh-rev0, Built by MinGW-W64 project) 

COMPILER_PATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/

LIBRARY_PATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../

COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d47de.exe' '-mtune=core2' '-march=nocona'

 E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe -plugin E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\Users\<USER>\AppData\Local\Temp\ccTNLHdh.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_d47de.exe E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. --whole-archive CMakeFiles\cmTC_d47de.dir/objects.a --no-whole-archive --out-implib libcmTC_d47de.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o

COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d47de.exe' '-mtune=core2' '-march=nocona'

mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_d47de/fast"]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_d47de.dir\build.make CMakeFiles/cmTC_d47de.dir/build]
  ignore line: [mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp']
  ignore line: [Building C object CMakeFiles/cmTC_d47de.dir/CMakeCCompilerABI.c.obj]
  ignore line: [E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -o CMakeFiles\cmTC_d47de.dir\CMakeCCompilerABI.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CMakeCCompilerABI.c"]
  ignore line: [Linking C executable cmTC_d47de.exe]
  ignore line: ["C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_d47de.dir\link.txt --verbose=1]
  ignore line: ["C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_d47de.dir/objects.a]
  ignore line: [E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_d47de.dir/objects.a @CMakeFiles\cmTC_d47de.dir\objects1.rsp]
  ignore line: [E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe     -v -Wl,--whole-archive CMakeFiles\cmTC_d47de.dir/objects.a -Wl,--no-whole-archive  -o cmTC_d47de.exe -Wl,--out-implib,libcmTC_d47de.dll.a -Wl,--major-image-version,0,--minor-image-version,0 ]
  ignore line: [Using built-in specs.]
  ignore line: [COLLECT_GCC=E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe]
  ignore line: [COLLECT_LTO_WRAPPER=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe]
  ignore line: [Target: x86_64-w64-mingw32]
  ignore line: [Configured with: ../../../src/gcc-8.1.0/configure --host=x86_64-w64-mingw32 --build=x86_64-w64-mingw32 --target=x86_64-w64-mingw32 --prefix=/mingw64 --with-sysroot=/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 --enable-shared --enable-static --disable-multilib --enable-languages=c,c++,fortran,lto --enable-libstdcxx-time=yes --enable-threads=posix --enable-libgomp --enable-libatomic --enable-lto --enable-graphite --enable-checking=release --enable-fully-dynamic-string --enable-version-specific-runtime-libs --disable-libstdcxx-pch --disable-libstdcxx-debug --enable-bootstrap --disable-rpath --disable-win32-registry --disable-nls --disable-werror --disable-symvers --with-gnu-as --with-gnu-ld --with-arch=nocona --with-tune=core2 --with-libiconv --with-system-zlib --with-gmp=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpfr=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-mpc=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-isl=/c/mingw810/prerequisites/x86_64-w64-mingw32-static --with-pkgversion='x86_64-posix-seh-rev0, Built by MinGW-W64 project' --with-bugurl=https://sourceforge.net/projects/mingw-w64 CFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CXXFLAGS='-O2 -pipe -fno-ident -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' CPPFLAGS=' -I/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/include -I/c/mingw810/prerequisites/x86_64-zlib-static/include -I/c/mingw810/prerequisites/x86_64-w64-mingw32-static/include' LDFLAGS='-pipe -fno-ident -L/c/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64/opt/lib -L/c/mingw810/prerequisites/x86_64-zlib-static/lib -L/c/mingw810/prerequisites/x86_64-w64-mingw32-static/lib ']
  ignore line: [Thread model: posix]
  ignore line: [gcc version 8.1.0 (x86_64-posix-seh-rev0, Built by MinGW-W64 project) ]
  ignore line: [COMPILER_PATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/bin/]
  ignore line: [LIBRARY_PATH=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib/]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/]
  ignore line: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../]
  ignore line: [COLLECT_GCC_OPTIONS='-v' '-o' 'cmTC_d47de.exe' '-mtune=core2' '-march=nocona']
  link line: [ E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe -plugin E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll -plugin-opt=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe -plugin-opt=-fresolution=C:\Users\<USER>\AppData\Local\Temp\ccTNLHdh.res -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt -plugin-opt=-pass-through=-lpthread -plugin-opt=-pass-through=-ladvapi32 -plugin-opt=-pass-through=-lshell32 -plugin-opt=-pass-through=-luser32 -plugin-opt=-pass-through=-lkernel32 -plugin-opt=-pass-through=-liconv -plugin-opt=-pass-through=-lmingw32 -plugin-opt=-pass-through=-lgcc -plugin-opt=-pass-through=-lgcc_eh -plugin-opt=-pass-through=-lmoldname -plugin-opt=-pass-through=-lmingwex -plugin-opt=-pass-through=-lmsvcrt --sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64 -m i386pep -Bdynamic -o cmTC_d47de.exe E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0 -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib -LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../.. --whole-archive CMakeFiles\cmTC_d47de.dir/objects.a --no-whole-archive --out-implib libcmTC_d47de.dll.a --major-image-version 0 --minor-image-version 0 -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt -lpthread -ladvapi32 -lshell32 -luser32 -lkernel32 -liconv -lmingw32 -lgcc -lgcc_eh -lmoldname -lmingwex -lmsvcrt E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o]
    arg [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/collect2.exe] ==> ignore
    arg [-plugin] ==> ignore
    arg [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/liblto_plugin-0.dll] ==> ignore
    arg [-plugin-opt=E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../libexec/gcc/x86_64-w64-mingw32/8.1.0/lto-wrapper.exe] ==> ignore
    arg [-plugin-opt=-fresolution=C:\Users\<USER>\AppData\Local\Temp\ccTNLHdh.res] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
    arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
    arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
    arg [-plugin-opt=-pass-through=-lpthread] ==> ignore
    arg [-plugin-opt=-pass-through=-ladvapi32] ==> ignore
    arg [-plugin-opt=-pass-through=-lshell32] ==> ignore
    arg [-plugin-opt=-pass-through=-luser32] ==> ignore
    arg [-plugin-opt=-pass-through=-lkernel32] ==> ignore
    arg [-plugin-opt=-pass-through=-liconv] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingw32] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc] ==> ignore
    arg [-plugin-opt=-pass-through=-lgcc_eh] ==> ignore
    arg [-plugin-opt=-pass-through=-lmoldname] ==> ignore
    arg [-plugin-opt=-pass-through=-lmingwex] ==> ignore
    arg [-plugin-opt=-pass-through=-lmsvcrt] ==> ignore
    arg [--sysroot=C:/mingw810/x86_64-810-posix-seh-rt_v6-rev0/mingw64] ==> ignore
    arg [-m] ==> ignore
    arg [i386pep] ==> ignore
    arg [-Bdynamic] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_d47de.exe] ==> ignore
    arg [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib/crt2.o] ==> ignore
    arg [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtbegin.o] ==> ignore
    arg [-LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0] ==> dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0]
    arg [-LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc] ==> dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc]
    arg [-LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib]
    arg [-LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib] ==> dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib]
    arg [-LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib] ==> dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib]
    arg [-LE:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..] ==> dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..]
    arg [--whole-archive] ==> ignore
    arg [CMakeFiles\cmTC_d47de.dir/objects.a] ==> ignore
    arg [--no-whole-archive] ==> ignore
    arg [--out-implib] ==> ignore
    arg [libcmTC_d47de.dll.a] ==> ignore
    arg [--major-image-version] ==> ignore
    arg [0] ==> ignore
    arg [--minor-image-version] ==> ignore
    arg [0] ==> ignore
    arg [-lmingw32] ==> lib [mingw32]
    arg [-lgcc] ==> lib [gcc]
    arg [-lgcc_eh] ==> lib [gcc_eh]
    arg [-lmoldname] ==> lib [moldname]
    arg [-lmingwex] ==> lib [mingwex]
    arg [-lmsvcrt] ==> lib [msvcrt]
    arg [-lpthread] ==> lib [pthread]
    arg [-ladvapi32] ==> lib [advapi32]
    arg [-lshell32] ==> lib [shell32]
    arg [-luser32] ==> lib [user32]
    arg [-lkernel32] ==> lib [kernel32]
    arg [-liconv] ==> lib [iconv]
    arg [-lmingw32] ==> lib [mingw32]
    arg [-lgcc] ==> lib [gcc]
    arg [-lgcc_eh] ==> lib [gcc_eh]
    arg [-lmoldname] ==> lib [moldname]
    arg [-lmingwex] ==> lib [mingwex]
    arg [-lmsvcrt] ==> lib [msvcrt]
    arg [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/crtend.o] ==> ignore
  remove lib [gcc_eh]
  remove lib [msvcrt]
  remove lib [gcc_eh]
  remove lib [msvcrt]
  collapse library dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0] ==> [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0]
  collapse library dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc] ==> [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc]
  collapse library dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib/../lib] ==> [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/x86_64-w64-mingw32/lib]
  collapse library dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../lib] ==> [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib]
  collapse library dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/lib] ==> [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/x86_64-w64-mingw32/lib]
  collapse library dir [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/../lib/gcc/x86_64-w64-mingw32/8.1.0/../../..] ==> [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib]
  implicit libs: [mingw32;gcc;moldname;mingwex;pthread;advapi32;shell32;user32;kernel32;iconv;mingw32;gcc;moldname;mingwex]
  implicit dirs: [E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/x86_64-w64-mingw32/lib;E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib]
  implicit fwks: []




Detecting C [-std=c11] compiler features compiled with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_1b9c1/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_1b9c1.dir\build.make CMakeFiles/cmTC_1b9c1.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_1b9c1.dir/feature_tests.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -std=c11 -o CMakeFiles\cmTC_1b9c1.dir\feature_tests.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\feature_tests.c

Linking C executable cmTC_1b9c1.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_1b9c1.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_1b9c1.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_1b9c1.dir/objects.a @CMakeFiles\cmTC_1b9c1.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe      -Wl,--whole-archive CMakeFiles\cmTC_1b9c1.dir/objects.a -Wl,--no-whole-archive  -o cmTC_1b9c1.exe -Wl,--out-implib,libcmTC_1b9c1.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_1b9c1.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



    Feature record: C_FEATURE:1c_function_prototypes
    Feature record: C_FEATURE:1c_restrict
    Feature record: C_FEATURE:1c_static_assert
    Feature record: C_FEATURE:1c_variadic_macros


Detecting C [-std=c99] compiler features compiled with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_bcc4f/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_bcc4f.dir\build.make CMakeFiles/cmTC_bcc4f.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_bcc4f.dir/feature_tests.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -std=c99 -o CMakeFiles\cmTC_bcc4f.dir\feature_tests.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\feature_tests.c

Linking C executable cmTC_bcc4f.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_bcc4f.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_bcc4f.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_bcc4f.dir/objects.a @CMakeFiles\cmTC_bcc4f.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe      -Wl,--whole-archive CMakeFiles\cmTC_bcc4f.dir/objects.a -Wl,--no-whole-archive  -o cmTC_bcc4f.exe -Wl,--out-implib,libcmTC_bcc4f.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_bcc4f.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



    Feature record: C_FEATURE:1c_function_prototypes
    Feature record: C_FEATURE:1c_restrict
    Feature record: C_FEATURE:0c_static_assert
    Feature record: C_FEATURE:1c_variadic_macros


Detecting C [-std=c90] compiler features compiled with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_b2bef/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_b2bef.dir\build.make CMakeFiles/cmTC_b2bef.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_b2bef.dir/feature_tests.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -std=c90 -o CMakeFiles\cmTC_b2bef.dir\feature_tests.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\feature_tests.c

Linking C executable cmTC_b2bef.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_b2bef.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_b2bef.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_b2bef.dir/objects.a @CMakeFiles\cmTC_b2bef.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe      -Wl,--whole-archive CMakeFiles\cmTC_b2bef.dir/objects.a -Wl,--no-whole-archive  -o cmTC_b2bef.exe -Wl,--out-implib,libcmTC_b2bef.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_b2bef.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



    Feature record: C_FEATURE:1c_function_prototypes
    Feature record: C_FEATURE:0c_restrict
    Feature record: C_FEATURE:0c_static_assert
    Feature record: C_FEATURE:0c_variadic_macros
Determining if the include file sys/types.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_08411/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_08411.dir\build.make CMakeFiles/cmTC_08411.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_08411.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_08411.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_08411.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_08411.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_08411.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_08411.dir/objects.a @CMakeFiles\cmTC_08411.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_08411.dir/objects.a -Wl,--no-whole-archive  -o cmTC_08411.exe -Wl,--out-implib,libcmTC_08411.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_08411.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the include file stdint.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_1613b/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_1613b.dir\build.make CMakeFiles/cmTC_1613b.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_1613b.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_1613b.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_1613b.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_1613b.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_1613b.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_1613b.dir/objects.a @CMakeFiles\cmTC_1613b.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_1613b.dir/objects.a -Wl,--no-whole-archive  -o cmTC_1613b.exe -Wl,--out-implib,libcmTC_1613b.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_1613b.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the include file stddef.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_3835c/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_3835c.dir\build.make CMakeFiles/cmTC_3835c.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_3835c.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_3835c.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_3835c.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_3835c.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_3835c.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_3835c.dir/objects.a @CMakeFiles\cmTC_3835c.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_3835c.dir/objects.a -Wl,--no-whole-archive  -o cmTC_3835c.exe -Wl,--out-implib,libcmTC_3835c.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_3835c.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining size of unsigned short passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_ab467/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_ab467.dir\build.make CMakeFiles/cmTC_ab467.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_ab467.dir/CMAKE_SIZEOF_UNSIGNED_SHORT.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_ab467.dir\CMAKE_SIZEOF_UNSIGNED_SHORT.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CheckTypeSize\CMAKE_SIZEOF_UNSIGNED_SHORT.c

Linking C executable cmTC_ab467.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_ab467.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_ab467.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_ab467.dir/objects.a @CMakeFiles\cmTC_ab467.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_ab467.dir/objects.a -Wl,--no-whole-archive  -o cmTC_ab467.exe -Wl,--out-implib,libcmTC_ab467.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_ab467.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the system is big endian passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_bfc9a/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_bfc9a.dir\build.make CMakeFiles/cmTC_bfc9a.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_bfc9a.dir/TestEndianess.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_bfc9a.dir\TestEndianess.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\TestEndianess.c

Linking C executable cmTC_bfc9a.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_bfc9a.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_bfc9a.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_bfc9a.dir/objects.a @CMakeFiles\cmTC_bfc9a.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_bfc9a.dir/objects.a -Wl,--no-whole-archive  -o cmTC_bfc9a.exe -Wl,--out-implib,libcmTC_bfc9a.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_bfc9a.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'


TestEndianess.c:
/* A 16 bit integer is required. */
typedef unsigned short cmakeint16;

/* On a little endian machine, these 16bit ints will give "THIS IS LITTLE ENDIAN."
   On a big endian machine the characters will be exchanged pairwise. */
const cmakeint16 info_little[] =  {0x4854, 0x5349, 0x4920, 0x2053, 0x494c, 0x5454, 0x454c, 0x4520, 0x444e, 0x4149, 0x2e4e, 0x0000};

/* on a big endian machine, these 16bit ints will give "THIS IS BIG ENDIAN."
   On a little endian machine the characters will be exchanged pairwise. */
const cmakeint16 info_big[] =     {0x5448, 0x4953, 0x2049, 0x5320, 0x4249, 0x4720, 0x454e, 0x4449, 0x414e, 0x2e2e, 0x0000};

#ifdef __CLASSIC_C__
int main(argc, argv) int argc; char *argv[];
#else
int main(int argc, char *argv[])
#endif
{
  int require = 0;
  require += info_little[argc];
  require += info_big[argc];
  (void)argv;
  return require;
}


Determining if the function fseeko exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_31235/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_31235.dir\build.make CMakeFiles/cmTC_31235.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_31235.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=fseeko -O3 -DNDEBUG   -o CMakeFiles\cmTC_31235.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

Linking C executable cmTC_31235.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_31235.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_31235.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_31235.dir/objects.a @CMakeFiles\cmTC_31235.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=fseeko -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_31235.dir/objects.a -Wl,--no-whole-archive  -o cmTC_31235.exe -Wl,--out-implib,libcmTC_31235.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_31235.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the include file unistd.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_4cec7/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_4cec7.dir\build.make CMakeFiles/cmTC_4cec7.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_4cec7.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_4cec7.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_4cec7.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_4cec7.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_4cec7.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_4cec7.dir/objects.a @CMakeFiles\cmTC_4cec7.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_4cec7.dir/objects.a -Wl,--no-whole-archive  -o cmTC_4cec7.exe -Wl,--out-implib,libcmTC_4cec7.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_4cec7.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining size of off64_t passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_e8bac/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_e8bac.dir\build.make CMakeFiles/cmTC_e8bac.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_e8bac.dir/OFF64_T.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_e8bac.dir\OFF64_T.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CheckTypeSize\OFF64_T.c

Linking C executable cmTC_e8bac.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_e8bac.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_e8bac.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_e8bac.dir/objects.a @CMakeFiles\cmTC_e8bac.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_e8bac.dir/objects.a -Wl,--no-whole-archive  -o cmTC_e8bac.exe -Wl,--out-implib,libcmTC_e8bac.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_e8bac.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining size of size_t passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_343d8/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_343d8.dir\build.make CMakeFiles/cmTC_343d8.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_343d8.dir/SIZEOF_SIZE_T.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -Wno-unused-parameter -Wno-sign-compare -Wno-implicit-fallthrough  -O3 -DNDEBUG   -o CMakeFiles\cmTC_343d8.dir\SIZEOF_SIZE_T.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CheckTypeSize\SIZEOF_SIZE_T.c

Linking C executable cmTC_343d8.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_343d8.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_343d8.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_343d8.dir/objects.a @CMakeFiles\cmTC_343d8.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -Wno-unused-parameter -Wno-sign-compare -Wno-implicit-fallthrough  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_343d8.dir/objects.a -Wl,--no-whole-archive  -o cmTC_343d8.exe -Wl,--out-implib,libcmTC_343d8.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_343d8.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining size of unsigned long passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_cdc10/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_cdc10.dir\build.make CMakeFiles/cmTC_cdc10.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_cdc10.dir/SIZEOF_UNSIGNED_LONG.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -Wno-unused-parameter -Wno-sign-compare -Wno-implicit-fallthrough  -O3 -DNDEBUG   -o CMakeFiles\cmTC_cdc10.dir\SIZEOF_UNSIGNED_LONG.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CheckTypeSize\SIZEOF_UNSIGNED_LONG.c

Linking C executable cmTC_cdc10.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_cdc10.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_cdc10.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_cdc10.dir/objects.a @CMakeFiles\cmTC_cdc10.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -Wno-unused-parameter -Wno-sign-compare -Wno-implicit-fallthrough  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_cdc10.dir/objects.a -Wl,--no-whole-archive  -o cmTC_cdc10.exe -Wl,--out-implib,libcmTC_cdc10.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_cdc10.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the include file assert.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_13f03/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_13f03.dir\build.make CMakeFiles/cmTC_13f03.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_13f03.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_13f03.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_13f03.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_13f03.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_13f03.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_13f03.dir/objects.a @CMakeFiles\cmTC_13f03.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_13f03.dir/objects.a -Wl,--no-whole-archive  -o cmTC_13f03.exe -Wl,--out-implib,libcmTC_13f03.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_13f03.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the include file fcntl.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_f69c8/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_f69c8.dir\build.make CMakeFiles/cmTC_f69c8.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_f69c8.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_f69c8.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_f69c8.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_f69c8.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_f69c8.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_f69c8.dir/objects.a @CMakeFiles\cmTC_f69c8.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_f69c8.dir/objects.a -Wl,--no-whole-archive  -o cmTC_f69c8.exe -Wl,--out-implib,libcmTC_f69c8.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_f69c8.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the include file inttypes.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_fdce2/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_fdce2.dir\build.make CMakeFiles/cmTC_fdce2.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_fdce2.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_fdce2.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_fdce2.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_fdce2.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_fdce2.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_fdce2.dir/objects.a @CMakeFiles\cmTC_fdce2.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_fdce2.dir/objects.a -Wl,--no-whole-archive  -o cmTC_fdce2.exe -Wl,--out-implib,libcmTC_fdce2.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_fdce2.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the include file io.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_69b2a/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_69b2a.dir\build.make CMakeFiles/cmTC_69b2a.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_69b2a.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_69b2a.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_69b2a.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_69b2a.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_69b2a.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_69b2a.dir/objects.a @CMakeFiles\cmTC_69b2a.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_69b2a.dir/objects.a -Wl,--no-whole-archive  -o cmTC_69b2a.exe -Wl,--out-implib,libcmTC_69b2a.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_69b2a.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the include file limits.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_7aa77/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_7aa77.dir\build.make CMakeFiles/cmTC_7aa77.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_7aa77.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_7aa77.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_7aa77.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_7aa77.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_7aa77.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_7aa77.dir/objects.a @CMakeFiles\cmTC_7aa77.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_7aa77.dir/objects.a -Wl,--no-whole-archive  -o cmTC_7aa77.exe -Wl,--out-implib,libcmTC_7aa77.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_7aa77.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the include file malloc.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_fea15/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_fea15.dir\build.make CMakeFiles/cmTC_fea15.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_fea15.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_fea15.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_fea15.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_fea15.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_fea15.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_fea15.dir/objects.a @CMakeFiles\cmTC_fea15.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_fea15.dir/objects.a -Wl,--no-whole-archive  -o cmTC_fea15.exe -Wl,--out-implib,libcmTC_fea15.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_fea15.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the include file memory.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_3bb3a/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_3bb3a.dir\build.make CMakeFiles/cmTC_3bb3a.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_3bb3a.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_3bb3a.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_3bb3a.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_3bb3a.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_3bb3a.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_3bb3a.dir/objects.a @CMakeFiles\cmTC_3bb3a.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_3bb3a.dir/objects.a -Wl,--no-whole-archive  -o cmTC_3bb3a.exe -Wl,--out-implib,libcmTC_3bb3a.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_3bb3a.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the include file search.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_bb426/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_bb426.dir\build.make CMakeFiles/cmTC_bb426.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_bb426.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_bb426.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_bb426.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_bb426.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_bb426.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_bb426.dir/objects.a @CMakeFiles\cmTC_bb426.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_bb426.dir/objects.a -Wl,--no-whole-archive  -o cmTC_bb426.exe -Wl,--out-implib,libcmTC_bb426.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_bb426.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the include file string.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_65b38/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_65b38.dir\build.make CMakeFiles/cmTC_65b38.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_65b38.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_65b38.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_65b38.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_65b38.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_65b38.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_65b38.dir/objects.a @CMakeFiles\cmTC_65b38.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_65b38.dir/objects.a -Wl,--no-whole-archive  -o cmTC_65b38.exe -Wl,--out-implib,libcmTC_65b38.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_65b38.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the include file strings.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_c85a5/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_c85a5.dir\build.make CMakeFiles/cmTC_c85a5.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_c85a5.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_c85a5.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_c85a5.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_c85a5.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_c85a5.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_c85a5.dir/objects.a @CMakeFiles\cmTC_c85a5.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_c85a5.dir/objects.a -Wl,--no-whole-archive  -o cmTC_c85a5.exe -Wl,--out-implib,libcmTC_c85a5.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_c85a5.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the include file sys/time.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_2ae42/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_2ae42.dir\build.make CMakeFiles/cmTC_2ae42.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_2ae42.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_2ae42.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_2ae42.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_2ae42.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_2ae42.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_2ae42.dir/objects.a @CMakeFiles\cmTC_2ae42.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_2ae42.dir/objects.a -Wl,--no-whole-archive  -o cmTC_2ae42.exe -Wl,--out-implib,libcmTC_2ae42.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_2ae42.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the include file unistd.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_f428c/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_f428c.dir\build.make CMakeFiles/cmTC_f428c.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_f428c.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_f428c.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_f428c.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_f428c.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_f428c.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_f428c.dir/objects.a @CMakeFiles\cmTC_f428c.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_f428c.dir/objects.a -Wl,--no-whole-archive  -o cmTC_f428c.exe -Wl,--out-implib,libcmTC_f428c.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_f428c.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Performing C SOURCE FILE Test C_HAS_inline succeeded with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_c41b7/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_c41b7.dir\build.make CMakeFiles/cmTC_c41b7.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_c41b7.dir/src.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe -Dinline=inline  -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DC_HAS_inline -O3 -DNDEBUG   -o CMakeFiles\cmTC_c41b7.dir\src.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\src.c

D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\src.c:3:29: warning: function declaration isn't a prototype [-Wstrict-prototypes]
         static inline foo_t static_foo() {return 0;}
                             ^~~~~~~~~~
D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\src.c:4:15: warning: function declaration isn't a prototype [-Wstrict-prototypes]
         foo_t foo(){return 0;}
               ^~~
D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\src.c: In function 'main':
D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\src.c:5:22: warning: unused parameter 'argc' [-Wunused-parameter]
         int main(int argc, char *argv[]) {return 0;}
                  ~~~~^~~~
D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\src.c:5:34: warning: unused parameter 'argv' [-Wunused-parameter]
         int main(int argc, char *argv[]) {return 0;}
                            ~~~~~~^~~~~~
Linking C executable cmTC_c41b7.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_c41b7.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_c41b7.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_c41b7.dir/objects.a @CMakeFiles\cmTC_c41b7.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DC_HAS_inline -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_c41b7.dir/objects.a -Wl,--no-whole-archive  -o cmTC_c41b7.exe -Wl,--out-implib,libcmTC_c41b7.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_c41b7.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'


Source file was:

        typedef int foo_t;
        static inline foo_t static_foo() {return 0;}
        foo_t foo(){return 0;}
        int main(int argc, char *argv[]) {return 0;}
Determining size of signed short passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_0786e/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_0786e.dir\build.make CMakeFiles/cmTC_0786e.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_0786e.dir/SIZEOF_SIGNED_SHORT.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_0786e.dir\SIZEOF_SIGNED_SHORT.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CheckTypeSize\SIZEOF_SIGNED_SHORT.c

Linking C executable cmTC_0786e.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_0786e.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_0786e.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_0786e.dir/objects.a @CMakeFiles\cmTC_0786e.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_0786e.dir/objects.a -Wl,--no-whole-archive  -o cmTC_0786e.exe -Wl,--out-implib,libcmTC_0786e.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_0786e.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining size of unsigned short passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_f1996/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_f1996.dir\build.make CMakeFiles/cmTC_f1996.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_f1996.dir/SIZEOF_UNSIGNED_SHORT.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_f1996.dir\SIZEOF_UNSIGNED_SHORT.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CheckTypeSize\SIZEOF_UNSIGNED_SHORT.c

Linking C executable cmTC_f1996.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_f1996.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_f1996.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_f1996.dir/objects.a @CMakeFiles\cmTC_f1996.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_f1996.dir/objects.a -Wl,--no-whole-archive  -o cmTC_f1996.exe -Wl,--out-implib,libcmTC_f1996.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_f1996.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining size of signed int passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_756e8/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_756e8.dir\build.make CMakeFiles/cmTC_756e8.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_756e8.dir/SIZEOF_SIGNED_INT.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_756e8.dir\SIZEOF_SIGNED_INT.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CheckTypeSize\SIZEOF_SIGNED_INT.c

Linking C executable cmTC_756e8.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_756e8.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_756e8.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_756e8.dir/objects.a @CMakeFiles\cmTC_756e8.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_756e8.dir/objects.a -Wl,--no-whole-archive  -o cmTC_756e8.exe -Wl,--out-implib,libcmTC_756e8.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_756e8.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining size of unsigned int passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_b9d8e/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_b9d8e.dir\build.make CMakeFiles/cmTC_b9d8e.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_b9d8e.dir/SIZEOF_UNSIGNED_INT.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_b9d8e.dir\SIZEOF_UNSIGNED_INT.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CheckTypeSize\SIZEOF_UNSIGNED_INT.c

Linking C executable cmTC_b9d8e.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_b9d8e.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_b9d8e.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_b9d8e.dir/objects.a @CMakeFiles\cmTC_b9d8e.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_b9d8e.dir/objects.a -Wl,--no-whole-archive  -o cmTC_b9d8e.exe -Wl,--out-implib,libcmTC_b9d8e.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_b9d8e.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining size of signed long passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_9a47b/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_9a47b.dir\build.make CMakeFiles/cmTC_9a47b.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_9a47b.dir/SIZEOF_SIGNED_LONG.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_9a47b.dir\SIZEOF_SIGNED_LONG.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CheckTypeSize\SIZEOF_SIGNED_LONG.c

Linking C executable cmTC_9a47b.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_9a47b.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_9a47b.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_9a47b.dir/objects.a @CMakeFiles\cmTC_9a47b.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_9a47b.dir/objects.a -Wl,--no-whole-archive  -o cmTC_9a47b.exe -Wl,--out-implib,libcmTC_9a47b.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_9a47b.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining size of signed long long passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_a8705/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_a8705.dir\build.make CMakeFiles/cmTC_a8705.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_a8705.dir/SIZEOF_SIGNED_LONG_LONG.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_a8705.dir\SIZEOF_SIGNED_LONG_LONG.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CheckTypeSize\SIZEOF_SIGNED_LONG_LONG.c

Linking C executable cmTC_a8705.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_a8705.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_a8705.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_a8705.dir/objects.a @CMakeFiles\cmTC_a8705.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_a8705.dir/objects.a -Wl,--no-whole-archive  -o cmTC_a8705.exe -Wl,--out-implib,libcmTC_a8705.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_a8705.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining size of unsigned long long passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_ff7e3/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_ff7e3.dir\build.make CMakeFiles/cmTC_ff7e3.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_ff7e3.dir/SIZEOF_UNSIGNED_LONG_LONG.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_ff7e3.dir\SIZEOF_UNSIGNED_LONG_LONG.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CheckTypeSize\SIZEOF_UNSIGNED_LONG_LONG.c

Linking C executable cmTC_ff7e3.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_ff7e3.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_ff7e3.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_ff7e3.dir/objects.a @CMakeFiles\cmTC_ff7e3.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_ff7e3.dir/objects.a -Wl,--no-whole-archive  -o cmTC_ff7e3.exe -Wl,--out-implib,libcmTC_ff7e3.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_ff7e3.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining size of unsigned char * passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_f3387/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_f3387.dir\build.make CMakeFiles/cmTC_f3387.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_f3387.dir/SIZEOF_UNSIGNED_CHAR_P.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_f3387.dir\SIZEOF_UNSIGNED_CHAR_P.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CheckTypeSize\SIZEOF_UNSIGNED_CHAR_P.c

Linking C executable cmTC_f3387.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_f3387.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_f3387.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_f3387.dir/objects.a @CMakeFiles\cmTC_f3387.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_f3387.dir/objects.a -Wl,--no-whole-archive  -o cmTC_f3387.exe -Wl,--out-implib,libcmTC_f3387.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_f3387.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining size of ptrdiff_t passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_1ffa5/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_1ffa5.dir\build.make CMakeFiles/cmTC_1ffa5.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_1ffa5.dir/SIZEOF_PTRDIFF_T.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_1ffa5.dir\SIZEOF_PTRDIFF_T.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CheckTypeSize\SIZEOF_PTRDIFF_T.c

Linking C executable cmTC_1ffa5.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_1ffa5.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_1ffa5.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_1ffa5.dir/objects.a @CMakeFiles\cmTC_1ffa5.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_1ffa5.dir/objects.a -Wl,--no-whole-archive  -o cmTC_1ffa5.exe -Wl,--out-implib,libcmTC_1ffa5.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_1ffa5.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function floor exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_6596c/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_6596c.dir\build.make CMakeFiles/cmTC_6596c.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_6596c.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=floor -O3 -DNDEBUG   -o CMakeFiles\cmTC_6596c.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

<command-line>: warning: conflicting types for built-in function 'floor' [-Wbuiltin-declaration-mismatch]
C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c:7:3: note: in expansion of macro 'CHECK_FUNCTION_EXISTS'
   CHECK_FUNCTION_EXISTS(void);
   ^~~~~~~~~~~~~~~~~~~~~
Linking C executable cmTC_6596c.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_6596c.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_6596c.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_6596c.dir/objects.a @CMakeFiles\cmTC_6596c.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=floor -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_6596c.dir/objects.a -Wl,--no-whole-archive  -o cmTC_6596c.exe -Wl,--out-implib,libcmTC_6596c.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_6596c.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function pow exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_2a24b/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_2a24b.dir\build.make CMakeFiles/cmTC_2a24b.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_2a24b.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=pow -O3 -DNDEBUG   -o CMakeFiles\cmTC_2a24b.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

<command-line>: warning: conflicting types for built-in function 'pow' [-Wbuiltin-declaration-mismatch]
C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c:7:3: note: in expansion of macro 'CHECK_FUNCTION_EXISTS'
   CHECK_FUNCTION_EXISTS(void);
   ^~~~~~~~~~~~~~~~~~~~~
Linking C executable cmTC_2a24b.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_2a24b.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_2a24b.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_2a24b.dir/objects.a @CMakeFiles\cmTC_2a24b.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=pow -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_2a24b.dir/objects.a -Wl,--no-whole-archive  -o cmTC_2a24b.exe -Wl,--out-implib,libcmTC_2a24b.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_2a24b.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function sqrt exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_a8cb4/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_a8cb4.dir\build.make CMakeFiles/cmTC_a8cb4.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_a8cb4.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=sqrt -O3 -DNDEBUG   -o CMakeFiles\cmTC_a8cb4.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

<command-line>: warning: conflicting types for built-in function 'sqrt' [-Wbuiltin-declaration-mismatch]
C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c:7:3: note: in expansion of macro 'CHECK_FUNCTION_EXISTS'
   CHECK_FUNCTION_EXISTS(void);
   ^~~~~~~~~~~~~~~~~~~~~
Linking C executable cmTC_a8cb4.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_a8cb4.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_a8cb4.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_a8cb4.dir/objects.a @CMakeFiles\cmTC_a8cb4.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=sqrt -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_a8cb4.dir/objects.a -Wl,--no-whole-archive  -o cmTC_a8cb4.exe -Wl,--out-implib,libcmTC_a8cb4.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_a8cb4.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function isascii exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_b922d/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_b922d.dir\build.make CMakeFiles/cmTC_b922d.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_b922d.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=isascii -O3 -DNDEBUG   -o CMakeFiles\cmTC_b922d.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

<command-line>: warning: conflicting types for built-in function 'isascii' [-Wbuiltin-declaration-mismatch]
C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c:7:3: note: in expansion of macro 'CHECK_FUNCTION_EXISTS'
   CHECK_FUNCTION_EXISTS(void);
   ^~~~~~~~~~~~~~~~~~~~~
Linking C executable cmTC_b922d.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_b922d.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_b922d.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_b922d.dir/objects.a @CMakeFiles\cmTC_b922d.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=isascii -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_b922d.dir/objects.a -Wl,--no-whole-archive  -o cmTC_b922d.exe -Wl,--out-implib,libcmTC_b922d.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_b922d.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function memset exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_97f41/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_97f41.dir\build.make CMakeFiles/cmTC_97f41.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_97f41.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=memset -O3 -DNDEBUG   -o CMakeFiles\cmTC_97f41.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

<command-line>: warning: conflicting types for built-in function 'memset' [-Wbuiltin-declaration-mismatch]
C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c:7:3: note: in expansion of macro 'CHECK_FUNCTION_EXISTS'
   CHECK_FUNCTION_EXISTS(void);
   ^~~~~~~~~~~~~~~~~~~~~
Linking C executable cmTC_97f41.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_97f41.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_97f41.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_97f41.dir/objects.a @CMakeFiles\cmTC_97f41.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=memset -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_97f41.dir/objects.a -Wl,--no-whole-archive  -o cmTC_97f41.exe -Wl,--out-implib,libcmTC_97f41.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_97f41.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function getopt exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_68c5d/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_68c5d.dir\build.make CMakeFiles/cmTC_68c5d.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_68c5d.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=getopt -O3 -DNDEBUG   -o CMakeFiles\cmTC_68c5d.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

Linking C executable cmTC_68c5d.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_68c5d.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_68c5d.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_68c5d.dir/objects.a @CMakeFiles\cmTC_68c5d.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=getopt -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_68c5d.dir/objects.a -Wl,--no-whole-archive  -o cmTC_68c5d.exe -Wl,--out-implib,libcmTC_68c5d.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_68c5d.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function memmove exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_67aac/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_67aac.dir\build.make CMakeFiles/cmTC_67aac.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_67aac.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=memmove -O3 -DNDEBUG   -o CMakeFiles\cmTC_67aac.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

<command-line>: warning: conflicting types for built-in function 'memmove' [-Wbuiltin-declaration-mismatch]
C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c:7:3: note: in expansion of macro 'CHECK_FUNCTION_EXISTS'
   CHECK_FUNCTION_EXISTS(void);
   ^~~~~~~~~~~~~~~~~~~~~
Linking C executable cmTC_67aac.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_67aac.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_67aac.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_67aac.dir/objects.a @CMakeFiles\cmTC_67aac.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=memmove -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_67aac.dir/objects.a -Wl,--no-whole-archive  -o cmTC_67aac.exe -Wl,--out-implib,libcmTC_67aac.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_67aac.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function setmode exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_c4f44/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_c4f44.dir\build.make CMakeFiles/cmTC_c4f44.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_c4f44.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=setmode -O3 -DNDEBUG   -o CMakeFiles\cmTC_c4f44.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

Linking C executable cmTC_c4f44.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_c4f44.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_c4f44.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_c4f44.dir/objects.a @CMakeFiles\cmTC_c4f44.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=setmode -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_c4f44.dir/objects.a -Wl,--no-whole-archive  -o cmTC_c4f44.exe -Wl,--out-implib,libcmTC_c4f44.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_c4f44.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function strcasecmp exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_12b22/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_12b22.dir\build.make CMakeFiles/cmTC_12b22.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_12b22.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=strcasecmp -O3 -DNDEBUG   -o CMakeFiles\cmTC_12b22.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

<command-line>: warning: conflicting types for built-in function 'strcasecmp' [-Wbuiltin-declaration-mismatch]
C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c:7:3: note: in expansion of macro 'CHECK_FUNCTION_EXISTS'
   CHECK_FUNCTION_EXISTS(void);
   ^~~~~~~~~~~~~~~~~~~~~
Linking C executable cmTC_12b22.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_12b22.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_12b22.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_12b22.dir/objects.a @CMakeFiles\cmTC_12b22.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=strcasecmp -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_12b22.dir/objects.a -Wl,--no-whole-archive  -o cmTC_12b22.exe -Wl,--out-implib,libcmTC_12b22.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_12b22.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function strchr exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_d3554/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_d3554.dir\build.make CMakeFiles/cmTC_d3554.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_d3554.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=strchr -O3 -DNDEBUG   -o CMakeFiles\cmTC_d3554.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

<command-line>: warning: conflicting types for built-in function 'strchr' [-Wbuiltin-declaration-mismatch]
C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c:7:3: note: in expansion of macro 'CHECK_FUNCTION_EXISTS'
   CHECK_FUNCTION_EXISTS(void);
   ^~~~~~~~~~~~~~~~~~~~~
Linking C executable cmTC_d3554.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_d3554.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_d3554.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_d3554.dir/objects.a @CMakeFiles\cmTC_d3554.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=strchr -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_d3554.dir/objects.a -Wl,--no-whole-archive  -o cmTC_d3554.exe -Wl,--out-implib,libcmTC_d3554.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_d3554.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function strrchr exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_f5c17/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_f5c17.dir\build.make CMakeFiles/cmTC_f5c17.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_f5c17.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=strrchr -O3 -DNDEBUG   -o CMakeFiles\cmTC_f5c17.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

<command-line>: warning: conflicting types for built-in function 'strrchr' [-Wbuiltin-declaration-mismatch]
C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c:7:3: note: in expansion of macro 'CHECK_FUNCTION_EXISTS'
   CHECK_FUNCTION_EXISTS(void);
   ^~~~~~~~~~~~~~~~~~~~~
Linking C executable cmTC_f5c17.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_f5c17.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_f5c17.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_f5c17.dir/objects.a @CMakeFiles\cmTC_f5c17.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=strrchr -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_f5c17.dir/objects.a -Wl,--no-whole-archive  -o cmTC_f5c17.exe -Wl,--out-implib,libcmTC_f5c17.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_f5c17.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function strstr exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_9bd0f/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_9bd0f.dir\build.make CMakeFiles/cmTC_9bd0f.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_9bd0f.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=strstr -O3 -DNDEBUG   -o CMakeFiles\cmTC_9bd0f.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

<command-line>: warning: conflicting types for built-in function 'strstr' [-Wbuiltin-declaration-mismatch]
C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c:7:3: note: in expansion of macro 'CHECK_FUNCTION_EXISTS'
   CHECK_FUNCTION_EXISTS(void);
   ^~~~~~~~~~~~~~~~~~~~~
Linking C executable cmTC_9bd0f.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_9bd0f.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_9bd0f.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_9bd0f.dir/objects.a @CMakeFiles\cmTC_9bd0f.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=strstr -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_9bd0f.dir/objects.a -Wl,--no-whole-archive  -o cmTC_9bd0f.exe -Wl,--out-implib,libcmTC_9bd0f.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_9bd0f.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function strtol exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_7f344/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_7f344.dir\build.make CMakeFiles/cmTC_7f344.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_7f344.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=strtol -O3 -DNDEBUG   -o CMakeFiles\cmTC_7f344.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

Linking C executable cmTC_7f344.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_7f344.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_7f344.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_7f344.dir/objects.a @CMakeFiles\cmTC_7f344.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=strtol -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_7f344.dir/objects.a -Wl,--no-whole-archive  -o cmTC_7f344.exe -Wl,--out-implib,libcmTC_7f344.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_7f344.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function strtol exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_c0eb7/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_c0eb7.dir\build.make CMakeFiles/cmTC_c0eb7.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_c0eb7.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=strtol -O3 -DNDEBUG   -o CMakeFiles\cmTC_c0eb7.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

Linking C executable cmTC_c0eb7.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_c0eb7.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_c0eb7.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_c0eb7.dir/objects.a @CMakeFiles\cmTC_c0eb7.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=strtol -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_c0eb7.dir/objects.a -Wl,--no-whole-archive  -o cmTC_c0eb7.exe -Wl,--out-implib,libcmTC_c0eb7.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_c0eb7.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function strtoull exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_d1516/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_d1516.dir\build.make CMakeFiles/cmTC_d1516.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_d1516.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=strtoull -O3 -DNDEBUG   -o CMakeFiles\cmTC_d1516.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

Linking C executable cmTC_d1516.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_d1516.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_d1516.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_d1516.dir/objects.a @CMakeFiles\cmTC_d1516.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=strtoull -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_d1516.dir/objects.a -Wl,--no-whole-archive  -o cmTC_d1516.exe -Wl,--out-implib,libcmTC_d1516.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_d1516.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the function lfind exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_d7a14/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_d7a14.dir\build.make CMakeFiles/cmTC_d7a14.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_d7a14.dir/CheckFunctionExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=lfind -O3 -DNDEBUG   -o CMakeFiles\cmTC_d7a14.dir\CheckFunctionExists.c.obj   -c "C:\Program Files\CMake\share\cmake-3.13\Modules\CheckFunctionExists.c"

Linking C executable cmTC_d7a14.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_d7a14.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_d7a14.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_d7a14.dir/objects.a @CMakeFiles\cmTC_d7a14.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DCHECK_FUNCTION_EXISTS=lfind -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_d7a14.dir/objects.a -Wl,--no-whole-archive  -o cmTC_d7a14.exe -Wl,--out-implib,libcmTC_d7a14.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_d7a14.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Performing C SOURCE FILE Test HAVE_SNPRINTF succeeded with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_f545e/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_f545e.dir\build.make CMakeFiles/cmTC_f545e.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_f545e.dir/src.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DHAVE_SNPRINTF -O3 -DNDEBUG   -o CMakeFiles\cmTC_f545e.dir\src.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\src.c

Linking C executable cmTC_f545e.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_f545e.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_f545e.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_f545e.dir/objects.a @CMakeFiles\cmTC_f545e.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden -DHAVE_SNPRINTF -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_f545e.dir/objects.a -Wl,--no-whole-archive  -o cmTC_f545e.exe -Wl,--out-implib,libcmTC_f545e.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_f545e.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'


Source file was:

#include <stdio.h>
int main(void) {
  char buf[10];
  snprintf(buf, 10, "Test %d", 1);
  return 0;
}
Determining if the system is big endian passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_d3fce/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_d3fce.dir\build.make CMakeFiles/cmTC_d3fce.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_d3fce.dir/TestEndianess.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_d3fce.dir\TestEndianess.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\TestEndianess.c

Linking C executable cmTC_d3fce.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_d3fce.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_d3fce.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_d3fce.dir/objects.a @CMakeFiles\cmTC_d3fce.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_d3fce.dir/objects.a -Wl,--no-whole-archive  -o cmTC_d3fce.exe -Wl,--out-implib,libcmTC_d3fce.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_d3fce.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'


TestEndianess.c:
/* A 16 bit integer is required. */
typedef unsigned short cmakeint16;

/* On a little endian machine, these 16bit ints will give "THIS IS LITTLE ENDIAN."
   On a big endian machine the characters will be exchanged pairwise. */
const cmakeint16 info_little[] =  {0x4854, 0x5349, 0x4920, 0x2053, 0x494c, 0x5454, 0x454c, 0x4520, 0x444e, 0x4149, 0x2e4e, 0x0000};

/* on a big endian machine, these 16bit ints will give "THIS IS BIG ENDIAN."
   On a little endian machine the characters will be exchanged pairwise. */
const cmakeint16 info_big[] =     {0x5448, 0x4953, 0x2049, 0x5320, 0x4249, 0x4720, 0x454e, 0x4449, 0x414e, 0x2e2e, 0x0000};

#ifdef __CLASSIC_C__
int main(argc, argv) int argc; char *argv[];
#else
int main(int argc, char *argv[])
#endif
{
  int require = 0;
  require += info_little[argc];
  require += info_big[argc];
  (void)argv;
  return require;
}


Determining if the include file pthread.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_3a08b/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_3a08b.dir\build.make CMakeFiles/cmTC_3a08b.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_3a08b.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_3a08b.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_3a08b.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_3a08b.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_3a08b.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_3a08b.dir/objects.a @CMakeFiles\cmTC_3a08b.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_3a08b.dir/objects.a -Wl,--no-whole-archive  -o cmTC_3a08b.exe -Wl,--out-implib,libcmTC_3a08b.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_3a08b.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



Determining if the pthread_create exist passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_188d3/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_188d3.dir\build.make CMakeFiles/cmTC_188d3.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_188d3.dir/CheckSymbolExists.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_188d3.dir\CheckSymbolExists.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckSymbolExists.c

Linking C executable cmTC_188d3.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_188d3.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_188d3.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_188d3.dir/objects.a @CMakeFiles\cmTC_188d3.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_188d3.dir/objects.a -Wl,--no-whole-archive  -o cmTC_188d3.exe -Wl,--out-implib,libcmTC_188d3.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_188d3.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'


File D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp/CheckSymbolExists.c:
/* */
#include <pthread.h>

int main(int argc, char** argv)
{
  (void)argv;
#ifndef pthread_create
  return ((int*)(&pthread_create))[argc];
#else
  (void)argc;
  return 0;
#endif
}

Determining if the include file dshow.h exists passed with the following output:
Change Dir: D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp

Run Build Command:"E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe" "cmTC_febed/fast"
E:/software/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/mingw32-make.exe -f CMakeFiles\cmTC_febed.dir\build.make CMakeFiles/cmTC_febed.dir/build

mingw32-make.exe[1]: Entering directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'

Building C object CMakeFiles/cmTC_febed.dir/CheckIncludeFile.c.obj

E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe   -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG   -o CMakeFiles\cmTC_febed.dir\CheckIncludeFile.c.obj   -c D:\unet\opencv\opencv\mingw_build\CMakeFiles\CMakeTmp\CheckIncludeFile.c

Linking C executable cmTC_febed.exe

"C:\Program Files\CMake\bin\cmake.exe" -E cmake_link_script CMakeFiles\cmTC_febed.dir\link.txt --verbose=1

"C:\Program Files\CMake\bin\cmake.exe" -E remove -f CMakeFiles\cmTC_febed.dir/objects.a
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\ar.exe cr CMakeFiles\cmTC_febed.dir/objects.a @CMakeFiles\cmTC_febed.dir\objects1.rsp
E:\software\x86_64-8.1.0-release-posix-seh-rt_v6-rev0\mingw64\bin\gcc.exe    -fsigned-char -W -Wall -Werror=return-type -Werror=non-virtual-dtor -Werror=address -Werror=sequence-point -Wformat -Werror=format-security -Wmissing-declarations -Wmissing-prototypes -Wstrict-prototypes -Wundef -Winit-self -Wpointer-arith -Wshadow -Wuninitialized -Winit-self -Wno-comment -Wimplicit-fallthrough=3 -Wno-strict-overflow -fdiagnostics-show-option -Wno-long-long -fomit-frame-pointer -ffunction-sections -fdata-sections  -msse -msse2 -msse3 -fvisibility=hidden  -O3 -DNDEBUG    -Wl,--gc-sections  -Wl,--whole-archive CMakeFiles\cmTC_febed.dir/objects.a -Wl,--no-whole-archive  -o cmTC_febed.exe -Wl,--out-implib,libcmTC_febed.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\cmTC_febed.dir\linklibs.rsp
mingw32-make.exe[1]: Leaving directory 'D:/unet/opencv/opencv/mingw_build/CMakeFiles/CMakeTmp'



