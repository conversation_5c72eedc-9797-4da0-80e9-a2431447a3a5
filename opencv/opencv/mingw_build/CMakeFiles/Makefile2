# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# The main recursive all target
all:

.PHONY : all

# The main recursive preinstall target
preinstall:

.PHONY : preinstall

# The main recursive clean target
clean:

.PHONY : clean

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\unet\opencv\opencv\sources

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\unet\opencv\opencv\mingw_build

#=============================================================================
# Target rules for target CMakeFiles/uninstall.dir

# All Build rule for target.
CMakeFiles/uninstall.dir/all:
	$(MAKE) -f CMakeFiles\uninstall.dir\build.make CMakeFiles/uninstall.dir/depend
	$(MAKE) -f CMakeFiles\uninstall.dir\build.make CMakeFiles/uninstall.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num= "Built target uninstall"
.PHONY : CMakeFiles/uninstall.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/uninstall.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
	$(MAKE) -f CMakeFiles\Makefile2 CMakeFiles/uninstall.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : CMakeFiles/uninstall.dir/rule

# Convenience name for target.
uninstall: CMakeFiles/uninstall.dir/rule

.PHONY : uninstall

# clean rule for target.
CMakeFiles/uninstall.dir/clean:
	$(MAKE) -f CMakeFiles\uninstall.dir\build.make CMakeFiles/uninstall.dir/clean
.PHONY : CMakeFiles/uninstall.dir/clean

# clean rule for target.
clean: CMakeFiles/uninstall.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/opencv_tests.dir

# All Build rule for target.
CMakeFiles/opencv_tests.dir/all:
	$(MAKE) -f CMakeFiles\opencv_tests.dir\build.make CMakeFiles/opencv_tests.dir/depend
	$(MAKE) -f CMakeFiles\opencv_tests.dir\build.make CMakeFiles/opencv_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num= "Built target opencv_tests"
.PHONY : CMakeFiles/opencv_tests.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/opencv_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
	$(MAKE) -f CMakeFiles\Makefile2 CMakeFiles/opencv_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : CMakeFiles/opencv_tests.dir/rule

# Convenience name for target.
opencv_tests: CMakeFiles/opencv_tests.dir/rule

.PHONY : opencv_tests

# clean rule for target.
CMakeFiles/opencv_tests.dir/clean:
	$(MAKE) -f CMakeFiles\opencv_tests.dir\build.make CMakeFiles/opencv_tests.dir/clean
.PHONY : CMakeFiles/opencv_tests.dir/clean

# clean rule for target.
clean: CMakeFiles/opencv_tests.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/opencv_modules.dir

# All Build rule for target.
CMakeFiles/opencv_modules.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
CMakeFiles/opencv_modules.dir/all: modules/ml/CMakeFiles/opencv_ml.dir/all
CMakeFiles/opencv_modules.dir/all: modules/flann/CMakeFiles/opencv_flann.dir/all
CMakeFiles/opencv_modules.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
CMakeFiles/opencv_modules.dir/all: modules/photo/CMakeFiles/opencv_photo.dir/all
CMakeFiles/opencv_modules.dir/all: modules/dnn/CMakeFiles/opencv_dnn.dir/all
CMakeFiles/opencv_modules.dir/all: modules/features2d/CMakeFiles/opencv_features2d.dir/all
CMakeFiles/opencv_modules.dir/all: modules/gapi/CMakeFiles/opencv_gapi.dir/all
CMakeFiles/opencv_modules.dir/all: modules/videoio/CMakeFiles/opencv_videoio.dir/all
CMakeFiles/opencv_modules.dir/all: modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all
CMakeFiles/opencv_modules.dir/all: modules/calib3d/CMakeFiles/opencv_calib3d.dir/all
CMakeFiles/opencv_modules.dir/all: modules/highgui/CMakeFiles/opencv_highgui.dir/all
CMakeFiles/opencv_modules.dir/all: modules/objdetect/CMakeFiles/opencv_objdetect.dir/all
CMakeFiles/opencv_modules.dir/all: modules/video/CMakeFiles/opencv_video.dir/all
CMakeFiles/opencv_modules.dir/all: modules/stitching/CMakeFiles/opencv_stitching.dir/all
	$(MAKE) -f CMakeFiles\opencv_modules.dir\build.make CMakeFiles/opencv_modules.dir/depend
	$(MAKE) -f CMakeFiles\opencv_modules.dir\build.make CMakeFiles/opencv_modules.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num= "Built target opencv_modules"
.PHONY : CMakeFiles/opencv_modules.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/opencv_modules.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 96
	$(MAKE) -f CMakeFiles\Makefile2 CMakeFiles/opencv_modules.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : CMakeFiles/opencv_modules.dir/rule

# Convenience name for target.
opencv_modules: CMakeFiles/opencv_modules.dir/rule

.PHONY : opencv_modules

# clean rule for target.
CMakeFiles/opencv_modules.dir/clean:
	$(MAKE) -f CMakeFiles\opencv_modules.dir\build.make CMakeFiles/opencv_modules.dir/clean
.PHONY : CMakeFiles/opencv_modules.dir/clean

# clean rule for target.
clean: CMakeFiles/opencv_modules.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/opencv_perf_tests.dir

# All Build rule for target.
CMakeFiles/opencv_perf_tests.dir/all:
	$(MAKE) -f CMakeFiles\opencv_perf_tests.dir\build.make CMakeFiles/opencv_perf_tests.dir/depend
	$(MAKE) -f CMakeFiles\opencv_perf_tests.dir\build.make CMakeFiles/opencv_perf_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num= "Built target opencv_perf_tests"
.PHONY : CMakeFiles/opencv_perf_tests.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/opencv_perf_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
	$(MAKE) -f CMakeFiles\Makefile2 CMakeFiles/opencv_perf_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : CMakeFiles/opencv_perf_tests.dir/rule

# Convenience name for target.
opencv_perf_tests: CMakeFiles/opencv_perf_tests.dir/rule

.PHONY : opencv_perf_tests

# clean rule for target.
CMakeFiles/opencv_perf_tests.dir/clean:
	$(MAKE) -f CMakeFiles\opencv_perf_tests.dir\build.make CMakeFiles/opencv_perf_tests.dir/clean
.PHONY : CMakeFiles/opencv_perf_tests.dir/clean

# clean rule for target.
clean: CMakeFiles/opencv_perf_tests.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory 3rdparty/zlib

# Convenience name for "all" pass in the directory.
3rdparty/zlib/all: 3rdparty/zlib/CMakeFiles/zlib.dir/all

.PHONY : 3rdparty/zlib/all

# Convenience name for "clean" pass in the directory.
3rdparty/zlib/clean: 3rdparty/zlib/CMakeFiles/zlib.dir/clean

.PHONY : 3rdparty/zlib/clean

# Convenience name for "preinstall" pass in the directory.
3rdparty/zlib/preinstall:

.PHONY : 3rdparty/zlib/preinstall

#=============================================================================
# Target rules for target 3rdparty/zlib/CMakeFiles/zlib.dir

# All Build rule for target.
3rdparty/zlib/CMakeFiles/zlib.dir/all:
	$(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/depend
	$(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=99,100 "Built target zlib"
.PHONY : 3rdparty/zlib/CMakeFiles/zlib.dir/all

# Include target in all.
all: 3rdparty/zlib/CMakeFiles/zlib.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
3rdparty/zlib/CMakeFiles/zlib.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 2
	$(MAKE) -f CMakeFiles\Makefile2 3rdparty/zlib/CMakeFiles/zlib.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : 3rdparty/zlib/CMakeFiles/zlib.dir/rule

# Convenience name for target.
zlib: 3rdparty/zlib/CMakeFiles/zlib.dir/rule

.PHONY : zlib

# clean rule for target.
3rdparty/zlib/CMakeFiles/zlib.dir/clean:
	$(MAKE) -f 3rdparty\zlib\CMakeFiles\zlib.dir\build.make 3rdparty/zlib/CMakeFiles/zlib.dir/clean
.PHONY : 3rdparty/zlib/CMakeFiles/zlib.dir/clean

# clean rule for target.
clean: 3rdparty/zlib/CMakeFiles/zlib.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory 3rdparty/libjpeg-turbo

# Convenience name for "all" pass in the directory.
3rdparty/libjpeg-turbo/all: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/all

.PHONY : 3rdparty/libjpeg-turbo/all

# Convenience name for "clean" pass in the directory.
3rdparty/libjpeg-turbo/clean: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/clean

.PHONY : 3rdparty/libjpeg-turbo/clean

# Convenience name for "preinstall" pass in the directory.
3rdparty/libjpeg-turbo/preinstall:

.PHONY : 3rdparty/libjpeg-turbo/preinstall

#=============================================================================
# Target rules for target 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir

# All Build rule for target.
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/all:
	$(MAKE) -f 3rdparty\libjpeg-turbo\CMakeFiles\libjpeg-turbo.dir\build.make 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/depend
	$(MAKE) -f 3rdparty\libjpeg-turbo\CMakeFiles\libjpeg-turbo.dir\build.make 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=16,17,18,19,20 "Built target libjpeg-turbo"
.PHONY : 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/all

# Include target in all.
all: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 5
	$(MAKE) -f CMakeFiles\Makefile2 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/rule

# Convenience name for target.
libjpeg-turbo: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/rule

.PHONY : libjpeg-turbo

# clean rule for target.
3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/clean:
	$(MAKE) -f 3rdparty\libjpeg-turbo\CMakeFiles\libjpeg-turbo.dir\build.make 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/clean
.PHONY : 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/clean

# clean rule for target.
clean: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory 3rdparty/libtiff

# Convenience name for "all" pass in the directory.
3rdparty/libtiff/all: 3rdparty/libtiff/CMakeFiles/libtiff.dir/all

.PHONY : 3rdparty/libtiff/all

# Convenience name for "clean" pass in the directory.
3rdparty/libtiff/clean: 3rdparty/libtiff/CMakeFiles/libtiff.dir/clean

.PHONY : 3rdparty/libtiff/clean

# Convenience name for "preinstall" pass in the directory.
3rdparty/libtiff/preinstall:

.PHONY : 3rdparty/libtiff/preinstall

#=============================================================================
# Target rules for target 3rdparty/libtiff/CMakeFiles/libtiff.dir

# All Build rule for target.
3rdparty/libtiff/CMakeFiles/libtiff.dir/all: 3rdparty/zlib/CMakeFiles/zlib.dir/all
	$(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/depend
	$(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=30,31,32,33 "Built target libtiff"
.PHONY : 3rdparty/libtiff/CMakeFiles/libtiff.dir/all

# Include target in all.
all: 3rdparty/libtiff/CMakeFiles/libtiff.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
3rdparty/libtiff/CMakeFiles/libtiff.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 6
	$(MAKE) -f CMakeFiles\Makefile2 3rdparty/libtiff/CMakeFiles/libtiff.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : 3rdparty/libtiff/CMakeFiles/libtiff.dir/rule

# Convenience name for target.
libtiff: 3rdparty/libtiff/CMakeFiles/libtiff.dir/rule

.PHONY : libtiff

# clean rule for target.
3rdparty/libtiff/CMakeFiles/libtiff.dir/clean:
	$(MAKE) -f 3rdparty\libtiff\CMakeFiles\libtiff.dir\build.make 3rdparty/libtiff/CMakeFiles/libtiff.dir/clean
.PHONY : 3rdparty/libtiff/CMakeFiles/libtiff.dir/clean

# clean rule for target.
clean: 3rdparty/libtiff/CMakeFiles/libtiff.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory 3rdparty/libwebp

# Convenience name for "all" pass in the directory.
3rdparty/libwebp/all: 3rdparty/libwebp/CMakeFiles/libwebp.dir/all

.PHONY : 3rdparty/libwebp/all

# Convenience name for "clean" pass in the directory.
3rdparty/libwebp/clean: 3rdparty/libwebp/CMakeFiles/libwebp.dir/clean

.PHONY : 3rdparty/libwebp/clean

# Convenience name for "preinstall" pass in the directory.
3rdparty/libwebp/preinstall:

.PHONY : 3rdparty/libwebp/preinstall

#=============================================================================
# Target rules for target 3rdparty/libwebp/CMakeFiles/libwebp.dir

# All Build rule for target.
3rdparty/libwebp/CMakeFiles/libwebp.dir/all:
	$(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/depend
	$(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=34,35,36,37,38,39,40,41,42,43,44 "Built target libwebp"
.PHONY : 3rdparty/libwebp/CMakeFiles/libwebp.dir/all

# Include target in all.
all: 3rdparty/libwebp/CMakeFiles/libwebp.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
3rdparty/libwebp/CMakeFiles/libwebp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 11
	$(MAKE) -f CMakeFiles\Makefile2 3rdparty/libwebp/CMakeFiles/libwebp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : 3rdparty/libwebp/CMakeFiles/libwebp.dir/rule

# Convenience name for target.
libwebp: 3rdparty/libwebp/CMakeFiles/libwebp.dir/rule

.PHONY : libwebp

# clean rule for target.
3rdparty/libwebp/CMakeFiles/libwebp.dir/clean:
	$(MAKE) -f 3rdparty\libwebp\CMakeFiles\libwebp.dir\build.make 3rdparty/libwebp/CMakeFiles/libwebp.dir/clean
.PHONY : 3rdparty/libwebp/CMakeFiles/libwebp.dir/clean

# clean rule for target.
clean: 3rdparty/libwebp/CMakeFiles/libwebp.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory 3rdparty/libjasper

# Convenience name for "all" pass in the directory.
3rdparty/libjasper/all: 3rdparty/libjasper/CMakeFiles/libjasper.dir/all

.PHONY : 3rdparty/libjasper/all

# Convenience name for "clean" pass in the directory.
3rdparty/libjasper/clean: 3rdparty/libjasper/CMakeFiles/libjasper.dir/clean

.PHONY : 3rdparty/libjasper/clean

# Convenience name for "preinstall" pass in the directory.
3rdparty/libjasper/preinstall:

.PHONY : 3rdparty/libjasper/preinstall

#=============================================================================
# Target rules for target 3rdparty/libjasper/CMakeFiles/libjasper.dir

# All Build rule for target.
3rdparty/libjasper/CMakeFiles/libjasper.dir/all:
	$(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/depend
	$(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=13,14,15 "Built target libjasper"
.PHONY : 3rdparty/libjasper/CMakeFiles/libjasper.dir/all

# Include target in all.
all: 3rdparty/libjasper/CMakeFiles/libjasper.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
3rdparty/libjasper/CMakeFiles/libjasper.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 3
	$(MAKE) -f CMakeFiles\Makefile2 3rdparty/libjasper/CMakeFiles/libjasper.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : 3rdparty/libjasper/CMakeFiles/libjasper.dir/rule

# Convenience name for target.
libjasper: 3rdparty/libjasper/CMakeFiles/libjasper.dir/rule

.PHONY : libjasper

# clean rule for target.
3rdparty/libjasper/CMakeFiles/libjasper.dir/clean:
	$(MAKE) -f 3rdparty\libjasper\CMakeFiles\libjasper.dir\build.make 3rdparty/libjasper/CMakeFiles/libjasper.dir/clean
.PHONY : 3rdparty/libjasper/CMakeFiles/libjasper.dir/clean

# clean rule for target.
clean: 3rdparty/libjasper/CMakeFiles/libjasper.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory 3rdparty/libpng

# Convenience name for "all" pass in the directory.
3rdparty/libpng/all: 3rdparty/libpng/CMakeFiles/libpng.dir/all

.PHONY : 3rdparty/libpng/all

# Convenience name for "clean" pass in the directory.
3rdparty/libpng/clean: 3rdparty/libpng/CMakeFiles/libpng.dir/clean

.PHONY : 3rdparty/libpng/clean

# Convenience name for "preinstall" pass in the directory.
3rdparty/libpng/preinstall:

.PHONY : 3rdparty/libpng/preinstall

#=============================================================================
# Target rules for target 3rdparty/libpng/CMakeFiles/libpng.dir

# All Build rule for target.
3rdparty/libpng/CMakeFiles/libpng.dir/all: 3rdparty/zlib/CMakeFiles/zlib.dir/all
	$(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/depend
	$(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=21,22 "Built target libpng"
.PHONY : 3rdparty/libpng/CMakeFiles/libpng.dir/all

# Include target in all.
all: 3rdparty/libpng/CMakeFiles/libpng.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
3rdparty/libpng/CMakeFiles/libpng.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 4
	$(MAKE) -f CMakeFiles\Makefile2 3rdparty/libpng/CMakeFiles/libpng.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : 3rdparty/libpng/CMakeFiles/libpng.dir/rule

# Convenience name for target.
libpng: 3rdparty/libpng/CMakeFiles/libpng.dir/rule

.PHONY : libpng

# clean rule for target.
3rdparty/libpng/CMakeFiles/libpng.dir/clean:
	$(MAKE) -f 3rdparty\libpng\CMakeFiles\libpng.dir\build.make 3rdparty/libpng/CMakeFiles/libpng.dir/clean
.PHONY : 3rdparty/libpng/CMakeFiles/libpng.dir/clean

# clean rule for target.
clean: 3rdparty/libpng/CMakeFiles/libpng.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory 3rdparty/openexr

# Convenience name for "all" pass in the directory.
3rdparty/openexr/all: 3rdparty/openexr/CMakeFiles/IlmImf.dir/all

.PHONY : 3rdparty/openexr/all

# Convenience name for "clean" pass in the directory.
3rdparty/openexr/clean: 3rdparty/openexr/CMakeFiles/IlmImf.dir/clean

.PHONY : 3rdparty/openexr/clean

# Convenience name for "preinstall" pass in the directory.
3rdparty/openexr/preinstall:

.PHONY : 3rdparty/openexr/preinstall

#=============================================================================
# Target rules for target 3rdparty/openexr/CMakeFiles/IlmImf.dir

# All Build rule for target.
3rdparty/openexr/CMakeFiles/IlmImf.dir/all: 3rdparty/zlib/CMakeFiles/zlib.dir/all
	$(MAKE) -f 3rdparty\openexr\CMakeFiles\IlmImf.dir\build.make 3rdparty/openexr/CMakeFiles/IlmImf.dir/depend
	$(MAKE) -f 3rdparty\openexr\CMakeFiles\IlmImf.dir\build.make 3rdparty/openexr/CMakeFiles/IlmImf.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10 "Built target IlmImf"
.PHONY : 3rdparty/openexr/CMakeFiles/IlmImf.dir/all

# Include target in all.
all: 3rdparty/openexr/CMakeFiles/IlmImf.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
3rdparty/openexr/CMakeFiles/IlmImf.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 12
	$(MAKE) -f CMakeFiles\Makefile2 3rdparty/openexr/CMakeFiles/IlmImf.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : 3rdparty/openexr/CMakeFiles/IlmImf.dir/rule

# Convenience name for target.
IlmImf: 3rdparty/openexr/CMakeFiles/IlmImf.dir/rule

.PHONY : IlmImf

# clean rule for target.
3rdparty/openexr/CMakeFiles/IlmImf.dir/clean:
	$(MAKE) -f 3rdparty\openexr\CMakeFiles\IlmImf.dir\build.make 3rdparty/openexr/CMakeFiles/IlmImf.dir/clean
.PHONY : 3rdparty/openexr/CMakeFiles/IlmImf.dir/clean

# clean rule for target.
clean: 3rdparty/openexr/CMakeFiles/IlmImf.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory 3rdparty/protobuf

# Convenience name for "all" pass in the directory.
3rdparty/protobuf/all: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/all

.PHONY : 3rdparty/protobuf/all

# Convenience name for "clean" pass in the directory.
3rdparty/protobuf/clean: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/clean

.PHONY : 3rdparty/protobuf/clean

# Convenience name for "preinstall" pass in the directory.
3rdparty/protobuf/preinstall:

.PHONY : 3rdparty/protobuf/preinstall

#=============================================================================
# Target rules for target 3rdparty/protobuf/CMakeFiles/libprotobuf.dir

# All Build rule for target.
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/all:
	$(MAKE) -f 3rdparty\protobuf\CMakeFiles\libprotobuf.dir\build.make 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/depend
	$(MAKE) -f 3rdparty\protobuf\CMakeFiles\libprotobuf.dir\build.make 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=23,24,25,26,27,28,29 "Built target libprotobuf"
.PHONY : 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/all

# Include target in all.
all: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 7
	$(MAKE) -f CMakeFiles\Makefile2 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/rule

# Convenience name for target.
libprotobuf: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/rule

.PHONY : libprotobuf

# clean rule for target.
3rdparty/protobuf/CMakeFiles/libprotobuf.dir/clean:
	$(MAKE) -f 3rdparty\protobuf\CMakeFiles\libprotobuf.dir\build.make 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/clean
.PHONY : 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/clean

# clean rule for target.
clean: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory 3rdparty/quirc

# Convenience name for "all" pass in the directory.
3rdparty/quirc/all: 3rdparty/quirc/CMakeFiles/quirc.dir/all

.PHONY : 3rdparty/quirc/all

# Convenience name for "clean" pass in the directory.
3rdparty/quirc/clean: 3rdparty/quirc/CMakeFiles/quirc.dir/clean

.PHONY : 3rdparty/quirc/clean

# Convenience name for "preinstall" pass in the directory.
3rdparty/quirc/preinstall:

.PHONY : 3rdparty/quirc/preinstall

#=============================================================================
# Target rules for target 3rdparty/quirc/CMakeFiles/quirc.dir

# All Build rule for target.
3rdparty/quirc/CMakeFiles/quirc.dir/all:
	$(MAKE) -f 3rdparty\quirc\CMakeFiles\quirc.dir\build.make 3rdparty/quirc/CMakeFiles/quirc.dir/depend
	$(MAKE) -f 3rdparty\quirc\CMakeFiles\quirc.dir\build.make 3rdparty/quirc/CMakeFiles/quirc.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num= "Built target quirc"
.PHONY : 3rdparty/quirc/CMakeFiles/quirc.dir/all

# Include target in all.
all: 3rdparty/quirc/CMakeFiles/quirc.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
3rdparty/quirc/CMakeFiles/quirc.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
	$(MAKE) -f CMakeFiles\Makefile2 3rdparty/quirc/CMakeFiles/quirc.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : 3rdparty/quirc/CMakeFiles/quirc.dir/rule

# Convenience name for target.
quirc: 3rdparty/quirc/CMakeFiles/quirc.dir/rule

.PHONY : quirc

# clean rule for target.
3rdparty/quirc/CMakeFiles/quirc.dir/clean:
	$(MAKE) -f 3rdparty\quirc\CMakeFiles\quirc.dir\build.make 3rdparty/quirc/CMakeFiles/quirc.dir/clean
.PHONY : 3rdparty/quirc/CMakeFiles/quirc.dir/clean

# clean rule for target.
clean: 3rdparty/quirc/CMakeFiles/quirc.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory include

# Convenience name for "all" pass in the directory.
include/all:

.PHONY : include/all

# Convenience name for "clean" pass in the directory.
include/clean:

.PHONY : include/clean

# Convenience name for "preinstall" pass in the directory.
include/preinstall:

.PHONY : include/preinstall

#=============================================================================
# Directory level rules for directory modules

# Convenience name for "all" pass in the directory.
modules/all: modules/CMakeFiles/ade.dir/all
modules/all: modules/.firstpass/calib3d/all
modules/all: modules/.firstpass/core/all
modules/all: modules/.firstpass/dnn/all
modules/all: modules/.firstpass/features2d/all
modules/all: modules/.firstpass/flann/all
modules/all: modules/.firstpass/gapi/all
modules/all: modules/.firstpass/highgui/all
modules/all: modules/.firstpass/imgcodecs/all
modules/all: modules/.firstpass/imgproc/all
modules/all: modules/.firstpass/java/all
modules/all: modules/.firstpass/js/all
modules/all: modules/.firstpass/ml/all
modules/all: modules/.firstpass/objdetect/all
modules/all: modules/.firstpass/photo/all
modules/all: modules/.firstpass/python/all
modules/all: modules/.firstpass/stitching/all
modules/all: modules/.firstpass/ts/all
modules/all: modules/.firstpass/video/all
modules/all: modules/.firstpass/videoio/all
modules/all: modules/.firstpass/world/all
modules/all: modules/core/all
modules/all: modules/flann/all
modules/all: modules/imgproc/all
modules/all: modules/java_bindings_generator/all
modules/all: modules/ml/all
modules/all: modules/photo/all
modules/all: modules/python_tests/all
modules/all: modules/dnn/all
modules/all: modules/features2d/all
modules/all: modules/gapi/all
modules/all: modules/imgcodecs/all
modules/all: modules/videoio/all
modules/all: modules/calib3d/all
modules/all: modules/highgui/all
modules/all: modules/objdetect/all
modules/all: modules/stitching/all
modules/all: modules/video/all
modules/all: modules/python_bindings_generator/all
modules/all: modules/python3/all

.PHONY : modules/all

# Convenience name for "clean" pass in the directory.
modules/clean: modules/CMakeFiles/ade.dir/clean
modules/clean: modules/.firstpass/calib3d/clean
modules/clean: modules/.firstpass/core/clean
modules/clean: modules/.firstpass/dnn/clean
modules/clean: modules/.firstpass/features2d/clean
modules/clean: modules/.firstpass/flann/clean
modules/clean: modules/.firstpass/gapi/clean
modules/clean: modules/.firstpass/highgui/clean
modules/clean: modules/.firstpass/imgcodecs/clean
modules/clean: modules/.firstpass/imgproc/clean
modules/clean: modules/.firstpass/java/clean
modules/clean: modules/.firstpass/js/clean
modules/clean: modules/.firstpass/ml/clean
modules/clean: modules/.firstpass/objdetect/clean
modules/clean: modules/.firstpass/photo/clean
modules/clean: modules/.firstpass/python/clean
modules/clean: modules/.firstpass/stitching/clean
modules/clean: modules/.firstpass/ts/clean
modules/clean: modules/.firstpass/video/clean
modules/clean: modules/.firstpass/videoio/clean
modules/clean: modules/.firstpass/world/clean
modules/clean: modules/core/clean
modules/clean: modules/flann/clean
modules/clean: modules/imgproc/clean
modules/clean: modules/java_bindings_generator/clean
modules/clean: modules/ml/clean
modules/clean: modules/photo/clean
modules/clean: modules/python_tests/clean
modules/clean: modules/dnn/clean
modules/clean: modules/features2d/clean
modules/clean: modules/gapi/clean
modules/clean: modules/imgcodecs/clean
modules/clean: modules/videoio/clean
modules/clean: modules/calib3d/clean
modules/clean: modules/highgui/clean
modules/clean: modules/objdetect/clean
modules/clean: modules/stitching/clean
modules/clean: modules/video/clean
modules/clean: modules/python_bindings_generator/clean
modules/clean: modules/python3/clean

.PHONY : modules/clean

# Convenience name for "preinstall" pass in the directory.
modules/preinstall: modules/.firstpass/calib3d/preinstall
modules/preinstall: modules/.firstpass/core/preinstall
modules/preinstall: modules/.firstpass/dnn/preinstall
modules/preinstall: modules/.firstpass/features2d/preinstall
modules/preinstall: modules/.firstpass/flann/preinstall
modules/preinstall: modules/.firstpass/gapi/preinstall
modules/preinstall: modules/.firstpass/highgui/preinstall
modules/preinstall: modules/.firstpass/imgcodecs/preinstall
modules/preinstall: modules/.firstpass/imgproc/preinstall
modules/preinstall: modules/.firstpass/java/preinstall
modules/preinstall: modules/.firstpass/js/preinstall
modules/preinstall: modules/.firstpass/ml/preinstall
modules/preinstall: modules/.firstpass/objdetect/preinstall
modules/preinstall: modules/.firstpass/photo/preinstall
modules/preinstall: modules/.firstpass/python/preinstall
modules/preinstall: modules/.firstpass/stitching/preinstall
modules/preinstall: modules/.firstpass/ts/preinstall
modules/preinstall: modules/.firstpass/video/preinstall
modules/preinstall: modules/.firstpass/videoio/preinstall
modules/preinstall: modules/.firstpass/world/preinstall
modules/preinstall: modules/core/preinstall
modules/preinstall: modules/flann/preinstall
modules/preinstall: modules/imgproc/preinstall
modules/preinstall: modules/java_bindings_generator/preinstall
modules/preinstall: modules/ml/preinstall
modules/preinstall: modules/photo/preinstall
modules/preinstall: modules/python_tests/preinstall
modules/preinstall: modules/dnn/preinstall
modules/preinstall: modules/features2d/preinstall
modules/preinstall: modules/gapi/preinstall
modules/preinstall: modules/imgcodecs/preinstall
modules/preinstall: modules/videoio/preinstall
modules/preinstall: modules/calib3d/preinstall
modules/preinstall: modules/highgui/preinstall
modules/preinstall: modules/objdetect/preinstall
modules/preinstall: modules/stitching/preinstall
modules/preinstall: modules/video/preinstall
modules/preinstall: modules/python_bindings_generator/preinstall
modules/preinstall: modules/python3/preinstall

.PHONY : modules/preinstall

#=============================================================================
# Target rules for target modules/CMakeFiles/ade.dir

# All Build rule for target.
modules/CMakeFiles/ade.dir/all:
	$(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/depend
	$(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=11 "Built target ade"
.PHONY : modules/CMakeFiles/ade.dir/all

# Include target in all.
all: modules/CMakeFiles/ade.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/CMakeFiles/ade.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 1
	$(MAKE) -f CMakeFiles\Makefile2 modules/CMakeFiles/ade.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/CMakeFiles/ade.dir/rule

# Convenience name for target.
ade: modules/CMakeFiles/ade.dir/rule

.PHONY : ade

# clean rule for target.
modules/CMakeFiles/ade.dir/clean:
	$(MAKE) -f modules\CMakeFiles\ade.dir\build.make modules/CMakeFiles/ade.dir/clean
.PHONY : modules/CMakeFiles/ade.dir/clean

# clean rule for target.
clean: modules/CMakeFiles/ade.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/.firstpass/calib3d

# Convenience name for "all" pass in the directory.
modules/.firstpass/calib3d/all:

.PHONY : modules/.firstpass/calib3d/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/calib3d/clean:

.PHONY : modules/.firstpass/calib3d/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/calib3d/preinstall:

.PHONY : modules/.firstpass/calib3d/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/core

# Convenience name for "all" pass in the directory.
modules/.firstpass/core/all:

.PHONY : modules/.firstpass/core/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/core/clean:

.PHONY : modules/.firstpass/core/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/core/preinstall:

.PHONY : modules/.firstpass/core/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/dnn

# Convenience name for "all" pass in the directory.
modules/.firstpass/dnn/all:

.PHONY : modules/.firstpass/dnn/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/dnn/clean:

.PHONY : modules/.firstpass/dnn/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/dnn/preinstall:

.PHONY : modules/.firstpass/dnn/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/features2d

# Convenience name for "all" pass in the directory.
modules/.firstpass/features2d/all:

.PHONY : modules/.firstpass/features2d/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/features2d/clean:

.PHONY : modules/.firstpass/features2d/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/features2d/preinstall:

.PHONY : modules/.firstpass/features2d/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/flann

# Convenience name for "all" pass in the directory.
modules/.firstpass/flann/all:

.PHONY : modules/.firstpass/flann/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/flann/clean:

.PHONY : modules/.firstpass/flann/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/flann/preinstall:

.PHONY : modules/.firstpass/flann/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/gapi

# Convenience name for "all" pass in the directory.
modules/.firstpass/gapi/all:

.PHONY : modules/.firstpass/gapi/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/gapi/clean:

.PHONY : modules/.firstpass/gapi/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/gapi/preinstall:

.PHONY : modules/.firstpass/gapi/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/highgui

# Convenience name for "all" pass in the directory.
modules/.firstpass/highgui/all:

.PHONY : modules/.firstpass/highgui/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/highgui/clean:

.PHONY : modules/.firstpass/highgui/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/highgui/preinstall:

.PHONY : modules/.firstpass/highgui/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/imgcodecs

# Convenience name for "all" pass in the directory.
modules/.firstpass/imgcodecs/all:

.PHONY : modules/.firstpass/imgcodecs/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/imgcodecs/clean:

.PHONY : modules/.firstpass/imgcodecs/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/imgcodecs/preinstall:

.PHONY : modules/.firstpass/imgcodecs/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/imgproc

# Convenience name for "all" pass in the directory.
modules/.firstpass/imgproc/all:

.PHONY : modules/.firstpass/imgproc/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/imgproc/clean:

.PHONY : modules/.firstpass/imgproc/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/imgproc/preinstall:

.PHONY : modules/.firstpass/imgproc/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/java

# Convenience name for "all" pass in the directory.
modules/.firstpass/java/all: modules/.firstpass/java/generator/all

.PHONY : modules/.firstpass/java/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/java/clean: modules/.firstpass/java/generator/clean

.PHONY : modules/.firstpass/java/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/java/preinstall: modules/.firstpass/java/generator/preinstall

.PHONY : modules/.firstpass/java/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/java/generator

# Convenience name for "all" pass in the directory.
modules/.firstpass/java/generator/all:

.PHONY : modules/.firstpass/java/generator/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/java/generator/clean:

.PHONY : modules/.firstpass/java/generator/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/java/generator/preinstall:

.PHONY : modules/.firstpass/java/generator/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/js

# Convenience name for "all" pass in the directory.
modules/.firstpass/js/all:

.PHONY : modules/.firstpass/js/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/js/clean:

.PHONY : modules/.firstpass/js/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/js/preinstall:

.PHONY : modules/.firstpass/js/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/ml

# Convenience name for "all" pass in the directory.
modules/.firstpass/ml/all:

.PHONY : modules/.firstpass/ml/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/ml/clean:

.PHONY : modules/.firstpass/ml/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/ml/preinstall:

.PHONY : modules/.firstpass/ml/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/objdetect

# Convenience name for "all" pass in the directory.
modules/.firstpass/objdetect/all:

.PHONY : modules/.firstpass/objdetect/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/objdetect/clean:

.PHONY : modules/.firstpass/objdetect/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/objdetect/preinstall:

.PHONY : modules/.firstpass/objdetect/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/photo

# Convenience name for "all" pass in the directory.
modules/.firstpass/photo/all:

.PHONY : modules/.firstpass/photo/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/photo/clean:

.PHONY : modules/.firstpass/photo/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/photo/preinstall:

.PHONY : modules/.firstpass/photo/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/python

# Convenience name for "all" pass in the directory.
modules/.firstpass/python/all: modules/.firstpass/python/bindings/all
modules/.firstpass/python/all: modules/.firstpass/python/test/all
modules/.firstpass/python/all: modules/.firstpass/python/python2/all
modules/.firstpass/python/all: modules/.firstpass/python/python3/all

.PHONY : modules/.firstpass/python/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/python/clean: modules/.firstpass/python/bindings/clean
modules/.firstpass/python/clean: modules/.firstpass/python/test/clean
modules/.firstpass/python/clean: modules/.firstpass/python/python2/clean
modules/.firstpass/python/clean: modules/.firstpass/python/python3/clean

.PHONY : modules/.firstpass/python/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/python/preinstall: modules/.firstpass/python/bindings/preinstall
modules/.firstpass/python/preinstall: modules/.firstpass/python/test/preinstall
modules/.firstpass/python/preinstall: modules/.firstpass/python/python2/preinstall
modules/.firstpass/python/preinstall: modules/.firstpass/python/python3/preinstall

.PHONY : modules/.firstpass/python/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/python/bindings

# Convenience name for "all" pass in the directory.
modules/.firstpass/python/bindings/all:

.PHONY : modules/.firstpass/python/bindings/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/python/bindings/clean:

.PHONY : modules/.firstpass/python/bindings/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/python/bindings/preinstall:

.PHONY : modules/.firstpass/python/bindings/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/python/test

# Convenience name for "all" pass in the directory.
modules/.firstpass/python/test/all:

.PHONY : modules/.firstpass/python/test/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/python/test/clean:

.PHONY : modules/.firstpass/python/test/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/python/test/preinstall:

.PHONY : modules/.firstpass/python/test/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/python/python2

# Convenience name for "all" pass in the directory.
modules/.firstpass/python/python2/all:

.PHONY : modules/.firstpass/python/python2/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/python/python2/clean:

.PHONY : modules/.firstpass/python/python2/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/python/python2/preinstall:

.PHONY : modules/.firstpass/python/python2/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/python/python3

# Convenience name for "all" pass in the directory.
modules/.firstpass/python/python3/all:

.PHONY : modules/.firstpass/python/python3/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/python/python3/clean:

.PHONY : modules/.firstpass/python/python3/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/python/python3/preinstall:

.PHONY : modules/.firstpass/python/python3/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/stitching

# Convenience name for "all" pass in the directory.
modules/.firstpass/stitching/all:

.PHONY : modules/.firstpass/stitching/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/stitching/clean:

.PHONY : modules/.firstpass/stitching/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/stitching/preinstall:

.PHONY : modules/.firstpass/stitching/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/ts

# Convenience name for "all" pass in the directory.
modules/.firstpass/ts/all:

.PHONY : modules/.firstpass/ts/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/ts/clean:

.PHONY : modules/.firstpass/ts/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/ts/preinstall:

.PHONY : modules/.firstpass/ts/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/video

# Convenience name for "all" pass in the directory.
modules/.firstpass/video/all:

.PHONY : modules/.firstpass/video/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/video/clean:

.PHONY : modules/.firstpass/video/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/video/preinstall:

.PHONY : modules/.firstpass/video/preinstall

#=============================================================================
# Directory level rules for directory modules/.firstpass/videoio

# Convenience name for "all" pass in the directory.
modules/.firstpass/videoio/all: modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir/all

.PHONY : modules/.firstpass/videoio/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/videoio/clean: modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir/clean

.PHONY : modules/.firstpass/videoio/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/videoio/preinstall:

.PHONY : modules/.firstpass/videoio/preinstall

#=============================================================================
# Target rules for target modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir

# All Build rule for target.
modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir/all:
	$(MAKE) -f modules\.firstpass\videoio\CMakeFiles\opencv_videoio_plugins.dir\build.make modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir/depend
	$(MAKE) -f modules\.firstpass\videoio\CMakeFiles\opencv_videoio_plugins.dir\build.make modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num= "Built target opencv_videoio_plugins"
.PHONY : modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir/all

# Include target in all.
all: modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
	$(MAKE) -f CMakeFiles\Makefile2 modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir/rule

# Convenience name for target.
opencv_videoio_plugins: modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir/rule

.PHONY : opencv_videoio_plugins

# clean rule for target.
modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir/clean:
	$(MAKE) -f modules\.firstpass\videoio\CMakeFiles\opencv_videoio_plugins.dir\build.make modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir/clean
.PHONY : modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir/clean

# clean rule for target.
clean: modules/.firstpass/videoio/CMakeFiles/opencv_videoio_plugins.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/.firstpass/world

# Convenience name for "all" pass in the directory.
modules/.firstpass/world/all:

.PHONY : modules/.firstpass/world/all

# Convenience name for "clean" pass in the directory.
modules/.firstpass/world/clean:

.PHONY : modules/.firstpass/world/clean

# Convenience name for "preinstall" pass in the directory.
modules/.firstpass/world/preinstall:

.PHONY : modules/.firstpass/world/preinstall

#=============================================================================
# Directory level rules for directory modules/core

# Convenience name for "all" pass in the directory.
modules/core/all: modules/core/CMakeFiles/opencv_core.dir/all

.PHONY : modules/core/all

# Convenience name for "clean" pass in the directory.
modules/core/clean: modules/core/CMakeFiles/opencv_core.dir/clean

.PHONY : modules/core/clean

# Convenience name for "preinstall" pass in the directory.
modules/core/preinstall:

.PHONY : modules/core/preinstall

#=============================================================================
# Target rules for target modules/core/CMakeFiles/opencv_core.dir

# All Build rule for target.
modules/core/CMakeFiles/opencv_core.dir/all: 3rdparty/zlib/CMakeFiles/zlib.dir/all
	$(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/depend
	$(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=49,50,51,52,53,54,55,56,57 "Built target opencv_core"
.PHONY : modules/core/CMakeFiles/opencv_core.dir/all

# Include target in all.
all: modules/core/CMakeFiles/opencv_core.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/core/CMakeFiles/opencv_core.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 11
	$(MAKE) -f CMakeFiles\Makefile2 modules/core/CMakeFiles/opencv_core.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/core/CMakeFiles/opencv_core.dir/rule

# Convenience name for target.
opencv_core: modules/core/CMakeFiles/opencv_core.dir/rule

.PHONY : opencv_core

# clean rule for target.
modules/core/CMakeFiles/opencv_core.dir/clean:
	$(MAKE) -f modules\core\CMakeFiles\opencv_core.dir\build.make modules/core/CMakeFiles/opencv_core.dir/clean
.PHONY : modules/core/CMakeFiles/opencv_core.dir/clean

# clean rule for target.
clean: modules/core/CMakeFiles/opencv_core.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/flann

# Convenience name for "all" pass in the directory.
modules/flann/all: modules/flann/CMakeFiles/opencv_flann.dir/all

.PHONY : modules/flann/all

# Convenience name for "clean" pass in the directory.
modules/flann/clean: modules/flann/CMakeFiles/opencv_flann.dir/clean

.PHONY : modules/flann/clean

# Convenience name for "preinstall" pass in the directory.
modules/flann/preinstall:

.PHONY : modules/flann/preinstall

#=============================================================================
# Target rules for target modules/flann/CMakeFiles/opencv_flann.dir

# All Build rule for target.
modules/flann/CMakeFiles/opencv_flann.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
	$(MAKE) -f modules\flann\CMakeFiles\opencv_flann.dir\build.make modules/flann/CMakeFiles/opencv_flann.dir/depend
	$(MAKE) -f modules\flann\CMakeFiles\opencv_flann.dir\build.make modules/flann/CMakeFiles/opencv_flann.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num= "Built target opencv_flann"
.PHONY : modules/flann/CMakeFiles/opencv_flann.dir/all

# Include target in all.
all: modules/flann/CMakeFiles/opencv_flann.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/flann/CMakeFiles/opencv_flann.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 11
	$(MAKE) -f CMakeFiles\Makefile2 modules/flann/CMakeFiles/opencv_flann.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/flann/CMakeFiles/opencv_flann.dir/rule

# Convenience name for target.
opencv_flann: modules/flann/CMakeFiles/opencv_flann.dir/rule

.PHONY : opencv_flann

# clean rule for target.
modules/flann/CMakeFiles/opencv_flann.dir/clean:
	$(MAKE) -f modules\flann\CMakeFiles\opencv_flann.dir\build.make modules/flann/CMakeFiles/opencv_flann.dir/clean
.PHONY : modules/flann/CMakeFiles/opencv_flann.dir/clean

# clean rule for target.
clean: modules/flann/CMakeFiles/opencv_flann.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/imgproc

# Convenience name for "all" pass in the directory.
modules/imgproc/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all

.PHONY : modules/imgproc/all

# Convenience name for "clean" pass in the directory.
modules/imgproc/clean: modules/imgproc/CMakeFiles/opencv_imgproc.dir/clean

.PHONY : modules/imgproc/clean

# Convenience name for "preinstall" pass in the directory.
modules/imgproc/preinstall:

.PHONY : modules/imgproc/preinstall

#=============================================================================
# Target rules for target modules/imgproc/CMakeFiles/opencv_imgproc.dir

# All Build rule for target.
modules/imgproc/CMakeFiles/opencv_imgproc.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
	$(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/depend
	$(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=80,81,82,83,84,85,86,87 "Built target opencv_imgproc"
.PHONY : modules/imgproc/CMakeFiles/opencv_imgproc.dir/all

# Include target in all.
all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/imgproc/CMakeFiles/opencv_imgproc.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 19
	$(MAKE) -f CMakeFiles\Makefile2 modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/imgproc/CMakeFiles/opencv_imgproc.dir/rule

# Convenience name for target.
opencv_imgproc: modules/imgproc/CMakeFiles/opencv_imgproc.dir/rule

.PHONY : opencv_imgproc

# clean rule for target.
modules/imgproc/CMakeFiles/opencv_imgproc.dir/clean:
	$(MAKE) -f modules\imgproc\CMakeFiles\opencv_imgproc.dir\build.make modules/imgproc/CMakeFiles/opencv_imgproc.dir/clean
.PHONY : modules/imgproc/CMakeFiles/opencv_imgproc.dir/clean

# clean rule for target.
clean: modules/imgproc/CMakeFiles/opencv_imgproc.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/java_bindings_generator

# Convenience name for "all" pass in the directory.
modules/java_bindings_generator/all:

.PHONY : modules/java_bindings_generator/all

# Convenience name for "clean" pass in the directory.
modules/java_bindings_generator/clean: modules/java_bindings_generator/CMakeFiles/gen_opencv_java_source.dir/clean

.PHONY : modules/java_bindings_generator/clean

# Convenience name for "preinstall" pass in the directory.
modules/java_bindings_generator/preinstall:

.PHONY : modules/java_bindings_generator/preinstall

#=============================================================================
# Target rules for target modules/java_bindings_generator/CMakeFiles/gen_opencv_java_source.dir

# All Build rule for target.
modules/java_bindings_generator/CMakeFiles/gen_opencv_java_source.dir/all:
	$(MAKE) -f modules\java_bindings_generator\CMakeFiles\gen_opencv_java_source.dir\build.make modules/java_bindings_generator/CMakeFiles/gen_opencv_java_source.dir/depend
	$(MAKE) -f modules\java_bindings_generator\CMakeFiles\gen_opencv_java_source.dir\build.make modules/java_bindings_generator/CMakeFiles/gen_opencv_java_source.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=12 "Built target gen_opencv_java_source"
.PHONY : modules/java_bindings_generator/CMakeFiles/gen_opencv_java_source.dir/all

# Build rule for subdir invocation for target.
modules/java_bindings_generator/CMakeFiles/gen_opencv_java_source.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 1
	$(MAKE) -f CMakeFiles\Makefile2 modules/java_bindings_generator/CMakeFiles/gen_opencv_java_source.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/java_bindings_generator/CMakeFiles/gen_opencv_java_source.dir/rule

# Convenience name for target.
gen_opencv_java_source: modules/java_bindings_generator/CMakeFiles/gen_opencv_java_source.dir/rule

.PHONY : gen_opencv_java_source

# clean rule for target.
modules/java_bindings_generator/CMakeFiles/gen_opencv_java_source.dir/clean:
	$(MAKE) -f modules\java_bindings_generator\CMakeFiles\gen_opencv_java_source.dir\build.make modules/java_bindings_generator/CMakeFiles/gen_opencv_java_source.dir/clean
.PHONY : modules/java_bindings_generator/CMakeFiles/gen_opencv_java_source.dir/clean

# clean rule for target.
clean: modules/java_bindings_generator/CMakeFiles/gen_opencv_java_source.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/ml

# Convenience name for "all" pass in the directory.
modules/ml/all: modules/ml/CMakeFiles/opencv_ml.dir/all

.PHONY : modules/ml/all

# Convenience name for "clean" pass in the directory.
modules/ml/clean: modules/ml/CMakeFiles/opencv_ml.dir/clean

.PHONY : modules/ml/clean

# Convenience name for "preinstall" pass in the directory.
modules/ml/preinstall:

.PHONY : modules/ml/preinstall

#=============================================================================
# Target rules for target modules/ml/CMakeFiles/opencv_ml.dir

# All Build rule for target.
modules/ml/CMakeFiles/opencv_ml.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
	$(MAKE) -f modules\ml\CMakeFiles\opencv_ml.dir\build.make modules/ml/CMakeFiles/opencv_ml.dir/depend
	$(MAKE) -f modules\ml\CMakeFiles\opencv_ml.dir\build.make modules/ml/CMakeFiles/opencv_ml.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=89,90 "Built target opencv_ml"
.PHONY : modules/ml/CMakeFiles/opencv_ml.dir/all

# Include target in all.
all: modules/ml/CMakeFiles/opencv_ml.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/ml/CMakeFiles/opencv_ml.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 13
	$(MAKE) -f CMakeFiles\Makefile2 modules/ml/CMakeFiles/opencv_ml.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/ml/CMakeFiles/opencv_ml.dir/rule

# Convenience name for target.
opencv_ml: modules/ml/CMakeFiles/opencv_ml.dir/rule

.PHONY : opencv_ml

# clean rule for target.
modules/ml/CMakeFiles/opencv_ml.dir/clean:
	$(MAKE) -f modules\ml\CMakeFiles\opencv_ml.dir\build.make modules/ml/CMakeFiles/opencv_ml.dir/clean
.PHONY : modules/ml/CMakeFiles/opencv_ml.dir/clean

# clean rule for target.
clean: modules/ml/CMakeFiles/opencv_ml.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/photo

# Convenience name for "all" pass in the directory.
modules/photo/all: modules/photo/CMakeFiles/opencv_photo.dir/all

.PHONY : modules/photo/all

# Convenience name for "clean" pass in the directory.
modules/photo/clean: modules/photo/CMakeFiles/opencv_photo.dir/clean

.PHONY : modules/photo/clean

# Convenience name for "preinstall" pass in the directory.
modules/photo/preinstall:

.PHONY : modules/photo/preinstall

#=============================================================================
# Target rules for target modules/photo/CMakeFiles/opencv_photo.dir

# All Build rule for target.
modules/photo/CMakeFiles/opencv_photo.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
modules/photo/CMakeFiles/opencv_photo.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
	$(MAKE) -f modules\photo\CMakeFiles\opencv_photo.dir\build.make modules/photo/CMakeFiles/opencv_photo.dir/depend
	$(MAKE) -f modules\photo\CMakeFiles\opencv_photo.dir\build.make modules/photo/CMakeFiles/opencv_photo.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=92,93 "Built target opencv_photo"
.PHONY : modules/photo/CMakeFiles/opencv_photo.dir/all

# Include target in all.
all: modules/photo/CMakeFiles/opencv_photo.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/photo/CMakeFiles/opencv_photo.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 21
	$(MAKE) -f CMakeFiles\Makefile2 modules/photo/CMakeFiles/opencv_photo.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/photo/CMakeFiles/opencv_photo.dir/rule

# Convenience name for target.
opencv_photo: modules/photo/CMakeFiles/opencv_photo.dir/rule

.PHONY : opencv_photo

# clean rule for target.
modules/photo/CMakeFiles/opencv_photo.dir/clean:
	$(MAKE) -f modules\photo\CMakeFiles\opencv_photo.dir\build.make modules/photo/CMakeFiles/opencv_photo.dir/clean
.PHONY : modules/photo/CMakeFiles/opencv_photo.dir/clean

# clean rule for target.
clean: modules/photo/CMakeFiles/opencv_photo.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/python_tests

# Convenience name for "all" pass in the directory.
modules/python_tests/all:

.PHONY : modules/python_tests/all

# Convenience name for "clean" pass in the directory.
modules/python_tests/clean:

.PHONY : modules/python_tests/clean

# Convenience name for "preinstall" pass in the directory.
modules/python_tests/preinstall:

.PHONY : modules/python_tests/preinstall

#=============================================================================
# Directory level rules for directory modules/dnn

# Convenience name for "all" pass in the directory.
modules/dnn/all: modules/dnn/CMakeFiles/opencv_dnn.dir/all

.PHONY : modules/dnn/all

# Convenience name for "clean" pass in the directory.
modules/dnn/clean: modules/dnn/CMakeFiles/opencv_dnn.dir/clean

.PHONY : modules/dnn/clean

# Convenience name for "preinstall" pass in the directory.
modules/dnn/preinstall:

.PHONY : modules/dnn/preinstall

#=============================================================================
# Target rules for target modules/dnn/CMakeFiles/opencv_dnn.dir

# All Build rule for target.
modules/dnn/CMakeFiles/opencv_dnn.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
modules/dnn/CMakeFiles/opencv_dnn.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
modules/dnn/CMakeFiles/opencv_dnn.dir/all: 3rdparty/protobuf/CMakeFiles/libprotobuf.dir/all
	$(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/depend
	$(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=58,59,60,61,62,63,64,65,66,67 "Built target opencv_dnn"
.PHONY : modules/dnn/CMakeFiles/opencv_dnn.dir/all

# Include target in all.
all: modules/dnn/CMakeFiles/opencv_dnn.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/dnn/CMakeFiles/opencv_dnn.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 36
	$(MAKE) -f CMakeFiles\Makefile2 modules/dnn/CMakeFiles/opencv_dnn.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/dnn/CMakeFiles/opencv_dnn.dir/rule

# Convenience name for target.
opencv_dnn: modules/dnn/CMakeFiles/opencv_dnn.dir/rule

.PHONY : opencv_dnn

# clean rule for target.
modules/dnn/CMakeFiles/opencv_dnn.dir/clean:
	$(MAKE) -f modules\dnn\CMakeFiles\opencv_dnn.dir\build.make modules/dnn/CMakeFiles/opencv_dnn.dir/clean
.PHONY : modules/dnn/CMakeFiles/opencv_dnn.dir/clean

# clean rule for target.
clean: modules/dnn/CMakeFiles/opencv_dnn.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/features2d

# Convenience name for "all" pass in the directory.
modules/features2d/all: modules/features2d/CMakeFiles/opencv_features2d.dir/all

.PHONY : modules/features2d/all

# Convenience name for "clean" pass in the directory.
modules/features2d/clean: modules/features2d/CMakeFiles/opencv_features2d.dir/clean

.PHONY : modules/features2d/clean

# Convenience name for "preinstall" pass in the directory.
modules/features2d/preinstall:

.PHONY : modules/features2d/preinstall

#=============================================================================
# Target rules for target modules/features2d/CMakeFiles/opencv_features2d.dir

# All Build rule for target.
modules/features2d/CMakeFiles/opencv_features2d.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
modules/features2d/CMakeFiles/opencv_features2d.dir/all: modules/flann/CMakeFiles/opencv_flann.dir/all
modules/features2d/CMakeFiles/opencv_features2d.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
	$(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/depend
	$(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=68,69,70 "Built target opencv_features2d"
.PHONY : modules/features2d/CMakeFiles/opencv_features2d.dir/all

# Include target in all.
all: modules/features2d/CMakeFiles/opencv_features2d.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/features2d/CMakeFiles/opencv_features2d.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 22
	$(MAKE) -f CMakeFiles\Makefile2 modules/features2d/CMakeFiles/opencv_features2d.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/features2d/CMakeFiles/opencv_features2d.dir/rule

# Convenience name for target.
opencv_features2d: modules/features2d/CMakeFiles/opencv_features2d.dir/rule

.PHONY : opencv_features2d

# clean rule for target.
modules/features2d/CMakeFiles/opencv_features2d.dir/clean:
	$(MAKE) -f modules\features2d\CMakeFiles\opencv_features2d.dir\build.make modules/features2d/CMakeFiles/opencv_features2d.dir/clean
.PHONY : modules/features2d/CMakeFiles/opencv_features2d.dir/clean

# clean rule for target.
clean: modules/features2d/CMakeFiles/opencv_features2d.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/gapi

# Convenience name for "all" pass in the directory.
modules/gapi/all: modules/gapi/CMakeFiles/opencv_gapi.dir/all

.PHONY : modules/gapi/all

# Convenience name for "clean" pass in the directory.
modules/gapi/clean: modules/gapi/CMakeFiles/opencv_gapi.dir/clean

.PHONY : modules/gapi/clean

# Convenience name for "preinstall" pass in the directory.
modules/gapi/preinstall:

.PHONY : modules/gapi/preinstall

#=============================================================================
# Target rules for target modules/gapi/CMakeFiles/opencv_gapi.dir

# All Build rule for target.
modules/gapi/CMakeFiles/opencv_gapi.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
modules/gapi/CMakeFiles/opencv_gapi.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
modules/gapi/CMakeFiles/opencv_gapi.dir/all: modules/CMakeFiles/ade.dir/all
	$(MAKE) -f modules\gapi\CMakeFiles\opencv_gapi.dir\build.make modules/gapi/CMakeFiles/opencv_gapi.dir/depend
	$(MAKE) -f modules\gapi\CMakeFiles\opencv_gapi.dir\build.make modules/gapi/CMakeFiles/opencv_gapi.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=71,72,73,74,75,76 "Built target opencv_gapi"
.PHONY : modules/gapi/CMakeFiles/opencv_gapi.dir/all

# Include target in all.
all: modules/gapi/CMakeFiles/opencv_gapi.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/gapi/CMakeFiles/opencv_gapi.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 26
	$(MAKE) -f CMakeFiles\Makefile2 modules/gapi/CMakeFiles/opencv_gapi.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/gapi/CMakeFiles/opencv_gapi.dir/rule

# Convenience name for target.
opencv_gapi: modules/gapi/CMakeFiles/opencv_gapi.dir/rule

.PHONY : opencv_gapi

# clean rule for target.
modules/gapi/CMakeFiles/opencv_gapi.dir/clean:
	$(MAKE) -f modules\gapi\CMakeFiles\opencv_gapi.dir\build.make modules/gapi/CMakeFiles/opencv_gapi.dir/clean
.PHONY : modules/gapi/CMakeFiles/opencv_gapi.dir/clean

# clean rule for target.
clean: modules/gapi/CMakeFiles/opencv_gapi.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/imgcodecs

# Convenience name for "all" pass in the directory.
modules/imgcodecs/all: modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all

.PHONY : modules/imgcodecs/all

# Convenience name for "clean" pass in the directory.
modules/imgcodecs/clean: modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/clean

.PHONY : modules/imgcodecs/clean

# Convenience name for "preinstall" pass in the directory.
modules/imgcodecs/preinstall:

.PHONY : modules/imgcodecs/preinstall

#=============================================================================
# Target rules for target modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir

# All Build rule for target.
modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all: 3rdparty/libwebp/CMakeFiles/libwebp.dir/all
modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all: 3rdparty/libtiff/CMakeFiles/libtiff.dir/all
modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all: 3rdparty/zlib/CMakeFiles/zlib.dir/all
modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all: 3rdparty/libjpeg-turbo/CMakeFiles/libjpeg-turbo.dir/all
modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all: 3rdparty/libpng/CMakeFiles/libpng.dir/all
modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all: 3rdparty/libjasper/CMakeFiles/libjasper.dir/all
modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all: 3rdparty/openexr/CMakeFiles/IlmImf.dir/all
	$(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/depend
	$(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=78,79 "Built target opencv_imgcodecs"
.PHONY : modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all

# Include target in all.
all: modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 56
	$(MAKE) -f CMakeFiles\Makefile2 modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/rule

# Convenience name for target.
opencv_imgcodecs: modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/rule

.PHONY : opencv_imgcodecs

# clean rule for target.
modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/clean:
	$(MAKE) -f modules\imgcodecs\CMakeFiles\opencv_imgcodecs.dir\build.make modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/clean
.PHONY : modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/clean

# clean rule for target.
clean: modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/videoio

# Convenience name for "all" pass in the directory.
modules/videoio/all: modules/videoio/CMakeFiles/opencv_videoio.dir/all

.PHONY : modules/videoio/all

# Convenience name for "clean" pass in the directory.
modules/videoio/clean: modules/videoio/CMakeFiles/opencv_videoio.dir/clean

.PHONY : modules/videoio/clean

# Convenience name for "preinstall" pass in the directory.
modules/videoio/preinstall:

.PHONY : modules/videoio/preinstall

#=============================================================================
# Target rules for target modules/videoio/CMakeFiles/opencv_videoio.dir

# All Build rule for target.
modules/videoio/CMakeFiles/opencv_videoio.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
modules/videoio/CMakeFiles/opencv_videoio.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
modules/videoio/CMakeFiles/opencv_videoio.dir/all: modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all
	$(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/depend
	$(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=97 "Built target opencv_videoio"
.PHONY : modules/videoio/CMakeFiles/opencv_videoio.dir/all

# Include target in all.
all: modules/videoio/CMakeFiles/opencv_videoio.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/videoio/CMakeFiles/opencv_videoio.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 57
	$(MAKE) -f CMakeFiles\Makefile2 modules/videoio/CMakeFiles/opencv_videoio.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/videoio/CMakeFiles/opencv_videoio.dir/rule

# Convenience name for target.
opencv_videoio: modules/videoio/CMakeFiles/opencv_videoio.dir/rule

.PHONY : opencv_videoio

# clean rule for target.
modules/videoio/CMakeFiles/opencv_videoio.dir/clean:
	$(MAKE) -f modules\videoio\CMakeFiles\opencv_videoio.dir\build.make modules/videoio/CMakeFiles/opencv_videoio.dir/clean
.PHONY : modules/videoio/CMakeFiles/opencv_videoio.dir/clean

# clean rule for target.
clean: modules/videoio/CMakeFiles/opencv_videoio.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/calib3d

# Convenience name for "all" pass in the directory.
modules/calib3d/all: modules/calib3d/CMakeFiles/opencv_calib3d.dir/all

.PHONY : modules/calib3d/all

# Convenience name for "clean" pass in the directory.
modules/calib3d/clean: modules/calib3d/CMakeFiles/opencv_calib3d.dir/clean

.PHONY : modules/calib3d/clean

# Convenience name for "preinstall" pass in the directory.
modules/calib3d/preinstall:

.PHONY : modules/calib3d/preinstall

#=============================================================================
# Target rules for target modules/calib3d/CMakeFiles/opencv_calib3d.dir

# All Build rule for target.
modules/calib3d/CMakeFiles/opencv_calib3d.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
modules/calib3d/CMakeFiles/opencv_calib3d.dir/all: modules/flann/CMakeFiles/opencv_flann.dir/all
modules/calib3d/CMakeFiles/opencv_calib3d.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
modules/calib3d/CMakeFiles/opencv_calib3d.dir/all: modules/features2d/CMakeFiles/opencv_features2d.dir/all
	$(MAKE) -f modules\calib3d\CMakeFiles\opencv_calib3d.dir\build.make modules/calib3d/CMakeFiles/opencv_calib3d.dir/depend
	$(MAKE) -f modules\calib3d\CMakeFiles\opencv_calib3d.dir\build.make modules/calib3d/CMakeFiles/opencv_calib3d.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=45,46,47,48 "Built target opencv_calib3d"
.PHONY : modules/calib3d/CMakeFiles/opencv_calib3d.dir/all

# Include target in all.
all: modules/calib3d/CMakeFiles/opencv_calib3d.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/calib3d/CMakeFiles/opencv_calib3d.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 26
	$(MAKE) -f CMakeFiles\Makefile2 modules/calib3d/CMakeFiles/opencv_calib3d.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/calib3d/CMakeFiles/opencv_calib3d.dir/rule

# Convenience name for target.
opencv_calib3d: modules/calib3d/CMakeFiles/opencv_calib3d.dir/rule

.PHONY : opencv_calib3d

# clean rule for target.
modules/calib3d/CMakeFiles/opencv_calib3d.dir/clean:
	$(MAKE) -f modules\calib3d\CMakeFiles\opencv_calib3d.dir\build.make modules/calib3d/CMakeFiles/opencv_calib3d.dir/clean
.PHONY : modules/calib3d/CMakeFiles/opencv_calib3d.dir/clean

# clean rule for target.
clean: modules/calib3d/CMakeFiles/opencv_calib3d.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/highgui

# Convenience name for "all" pass in the directory.
modules/highgui/all: modules/highgui/CMakeFiles/opencv_highgui.dir/all

.PHONY : modules/highgui/all

# Convenience name for "clean" pass in the directory.
modules/highgui/clean: modules/highgui/CMakeFiles/opencv_highgui.dir/clean

.PHONY : modules/highgui/clean

# Convenience name for "preinstall" pass in the directory.
modules/highgui/preinstall:

.PHONY : modules/highgui/preinstall

#=============================================================================
# Target rules for target modules/highgui/CMakeFiles/opencv_highgui.dir

# All Build rule for target.
modules/highgui/CMakeFiles/opencv_highgui.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
modules/highgui/CMakeFiles/opencv_highgui.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
modules/highgui/CMakeFiles/opencv_highgui.dir/all: modules/videoio/CMakeFiles/opencv_videoio.dir/all
modules/highgui/CMakeFiles/opencv_highgui.dir/all: modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all
	$(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/depend
	$(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=77 "Built target opencv_highgui"
.PHONY : modules/highgui/CMakeFiles/opencv_highgui.dir/all

# Include target in all.
all: modules/highgui/CMakeFiles/opencv_highgui.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/highgui/CMakeFiles/opencv_highgui.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 58
	$(MAKE) -f CMakeFiles\Makefile2 modules/highgui/CMakeFiles/opencv_highgui.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/highgui/CMakeFiles/opencv_highgui.dir/rule

# Convenience name for target.
opencv_highgui: modules/highgui/CMakeFiles/opencv_highgui.dir/rule

.PHONY : opencv_highgui

# clean rule for target.
modules/highgui/CMakeFiles/opencv_highgui.dir/clean:
	$(MAKE) -f modules\highgui\CMakeFiles\opencv_highgui.dir\build.make modules/highgui/CMakeFiles/opencv_highgui.dir/clean
.PHONY : modules/highgui/CMakeFiles/opencv_highgui.dir/clean

# clean rule for target.
clean: modules/highgui/CMakeFiles/opencv_highgui.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/objdetect

# Convenience name for "all" pass in the directory.
modules/objdetect/all: modules/objdetect/CMakeFiles/opencv_objdetect.dir/all

.PHONY : modules/objdetect/all

# Convenience name for "clean" pass in the directory.
modules/objdetect/clean: modules/objdetect/CMakeFiles/opencv_objdetect.dir/clean

.PHONY : modules/objdetect/clean

# Convenience name for "preinstall" pass in the directory.
modules/objdetect/preinstall:

.PHONY : modules/objdetect/preinstall

#=============================================================================
# Target rules for target modules/objdetect/CMakeFiles/opencv_objdetect.dir

# All Build rule for target.
modules/objdetect/CMakeFiles/opencv_objdetect.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
modules/objdetect/CMakeFiles/opencv_objdetect.dir/all: modules/flann/CMakeFiles/opencv_flann.dir/all
modules/objdetect/CMakeFiles/opencv_objdetect.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
modules/objdetect/CMakeFiles/opencv_objdetect.dir/all: modules/features2d/CMakeFiles/opencv_features2d.dir/all
modules/objdetect/CMakeFiles/opencv_objdetect.dir/all: modules/calib3d/CMakeFiles/opencv_calib3d.dir/all
modules/objdetect/CMakeFiles/opencv_objdetect.dir/all: 3rdparty/quirc/CMakeFiles/quirc.dir/all
	$(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/depend
	$(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=91 "Built target opencv_objdetect"
.PHONY : modules/objdetect/CMakeFiles/opencv_objdetect.dir/all

# Include target in all.
all: modules/objdetect/CMakeFiles/opencv_objdetect.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/objdetect/CMakeFiles/opencv_objdetect.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 27
	$(MAKE) -f CMakeFiles\Makefile2 modules/objdetect/CMakeFiles/opencv_objdetect.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/objdetect/CMakeFiles/opencv_objdetect.dir/rule

# Convenience name for target.
opencv_objdetect: modules/objdetect/CMakeFiles/opencv_objdetect.dir/rule

.PHONY : opencv_objdetect

# clean rule for target.
modules/objdetect/CMakeFiles/opencv_objdetect.dir/clean:
	$(MAKE) -f modules\objdetect\CMakeFiles\opencv_objdetect.dir\build.make modules/objdetect/CMakeFiles/opencv_objdetect.dir/clean
.PHONY : modules/objdetect/CMakeFiles/opencv_objdetect.dir/clean

# clean rule for target.
clean: modules/objdetect/CMakeFiles/opencv_objdetect.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/stitching

# Convenience name for "all" pass in the directory.
modules/stitching/all: modules/stitching/CMakeFiles/opencv_stitching.dir/all

.PHONY : modules/stitching/all

# Convenience name for "clean" pass in the directory.
modules/stitching/clean: modules/stitching/CMakeFiles/opencv_stitching.dir/clean

.PHONY : modules/stitching/clean

# Convenience name for "preinstall" pass in the directory.
modules/stitching/preinstall:

.PHONY : modules/stitching/preinstall

#=============================================================================
# Target rules for target modules/stitching/CMakeFiles/opencv_stitching.dir

# All Build rule for target.
modules/stitching/CMakeFiles/opencv_stitching.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
modules/stitching/CMakeFiles/opencv_stitching.dir/all: modules/flann/CMakeFiles/opencv_flann.dir/all
modules/stitching/CMakeFiles/opencv_stitching.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
modules/stitching/CMakeFiles/opencv_stitching.dir/all: modules/features2d/CMakeFiles/opencv_features2d.dir/all
modules/stitching/CMakeFiles/opencv_stitching.dir/all: modules/calib3d/CMakeFiles/opencv_calib3d.dir/all
	$(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/depend
	$(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=94 "Built target opencv_stitching"
.PHONY : modules/stitching/CMakeFiles/opencv_stitching.dir/all

# Include target in all.
all: modules/stitching/CMakeFiles/opencv_stitching.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/stitching/CMakeFiles/opencv_stitching.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 27
	$(MAKE) -f CMakeFiles\Makefile2 modules/stitching/CMakeFiles/opencv_stitching.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/stitching/CMakeFiles/opencv_stitching.dir/rule

# Convenience name for target.
opencv_stitching: modules/stitching/CMakeFiles/opencv_stitching.dir/rule

.PHONY : opencv_stitching

# clean rule for target.
modules/stitching/CMakeFiles/opencv_stitching.dir/clean:
	$(MAKE) -f modules\stitching\CMakeFiles\opencv_stitching.dir\build.make modules/stitching/CMakeFiles/opencv_stitching.dir/clean
.PHONY : modules/stitching/CMakeFiles/opencv_stitching.dir/clean

# clean rule for target.
clean: modules/stitching/CMakeFiles/opencv_stitching.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/video

# Convenience name for "all" pass in the directory.
modules/video/all: modules/video/CMakeFiles/opencv_video.dir/all

.PHONY : modules/video/all

# Convenience name for "clean" pass in the directory.
modules/video/clean: modules/video/CMakeFiles/opencv_video.dir/clean

.PHONY : modules/video/clean

# Convenience name for "preinstall" pass in the directory.
modules/video/preinstall:

.PHONY : modules/video/preinstall

#=============================================================================
# Target rules for target modules/video/CMakeFiles/opencv_video.dir

# All Build rule for target.
modules/video/CMakeFiles/opencv_video.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
modules/video/CMakeFiles/opencv_video.dir/all: modules/flann/CMakeFiles/opencv_flann.dir/all
modules/video/CMakeFiles/opencv_video.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
modules/video/CMakeFiles/opencv_video.dir/all: modules/features2d/CMakeFiles/opencv_features2d.dir/all
modules/video/CMakeFiles/opencv_video.dir/all: modules/calib3d/CMakeFiles/opencv_calib3d.dir/all
	$(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/depend
	$(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=96 "Built target opencv_video"
.PHONY : modules/video/CMakeFiles/opencv_video.dir/all

# Include target in all.
all: modules/video/CMakeFiles/opencv_video.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/video/CMakeFiles/opencv_video.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 27
	$(MAKE) -f CMakeFiles\Makefile2 modules/video/CMakeFiles/opencv_video.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/video/CMakeFiles/opencv_video.dir/rule

# Convenience name for target.
opencv_video: modules/video/CMakeFiles/opencv_video.dir/rule

.PHONY : opencv_video

# clean rule for target.
modules/video/CMakeFiles/opencv_video.dir/clean:
	$(MAKE) -f modules\video\CMakeFiles\opencv_video.dir\build.make modules/video/CMakeFiles/opencv_video.dir/clean
.PHONY : modules/video/CMakeFiles/opencv_video.dir/clean

# clean rule for target.
clean: modules/video/CMakeFiles/opencv_video.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/python_bindings_generator

# Convenience name for "all" pass in the directory.
modules/python_bindings_generator/all:

.PHONY : modules/python_bindings_generator/all

# Convenience name for "clean" pass in the directory.
modules/python_bindings_generator/clean: modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/clean

.PHONY : modules/python_bindings_generator/clean

# Convenience name for "preinstall" pass in the directory.
modules/python_bindings_generator/preinstall:

.PHONY : modules/python_bindings_generator/preinstall

#=============================================================================
# Target rules for target modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir

# All Build rule for target.
modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/all:
	$(MAKE) -f modules\python_bindings_generator\CMakeFiles\gen_opencv_python_source.dir\build.make modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/depend
	$(MAKE) -f modules\python_bindings_generator\CMakeFiles\gen_opencv_python_source.dir\build.make modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num= "Built target gen_opencv_python_source"
.PHONY : modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/all

# Build rule for subdir invocation for target.
modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
	$(MAKE) -f CMakeFiles\Makefile2 modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/rule

# Convenience name for target.
gen_opencv_python_source: modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/rule

.PHONY : gen_opencv_python_source

# clean rule for target.
modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/clean:
	$(MAKE) -f modules\python_bindings_generator\CMakeFiles\gen_opencv_python_source.dir\build.make modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/clean
.PHONY : modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/clean

# clean rule for target.
clean: modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory modules/python3

# Convenience name for "all" pass in the directory.
modules/python3/all: modules/python3/CMakeFiles/opencv_python3.dir/all

.PHONY : modules/python3/all

# Convenience name for "clean" pass in the directory.
modules/python3/clean: modules/python3/CMakeFiles/opencv_python3.dir/clean

.PHONY : modules/python3/clean

# Convenience name for "preinstall" pass in the directory.
modules/python3/preinstall:

.PHONY : modules/python3/preinstall

#=============================================================================
# Target rules for target modules/python3/CMakeFiles/opencv_python3.dir

# All Build rule for target.
modules/python3/CMakeFiles/opencv_python3.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
modules/python3/CMakeFiles/opencv_python3.dir/all: modules/ml/CMakeFiles/opencv_ml.dir/all
modules/python3/CMakeFiles/opencv_python3.dir/all: modules/flann/CMakeFiles/opencv_flann.dir/all
modules/python3/CMakeFiles/opencv_python3.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
modules/python3/CMakeFiles/opencv_python3.dir/all: modules/photo/CMakeFiles/opencv_photo.dir/all
modules/python3/CMakeFiles/opencv_python3.dir/all: modules/dnn/CMakeFiles/opencv_dnn.dir/all
modules/python3/CMakeFiles/opencv_python3.dir/all: modules/features2d/CMakeFiles/opencv_features2d.dir/all
modules/python3/CMakeFiles/opencv_python3.dir/all: modules/videoio/CMakeFiles/opencv_videoio.dir/all
modules/python3/CMakeFiles/opencv_python3.dir/all: modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all
modules/python3/CMakeFiles/opencv_python3.dir/all: modules/calib3d/CMakeFiles/opencv_calib3d.dir/all
modules/python3/CMakeFiles/opencv_python3.dir/all: modules/highgui/CMakeFiles/opencv_highgui.dir/all
modules/python3/CMakeFiles/opencv_python3.dir/all: modules/objdetect/CMakeFiles/opencv_objdetect.dir/all
modules/python3/CMakeFiles/opencv_python3.dir/all: modules/video/CMakeFiles/opencv_video.dir/all
modules/python3/CMakeFiles/opencv_python3.dir/all: modules/python_bindings_generator/CMakeFiles/gen_opencv_python_source.dir/all
modules/python3/CMakeFiles/opencv_python3.dir/all: modules/stitching/CMakeFiles/opencv_stitching.dir/all
	$(MAKE) -f modules\python3\CMakeFiles\opencv_python3.dir\build.make modules/python3/CMakeFiles/opencv_python3.dir/depend
	$(MAKE) -f modules\python3\CMakeFiles\opencv_python3.dir\build.make modules/python3/CMakeFiles/opencv_python3.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num= "Built target opencv_python3"
.PHONY : modules/python3/CMakeFiles/opencv_python3.dir/all

# Include target in all.
all: modules/python3/CMakeFiles/opencv_python3.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
modules/python3/CMakeFiles/opencv_python3.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 89
	$(MAKE) -f CMakeFiles\Makefile2 modules/python3/CMakeFiles/opencv_python3.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : modules/python3/CMakeFiles/opencv_python3.dir/rule

# Convenience name for target.
opencv_python3: modules/python3/CMakeFiles/opencv_python3.dir/rule

.PHONY : opencv_python3

# clean rule for target.
modules/python3/CMakeFiles/opencv_python3.dir/clean:
	$(MAKE) -f modules\python3\CMakeFiles\opencv_python3.dir\build.make modules/python3/CMakeFiles/opencv_python3.dir/clean
.PHONY : modules/python3/CMakeFiles/opencv_python3.dir/clean

# clean rule for target.
clean: modules/python3/CMakeFiles/opencv_python3.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory doc

# Convenience name for "all" pass in the directory.
doc/all:

.PHONY : doc/all

# Convenience name for "clean" pass in the directory.
doc/clean:

.PHONY : doc/clean

# Convenience name for "preinstall" pass in the directory.
doc/preinstall:

.PHONY : doc/preinstall

#=============================================================================
# Directory level rules for directory data

# Convenience name for "all" pass in the directory.
data/all:

.PHONY : data/all

# Convenience name for "clean" pass in the directory.
data/clean:

.PHONY : data/clean

# Convenience name for "preinstall" pass in the directory.
data/preinstall:

.PHONY : data/preinstall

#=============================================================================
# Directory level rules for directory apps

# Convenience name for "all" pass in the directory.
apps/all: apps/annotation/all
apps/all: apps/visualisation/all
apps/all: apps/interactive-calibration/all
apps/all: apps/version/all

.PHONY : apps/all

# Convenience name for "clean" pass in the directory.
apps/clean: apps/annotation/clean
apps/clean: apps/visualisation/clean
apps/clean: apps/interactive-calibration/clean
apps/clean: apps/version/clean

.PHONY : apps/clean

# Convenience name for "preinstall" pass in the directory.
apps/preinstall: apps/annotation/preinstall
apps/preinstall: apps/visualisation/preinstall
apps/preinstall: apps/interactive-calibration/preinstall
apps/preinstall: apps/version/preinstall

.PHONY : apps/preinstall

#=============================================================================
# Directory level rules for directory apps/annotation

# Convenience name for "all" pass in the directory.
apps/annotation/all: apps/annotation/CMakeFiles/opencv_annotation.dir/all

.PHONY : apps/annotation/all

# Convenience name for "clean" pass in the directory.
apps/annotation/clean: apps/annotation/CMakeFiles/opencv_annotation.dir/clean

.PHONY : apps/annotation/clean

# Convenience name for "preinstall" pass in the directory.
apps/annotation/preinstall:

.PHONY : apps/annotation/preinstall

#=============================================================================
# Target rules for target apps/annotation/CMakeFiles/opencv_annotation.dir

# All Build rule for target.
apps/annotation/CMakeFiles/opencv_annotation.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
apps/annotation/CMakeFiles/opencv_annotation.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
apps/annotation/CMakeFiles/opencv_annotation.dir/all: modules/videoio/CMakeFiles/opencv_videoio.dir/all
apps/annotation/CMakeFiles/opencv_annotation.dir/all: modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all
apps/annotation/CMakeFiles/opencv_annotation.dir/all: modules/highgui/CMakeFiles/opencv_highgui.dir/all
	$(MAKE) -f apps\annotation\CMakeFiles\opencv_annotation.dir\build.make apps/annotation/CMakeFiles/opencv_annotation.dir/depend
	$(MAKE) -f apps\annotation\CMakeFiles\opencv_annotation.dir\build.make apps/annotation/CMakeFiles/opencv_annotation.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num= "Built target opencv_annotation"
.PHONY : apps/annotation/CMakeFiles/opencv_annotation.dir/all

# Include target in all.
all: apps/annotation/CMakeFiles/opencv_annotation.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
apps/annotation/CMakeFiles/opencv_annotation.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 58
	$(MAKE) -f CMakeFiles\Makefile2 apps/annotation/CMakeFiles/opencv_annotation.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : apps/annotation/CMakeFiles/opencv_annotation.dir/rule

# Convenience name for target.
opencv_annotation: apps/annotation/CMakeFiles/opencv_annotation.dir/rule

.PHONY : opencv_annotation

# clean rule for target.
apps/annotation/CMakeFiles/opencv_annotation.dir/clean:
	$(MAKE) -f apps\annotation\CMakeFiles\opencv_annotation.dir\build.make apps/annotation/CMakeFiles/opencv_annotation.dir/clean
.PHONY : apps/annotation/CMakeFiles/opencv_annotation.dir/clean

# clean rule for target.
clean: apps/annotation/CMakeFiles/opencv_annotation.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory apps/visualisation

# Convenience name for "all" pass in the directory.
apps/visualisation/all: apps/visualisation/CMakeFiles/opencv_visualisation.dir/all

.PHONY : apps/visualisation/all

# Convenience name for "clean" pass in the directory.
apps/visualisation/clean: apps/visualisation/CMakeFiles/opencv_visualisation.dir/clean

.PHONY : apps/visualisation/clean

# Convenience name for "preinstall" pass in the directory.
apps/visualisation/preinstall:

.PHONY : apps/visualisation/preinstall

#=============================================================================
# Target rules for target apps/visualisation/CMakeFiles/opencv_visualisation.dir

# All Build rule for target.
apps/visualisation/CMakeFiles/opencv_visualisation.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
apps/visualisation/CMakeFiles/opencv_visualisation.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
apps/visualisation/CMakeFiles/opencv_visualisation.dir/all: modules/videoio/CMakeFiles/opencv_videoio.dir/all
apps/visualisation/CMakeFiles/opencv_visualisation.dir/all: modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all
apps/visualisation/CMakeFiles/opencv_visualisation.dir/all: modules/highgui/CMakeFiles/opencv_highgui.dir/all
	$(MAKE) -f apps\visualisation\CMakeFiles\opencv_visualisation.dir\build.make apps/visualisation/CMakeFiles/opencv_visualisation.dir/depend
	$(MAKE) -f apps\visualisation\CMakeFiles\opencv_visualisation.dir\build.make apps/visualisation/CMakeFiles/opencv_visualisation.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=98 "Built target opencv_visualisation"
.PHONY : apps/visualisation/CMakeFiles/opencv_visualisation.dir/all

# Include target in all.
all: apps/visualisation/CMakeFiles/opencv_visualisation.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
apps/visualisation/CMakeFiles/opencv_visualisation.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 59
	$(MAKE) -f CMakeFiles\Makefile2 apps/visualisation/CMakeFiles/opencv_visualisation.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : apps/visualisation/CMakeFiles/opencv_visualisation.dir/rule

# Convenience name for target.
opencv_visualisation: apps/visualisation/CMakeFiles/opencv_visualisation.dir/rule

.PHONY : opencv_visualisation

# clean rule for target.
apps/visualisation/CMakeFiles/opencv_visualisation.dir/clean:
	$(MAKE) -f apps\visualisation\CMakeFiles\opencv_visualisation.dir\build.make apps/visualisation/CMakeFiles/opencv_visualisation.dir/clean
.PHONY : apps/visualisation/CMakeFiles/opencv_visualisation.dir/clean

# clean rule for target.
clean: apps/visualisation/CMakeFiles/opencv_visualisation.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory apps/interactive-calibration

# Convenience name for "all" pass in the directory.
apps/interactive-calibration/all: apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/all

.PHONY : apps/interactive-calibration/all

# Convenience name for "clean" pass in the directory.
apps/interactive-calibration/clean: apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/clean

.PHONY : apps/interactive-calibration/clean

# Convenience name for "preinstall" pass in the directory.
apps/interactive-calibration/preinstall:

.PHONY : apps/interactive-calibration/preinstall

#=============================================================================
# Target rules for target apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir

# All Build rule for target.
apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/all: modules/imgproc/CMakeFiles/opencv_imgproc.dir/all
apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/all: modules/flann/CMakeFiles/opencv_flann.dir/all
apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/all: modules/features2d/CMakeFiles/opencv_features2d.dir/all
apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/all: modules/videoio/CMakeFiles/opencv_videoio.dir/all
apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/all: modules/imgcodecs/CMakeFiles/opencv_imgcodecs.dir/all
apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/all: modules/calib3d/CMakeFiles/opencv_calib3d.dir/all
apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/all: modules/highgui/CMakeFiles/opencv_highgui.dir/all
	$(MAKE) -f apps\interactive-calibration\CMakeFiles\opencv_interactive-calibration.dir\build.make apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/depend
	$(MAKE) -f apps\interactive-calibration\CMakeFiles\opencv_interactive-calibration.dir\build.make apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=88 "Built target opencv_interactive-calibration"
.PHONY : apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/all

# Include target in all.
all: apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 66
	$(MAKE) -f CMakeFiles\Makefile2 apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/rule

# Convenience name for target.
opencv_interactive-calibration: apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/rule

.PHONY : opencv_interactive-calibration

# clean rule for target.
apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/clean:
	$(MAKE) -f apps\interactive-calibration\CMakeFiles\opencv_interactive-calibration.dir\build.make apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/clean
.PHONY : apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/clean

# clean rule for target.
clean: apps/interactive-calibration/CMakeFiles/opencv_interactive-calibration.dir/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory apps/version

# Convenience name for "all" pass in the directory.
apps/version/all: apps/version/CMakeFiles/opencv_version.dir/all
apps/version/all: apps/version/CMakeFiles/opencv_version_win32.dir/all

.PHONY : apps/version/all

# Convenience name for "clean" pass in the directory.
apps/version/clean: apps/version/CMakeFiles/opencv_version.dir/clean
apps/version/clean: apps/version/CMakeFiles/opencv_version_win32.dir/clean

.PHONY : apps/version/clean

# Convenience name for "preinstall" pass in the directory.
apps/version/preinstall:

.PHONY : apps/version/preinstall

#=============================================================================
# Target rules for target apps/version/CMakeFiles/opencv_version.dir

# All Build rule for target.
apps/version/CMakeFiles/opencv_version.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
	$(MAKE) -f apps\version\CMakeFiles\opencv_version.dir\build.make apps/version/CMakeFiles/opencv_version.dir/depend
	$(MAKE) -f apps\version\CMakeFiles\opencv_version.dir\build.make apps/version/CMakeFiles/opencv_version.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num=95 "Built target opencv_version"
.PHONY : apps/version/CMakeFiles/opencv_version.dir/all

# Include target in all.
all: apps/version/CMakeFiles/opencv_version.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
apps/version/CMakeFiles/opencv_version.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 12
	$(MAKE) -f CMakeFiles\Makefile2 apps/version/CMakeFiles/opencv_version.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : apps/version/CMakeFiles/opencv_version.dir/rule

# Convenience name for target.
opencv_version: apps/version/CMakeFiles/opencv_version.dir/rule

.PHONY : opencv_version

# clean rule for target.
apps/version/CMakeFiles/opencv_version.dir/clean:
	$(MAKE) -f apps\version\CMakeFiles\opencv_version.dir\build.make apps/version/CMakeFiles/opencv_version.dir/clean
.PHONY : apps/version/CMakeFiles/opencv_version.dir/clean

# clean rule for target.
clean: apps/version/CMakeFiles/opencv_version.dir/clean

.PHONY : clean

#=============================================================================
# Target rules for target apps/version/CMakeFiles/opencv_version_win32.dir

# All Build rule for target.
apps/version/CMakeFiles/opencv_version_win32.dir/all: modules/core/CMakeFiles/opencv_core.dir/all
	$(MAKE) -f apps\version\CMakeFiles\opencv_version_win32.dir\build.make apps/version/CMakeFiles/opencv_version_win32.dir/depend
	$(MAKE) -f apps\version\CMakeFiles\opencv_version_win32.dir\build.make apps/version/CMakeFiles/opencv_version_win32.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=D:\unet\opencv\opencv\mingw_build\CMakeFiles --progress-num= "Built target opencv_version_win32"
.PHONY : apps/version/CMakeFiles/opencv_version_win32.dir/all

# Include target in all.
all: apps/version/CMakeFiles/opencv_version_win32.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
apps/version/CMakeFiles/opencv_version_win32.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 11
	$(MAKE) -f CMakeFiles\Makefile2 apps/version/CMakeFiles/opencv_version_win32.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\unet\opencv\opencv\mingw_build\CMakeFiles 0
.PHONY : apps/version/CMakeFiles/opencv_version_win32.dir/rule

# Convenience name for target.
opencv_version_win32: apps/version/CMakeFiles/opencv_version_win32.dir/rule

.PHONY : opencv_version_win32

# clean rule for target.
apps/version/CMakeFiles/opencv_version_win32.dir/clean:
	$(MAKE) -f apps\version\CMakeFiles\opencv_version_win32.dir\build.make apps/version/CMakeFiles/opencv_version_win32.dir/clean
.PHONY : apps/version/CMakeFiles/opencv_version_win32.dir/clean

# clean rule for target.
clean: apps/version/CMakeFiles/opencv_version_win32.dir/clean

.PHONY : clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

