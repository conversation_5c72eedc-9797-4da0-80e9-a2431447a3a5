{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "D:\\unet\\opencv\\opencv\\mingw_build\\install\\include", "D:\\unet\\opencv\\opencv\\mingw_build\\install\\include\\opencv2"], "defines": [], "windowsSdkVersion": "8.1", "compilerPath": "D:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/gcc.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "clang-x64"}], "version": 4}