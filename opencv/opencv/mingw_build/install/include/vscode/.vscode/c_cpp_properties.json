{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**", "D:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/include/*", "D:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/*", "D:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32/*", "D:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward/*", "D:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/*", "D:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed", "D:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/../../../../x86_64-w64-mingw32/include", "D:/unet/opencv/opencv/mingw_build/install/include/**", "D:/unet/opencv/opencv/mingw_build/install/include/opencv2/**", "D:/unet/opencv/opencv/mingw_build/install/include"], "defines": [], "compilerPath": "D:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/gcc.exe", "cStandard": "c11", "cppStandard": "c++17", "intelliSenseMode": "gcc-x64", "browse": {"path": ["${workspaceFolder}", "D:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/include/**", "D:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include", "D:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++", "D:/unet/opencv/opencv/mingw_build/install/include"], "limitSymbolsToIncludedHeaders": false, "databaseFilename": ""}}], "version": 4}