{"version": "2.0.0", "command": "g++", "args": ["-g", "${fileDirname}\\duqu.cpp", "-o", "${fileBasenameNoExtension}.exe", "-I", "${fileDirname}\\D:/unet/opencv/opencv/mingw_build/install/include", "-I", "${fileDirname}\\D:/unet/opencv/opencv/mingw_build/install/include/opencv2", "-LD:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin", "-LD:/unet/opencv/opencv/mingw_build/install/x64/mingw/lib", "-llibopencv_calib3d420", "-llibopencv_core420", "-llibopencv_dnn420", "-llibopencv_features2d420", "-llibopencv_flann420", "-llibopencv_highgui420", "-llibopencv_imgcodecs420", "-llibopencv_imgproc420", "-llibopencv_ml420", "-llibopencv_objdetect420", "-llibopencv_photo420", "-llibopencv_stitching420", "-llibopencv_video420", "-llibopencv_videoio420"], "problemMatcher": {"owner": "cpp", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}, "group": {"kind": "build", "isDefault": true}}