/*M///////////////////////////////////////////////////////////////////////////////////////
//
//  IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
//
//  By downloading, copying, installing or using the software you agree to this license.
//  If you do not agree to this license, do not download, install,
//  copy or use the software.
//
//
//                           License Agreement
//                For Open Source Computer Vision Library
//
// Copyright (C) 2000-2008, Intel Corporation, all rights reserved.
// Copyright (C) 2009, Willow Garage Inc., all rights reserved.
// Third party copyrights are property of their respective owners.
//
// Redistribution and use in source and binary forms, with or without modification,
// are permitted provided that the following conditions are met:
//
//   * Redistribution's of source code must retain the above copyright notice,
//     this list of conditions and the following disclaimer.
//
//   * Redistribution's in binary form must reproduce the above copyright notice,
//     this list of conditions and the following disclaimer in the documentation
//     and/or other materials provided with the distribution.
//
//   * The name of the copyright holders may not be used to endorse or promote products
//     derived from this software without specific prior written permission.
//
// This software is provided by the copyright holders and contributors "as is" and
// any express or implied warranties, including, but not limited to, the implied
// warranties of merchantability and fitness for a particular purpose are disclaimed.
// In no event shall the Intel Corporation or contributors be liable for any direct,
// indirect, incidental, special, exemplary, or consequential damages
// (including, but not limited to, procurement of substitute goods or services;
// loss of use, data, or profits; or business interruption) however caused
// and on any theory of liability, whether in contract, strict liability,
// or tort (including negligence or otherwise) arising in any way out of
// the use of this software, even if advised of the possibility of such damage.
//
//M*/

#ifndef OPENCV_CORE_CUDA_STREAM_ACCESSOR_HPP
#define OPENCV_CORE_CUDA_STREAM_ACCESSOR_HPP

#ifndef __cplusplus
#  error cuda_stream_accessor.hpp header must be compiled as C++
#endif

/** @file cuda_stream_accessor.hpp
 * This is only header file that depends on CUDA Runtime API. All other headers are independent.
 */

#include <cuda_runtime.h>
#include "D:/unet/opencv/opencv/mingw_build/install/include/opencv2/core/cuda.hpp"

namespace cv
{
    namespace cuda
    {

//! @addtogroup cudacore_struct
//! @{

        /** @brief Class that enables getting cudaStream_t from cuda::Stream
         */
        struct StreamAccessor
        {
            CV_EXPORTS static cudaStream_t getStream(const Stream& stream);
            CV_EXPORTS static Stream wrapStream(cudaStream_t stream);
        };

        /** @brief Class that enables getting cudaEvent_t from cuda::Event
         */
        struct EventAccessor
        {
            CV_EXPORTS static cudaEvent_t getEvent(const Event& event);
            CV_EXPORTS static Event wrapEvent(cudaEvent_t event);
        };

//! @}

    }
}

#endif /* OPENCV_CORE_CUDA_STREAM_ACCESSOR_HPP */
