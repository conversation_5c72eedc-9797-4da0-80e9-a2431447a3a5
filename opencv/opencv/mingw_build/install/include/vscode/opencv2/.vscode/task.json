{"version": "2.0.0", "tasks": [{"type": "shell", "label": "opencv4.2.0 compile task", "command": "D:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin/g++.exe", "args": ["-g", "${file}", "-o", "${workspaceFolder}\\${fileBasenameNoExtension}.exe", "D:/unet/opencv/opencv/mingw_build/install/x64/mingw/bin/libopencv_world342.dll", "-I", "D:/unet/opencv/opencv/mingw_build/install/include", "-I", "D:/unet/opencv/opencv/mingw_build/install/include/opencv", "-I", "D:/unet/opencv/opencv/mingw_build/install/include/D:/unet/opencv/opencv/mingw_build/install/include/opencv2"], "options": {"cwd": "D:/x86_64-8.1.0-release-posix-seh-rt_v6-rev0/mingw64/bin"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}}]}