// This file is part of OpenCV project.
// It is subject to the license terms in the LICENSE file found in the top-level directory
// of this distribution and at http://opencv.org/license.html.
//
// Copyright (C) 2018 Intel Corporation


#ifndef OPENCV_GAPI_HPP
#define OPENCV_GAPI_HPP

#include <memory>

/** \defgroup gapi G-API framework
@{
    @defgroup gapi_main_classes G-API Main Classes
    @defgroup gapi_data_objects G-API Data Types
    @{
      @defgroup gapi_meta_args G-API Metadata Descriptors
    @}
    @defgroup gapi_std_backends G-API Standard Backends
    @defgroup gapi_compile_args G-API Graph Compilation Arguments
@}
 */

#include <D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gmat.hpp>
#include <D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/garray.hpp>
#include <D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gcomputation.hpp>
#include <D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gcompiled.hpp>
#include <D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gtyped.hpp>
#include <D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/gkernel.hpp>
#include <D:/unet/opencv/opencv/mingw_build/install/include/opencv2/gapi/operators.hpp>

#endif // OPENCV_GAPI_HPP
