curl --create-dirs --output "D:/unet/opencv/opencv/sources/.cache/ffmpeg/5de6044cad9398549e57bc46fc13908d-opencv_videoio_ffmpeg.dll" "https://raw.githubusercontent.com/opencv/opencv_3rdparty/a66a24e9f410ae05da4baeeb8b451912664ce49c/ffmpeg/opencv_videoio_ffmpeg.dll"
curl --create-dirs --output "D:/unet/opencv/opencv/sources/.cache/ffmpeg/55c0bc8ad27db00116fabf06508de196-opencv_videoio_ffmpeg_64.dll" "https://raw.githubusercontent.com/opencv/opencv_3rdparty/a66a24e9f410ae05da4baeeb8b451912664ce49c/ffmpeg/opencv_videoio_ffmpeg_64.dll"
curl --create-dirs --output "D:/unet/opencv/opencv/sources/.cache/ffmpeg/ad57c038ba34b868277ccbe6dd0f9602-ffmpeg_version.cmake" "https://raw.githubusercontent.com/opencv/opencv_3rdparty/a66a24e9f410ae05da4baeeb8b451912664ce49c/ffmpeg/ffmpeg_version.cmake"
