#use_cache "D:/unet/opencv/opencv/sources/.cache"
#match_hash_in_cmake_cache "OCV_DOWNLOAD_ADE_HASH_3rdparty_ade_v0_1_1f_zip"
#do_copy "opencv_videoio_ffmpeg.dll" "5de6044cad9398549e57bc46fc13908d" "https://raw.githubusercontent.com/opencv/opencv_3rdparty/a66a24e9f410ae05da4baeeb8b451912664ce49c/ffmpeg/opencv_videoio_ffmpeg.dll" "D:/unet/opencv/opencv/mingw_build/3rdparty/ffmpeg"
#missing "D:/unet/opencv/opencv/mingw_build/3rdparty/ffmpeg/opencv_videoio_ffmpeg.dll"
#check_md5 "D:/unet/opencv/opencv/sources/.cache/ffmpeg/5de6044cad9398549e57bc46fc13908d-opencv_videoio_ffmpeg.dll"
#mismatch_md5 "D:/unet/opencv/opencv/sources/.cache/ffmpeg/5de6044cad9398549e57bc46fc13908d-opencv_videoio_ffmpeg.dll" "d41d8cd98f00b204e9800998ecf8427e"
#delete "D:/unet/opencv/opencv/sources/.cache/ffmpeg/5de6044cad9398549e57bc46fc13908d-opencv_videoio_ffmpeg.dll"
#cmake_download "D:/unet/opencv/opencv/sources/.cache/ffmpeg/5de6044cad9398549e57bc46fc13908d-opencv_videoio_ffmpeg.dll" "https://raw.githubusercontent.com/opencv/opencv_3rdparty/a66a24e9f410ae05da4baeeb8b451912664ce49c/ffmpeg/opencv_videoio_ffmpeg.dll"
#try 1
# timeout on name lookup is not supported
#   Trying 185.199.110.133...
# TCP_NODELAY set
# Connected to raw.githubusercontent.com (185.199.110.133) port 443 (#0)
# schannel: SSL/TLS connection with raw.githubusercontent.com port 443 (step 1/3)
# schannel: disabled server certificate revocation checks
# schannel: sending initial handshake data: sending 187 bytes...
# schannel: sent initial handshake data: sent 187 bytes
# schannel: SSL/TLS connection with raw.githubusercontent.com port 443 (step 2/3)
# schannel: failed to receive handshake, need more data
# schannel: SSL/TLS connection with raw.githubusercontent.com port 443 (step 2/3)
# schannel: failed to receive handshake, SSL/TLS connection failed
# Closing connection 0
# schannel: shutting down SSL/TLS connection with raw.githubusercontent.com port 443
# Send failure: Connection was reset
# schannel: failed to send close msg: Failed sending data to the peer (bytes written: -1)
# schannel: clear security context handle
# 

#do_copy "opencv_videoio_ffmpeg_64.dll" "55c0bc8ad27db00116fabf06508de196" "https://raw.githubusercontent.com/opencv/opencv_3rdparty/a66a24e9f410ae05da4baeeb8b451912664ce49c/ffmpeg/opencv_videoio_ffmpeg_64.dll" "D:/unet/opencv/opencv/mingw_build/3rdparty/ffmpeg"
#missing "D:/unet/opencv/opencv/mingw_build/3rdparty/ffmpeg/opencv_videoio_ffmpeg_64.dll"
#check_md5 "D:/unet/opencv/opencv/sources/.cache/ffmpeg/55c0bc8ad27db00116fabf06508de196-opencv_videoio_ffmpeg_64.dll"
#mismatch_md5 "D:/unet/opencv/opencv/sources/.cache/ffmpeg/55c0bc8ad27db00116fabf06508de196-opencv_videoio_ffmpeg_64.dll" "d41d8cd98f00b204e9800998ecf8427e"
#delete "D:/unet/opencv/opencv/sources/.cache/ffmpeg/55c0bc8ad27db00116fabf06508de196-opencv_videoio_ffmpeg_64.dll"
#cmake_download "D:/unet/opencv/opencv/sources/.cache/ffmpeg/55c0bc8ad27db00116fabf06508de196-opencv_videoio_ffmpeg_64.dll" "https://raw.githubusercontent.com/opencv/opencv_3rdparty/a66a24e9f410ae05da4baeeb8b451912664ce49c/ffmpeg/opencv_videoio_ffmpeg_64.dll"
#try 1
# timeout on name lookup is not supported
#   Trying 185.199.110.133...
# TCP_NODELAY set
# Connected to raw.githubusercontent.com (185.199.110.133) port 443 (#0)
# schannel: SSL/TLS connection with raw.githubusercontent.com port 443 (step 1/3)
# schannel: disabled server certificate revocation checks
# schannel: sending initial handshake data: sending 187 bytes...
# schannel: sent initial handshake data: sent 187 bytes
# schannel: SSL/TLS connection with raw.githubusercontent.com port 443 (step 2/3)
# schannel: failed to receive handshake, need more data
# schannel: SSL/TLS connection with raw.githubusercontent.com port 443 (step 2/3)
# schannel: failed to receive handshake, SSL/TLS connection failed
# Closing connection 0
# schannel: shutting down SSL/TLS connection with raw.githubusercontent.com port 443
# Send failure: Connection was reset
# schannel: failed to send close msg: Failed sending data to the peer (bytes written: -1)
# schannel: clear security context handle
# 

#do_copy "ffmpeg_version.cmake" "ad57c038ba34b868277ccbe6dd0f9602" "https://raw.githubusercontent.com/opencv/opencv_3rdparty/a66a24e9f410ae05da4baeeb8b451912664ce49c/ffmpeg/ffmpeg_version.cmake" "D:/unet/opencv/opencv/mingw_build/3rdparty/ffmpeg"
#missing "D:/unet/opencv/opencv/mingw_build/3rdparty/ffmpeg/ffmpeg_version.cmake"
#check_md5 "D:/unet/opencv/opencv/sources/.cache/ffmpeg/ad57c038ba34b868277ccbe6dd0f9602-ffmpeg_version.cmake"
#mismatch_md5 "D:/unet/opencv/opencv/sources/.cache/ffmpeg/ad57c038ba34b868277ccbe6dd0f9602-ffmpeg_version.cmake" "d41d8cd98f00b204e9800998ecf8427e"
#delete "D:/unet/opencv/opencv/sources/.cache/ffmpeg/ad57c038ba34b868277ccbe6dd0f9602-ffmpeg_version.cmake"
#cmake_download "D:/unet/opencv/opencv/sources/.cache/ffmpeg/ad57c038ba34b868277ccbe6dd0f9602-ffmpeg_version.cmake" "https://raw.githubusercontent.com/opencv/opencv_3rdparty/a66a24e9f410ae05da4baeeb8b451912664ce49c/ffmpeg/ffmpeg_version.cmake"
#try 1
# timeout on name lookup is not supported
#   Trying 185.199.110.133...
# TCP_NODELAY set
# Connected to raw.githubusercontent.com (185.199.110.133) port 443 (#0)
# schannel: SSL/TLS connection with raw.githubusercontent.com port 443 (step 1/3)
# schannel: disabled server certificate revocation checks
# schannel: sending initial handshake data: sending 187 bytes...
# schannel: sent initial handshake data: sent 187 bytes
# schannel: SSL/TLS connection with raw.githubusercontent.com port 443 (step 2/3)
# schannel: failed to receive handshake, need more data
# schannel: SSL/TLS connection with raw.githubusercontent.com port 443 (step 2/3)
# schannel: failed to receive handshake, SSL/TLS connection failed
# Closing connection 0
# schannel: shutting down SSL/TLS connection with raw.githubusercontent.com port 443
# Send failure: Connection was reset
# schannel: failed to send close msg: Failed sending data to the peer (bytes written: -1)
# schannel: clear security context handle
# 

